import OperationIconTitle from '@/components/OperationIconTitle';
import useI18n from '@/hooks/useI8n';
import { TableColumnsType } from 'antd';
import { CreationSource } from '../../const';
import ResizableTable from '@/components/ResizableTable';
import { useTableFilter } from '@/hooks/useTableFilter';
import { useEffect, useMemo } from 'react';

interface IEntitiesUsing {
  usingEnumEntityProp: any[];
}

const EntitiesUsingTable: React.FC<IEntitiesUsing> = ({ usingEnumEntityProp = [] }) => {
  const { formatMessage } = useI18n();

  const PropTypeSource = [
    { name: 'String', key: 'String' },
    { name: 'Enum', key: 'Enum' },
    { name: 'Long', key: 'Long' },
  ];
  // 使用 useMemo 确保 dataSource 是稳定的引用
  const memoizedDataSource = useMemo(
    () => (Array.isArray(usingEnumEntityProp) ? usingEnumEntityProp : []),
    [usingEnumEntityProp],
  );
  // 自定义hooks，支持表格列筛选并分页展示
  const { filteredDataSource, getColumnSearchProps, getColumnEnumSearchProps, clearAllFilters } =
    useTableFilter(memoizedDataSource);

  useEffect(() => {
    // 重置表格列的筛选条件
    clearAllFilters();
  }, [usingEnumEntityProp]);

  const columns: TableColumnsType = [
    {
      dataIndex: 'entityName',
      width: '15%',
      ellipsis: true,
      title: formatMessage('ENUMMANAGEMENTLIST.ENUMS.ENTITYNAME'),
      ...getColumnSearchProps('entityName'),
    },
    {
      dataIndex: 'propName',
      width: '15%',
      ellipsis: true,
      title: formatMessage('ENUMMANAGEMENTLIST.ENUMS.PROPERTYNAME'),
      ...getColumnSearchProps('propName'),
    },
    {
      dataIndex: 'propType',
      width: '150px',
      ellipsis: true,
      title: formatMessage('ENUMMANAGEMENTLIST.ENUMS.PROPERTYTYPE'),
      ...getColumnEnumSearchProps('propType', PropTypeSource),
    },
    {
      dataIndex: 'creationSrc',
      width: '150px',
      ellipsis: true,
      title: formatMessage('ENUMMANAGEMENTLIST.ENUMS.CREATIONSOURCE'),
      render: (_: string, record: any) => {
        const matchedItem = CreationSource.find((i: { key: string; name: string }) => i.key === record.creationSrc);
        return matchedItem ? matchedItem.name : '';
      },
      ...getColumnEnumSearchProps('creationSrc', CreationSource),
    },
    {
      dataIndex: 'comments',
      ellipsis: true,
      title: formatMessage('ENUMMANAGEMENTLIST.ENUMS.DESC'),
      ...getColumnSearchProps('comments'),
    },
  ];

  return (
    <div>
      <OperationIconTitle title={formatMessage('ENUMMANAGEMENTLIST.ENUMS.TITLE')} />
      <ResizableTable
        size="small"
        rowKey="key"
        columns={columns}
        pagination={{
          hideOnSinglePage: true,
          pageSize: 5,
          showTotal: (total) => formatMessage('PROJECT.COMMON.PAGINATION.TOTAL', { total }),
        }}
        dataSource={(filteredDataSource || [])?.map((el: any, index: number) => ({
          ...el,
          key: `EntitiesUsingTable-${index}-${el?.entityId}`,
        }))}
      />
    </div>
  );
};

export default EntitiesUsingTable;
