import React, { useEffect, useState } from 'react';
import { DeleteOutlined } from '@ant-design/icons';
import { Modal, Row, Col, Button, Form, Input, Popconfirm, Spin, TableColumnsType } from 'antd';
import { UseCaseBeQueriedPropsType, UseCaseBeQueriedListPropsType } from '@/services/typing';
import styles from './index.less';
import {
  getUseCaseServicePropsType,
  getUseCaseService,
  saveAsUseCaseServicePropsType,
  deleteUseCaseService,
} from '@/services';
import ResizableTable from '../ResizableTable';

interface UseCaseBeSelectedModalPropsType {
  title: string;
  open: boolean;
  formatMessage: (k: string, values?: Record<string, any>) => any;
  setOpen: (e: any) => void;
  initQueryValue: getUseCaseServicePropsType;
  saveDebugBackFillingInfo: (
    e: Pick<saveAsUseCaseServicePropsType, 'serviceReqParamList' | 'serviceRespParamList'>,
  ) => any;
}

const UseCaseBeSelectedModal: React.FC<UseCaseBeSelectedModalPropsType> = ({
  title = '',
  open = false,
  setOpen = () => {},
  formatMessage = () => {},
  initQueryValue,
  saveDebugBackFillingInfo = () => {},
}) => {
  const [form] = Form.useForm();

  // 高亮，设置
  const [rowId, setRowId] = useState<string | number>('');
  const [selectedValues, setSelectedValues] = useState<UseCaseBeQueriedListPropsType | any>({});
  const setRowClass = (record: UseCaseBeQueriedListPropsType) => {
    return record.useCaseId === rowId ? styles.rowSelectClassName : styles.rowClassName;
  };

  // 重置已选择的数据
  const resetSelectedValueFn = () => {
    setSelectedValues({});
    setRowId('');
  };

  // 表格，删除
  const onDeleteConfirm = async (useCaseId: string | number) => {
    if (useCaseId) {
      try {
        await deleteUseCaseService(useCaseId).then((res) => {
          if (res?.success) {
            // 删除成功后重新查询
            setInfoUsedforSearching({
              ...infoUsedforSearching,
              pageNo: 1,
            });
          }
        });
      } catch (error) {}
    }
  };

  // 表格，当前用于查询的信息
  const [infoUsedforSearching, setInfoUsedforSearching] = useState<getUseCaseServicePropsType>(initQueryValue);
  const [tableSource, setTableSource] = useState<UseCaseBeQueriedPropsType>({});
  // 表格，列
  const columns: TableColumnsType = [
    {
      dataIndex: 'useCaseName',
      title: formatMessage('DEBUGSERVICE.USECASEBESELECTEDMODAL.USECASENAME'),
    },
    {
      dataIndex: 'includeResp',
      title: formatMessage('DEBUGSERVICE.USECASEBESELECTEDMODAL.INCLUDERESPONSE'),
      render: (text) => {
        switch (text) {
          case 'Y':
            return 'Yes';
          case 'N':
            return 'No';
          default:
            break;
        }
      },
    },
    {
      dataIndex: 'comments',
      title: formatMessage('DEBUGSERVICE.USECASEBESELECTEDMODAL.COMMENTS'),
    },
    {
      dataIndex: 'operation',
      title: formatMessage('DEBUGSERVICE.USECASEBESELECTEDMODAL.OPERATION'),
      render: (text: any, record: UseCaseBeQueriedListPropsType, index: number) => {
        return (
          <Popconfirm
            title=""
            description={formatMessage('DEBUGSERVICE.USECASEBESELECTEDMODAL.CONFIRMTIP')}
            onConfirm={() => {
              onDeleteConfirm(record?.useCaseId);
            }}
            okText="Yes"
            cancelText="No"
          >
            <DeleteOutlined />
          </Popconfirm>
        );
      },
    },
  ];

  const [spinning, setSpinning] = useState(false);
  // 表格，查询
  const getUseCaseServiceFn = async () => {
    try {
      setSpinning(true);
      await getUseCaseService(infoUsedforSearching).then((res) => {
        const { success, data } = res;
        if (success) {
          setTableSource(data);
        } else {
          setTableSource({});
        }
        resetSelectedValueFn();
      });
    } catch (error) {
    } finally {
      setSpinning(false);
    }
  };
  const onQuery = () => {
    const tempValue = form.getFieldsValue();
    setInfoUsedforSearching({
      ...infoUsedforSearching,
      ...tempValue,
      pageNo: 1,
    });
  };
  const onInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.value) {
      // 清除消息时，数据初始化
      setInfoUsedforSearching({ ...infoUsedforSearching, useCaseName: '' });
    }
  };

  const onCancel = () => {
    setOpen(false);
    form.resetFields();
    setInfoUsedforSearching({
      ...infoUsedforSearching,
      useCaseName: '',
    });
  };

  // modal，提交
  const onOK = () => {
    const { serviceRespParamList = [], serviceReqParamList = [] } = selectedValues;
    saveDebugBackFillingInfo({ serviceReqParamList, serviceRespParamList });
    onCancel();
  };

  useEffect(() => {
    if (open) {
      getUseCaseServiceFn();
    } else {
      resetSelectedValueFn();
    }
    return () => {};
  }, [open, infoUsedforSearching]);

  return (
    <Modal
      title={title}
      open={open}
      onCancel={onCancel}
      width={window.innerWidth * 0.6}
      onOk={onOK}
      okButtonProps={{
        disabled: Reflect.ownKeys(selectedValues).length === 0,
      }}
    >
      <Row>
        <Col span={20}>
          <Form form={form} labelCol={{ span: 8 }} wrapperCol={{ span: 15 }}>
            <Form.Item label={formatMessage('DEBUGSERVICE.USECASEBESELECTEDMODAL.USECASENAME')} name="useCaseName">
              <Input allowClear onChange={onInputChange} onPressEnter={onQuery} />
            </Form.Item>
          </Form>
        </Col>
        <Col span={4}>
          <Button type="primary" onClick={onQuery}>
            {formatMessage('DEBUGSERVICE.USECASEBESELECTEDMODAL.QUERY')}
          </Button>
        </Col>
      </Row>
      <Spin spinning={spinning}>
        <ResizableTable
          className={styles.tableWrapper}
          rowClassName={setRowClass}
          rowKey={(row: any) => row.useCaseId}
          scroll={{ y: 300 }}
          dataSource={tableSource?.list}
          columns={columns}
          pagination={{
            hideOnSinglePage: true,
            pageSizeOptions: [5, 10, 15, 20],
            total: tableSource?.totalCount,
            current: tableSource?.pageNo,
            pageSize: tableSource?.pageSize as any,
            showTotal: (total) => formatMessage('PROJECT.COMMON.PAGINATION.TOTAL', { total }),
            onChange: (page: any, pageSize: any) => {
              setInfoUsedforSearching({
                ...infoUsedforSearching,
                pageSize,
                pageNo: page,
              });
            },
          }}
          onRow={(record: any) => ({
            onClick: () => {
              setRowId(record.useCaseId);
              setSelectedValues(record);
            },
          })}
        />
      </Spin>
    </Modal>
  );
};

export default UseCaseBeSelectedModal;
