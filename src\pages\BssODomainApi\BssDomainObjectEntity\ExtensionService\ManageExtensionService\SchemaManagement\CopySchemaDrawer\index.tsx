import useI18n from '@/hooks/useI8n';
import { Button, Drawer, Form, Input, message, Select, Space, Spin } from 'antd';
import { useForm } from 'antd/es/form/Form';
import TextArea from 'antd/es/input/TextArea';
import { useEffect, useState } from 'react';
import { CreationSource } from '../../const';
import { addSchemaByDomainObjId, modifySchemaInfo, qrySchemaDetail } from '@/services/entityService';
import { useModel } from 'umi';

interface ICopySchemaDrawer {
  copyOpen: boolean;
  setCopyOpen: (open: boolean) => void;
  domainSchemaList: any[];
  copyingSchema: any; // 待拷贝的Schema
  updateDomainSchemaList: () => Promise<void>;
  setNewSchemaName?: (value: any) => void;
}
const CopySchemaDrawer: React.FC<ICopySchemaDrawer> = (props) => {
  const {
    copyOpen,
    setCopyOpen,
    domainSchemaList = [],
    copyingSchema = {},
    updateDomainSchemaList,
    setNewSchemaName,
  } = props;
  const { formatMessage } = useI18n();
  const [form] = useForm();

  const [spining, setSpining] = useState<boolean>(false);
  const [submitSpining, setSubmitSpining] = useState<boolean>(false);
  const [copiedSchema, setCopiedSchema] = useState<any>();

  const handleClose = () => {
    setCopyOpen(false);
  };

  // 查询Schema详情
  const querySchemaDetail = async (id: number) => {
    setSpining(true);
    try {
      const { success, data } = await qrySchemaDetail({ schemaId: id });
      if (success) {
        setCopiedSchema({
          ...data,
          schemaName: `CopyOf${data?.schemaName}`,
        });
      }
    } catch (e) {
      message.error(formatMessage('SCHEMAMANAGEMENT.QUERY_ERROR'));
    } finally {
      setSpining(false);
    }
  };

  // 受限于接口，拷贝Schema时需要先新增Schema，再修改刚新增的Schema，为其注入属性
  const copySchema = async () => {
    setSubmitSpining(true);
    const formValues = form.getFieldsValue();
    try {
      const { success: addSuccess, data: copiedSchemaId } = await addSchemaByDomainObjId({
        schemaName: formValues?.schemaName,
        domainObjId: formValues?.domainObjId,
      });
      if (addSuccess) {
        const copiedPropList = copiedSchema?.propList?.map((prop: any) => {
          const { propId, ...rest } = prop; // 解构 prop，去掉 propId，因为新增prop时不传propId
          return rest; // 返回去掉 propId 的新对象
        });
        const param = {
          ...copiedSchema,
          schemaName: formValues?.schemaName,
          domainObjId: formValues?.domainObjId,
          description: formValues?.description,
          schemaId: copiedSchemaId,
          propList: copiedPropList,
        };
        const { success, data } = await modifySchemaInfo(param);
        if (success) {
          message.success(formatMessage('COPYSCHEMA.SUCCESS'));
          setNewSchemaName?.(param?.schemaName); // 通过操作请求或响应参数界面来拷贝Schema时，拷贝成功默认选中新创建的Schema
          await updateDomainSchemaList(); // 更新Schema列表

          handleClose();
        }
      }
    } finally {
      setSubmitSpining(false);
    }
  };

  const handleSubmit = async () => {
    form.validateFields().then(() => {
      copySchema();
    });
  };

  useEffect(() => {
    if (copyingSchema?.schemaId) {
      querySchemaDetail(copyingSchema?.schemaId);
    }
  }, [copyingSchema]);

  useEffect(() => {
    // initialValues={copiedSchema}仅在组件首次渲染时设置一次，需要以下方式手动更新
    if (copiedSchema) {
      form.setFieldsValue({
        schemaName: copiedSchema.schemaName,
        domainObjId: copiedSchema.domainObjId,
        creationSrc: copiedSchema.creationSrc,
        description: copiedSchema.description,
      });
    }
  }, [copiedSchema]);

  return (
    <Drawer
      title={formatMessage('COPYSCHEMA.TITLE')}
      onClose={handleClose}
      maskClosable={false}
      open={copyOpen}
      width={900}
      afterOpenChange={() => {
        form.resetFields();
      }}
      footer={
        <Space style={{ float: 'right' }}>
          <Spin spinning={submitSpining}>
            <Button type="primary" onClick={handleSubmit}>
              {formatMessage('PROJECT.COMMON.SUBMIT')}
            </Button>
          </Spin>
          <Button onClick={handleClose}>{formatMessage('PROJECT.COMMON.CANCEL')}</Button>
        </Space>
      }
    >
      <Spin spinning={spining}>
        <Form form={form} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }} initialValues={copiedSchema}>
          <Form.Item
            label={formatMessage('PROJECT.COMMON.NAME')}
            name="schemaName"
            rules={[
              { required: true, message: '' },
              {
                pattern: /^[^\s]+$/, // 限制输入不能包含空格
                message: formatMessage('COPYSCHEMA.NAME.VALID'),
              },
              {
                pattern: /^[A-Z][A-Za-z0-9_]*$/, // 校验开头是大写字母，只能包含英文字母、数字和下划线
                message: formatMessage('COPYSCHEMA.NAME.INVALID_FORMAT'),
              },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label={formatMessage('PROJECT.COMMON.BELONGDOMAINOBJECT')}
            name="domainObjId"
            rules={[{ required: true, message: '' }]}
          >
            <Select>
              {domainSchemaList.map((i) => (
                <Select.Option key={i.domainObjId} value={i.domainObjId}>
                  {i.domainObjName}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            label={formatMessage('PROJECT.COMMON.CREATIONSOURCE')}
            name="creationSrc"
            rules={[{ required: true, message: '' }]}
          >
            <Select disabled>
              {CreationSource.map((i) => (
                <Select.Option key={i.key} value={i.key}>
                  {i.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>

          <Form.Item label={formatMessage('PROJECT.COMMON.DESCRIPTION')} name="description">
            <TextArea rows={2} />
          </Form.Item>
        </Form>
      </Spin>
    </Drawer>
  );
};
export default CopySchemaDrawer;
