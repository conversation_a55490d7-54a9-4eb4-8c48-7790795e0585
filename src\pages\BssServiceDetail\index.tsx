import { useState, useEffect, useRef } from 'react';
import { MenuProps, message, Spin } from 'antd';
import BssDetailBase from '../BssDetailBase';
import styles from './index.less';
import useI18n from '@/hooks/useI8n';
import { getAPIGServiceCatalog, queryServiceDetailByCode, saveAsAPIGPropsType, saveAsAPIGService } from '@/services';
import { APIGServiceCatalogItemType, IServiceDetail } from '@/services/typing';
import { HoverWrapper } from '@/components';
import { getRequestFormattingData } from '@/utils/modifyTableValue';
import BssDebugService from '../BssDebugService';
import DownLoadCurrentFileModal from './DownLoadCurrentFileModal';
import AddAPIGServiceDrawer from '@/components/APIGServiceDrawer/AddAPIGServiceDrawer';
import { useModel } from 'umi';

interface IServiceDetailCom {
  serviceCode: string;
  onReturn?: () => void;
  switchToDebugMode?: () => void;
  debugMode?: boolean;
  fromType: string; // BYCATALOG | BYDOMAIN
}

type onMenuClickProps = ({ key }: { key: string }) => void;

enum Itemkeys {
  DEBUGSERVICE = 'DEBUGSERVICE',
  SAVEASAPIG = 'SAVEASAPIG',
  DOWNLOADDOC = 'DOWNLOADDOC',
}

const BssServiceDetail = ({ serviceCode, onReturn, fromType = '' }: IServiceDetailCom) => {
  const { formatMessage } = useI18n();
  const { isConfigEnvironment } = useModel('useSystemConfigDataModel');

  const [spinning, setSpinning] = useState(false);
  const [serviceDetail, setServiceDetail] = useState<IServiceDetail>();
  const [requestDataSource, setRequestDataSource] = useState({});

  // debug 服务相关
  const [debugMode, setDebugMode] = useState<boolean>(false);

  const [openDownload, setOpenDownload] = useState<boolean>(false); // download modal

  // APIG Service, 控制 Drawer
  const [APIGOpen, setAPIGOpen] = useState<boolean>(false);
  const [APIGLoading, setAPIGLoading] = useState(false);
  const [APIGServiceCatalogList, setAPIGServiceCatalogList] = useState<APIGServiceCatalogItemType[]>([]); // APIG服务目录列表

  const APIGDrawerRef = useRef({ onCancel: () => {} } as {
    onCancel: () => void;
  });
  // 查询服务详情
  const queryServiceDetail = async (serviceCode: string) => {
    if (!serviceCode) return;
    setSpinning(true);
    try {
      const { data } = await queryServiceDetailByCode({
        serviceCode,
      });
      if (data?.serviceCode) {
        setServiceDetail(data);
        const { newArrObj = {} } = getRequestFormattingData(data);
        setRequestDataSource(newArrObj);
      }
      setSpinning(false);
    } catch (error) {
      setSpinning(false);
    }
  };

  useEffect(() => {
    if (serviceCode) {
      queryServiceDetail(serviceCode);
    }
  }, [serviceCode]);

  // 悬浮框，设置 悬浮框
  const menuItems: MenuProps['items'] = [
    {
      key: Itemkeys.DEBUGSERVICE,
      label: <span>{formatMessage('SERVICEDETAIL.HOVERWRAPPER.DEBUGSERVICE')}</span>,
    },
    ...(isConfigEnvironment
      ? [
          {
            key: Itemkeys.SAVEASAPIG,
            label: <span>{formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.TITLE')}</span>,
          },
        ]
      : []),
    {
      key: Itemkeys.DOWNLOADDOC,
      label: <span>{formatMessage('SERVICEDETAIL.HOVERWRAPPER.DOWNLOADDOC')}</span>,
    },
  ];

  // 悬浮框，操作 悬浮框
  const menuClicks = {
    hiddenPopover() {
      setDebugMode(!debugMode);
    },
    openSaveAsAPIG() {
      setAPIGOpen(true);
    },
    // TODO，用于文档下载
    downloadDoc() {
      setOpenDownload(true);
    },
  };

  // 悬浮框，操作 悬浮框
  const onMenuClick: onMenuClickProps = ({ key }) => {
    switch (key) {
      case Itemkeys.DEBUGSERVICE:
        menuClicks.hiddenPopover();
        break;
      case Itemkeys.SAVEASAPIG:
        menuClicks.openSaveAsAPIG();
        break;
      case Itemkeys.DOWNLOADDOC:
        menuClicks.downloadDoc();
      default:
        break;
    }
  };

  // APIG Service 信息发送
  const onAPIGServiceDrawerSend = async (val: saveAsAPIGPropsType) => {
    let curPostData = {
      ...val,
      serviceCode,
    };
    const operateType = 'new';

    try {
      setAPIGLoading(true);
      await saveAsAPIGService(operateType, curPostData).then(() => {
        APIGDrawerRef.current.onCancel();
        message.success(`Succeed in adding APIG Service!`);
      });
    } catch (error) {
      console.error(error);
    } finally {
      setAPIGLoading(false);
    }
  };

  // 查询APIG的服务分类
  const queryAPIGServiceCatalog = async () => {
    try {
      const { success, data } = await getAPIGServiceCatalog();
      if (success) {
        setAPIGServiceCatalogList(data);
      }
    } catch (error) {
      console.error(error);
    }
  };

  useEffect(() => {
    queryAPIGServiceCatalog();
  }, []);

  return (
    <Spin wrapperClassName={styles.spinContainer} spinning={spinning}>
      {debugMode ? (
        <BssDebugService
          debugMode={debugMode}
          onReturn={menuClicks.hiddenPopover}
          serviceCode={serviceCode}
          serviceDetail={serviceDetail}
          APIGServiceCatalogList={APIGServiceCatalogList}
        />
      ) : (
        <BssDetailBase
          fromType={fromType}
          requestDataSource={requestDataSource}
          onReturn={onReturn}
          serviceDetail={serviceDetail}
          key="bssDetail"
        />
      )}

      <AddAPIGServiceDrawer
        key="AddAPIGServiceDrawer"
        ref={APIGDrawerRef}
        title={formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.TITLE')}
        open={APIGOpen}
        setOpen={setAPIGOpen}
        onAPIGServiceDrawerSend={onAPIGServiceDrawerSend}
        loading={APIGLoading}
        dataToBeSent={{}}
        serviceDetail={serviceDetail}
        APIGServiceCatalogList={APIGServiceCatalogList}
      />

      <HoverWrapper key="hoverWrapper" onMenuClick={onMenuClick} menuItems={menuItems} isHidden={debugMode} />

      <DownLoadCurrentFileModal
        serviceDetail={serviceDetail}
        open={openDownload}
        onCancel={() => setOpenDownload(false)}
      />
    </Spin>
  );
};

export default BssServiceDetail;
