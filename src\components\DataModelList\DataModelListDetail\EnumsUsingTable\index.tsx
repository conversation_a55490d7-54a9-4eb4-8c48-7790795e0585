import OperationIconTitle from '@/components/OperationIconTitle';
import useI18n from '@/hooks/useI8n';
import { TableColumnsType } from 'antd';
import { CreationSource } from '../../const';
import ResizableTable from '@/components/ResizableTable';
import { useTableFilter } from '@/hooks/useTableFilter';
import { useEffect, useMemo } from 'react';

interface IEnumsUsingTable {
  enumsDataSource: any[];
}

const EnumsUsingTable: React.FC<IEnumsUsingTable> = ({ enumsDataSource = [] }) => {
  const { formatMessage } = useI18n();
  // 使用 useMemo 确保 dataSource 是稳定的引用
  const memoizedDataSource = useMemo(() => (Array.isArray(enumsDataSource) ? enumsDataSource : []), [enumsDataSource]);
  // 自定义hooks，支持表格列筛选并分页展示
  const { filteredDataSource, getColumnSearchProps, getColumnEnumSearchProps, clearAllFilters } =
    useTableFilter(memoizedDataSource);

  useEffect(() => {
    // 重置表格列的筛选条件
    clearAllFilters();
  }, [memoizedDataSource]);

  const columns: TableColumnsType = [
    {
      dataIndex: 'enumName',
      width: '20%',
      ellipsis: true,
      title: formatMessage('DATAMODELLISTDETAIL.ENUMS.NAME'),
      ...getColumnSearchProps('enumName'),
    },
    {
      dataIndex: 'creationSrc',
      width: '30%',
      ellipsis: true,
      title: formatMessage('DATAMODELLISTDETAIL.ENUMS.CREATIONSOURCE'),
      render: (_: string, record: any) => {
        const matchedItem = CreationSource.find((i: { key: string; name: string }) => i.key === record.creationSrc);
        return matchedItem ? matchedItem.name : '';
      },
      ...getColumnEnumSearchProps('creationSrc', CreationSource),
    },
    {
      dataIndex: 'comments',
      width: '50%',
      ellipsis: true,
      title: formatMessage('DATAMODELLISTDETAIL.ENUMS.DESC'),
      ...getColumnSearchProps('comments'),
    },
  ];

  return (
    <div>
      <OperationIconTitle title={formatMessage('DATAMODELLISTDETAIL.ENUMS.TITLE')} />
      <ResizableTable
        size="small"
        rowKey="key"
        columns={columns}
        pagination={{
          hideOnSinglePage: true,
          pageSize: 5,
          showTotal: (total) => formatMessage('PROJECT.COMMON.PAGINATION.TOTAL', { total }),
        }}
        dataSource={filteredDataSource?.map((el: any, index: number) => ({
          ...el,
          key: `enumsUsingTable-${index}-${el?.enumCode}`,
        }))}
      />
    </div>
  );
};

export default EnumsUsingTable;
