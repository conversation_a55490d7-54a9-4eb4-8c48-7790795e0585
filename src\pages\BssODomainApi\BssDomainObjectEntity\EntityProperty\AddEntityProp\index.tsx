import React, { useEffect, useState } from 'react';
import { Button, Drawer, Form, Input, Select, Space, message, InputNumber, Spin } from 'antd';
import { addDomainEntityProp } from '@/services/entityService';
import { SelectedEntityProps, ColumnNameSourceProps, EnumSourceProps, SensitiveLeveType } from '../../types';
import { autoFillDescByPropName } from '@/utils/common';
import useI18n from '@/hooks/useI8n';
import SelectWithAddButton from '@/components/SelectWithAddButton';
import { PublicSensitiveLevelId } from '@/pages/BssODomainApi/const';

interface IAddExpandEntityDrawer {
  sensitiveLevelList: SensitiveLeveType[];
  isProduct: boolean;
  selectedEntity: SelectedEntityProps;
  columnNameSource: ColumnNameSourceProps[];
  enumSource: EnumSourceProps[];
  open: boolean;
  ignoreVersionSource: any;
  indexFunctionSource: any;
  onCancel: () => void;
  onOk: () => void;
}

interface optionProps {
  label: string;
  value: string;
}

interface IIndexFunctionSource {
  indexFuncId: number;
  indexFuncName: string;
  indexFuncExp: string;
  comments: string;
  applyPropType: string;
}

const AddEntityPropDrawer: React.FC<IAddExpandEntityDrawer> = (props) => {
  const {
    open,
    ignoreVersionSource,
    indexFunctionSource,
    selectedEntity,
    isProduct,
    enumSource,
    columnNameSource,
    sensitiveLevelList,
    onCancel,
    onOk,
  } = props;
  const { formatMessage } = useI18n();
  const [form] = Form.useForm();
  const [formValues, setFormValues] = useState<any>({}); // formValues
  const [avlValueTypeSource, setAvlValueTypeSource] = useState<optionProps[]>([]);
  const [avlValueTypeValue, setAvlValueTypeValue] = useState<string>('');
  const [disabled, setDisabled] = useState<boolean>(false);
  const [required, setRequired] = useState<boolean>(false);
  const [showIndexFunc, setShowIndexFunc] = useState<boolean>(false);
  const [indexFuncSource, setIndexFuncSource] = useState<optionProps[]>([]);
  const [submitSpinning, setSubmitSpinning] = useState<boolean>(false);

  const currentColumn = Form.useWatch('columnName', form);

  const CREATIONSOURCE = [
    { name: 'Product-Owned', key: 'P' },
    { name: 'Project-Customized', key: 'C' },
  ];
  const FormatDateSource = [
    { label: 'TIMESTAMP', value: 'TIMESTAMP' },
    { label: 'DATE', value: 'DATE' },
  ];

  const PropTypeStaticSource = ['String', 'Bytes', 'Long', 'Number', 'Datetime'];

  const SupportFeaturesSource = [
    { name: 'Can Filter', key: 'canFilter' },
    { name: 'Primary Key', key: 'pk' },
    { name: 'Index', key: 'index' },
    { name: 'Mandatory', key: 'mandatory' },
    { name: 'Can Order', key: 'canOrder' },
  ];

  const initValues = {
    creationSrc: isProduct ? 'P' : 'C',
  };

  // modal close
  const onClose = async () => {
    setAvlValueTypeValue('');
    form.resetFields();
    onCancel?.();
  };

  const addEntityPropData = async (param: any) => {
    setSubmitSpinning(true);
    try {
      // 调用新增实体属性接口
      const resultData = await addDomainEntityProp(param);

      if (resultData?.success) {
        message.success(formatMessage('DOMAIN.ENTITY.ADD.SUCCESS'));
        form.resetFields();
        onOk?.();
      }
      onCancel?.();
    } finally {
      setSubmitSpinning(false);
    }
  };

  const checkEntityPropData = () => {
    form.validateFields().then((values) => {
      // 构造入参
      const param = {
        ...values,
        ignoreVer: values?.ignoreVer?.length ? values?.ignoreVer?.join(',') : null, // 处理ignoreVer显示空选项的bug
        entityId: selectedEntity.entityId,
      };
      // PublicSensitiveLevelId为前台自定义的不存在于后台表中的sensitiveLevelId，表示该值不会入库。
      if (param?.sensitiveLevelId === PublicSensitiveLevelId) delete param?.sensitiveLevelId;
      if (!showIndexFunc) delete param?.indexFuncId;

      param.keyFlag = param.supportFeatures?.includes('pk') ? 'Y' : 'N';
      param.required = param.supportFeatures?.includes('mandatory') ? 'Y' : 'N';
      param.indexFlag = param.supportFeatures?.includes('index') ? 'Y' : 'N';
      param.orderFlag = param.supportFeatures?.includes('canOrder') ? 'Y' : 'N';
      param.filterFlag = param.supportFeatures?.includes('canFilter') ? 'Y' : 'N';

      if (!PropTypeStaticSource.includes(param.propType)) {
        param.enumId = param.propType;
        param.propType = 'Enum';
      }
      if (param.avlValueType) {
        switch (param.avlValueType) {
          case 'Length':
            param.avlValue = `Length[${param.min},${param.max}]`;
            break;

          case 'Enum':
            param.avlValue = param.enum;
            param.enumId = param.enum;
            break;

          case 'Empty':
            param.avlValue = '';
            break;
          case 'Range':
            param.avlValue = `Range[${param.min},${param.max}]`;
            break;
          case 'Precision&Scale':
            param.avlValue = `Precision=${param.precision},Scale=${param.scale}`;
            break;
          case 'Format':
            param.avlValue = param.format;
            break;

          default:
            param.avlValue = '';
            break;
        }
      }
      addEntityPropData(param);
    });
  };

  const onPropTypeChange = (value: any) => {
    let source = [];
    form.setFieldsValue({
      avlValueType: '',
      supportFeatures: [],
    });
    setAvlValueTypeValue('');
    setDisabled(false);
    setRequired(true);
    switch (value) {
      case 'String':
        source = [
          { label: 'Length', value: 'Length' },
          { label: 'Enum', value: 'Enum' },
          { label: 'Empty', value: 'Empty' },
        ];
        setAvlValueTypeSource(source);
        form.setFieldsValue({
          avlValueType: 'Empty',
          supportFeatures: ['canFilter'],
        });
        break;

      case 'Bytes':
        source = [{ label: 'Empty', value: 'Empty' }];
        setAvlValueTypeSource(source);
        form.setFieldsValue({
          avlValueType: 'Empty',
          supportFeatures: ['canFilter'],
        });
        break;

      case 'Long':
        source = [
          { label: 'Length', value: 'Length' },
          { label: 'Range', value: 'Range' },
          { label: 'Enum', value: 'Enum' },
          { label: 'Empty', value: 'Empty' },
        ];
        setAvlValueTypeSource(source);
        form.setFieldsValue({
          avlValueType: 'Empty',
          supportFeatures: ['canFilter'],
        });
        break;
      case 'Number':
        source = [{ label: 'Precision&Scale', value: 'Precision&Scale' }];
        setAvlValueTypeSource(source);
        break;
      case 'Datetime':
        source = [{ label: 'Format', value: 'Format' }];
        setAvlValueTypeSource(source);
        break;

      default:
        setAvlValueTypeSource([]);
        form.setFieldsValue({
          supportFeatures: [],
        });
        setDisabled(true);
        setRequired(false);
        break;
    }
  };

  // 通用的重置表单字段函数
  const resetFormFields = (resetAvlValueType = true) => {
    // 重置所有相关字段
    const resetFields: any = {
      format: undefined,
      min: undefined,
      max: undefined,
      values: undefined,
      enum: undefined,
      precision: undefined,
      scale: undefined,
    };

    if (resetAvlValueType) {
      resetFields.avlValueType = '';
      setAvlValueTypeValue('');
    }

    form.setFieldsValue(resetFields);
  };

  const onAvlValueTypeChange = (value: any) => {
    setAvlValueTypeValue(value);

    // 重置avlValueTypeValue相关字段
    resetFormFields(false);
  };

  const handleEnumFilter = (input: any, option: any) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };
  const handlePropTypeFilter = (input: string, option: any) => {
    if (option.options) {
      return option.options.every((i: optionProps) => {
        return i.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      });
    }
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };

  // 传给子组件SelectWithAddButton的自定义方法，将输入自动转为大写
  const handleCustomInputChange = (value: string) => {
    return value.toUpperCase();
  };

  useEffect(() => {
    const existItem = columnNameSource?.find((item) => item?.value === currentColumn);
    form.setFieldsValue({
      propType: existItem ? existItem.type : 'String',
    });
    onPropTypeChange(existItem ? existItem.type : 'String');
  }, [currentColumn]);

  useEffect(() => {
    const formData = form.getFieldsValue();
    // 不能使用例如formValues?.propType来进行判断，否则视图和数据更新不及时
    if (formData?.propType && formData?.supportFeatures?.find((item: any) => item === 'index')) {
      const indexFuncOption = indexFunctionSource
        ?.filter((item: IIndexFunctionSource) => item?.applyPropType === formData?.propType)
        .map((item: IIndexFunctionSource) => ({
          label: item?.indexFuncName,
          value: item?.indexFuncId,
        }));

      if (indexFuncOption.length > 0) {
        setIndexFuncSource(indexFuncOption);
        setShowIndexFunc(true);
      }
    } else {
      setShowIndexFunc(false);
    }
  }, [formValues]); // 不能依赖form.getFieldsValue()，否则死循环

  return (
    <Drawer
      title={formatMessage('ENTITYPROP.ADD.TITLE')}
      onClose={onClose}
      maskClosable={false}
      open={open}
      width={720}
      afterOpenChange={() => {
        form.resetFields();
      }}
      footer={
        <Space style={{ float: 'right' }}>
          <Spin spinning={submitSpinning}>
            <Button type="primary" onClick={checkEntityPropData}>
              {formatMessage('PROJECT.COMMON.SUBMIT')}
            </Button>
          </Spin>
          <Button onClick={onClose}>{formatMessage('PROJECT.COMMON.CANCEL')}</Button>
        </Space>
      }
    >
      <Form
        form={form}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
        initialValues={{ ...initValues, sensitiveLevelId: -1 }}
        onValuesChange={(cValues, alValues) => {
          setFormValues(alValues);
        }}
      >
        <Form.Item
          label={formatMessage('PROJECT.COMMON.NAME')}
          name="propName"
          rules={[
            { required: true, message: '' },
            {
              validator: (_, value) => {
                if (value?.includes(' ')) {
                  return Promise.reject(new Error(formatMessage('ENTITYPROP.NAME.NOSPACES')));
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <Input allowClear onBlur={() => autoFillDescByPropName(form, 'propName', 'comments')} />
        </Form.Item>
        <Form.Item
          label={formatMessage('ENTITYPROP.COLUMN_NAME')}
          name="columnName"
          rules={[
            { required: true, message: '' },
            {
              pattern: /^[A-Z0-9_]+$/,
              message: formatMessage('ENTITYPROP.COLUMN_NAME.FORMAT'),
            },
          ]}
        >
          <SelectWithAddButton
            originalOptionsList={columnNameSource}
            parentForm={form}
            formFieldName="columnName"
            customInputChange={handleCustomInputChange}
          />
        </Form.Item>
        <Form.Item
          label={formatMessage('PROJECT.COMMON.TYPE')}
          name="propType"
          rules={[{ required: true, message: '' }]}
        >
          <Select
            options={[
              { label: 'String', value: 'String' },
              { label: 'Bytes', value: 'Bytes' },
              { label: 'Long', value: 'Long' },
              { label: 'Number', value: 'Number' },
              { label: 'Datetime', value: 'Datetime' },
              {
                label: <span>Enum</span>,
                title: 'Enum',
                options: enumSource,
              },
            ]} // 数据源为固定数据源+接口返回数据源
            onChange={onPropTypeChange}
            showSearch
            filterOption={handlePropTypeFilter}
          />
        </Form.Item>
        <Form.Item label={formatMessage('ENTITYPROP.ALLOWABLE_VALUES')} style={{ marginBottom: 0 }}>
          <Form.Item
            name="avlValueType"
            style={{ display: 'inline-block', width: 'calc(33% - 5px)' }}
            rules={[{ required, message: '' }]}
          >
            <Select options={avlValueTypeSource} onChange={onAvlValueTypeChange} disabled={disabled} />
          </Form.Item>
          {avlValueTypeValue === 'Length' ? (
            <Form.Item
              name="min"
              style={{ display: 'inline-block', width: 'calc(33% - 5px)', marginLeft: '5px' }}
              rules={[{ required: true, message: '' }]}
            >
              <InputNumber
                addonBefore={formatMessage('ENTITYPROP.MIN')}
                precision={0}
                min={1}
                max={formValues.max}
                style={{ width: '100%' }}
              />
            </Form.Item>
          ) : null}
          {avlValueTypeValue === 'Length' ? (
            <Form.Item
              name="max"
              style={{ display: 'inline-block', width: 'calc(33% - 8px)', marginLeft: '5px' }}
              rules={[{ required: true, message: '' }]}
            >
              <InputNumber
                addonBefore={formatMessage('ENTITYPROP.MAX')}
                precision={0}
                min={formValues.min}
                style={{ width: '100%' }}
              />
            </Form.Item>
          ) : null}
          {avlValueTypeValue === 'Enum' ? (
            <Form.Item
              name="enum"
              style={{ display: 'inline-block', width: 'calc(66% - 8px)', marginLeft: '5px' }}
              rules={[{ required: true, message: '' }]}
            >
              <Select showSearch options={enumSource} filterOption={handleEnumFilter} />
            </Form.Item>
          ) : null}
          {avlValueTypeValue === 'Format' ? (
            <Form.Item
              name="format"
              style={{ display: 'inline-block', width: 'calc(66% - 8px)', marginLeft: '5px' }}
              rules={[{ required: true, message: '' }]}
            >
              <Select options={FormatDateSource} />
            </Form.Item>
          ) : null}
          {avlValueTypeValue === 'Precision&Scale' ? (
            <Form.Item
              name="precision"
              style={{ display: 'inline-block', width: 'calc(33% - 8px)', marginLeft: '5px' }}
              rules={[{ required: true, message: '' }]}
            >
              <InputNumber
                addonBefore={formatMessage('ENTITYPROP.PRECISION')}
                precision={0}
                style={{ width: '100%' }}
              />
            </Form.Item>
          ) : null}
          {avlValueTypeValue === 'Precision&Scale' ? (
            <Form.Item
              name="scale"
              style={{ display: 'inline-block', width: 'calc(33% - 8px)', marginLeft: '5px' }}
              rules={[{ required: true, message: '' }]}
            >
              <InputNumber addonBefore={formatMessage('ENTITYPROP.SCALE')} precision={0} style={{ width: '100%' }} />
            </Form.Item>
          ) : null}
          {avlValueTypeValue === 'Range' ? (
            <Form.Item
              name="min"
              style={{ display: 'inline-block', width: 'calc(33% - 5px)', marginLeft: '5px' }}
              rules={[{ required: true, message: '' }]}
            >
              <InputNumber
                addonBefore={formatMessage('ENTITYPROP.MIN')}
                precision={0}
                max={formValues.max}
                style={{ width: '100%' }}
              />
            </Form.Item>
          ) : null}
          {avlValueTypeValue === 'Range' ? (
            <Form.Item
              name="max"
              style={{ display: 'inline-block', width: 'calc(33% - 8px)', marginLeft: '5px' }}
              rules={[{ required: true, message: '' }]}
            >
              <InputNumber
                addonBefore={formatMessage('ENTITYPROP.MAX')}
                precision={0}
                min={formValues.min}
                style={{ width: '100%' }}
              />
            </Form.Item>
          ) : null}
        </Form.Item>
        <Form.Item label={formatMessage('ENTITYPROP.DEFAULT_VALUE')} name="defValue">
          <Input />
        </Form.Item>
        <Form.Item label={formatMessage('DOMAINDETAIL.PROPERTIES.FEATURES')} name="supportFeatures">
          <Select mode="multiple" disabled={disabled}>
            {SupportFeaturesSource.map((i) => (
              <Select.Option key={i.key} value={i.key}>
                {i.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        {showIndexFunc && (
          <Form.Item label={formatMessage('ENTITYPROP.INDEX_FUNCTION')} name="indexFuncId">
            <Select allowClear options={indexFuncSource} />
          </Form.Item>
        )}
        <Form.Item label={formatMessage('ENTITYPROP.SENSITIVE_DATA_LEVEL')} name="sensitiveLevelId">
          <Select>
            {sensitiveLevelList.map((i) => (
              <Select.Option key={i.sensitiveLevelId} value={i.sensitiveLevelId}>
                {i.sensitiveLevelName}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item label={formatMessage('PROJECT.COMMON.CREATIONSOURCE')} name="creationSrc">
          <Select disabled>
            {CREATIONSOURCE.map((i) => (
              <Select.Option key={i.key} value={i.key}>
                {i.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        {isProduct ? (
          <Form.Item label={formatMessage('ENTITYPROP.IGNORE_VERSION')} name="ignoreVer">
            <Select mode="multiple">
              {ignoreVersionSource?.map((i: any) => (
                <Select.Option key={i.key} value={i.key}>
                  {i.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        ) : null}
        <Form.Item
          label={formatMessage('PROJECT.COMMON.DESCRIPTION')}
          name="comments"
          rules={[{ required: true, message: '' }]}
          extra={formatMessage('PROJECT.COMMON.GETDESCBYPROPNAME')}
        >
          <Input.TextArea allowClear onBlur={() => autoFillDescByPropName(form, 'propName', 'comments')} />
        </Form.Item>
      </Form>
    </Drawer>
  );
};
export default AddEntityPropDrawer;
