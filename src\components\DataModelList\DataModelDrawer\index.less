@keyframes showit {
  from {
    height: 0;
  }
  to {
    height: auto;
  }
}

@keyframes hiddenit {
  from {
    height: auto;
  }
  to {
    height: 0;
  }
}

.inputPanel {
  display: flex;
  flex-direction: row;
  align-items: center;

  .inputPanel-input {
    display: flex;
    align-items: center;
    border: 1px solid #d9d9d9;

    .custInput {
      :global {
        .ant-select-selector {
          border: none;
        }
      }
    }

    .custSpan {
      cursor: pointer;
      padding: 0 4px;
      border-left: 1px solid #d9d9d9;
    }

    :global {
      .ant-input-outlined:focus {
        box-shadow: none;
      }
    }
  }

  .inputPanel-inputTextArea {
    border: 1px solid #d9d9d9;
    overflow: hidden;

    &.hiddenIt {
      animation: hiddenit 0.3s ease-in-out 1;
      height: 0;
    }

    .custInputTextArea {
      border: none;
    }

    :global {
      .ant-input-outlined:focus {
        box-shadow: none;
      }
    }
  }
}
