import React, { CSSProperties, useEffect, useState } from 'react';
import { IServiceDetail, IParameter } from '@/services/typing.d';
import SingleRequestTable from './SingleRequestTable';
import { ITableDataInterface } from '@/typing';
import { processTreeData } from '@/utils/modifyTableValue';
import { Empty, Flex, Tabs } from 'antd';
import { CodeMirrorRender, BlockTitle } from '@/components';
import useI18n from '@/hooks/useI8n';

interface IRequestTable {
  requestDataSource?: any;
  servicemethod?: string;
  debugMode?: boolean;
  className?: string;
  style?: CSSProperties;
  isEditAPIGService?: boolean;
}

const RequestTable: React.FC<IRequestTable> = ({
  requestDataSource = {},
  servicemethod,
  debugMode = false,
  className = '',
  style = {},
  isEditAPIGService = false,
}) => {
  const { formatMessage } = useI18n();

  const [header, setHeader] = useState<Array<ITableDataInterface>>([]); // parameters header
  const [path, setPath] = useState<Array<ITableDataInterface>>([]); // parameters path
  const [query, setQuery] = useState<Array<ITableDataInterface>>([]); // parameters query
  const [body, setBody] = useState<Array<ITableDataInterface>>([]); // parameters body
  const [cookie, setCookie] = useState<Array<ITableDataInterface>>([]); // parameters cookie

  const [activerKey, setActiveKey] = useState(''); // body 示例的当前tab
  const [activeCode, setActiveCode] = useState(JSON.stringify('{}')); // body tab item 对应的codemirror示例参数

  // 切换tab
  const switchTab = (key: any) => {
    setActiveKey(key);
    const filterArr = body.filter((item) => {
      return item.rowKey === key;
    });
    setActiveCode(filterArr[0].example || '{}');
  };

  // 开始筛选header path query body的数据
  useEffect(() => {
    if (Reflect.ownKeys(requestDataSource).length > 0) {
      const { headerArr = [], pathArr = [], queryArr = [], bodyArr = [], cookieArr = [] } = requestDataSource;

      setHeader(headerArr);
      setPath(pathArr);
      setQuery(queryArr);
      setBody(bodyArr);
      setCookie(cookieArr);

      // 有body 需要设置第一个tab对应的json codemirror展示
      if (bodyArr?.length) {
        setActiveKey(bodyArr?.[0]?.rowKey || '');
        setActiveCode(bodyArr?.[0]?.example || '{}');
      }
    }
  }, [requestDataSource]);

  return (
    <div className={className} style={style}>
      {/* 特殊操作 勿删 给后面计算列的宽度能否满足字符串用的 */}
      <div style={{ visibility: 'hidden', height: 0 }}>
        <SingleRequestTable tableData={[]} />
      </div>
      {/* header参数 */}
      {cookie?.length ? (
        <>
          <BlockTitle>Cookie</BlockTitle>
          <SingleRequestTable debugMode={debugMode} tableData={cookie} key="C-Table" type="C" />
        </>
      ) : null}

      {/* header参数 */}
      {header?.length ? (
        <>
          <BlockTitle>Header</BlockTitle>
          <SingleRequestTable debugMode={debugMode} tableData={header} key="H-Table" type="H" />
        </>
      ) : null}

      {/* path参数 */}
      {path?.length ? (
        <>
          <BlockTitle>Path</BlockTitle>
          <SingleRequestTable debugMode={debugMode} tableData={path} key="B-Table" type="P" />
        </>
      ) : null}
      {/* query参数 */}
      {query?.length ? (
        <>
          <BlockTitle>Query</BlockTitle>
          <SingleRequestTable debugMode={debugMode} tableData={query} key="Q-Table" type="Q" />
        </>
      ) : null}
      {/* body 参数 */}
      {servicemethod !== 'GET' && body?.length > 0 && (
        <>
          {/* Body */}
          <BlockTitle>
            Body{' '}
            <span style={{ fontSize: '14px', fontFamily: 'Nunito Sans' }}>Content Type: {body[0]?.contentType}</span>
          </BlockTitle>

          <SingleRequestTable tableData={body} key="B-Table" type="B" />

          {/* Body Tabs */}
          {body?.map(
            (reqItem) =>
              reqItem && (
                <Tabs
                  defaultActiveKey={activerKey}
                  onChange={switchTab}
                  activeKey={activerKey}
                  size="small"
                  tabBarStyle={{ marginBottom: 12 }}
                  items={[
                    reqItem?.dataModel && {
                      label: reqItem?.name,
                      key: reqItem?.rowKey,
                      children: (
                        <div>
                          {/* Request Example */}
                          <Flex align="center" gap={20}>
                            <BlockTitle>{formatMessage('SERVICEDETAIL.REQUEST.TITLEEXAMPLE')}</BlockTitle>
                            {!isEditAPIGService && <span>{formatMessage('SERVICEDETAIL.REQUEST.EXAMPLENOTE')}</span>}
                          </Flex>
                          <CodeMirrorRender debugMode={debugMode} value={activeCode} />
                        </div>
                      ),
                    },
                  ].filter(Boolean)} // 过滤掉没有dataModel的item,否则item={[false,null,undefined等]}Tabs会报错
                />
              ),
          )}
        </>
      )}
      {!(header?.length || path?.length || query?.length || body?.length) && (
        <Empty description={formatMessage('DEBUGSERVICE.REQUEST.EMPTY')} />
      )}
    </div>
  );
};

export default RequestTable;
