import React, { useState } from 'react';
import { Button, Drawer, Form, Input, Select, Space, Spin, message } from 'antd';
import { addDomainEntityEvent } from '@/services/entityService';
import { RlcEventCatgSourceProps, RlcEventCatgSourceEventProps } from '../../types';
import useI18n from '@/hooks/useI8n';

interface IAddEntityEventDrawer {
  isProduct: boolean;
  entityId: number;
  open: boolean;
  rlcEventCatgSource: RlcEventCatgSourceProps[];
  ignoreVersionSource: any;
  onCancel: () => void;
  onOk: () => void;
}

const AddEntityEventDrawer: React.FC<IAddEntityEventDrawer> = (props) => {
  const { open, ignoreVersionSource, entityId, isProduct, rlcEventCatgSource, onCancel, onOk } = props;
  const { formatMessage } = useI18n();
  const [form] = Form.useForm();
  const [formValues, setFormValues] = useState<any>({}); // formValues
  const [eventCodeSource, setEventCodeSource] = useState<any>([]);
  const [submitSpinning, setSubmitSpinning] = useState<boolean>(false);

  const CREATIONSOURCE = [
    { name: 'Product-Owned', key: 'P' },
    { name: 'Project-Customized', key: 'C' },
  ];

  const initValues = {
    creationSrc: isProduct ? 'P' : 'C',
  };

  // modal close
  const onClose = async () => {
    form.resetFields();
    onCancel?.();
  };

  const addEntityEvent = async () => {
    setSubmitSpinning(true);
    try {
      // 调用新增领域实体接口
      const curPostData = {
        ...formValues,
        ignoreVer: formValues?.ignoreVer?.length ? formValues?.ignoreVer?.join(',') : null, // 处理ignoreVer显示空选项的bug
        entityId,
      };
      const resultData = await addDomainEntityEvent(curPostData);

      if (resultData?.success) {
        message.success(formatMessage('ENTITYEVENT.ADD.SUCCESS'));
        form.resetFields();
        onOk?.();
      }
    } finally {
      setSubmitSpinning(false);
    }
  };

  const onEventCatgChange = (value: any) => {
    form.setFieldsValue({
      eventCode: '',
    });
    const eventCodeData = rlcEventCatgSource.filter((i: any) => i.catalogId === value);
    if (eventCodeData?.length > 0) {
      setEventCodeSource(eventCodeData[0].events);
    }
  };

  const checkEntityEventData = () => {
    form.validateFields().then(() => {
      addEntityEvent();
    });
  };

  return (
    <Drawer
      title={formatMessage('ENTITYEVENT.ADD.TITLE')}
      onClose={onClose}
      maskClosable={false}
      open={open}
      width={720}
      afterOpenChange={() => {
        form.resetFields();
      }}
      footer={
        <Space style={{ float: 'right' }}>
          <Spin spinning={submitSpinning}>
            <Button type="primary" onClick={checkEntityEventData}>
              {formatMessage('ENTITYEVENT.ADD.SUBMIT')}
            </Button>
          </Spin>
          <Button onClick={onClose}>{formatMessage('ENTITYEVENT.ADD.CANCEL')}</Button>
        </Space>
      }
    >
      <Form
        form={form}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
        initialValues={initValues}
        onValuesChange={(cValues, alValues) => {
          setFormValues(alValues);
        }}
      >
        <Form.Item
          label={formatMessage('ENTITYEVENT.ADD.EVENT_NAME')}
          name="eventName"
          rules={[{ required: true, message: '' }]}
        >
          <Input allowClear />
        </Form.Item>
        <Form.Item
          label={formatMessage('ENTITYEVENT.ADD.RLC_EVENT_CATALOG')}
          name="rlcEventCatg"
          rules={[{ required: true, message: '' }]}
        >
          <Select onChange={onEventCatgChange}>
            {rlcEventCatgSource.map((i: RlcEventCatgSourceProps) => (
              <Select.Option key={i.catalogId} value={i.catalogId}>
                {i.catalogName}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          label={formatMessage('ENTITYEVENT.ADD.RLC_EVENT_NAME')}
          name="eventCode"
          rules={[{ required: true, message: '' }]}
        >
          <Select>
            {eventCodeSource?.map((i: RlcEventCatgSourceEventProps) => (
              <Select.Option key={i.eventCode} value={i.eventCode}>
                {i.eventName}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item label={formatMessage('ENTITYEVENT.ADD.CREATION_SOURCE')} name="creationSrc">
          <Select disabled>
            {CREATIONSOURCE.map((i) => (
              <Select.Option key={i.key} value={i.key}>
                {i.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        {isProduct ? (
          <Form.Item label={formatMessage('ENTITYEVENT.ADD.IGNORE_VERSION')} name="ignoreVer">
            <Select mode="multiple">
              {ignoreVersionSource?.map((i: any) => (
                <Select.Option key={i.key} value={i.key}>
                  {i.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        ) : null}
        <Form.Item
          label={formatMessage('ENTITYEVENT.ADD.DESCRIPTIONS')}
          name="comments"
          rules={[{ required: true, message: '' }]}
        >
          <Input.TextArea allowClear />
        </Form.Item>
      </Form>
    </Drawer>
  );
};
export default AddEntityEventDrawer;
