import React, { useEffect, useRef, useState } from 'react';
import { Button, Drawer, Form, Input, Select, Space, message, InputNumber, Divider, InputRef, Flex } from 'antd';
import { addSchemaByDomainObjId, qrySchemaListByDomainId } from '@/services/entityService';
import { EnumSourceProps } from '../../../../types';
import { useModel } from 'umi';
import { getFormatTypeSource } from '../../utils';
import useI18n from '@/hooks/useI8n';

interface IAddRequestInformationDrawer {
  operateType: string;
  enumSource: EnumSourceProps[];
  open: boolean;
  onCancel: () => void;
  onOk: (data: any) => void;
  selectedParam?: any;
  selectedRootNode: any;
  updateDomainSchemaList: () => Promise<void>;
}

interface optionProps {
  label: string;
  value: string;
}

interface DomainSchemaObj {
  domainObjId: number;
  domainObjName: string;
  schemaList: Array<any>;
}

const AddCompositionSchema: React.FC<IAddRequestInformationDrawer> = (props) => {
  const { open, enumSource, onCancel, onOk, updateDomainSchemaList, selectedParam, operateType, selectedRootNode } =
    props;
  const { formatMessage } = useI18n();
  const [form] = Form.useForm();
  const [formValues, setFormValues] = useState<any>({}); // formValues
  const [avlValueType, setAvlValueType] = useState<string>('');
  const [avlValueTypeSource, setAvlValueTypeSource] = useState<optionProps[]>([]);
  const [avlValueTypeValue, setAvlValueTypeValue] = useState<string>('');
  const [disabled, setDisabled] = useState<boolean>(false);
  const [required, setRequired] = useState<boolean>(false);
  const [defaultValues, setDeFaultValues] = useState<any>({});
  const { currentDomainObjId } = useModel('manageExtensionServiceModel');
  const addSchemaInputRef = useRef<InputRef>(null);
  const [newSchemaName, setNewSchemaName] = useState('');
  const [domainSchemaList, setDomainSchemaList] = useState<any>([]);
  const [selectedTypeObj, setSelectedTypeObj] = useState<any>({});
  const [isSchemaNameValid, setIsSchemaNameValid] = useState<boolean>(true);

  const paramTypeSource = ['String', 'Boolean', 'Long', 'Number', 'Datetime', 'Integer'];

  const FormatSource = getFormatTypeSource(avlValueType);

  // 弹框关闭
  const onClose = async () => {
    setAvlValueTypeValue('');
    form.resetFields();
    onCancel?.();
  };

  const getStringBetween = (str: string, start: string, end: string) => {
    // 正则表达式匹配两个指定字符之间的内容
    const reg = /\[(\S*)\]/;
    if ((str.match(reg) || []).length > 1) {
      return (str.match(reg) || [])[1];
    }
    return '';
  };

  const onAddSchemaNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setNewSchemaName(value);

    // 校验输入值,开头是大写字母，只能包含英文字母、数字和下划线
    const isValid = /^[A-Z][A-Za-z0-9_]*$/.test(value);
    setIsSchemaNameValid(isValid);
  };

  // 查询领域下schema列表
  const querySchemaList = async () => {
    const resultData = await qrySchemaListByDomainId({ domainId: selectedRootNode?.domainId });
    if (resultData?.success) {
      setDomainSchemaList(resultData?.data);
    }
  };

  // 新增Schema
  const addSchema = async (e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>) => {
    e.preventDefault();

    if (isSchemaNameValid) {
      if (currentDomainObjId) {
        const resultData = await addSchemaByDomainObjId({ schemaName: newSchemaName, domainObjId: currentDomainObjId });
        if (resultData?.success) {
          message.success(formatMessage('PROJECT.COMMON.ADDSUCCESS'));

          await querySchemaList();
          setNewSchemaName('');
          await updateDomainSchemaList();
          setTimeout(() => {
            addSchemaInputRef.current?.focus();
          }, 0);
        }
      }
    } else {
      message.warning(formatMessage('COPYSCHEMA.NAME.INVALID_FORMAT'));
    }
  };

  // 入参校验，构造入参
  const checkServiceReqParamData = () => {
    form.validateFields().then((values) => {
      // 构造入参

      let param = {
        ...selectedParam,
        ...values,
        isArray: 'N',
        required: 'N',
      };

      // 新增时会生成随机id，根据id判断是否是修改
      if (operateType === 'Add') {
        param = {
          ...param,
          id: `${Date.now()}-${Math.floor(Math.random() * 10000)}`,
        };
      } else {
        if (selectedParam?.propId) {
          param = {
            ...param,
            propId: selectedParam?.propId,
          };
        } else {
          param = {
            ...param,
            id: selectedParam?.id,
          };
        }
      }

      if (!paramTypeSource.includes(param.type)) {
        // 如果是从后端获取的属性，第一次编辑时删除refSchemaInfo，后续都以refSchemaId和refSchemaName为准渲染对应的Schema
        if (param?.refSchemaInfo) {
          delete param?.refSchemaInfo;
        }
        param = {
          ...param,
          refSchemaId: selectedTypeObj?.key,
          refSchemaName: selectedTypeObj?.value,
          type: 'Ref',
        };
      }
      if (param.avlValueType) {
        switch (param.avlValueType) {
          case 'Length':
            param.avlValue = `Length[${param.min},${param.max}]`;
            break;

          case 'Enum':
            param.avlValue = enumSource.find((i: any) => `${i?.label}` === `${param.enum}`)?.value;
            param.enumId = enumSource.find((i: any) => `${i?.label}` === `${param.enum}`)?.value;
            break;

          case 'Empty':
            param.avlValue = '';
            break;
          case 'Range':
            param.avlValue = `Range[${param.min},${param.max}]`;
            break;
          case 'Format':
            param.avlValue = param.format;
            param = {
              ...param,
              typeFormat: param.format,
            };
            break;
          case 'Values':
            param.avlValue = param.values;
            break;

          default:
            param.avlValue = '';
            break;
        }
      }
      onOk(param);
    });
  };

  // 通用的重置表单字段函数
  const resetFormFields = (resetAvlValueType = true) => {
    // 重置所有相关字段
    const resetFields: any = {
      format: undefined,
      min: undefined,
      max: undefined,
      values: undefined,
      enum: undefined,
      precision: undefined,
      scale: undefined,
    };

    if (resetAvlValueType) {
      resetFields.avlValueType = '';
      setAvlValueTypeValue('');
    }

    form.setFieldsValue(resetFields);
  };

  const onPropTypeChange = (value: any, option?: any) => {
    if (option?.value === value) {
      setSelectedTypeObj(option);
    }

    setAvlValueType(value);
    let source = [];
    form.setFieldsValue({
      avlValueType: '',
    });
    setAvlValueTypeValue('');
    setDisabled(false);
    setRequired(true);
    switch (value) {
      case 'String':
        source = [
          { label: 'Empty', value: 'Empty' },
          { label: 'Length', value: 'Length' },
          { label: 'Format', value: 'Format' },
          { label: 'Enum', value: 'Enum' },
          { label: 'Values', value: 'Values' },
        ];
        setAvlValueTypeSource(source);
        form.setFieldsValue({
          avlValueType: 'Empty',
        });
        break;

      case 'Boolean':
        source = [{ label: 'Empty', value: 'Empty' }];
        setAvlValueTypeSource(source);
        form.setFieldsValue({
          avlValueType: 'Empty',
        });
        break;

      case 'Long':
        source = [
          { label: 'Empty', value: 'Empty' },
          { label: 'Length', value: 'Length' },
          { label: 'Range', value: 'Range' },
          { label: 'Enum', value: 'Enum' },
          { label: 'Values', value: 'Values' },
        ];
        setAvlValueTypeSource(source);
        form.setFieldsValue({
          avlValueType: 'Empty',
        });
        break;
      case 'Integer':
        source = [
          { label: 'Empty', value: 'Empty' },
          { label: 'Length', value: 'Length' },
          { label: 'Range', value: 'Range' },
          { label: 'Enum', value: 'Enum' },
          { label: 'Values', value: 'Values' },
        ];
        setAvlValueTypeSource(source);
        form.setFieldsValue({
          avlValueType: 'Empty',
        });
        break;
      case 'Number':
        source = [
          { label: 'Empty', value: 'Empty' },
          { label: 'Length', value: 'Length' },
          { label: 'Range', value: 'Range' },
          { label: 'Format', value: 'Format' },
          { label: 'Values', value: 'Values' },
        ];
        setAvlValueTypeSource(source);
        form.setFieldsValue({
          avlValueType: 'Empty',
        });
        break;
      case 'Datetime':
        source = [
          { label: 'Empty', value: 'Empty' },
          { label: 'Format', value: 'Format' },
          { label: 'Values', value: 'Values' },
        ];
        setAvlValueTypeSource(source);
        form.setFieldsValue({
          avlValueType: 'Empty',
        });
        break;

      default:
        // TODO 可能不需要默认值
        setAvlValueTypeSource([]);
        setDisabled(true);
        setRequired(false);
        break;
    }
  };

  const onAvlValueTypeChange = (value: any) => {
    setAvlValueTypeValue(value);

    // 重置avlValueTypeValue相关字段
    resetFormFields(false);
  };

  const handleEnumFilter = (input: any, option: any) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };

  useEffect(() => {
    if (open) {
      // 查询领域下schema列表
      querySchemaList();
    }
  }, [open]);

  useEffect(() => {
    // 深拷贝行数据后初始化值
    const defaultValues: any = {};

    Object.assign(defaultValues, selectedParam);

    if (!paramTypeSource.includes(defaultValues.type)) {
      // 如果是非基本类型，且存在refSchemaInfo，则该数据是从后端获取的；
      if (defaultValues?.refSchemaInfo) {
        defaultValues.type = defaultValues?.refSchemaInfo?.schemaName;
        setSelectedTypeObj({
          key: defaultValues?.refSchemaInfo?.schemaId,
          value: defaultValues?.refSchemaInfo?.schemaName,
        });
      } else {
        // 如果是非基本类型，但不存在refSchemaInfo，则该数据是通过'Add'添加的，或者原本是从后端获取的数据，且已经修改过，但还未调接口入库；
        defaultValues.type = defaultValues?.refSchemaName;
        setSelectedTypeObj({ key: defaultValues?.refSchemaId, value: defaultValues?.refSchemaName });
      }
      onPropTypeChange(defaultValues.type);
    } else {
      // 五种数据基本类型，处理avlValue字段的回显问题
      const { avlValue } = defaultValues;
      if (avlValue === '' || avlValue === null) {
        defaultValues.avlValueType = 'Empty';
      } else if (`${avlValue}` === `${defaultValues.enumId}`) {
        defaultValues.avlValueType = 'Enum';
        defaultValues.enum = enumSource.find((i: any) => `${i.value}` === `${avlValue}`)?.label;
      } else if (typeof avlValue === 'string' && avlValue?.toLowerCase().startsWith('length')) {
        defaultValues.avlValueType = 'Length';
        const betweenVal = getStringBetween(avlValue, '[', ']')?.split(',');
        defaultValues.min = betweenVal[0];
        defaultValues.max = betweenVal[1];
      } else if (typeof avlValue === 'string' && avlValue?.toLowerCase().startsWith('range')) {
        defaultValues.avlValueType = 'Range';
        const betweenVal = getStringBetween(avlValue, '[', ']')?.split(',');
        defaultValues.min = betweenVal[0];
        defaultValues.max = betweenVal[1];
      } else if (FormatSource.filter((item) => item.value === avlValue).length > 0) {
        defaultValues.avlValueType = 'Format';
        defaultValues.format = avlValue;
      } else {
        defaultValues.avlValueType = 'Values';
        defaultValues.values = avlValue;
      }
      onPropTypeChange(defaultValues.type);
      onAvlValueTypeChange(defaultValues.avlValueType);
    }
    if (operateType === 'Add') {
      defaultValues.isArray = 'N';
      defaultValues.required = 'N';
    }
    setDeFaultValues(defaultValues);
  }, [selectedParam, open]);

  return (
    <Drawer
      title={`${operateType} ${formatMessage('SCHEMAMANAGEMENT.COMPOSITION_SCHEMAS')}`}
      onClose={onClose}
      maskClosable={false}
      open={open}
      width={720}
      afterOpenChange={() => {
        form.resetFields();
      }}
      footer={
        <Space style={{ float: 'right' }}>
          <Button type="primary" onClick={checkServiceReqParamData}>
            {formatMessage('PROJECT.COMMON.CONFIRM')}
          </Button>
          <Button onClick={onClose}>{formatMessage('PROJECT.COMMON.CANCEL')}</Button>
        </Space>
      }
    >
      <Form
        form={form}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
        initialValues={defaultValues}
        onValuesChange={(cValues, alValues) => {
          setFormValues(alValues);
        }}
      >
        <Form.Item
          label={formatMessage('PROJECT.COMMON.TYPE')}
          name="type"
          rules={[{ required: true, message: '' }]}
          extra={
            avlValueType &&
            !paramTypeSource.includes(avlValueType) &&
            formatMessage('MANAGEEXTENSIONSERVICE.SCHEMA.EDIT.NOTES')
          }
        >
          <Select
            options={[
              ...paramTypeSource.map((item) => ({ label: item, value: item, key: item })),
              ...domainSchemaList.map((item: DomainSchemaObj) => ({
                label: <span>{item?.domainObjName}</span>,
                value: item?.domainObjName,
                options: item?.schemaList.map((i) => ({
                  label: i?.schemaName,
                  value: i?.schemaId,
                  key: i?.schemaId,
                })),
              })),
            ]}
            onChange={onPropTypeChange}
            showSearch
            filterOption={(input, option) => {
              // 基本类型直接过滤
              if (option?.label && typeof option.label === 'string') {
                return option.label.toLowerCase().includes(input.toLowerCase());
              }
              // 组标题不参与过滤，总是显示
              return false;
            }}
            dropdownRender={(menu) => (
              <>
                {menu}
                <Divider style={{ margin: '8px 0' }} />
                <Space style={{ padding: '0 8px 4px' }}>
                  <Input
                    value={newSchemaName}
                    placeholder={formatMessage('SCHEMAMANAGEMENT.PLEASE_ENTER_ITEM')}
                    ref={addSchemaInputRef}
                    onChange={onAddSchemaNameChange}
                    onKeyDown={(e) => e.stopPropagation()}
                  />
                  <Button type="text" onClick={addSchema}>
                    + {formatMessage('SCHEMAMANAGEMENT.NEW_SCHEMA')}
                  </Button>
                </Space>
              </>
            )}
          />
        </Form.Item>
        {/* avlValueType 如果为复杂类型则不展示'Example'和'Allowable Values' */}
        {!avlValueType || paramTypeSource.includes(avlValueType) ? (
          <>
            <Form.Item label={formatMessage('ENTITYPROP.ALLOWABLE_VALUES')} style={{ marginBottom: 0 }}>
              <Form.Item
                name="avlValueType"
                style={{ display: 'inline-block', width: 'calc(33% - 5px)' }}
                rules={[{ required, message: '' }]}
              >
                <Select options={avlValueTypeSource} onChange={onAvlValueTypeChange} disabled={disabled} />
              </Form.Item>
              {avlValueTypeValue === 'Length' ? (
                <Form.Item
                  name="min"
                  style={{ display: 'inline-block', width: 'calc(33% - 5px)', marginLeft: '5px' }}
                  rules={[{ required: true, message: '' }]}
                >
                  <InputNumber
                    addonBefore={formatMessage('ENTITYPROP.MIN')}
                    precision={0}
                    min={1}
                    max={formValues.max}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              ) : null}
              {avlValueTypeValue === 'Length' ? (
                <Form.Item
                  name="max"
                  style={{ display: 'inline-block', width: 'calc(33% - 8px)', marginLeft: '5px' }}
                  rules={[{ required: true, message: '' }]}
                >
                  <InputNumber
                    addonBefore={formatMessage('ENTITYPROP.MAX')}
                    precision={0}
                    min={formValues.min}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              ) : null}
              {avlValueTypeValue === 'Enum' ? (
                <Form.Item
                  name="enum"
                  style={{ display: 'inline-block', width: 'calc(66% - 8px)', marginLeft: '5px' }}
                  rules={[{ required: true, message: '' }]}
                >
                  <Select showSearch filterOption={handleEnumFilter}>
                    {enumSource?.map((item: any) => (
                      <Select.Option key={item.value} value={item.label}>
                        {item.label}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              ) : null}
              {avlValueTypeValue === 'Format' ? (
                <Form.Item
                  name="format"
                  style={{ display: 'inline-block', width: 'calc(66% - 8px)', marginLeft: '5px' }}
                  rules={[{ required: true, message: '' }]}
                >
                  <Select options={FormatSource} />
                </Form.Item>
              ) : null}
              {avlValueTypeValue === 'Values' ? (
                <Form.Item
                  name="values"
                  style={{ display: 'inline-block', width: 'calc(66% - 8px)', marginLeft: '5px' }}
                  rules={[{ required: true, message: '' }]}
                >
                  <Input placeholder={formatMessage('SCHEMAMANAGEMENT.VALUES_PLACEHOLDER')} />
                </Form.Item>
              ) : null}
              {avlValueTypeValue === 'Range' ? (
                <Form.Item
                  name="min"
                  style={{ display: 'inline-block', width: 'calc(33% - 5px)', marginLeft: '5px' }}
                  rules={[{ required: true, message: '' }]}
                >
                  <InputNumber
                    addonBefore={formatMessage('ENTITYPROP.MIN')}
                    precision={0}
                    max={formValues.max}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              ) : null}
              {avlValueTypeValue === 'Range' ? (
                <Form.Item
                  name="max"
                  style={{ display: 'inline-block', width: 'calc(33% - 8px)', marginLeft: '5px' }}
                  rules={[{ required: true, message: '' }]}
                >
                  <InputNumber
                    addonBefore={formatMessage('ENTITYPROP.MAX')}
                    precision={0}
                    min={formValues.min}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              ) : null}
            </Form.Item>
            <Form.Item label={formatMessage('PROJECT.COMMON.EXAMPLE')} name="example">
              <Input allowClear />
            </Form.Item>
          </>
        ) : null}
        <Form.Item label={formatMessage('PROJECT.COMMON.DESCRIPTION')} name="description">
          <Input.TextArea allowClear />
        </Form.Item>
      </Form>
    </Drawer>
  );
};
export default AddCompositionSchema;
