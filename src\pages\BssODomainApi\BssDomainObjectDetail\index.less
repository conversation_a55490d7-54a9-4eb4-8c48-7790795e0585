.activeLineContainer {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  padding: 12px 16px;
  box-sizing: border-box;
}

.topContent {
  margin-top: 5px;
  display: inline-block;
}

.hide {
  display: none;
}

.selectContent {
  margin-left: 20px;
}

.domainObjContent {
  .title {
    font-size: 16px;
    font-family: Nunito Sans-Bold;
    font-weight: bold;
    color: #2d3040;
    line-height: 24px;
  }
}

.domainObjName {
  color: #47e;
  cursor: pointer;
}

.iconStyle {
  color: #47e;
  cursor: pointer;
}

.versionIcon1 {
  margin-left: 20px;
  color: #21f17f;
  cursor: pointer;
}

.versionIcon2 {
  margin-left: 20px;
  color: #47e;
  cursor: pointer;
}
.arrowRight {
  padding: 0 10px;
}
.noListStyle {
  list-style-type: none;
  padding: 0;
  margin: 0;
}
