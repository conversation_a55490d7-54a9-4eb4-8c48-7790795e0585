import { MinusCircleOutlined, PlusCircleOutlined, ReloadOutlined } from '@ant-design/icons';
import React, { useEffect, useRef, useState } from 'react';
import styles from './index.less';

interface IDraggableSVGByInnerHTML {
  innerHTML: string; // SVG 的 HTML 内容
  baseRate?: number; // 缩放的基准值，默认为 0.3
}

const DraggableSVGByInnerHTML: React.FC<IDraggableSVGByInnerHTML> = (props) => {
  const { innerHTML, baseRate = 0.3 } = props; // 解构 props
  const svgRef = useRef<HTMLDivElement | null>(null); // 引用 SVG 元素
  const containerRef = useRef<HTMLDivElement | null>(null); // 引用容器元素
  const [isDragging, setIsDragging] = useState(false); // 拖动状态
  const [startPosition, setStartPosition] = useState({ x: 0, y: 0 }); // 鼠标按下时的位置
  const [initialPosition, setInitialPosition] = useState({ left: 0, top: 0 }); // SVG 元素的初始位置
  const [scale, setScale] = useState(1); // 当前缩放比例
  const [containerHeight, setContainerHeight] = useState<number>(0); // 容器高度

  // 获取容器的边界矩形
  const getContainerRect = () => {
    return containerRef.current?.getBoundingClientRect();
  };

  // 计算 SVG 的中心位置
  const calculateCenterPosition = (svgWidth: number, svgHeight: number) => {
    const containerRect = getContainerRect();
    if (!containerRect) return { x: 0, y: 0 };

    const centerX = containerRect.left + (containerRect.width - svgWidth) / 2; // 计算中心 X 坐标
    const centerY = containerRect.top + (containerRect.height - svgHeight) / 2; // 计算中心 Y 坐标

    return { x: centerX, y: centerY };
  };

  // 更新 SVG 的位置
  const updateSVGPosition = (x: number, y: number) => {
    if (svgRef.current) {
      const parentRect = svgRef.current.parentElement?.getBoundingClientRect();
      if (!parentRect) return;

      svgRef.current.style.left = `${x - parentRect.left}px`; // 设置 SVG 的左边距
      svgRef.current.style.top = `${y - parentRect.top}px`; // 设置 SVG 的上边距
    }
  };

  // 鼠标按下事件处理
  const handleMouseDown = (event: React.MouseEvent<HTMLDivElement>) => {
    setIsDragging(true); // 设置拖动状态为 true
    setStartPosition({ x: event.clientX, y: event.clientY }); // 记录鼠标按下时的位置

    if (svgRef.current) {
      const rect = svgRef.current.getBoundingClientRect(); // 获取 SVG 的边界矩形
      setInitialPosition({ left: rect.left, top: rect.top }); // 记录 SVG 的初始位置
      svgRef.current.style.cursor = 'grabbing'; // 更改鼠标光标样式
    }
  };

  // 鼠标移动事件处理
  const handleMouseMove = (event: React.MouseEvent<HTMLDivElement>) => {
    if (!isDragging || !svgRef.current) return; // 如果不在拖动状态，直接返回

    const dx = event.clientX - startPosition.x; // 计算鼠标移动的 X 距离
    const dy = event.clientY - startPosition.y; // 计算鼠标移动的 Y 距离

    const newX = initialPosition.left + dx; // 计算新的 X 坐标
    const newY = initialPosition.top + dy; // 计算新的 Y 坐标

    const svgWidth = svgRef.current.clientWidth * scale; // 计算缩放后的 SVG 宽度
    const svgHeight = svgRef.current.clientHeight * scale; // 计算缩放后的 SVG 高度

    const containerRect = getContainerRect(); // 获取容器的边界矩形
    if (!containerRect) return;

    // 限制 SVG 的移动范围
    const minX = containerRect.left - svgWidth;
    const maxX = containerRect.right;
    const minY = containerRect.top - svgHeight;
    const maxY = containerRect.bottom;

    // 计算限制后的新坐标
    const constrainedX = Math.max(minX, Math.min(newX, maxX));
    const constrainedY = Math.max(minY, Math.min(newY, maxY));

    updateSVGPosition(constrainedX, constrainedY); // 更新 SVG 的位置
  };

  // 鼠标抬起事件处理
  const handleMouseUp = () => {
    setIsDragging(false); // 设置拖动状态为 false
    if (svgRef.current) {
      svgRef.current.style.cursor = 'grab'; // 更改鼠标光标样式
    }
  };

  // 放大 SVG
  const zoomOut = () => {
    setScale((prevScale) => prevScale + baseRate); // 增加缩放比例
  };

  // 缩小 SVG
  const zoomIn = () => {
    setScale((prevScale) => Math.max(prevScale - baseRate, baseRate)); // 减小缩放比例，确保不小于最小值
  };

  // 重置缩放
  const zoomReset = () => {
    setScale(1); // 重置缩放比例为 1
    if (svgRef.current) {
      const svgWidth = svgRef.current.clientWidth; // 获取 SVG 的宽度
      const svgHeight = svgRef.current.clientHeight; // 获取 SVG 的高度

      const { x, y } = calculateCenterPosition(svgWidth, svgHeight); // 计算中心位置
      updateSVGPosition(x, y); // 更新 SVG 的位置
    }
  };

  // 组件挂载后，初始化 SVG 和容器高度
  useEffect(() => {
    zoomReset(); // 重置缩放
    const svgHeight = document.querySelector('#targetContainer svg')?.clientHeight || 0; // 获取 SVG 的高度
    setContainerHeight(svgHeight); // 设置容器高度
  }, [innerHTML]);

  // 监测容器高度和缩放比例变化，更新 SVG 位置
  useEffect(() => {
    if (containerRef.current && svgRef.current) {
      const svgWidth = svgRef.current.clientWidth * scale; // 计算缩放后的 SVG 宽度
      const svgHeight = svgRef.current.clientHeight * scale; // 计算缩放后的 SVG 高度

      const { x, y } = calculateCenterPosition(svgWidth, svgHeight); // 计算中心位置
      updateSVGPosition(x, y); // 更新 SVG 的位置
      setInitialPosition({ left: x, top: y }); // 更新初始位置
    }
  }, [containerHeight, scale]);

  return (
    <div
      className={styles.draggableContainer}
      style={{ height: containerHeight }} // 设置容器高度
      onMouseMove={handleMouseMove} // 绑定鼠标移动事件
      onMouseUp={handleMouseUp} // 绑定鼠标抬起事件
      onMouseLeave={handleMouseUp} // 绑定鼠标离开事件
      ref={containerRef} // 绑定容器引用
    >
      <div>
        <PlusCircleOutlined className={`${styles.operationIcon}`} onClick={zoomOut} /> {/* 放大按钮 */}
        <MinusCircleOutlined className={`${styles.operationIcon}`} onClick={zoomIn} /> {/* 缩小按钮 */}
        <ReloadOutlined className={`${styles.operationIcon}`} onClick={zoomReset} /> {/* 重置按钮 */}
      </div>
      <div
        id="targetContainer"
        ref={svgRef} // 绑定 SVG 引用
        className={styles.draggableSVG}
        onMouseDown={handleMouseDown} // 绑定鼠标按下事件
        dangerouslySetInnerHTML={{ __html: innerHTML }} // 设置 SVG 的 HTML 内容
        style={{
          position: 'absolute',
          transform: `scale(${scale})`, // 应用缩放
        }}
      />
    </div>
  );
};

export default DraggableSVGByInnerHTML;
