* {
  padding: 0;
  margin: 0;
  box-sizing: border-box;
}

#root,
body,
html {
  width: 100%;
  height: 100%;
}

.mr10 {
  margin-right: 10px;
}

.search {
  background-color: var(--dark-bg-color);
  padding: 20px;
  border-radius: 5px;
}

.table {
  background-color: var(--dark-bg-color);
  border-radius: 5px;

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-radius: 5px 5px 0 0;
    color: var(--dark-color);
    padding: 15px;

    .action button {
      margin-left: 15px;
    }
  }
}

.search+.table {
  margin-top: 20px;
}