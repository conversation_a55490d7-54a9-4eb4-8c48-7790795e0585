// 解析字符串的link规则
// 规则如下
// 转还前 "Show only the first n items, see [Paging - Top](http://docs.oasis-open.org/odata/odata/v4.01/odata-v4.01-part1-protocol.html#sec_SystemQueryOptiontop)"

import { getDescByProp } from "@/services/entityService";
import { FormInstance } from "antd";

// 转换后 "Show only the first n items, see <a target="http://docs.oasis-open.org/odata/odata/v4.01/odata-v4.01-part1-protocol.html#sec_SystemQueryOptiontop" target="_blank">Paging - Top</a>"
const parseLinkFromString = (value: string) => {
  let valueAfterTransform = value || '';
  const regex = /\[(.*?)\)/;
  const match = (value || '').match(regex);
  if (match) {
    // 匹配link
    const regexLink = /\((.*?)\)/;
    const matchLInk = match[0].match(regexLink);

    // 匹配linkname
    const regexLinkName = /\[(.*?)\]/;
    const matchLinkName = match[0].match(regexLinkName);

    valueAfterTransform =
      valueAfterTransform.replaceAll(match[0], "") +
      `<a href="${matchLInk?.[1]}" target="_blank">${matchLinkName?.[1]}</a>`;
  }
  return valueAfterTransform;
};

// 根据propName自动匹配desc
const autoFillDescByPropName = async(form:FormInstance ,originField:string, targetFiled:string) => {
  try {
    const formValues = form.getFieldsValue()
    if(!formValues[targetFiled] && formValues[originField]) {
      const {success, data} = await getDescByProp({prop: formValues[originField]})
      if(success) {
        form.setFieldValue(targetFiled, data)
      }
    }
  } catch(error) {
    console.error(error)
  }
}

const enCodeParamsFn = (params: any = {}) => {
  return Object.keys(params)
    ?.map((key) => {
      const value = params[key];
      // 处理数组类型参数，并移除不可见字符
      if (Array.isArray(value)) {
        // 移除每个数组元素中可能存在的零宽空格等不可见字符
        const cleanedValues = value.map((item) =>
          typeof item === 'string' ? item.replace(/[\u200B-\u200D\uFEFF]/g, '') : item,
        );
        return key + '=' + cleanedValues.join(',');
      }
      // 处理字符串类型，移除可能的不可见字符
      if (typeof value === 'string') {
        return key + '=' + value.replace(/[\u200B-\u200D\uFEFF]/g, '');
      }
      return key + '=' + value;
    })
    ?.join('&');
};

// 删除零宽空格
const removeInvisibleChars = (str: string) => str.replace(/[\u200B-\u200D\uFEFF]/g, '');

// 递归过滤对象中的零宽空格
const filteredZeroWidthSpace = (params: any): any => {
  if (typeof params === 'string') {
    return removeInvisibleChars(params);
  }
  if (Array.isArray(params)) {
    return params.map((item) => filteredZeroWidthSpace(item));
  }
  if (params && typeof params === 'object') {
    return Object.keys(params).reduce((acc, key) => {
      acc[key] = filteredZeroWidthSpace(params[key]);
      return acc;
    }, {} as Record<string, any>);
  }
  return params; // 其他类型直接返回
};

export { parseLinkFromString, autoFillDescByPropName, enCodeParamsFn, filteredZeroWidthSpace };
