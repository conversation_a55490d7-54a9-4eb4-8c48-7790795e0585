import React, { useEffect, useState, forwardRef, useImperativeHandle, useRef } from 'react';
import { queryCatalogMenu, queryDomainMenu } from '@/services';
import { IGroupList, ICatalog, IOriginalCatalogList } from '@/services/typing.d';
import { BYCATALOG } from '@/constants';
import { Input, Popover, Divider, Button, Space } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import useI18n from '@/hooks/useI8n';
import { searchDomainByText, searchCatalogByText, addDomainKey, addCatalogKey } from '@/utils/searchDataByKeyWords';
import classNames from 'classnames';
import styles from './index.less';
import ResizableTable from '@/components/ResizableTable';
import { getReflectedCatalogData } from '@/utils/cleanDataAmount';

interface IMenuTree {
  selectType: string;
  onSelectKeys?: (selectKyes: string[]) => void;
  handleSetIsSelectedMultipleDomains: (isSelectedMultipleDomains: boolean) => void;
}

interface IStep2Ref {
  getSelectKeys: () => string[];
}

const MenuTree: React.ForwardRefRenderFunction<IStep2Ref, IMenuTree> = (props, ref) => {
  const { selectType, onSelectKeys, handleSetIsSelectedMultipleDomains } = props;
  const { formatMessage } = useI18n();
  const searchref = useRef<any>();

  const [listData, setListData] = useState<Array<IGroupList>>([]); // domain origin Data
  const [listDataAfterSearch, setListDataAfterSearch] = useState<Array<IGroupList>>([]); // domain Data after search
  const [catalogListData, setCatalogListData] = useState<Array<ICatalog>>([]); // catalog origin data
  const [catalogListDataAfterSearch, setCatalogListDataAfterSearch] = useState<Array<ICatalog>>([]); // catalog origin data after search
  const [selectKeys, setSelectKeys] = useState<string[]>([]); // select keys
  const [searchValue, setSearchValue] = useState<string>(''); // search value
  const [searchFlag, setSearchFlag] = useState<boolean>(false); // search flag

  // search key words
  const handleSearch = async (searchValue: string) => {
    if (selectType === BYCATALOG) {
      const res = searchCatalogByText(searchValue || '', catalogListData);
      const serializeRes = addCatalogKey(res || []);
      setCatalogListDataAfterSearch(serializeRes);
    } else {
      const res = searchDomainByText(searchValue || '', listData);
      const serializeRes = addDomainKey(res);
      setListDataAfterSearch(serializeRes || []);
    }
  };

  // init request menu
  const requestCatalogData = async () => {
    if (selectType === BYCATALOG) {
      const { data } = await queryCatalogMenu();
      // 因queryCatalogMenu接口改变，返回的字段都改变，所以需要映射一下相应字段
      const reflectedData = getReflectedCatalogData(data || []);
      const serializeRes = addCatalogKey(reflectedData);
      setCatalogListData(serializeRes || []);
      setCatalogListDataAfterSearch(serializeRes || []);
    } else {
      const { data } = await queryDomainMenu();
      const serializeRes = addDomainKey(data);
      setListData(serializeRes || []);
      setListDataAfterSearch(serializeRes || []);
    }
  };

  useImperativeHandle(ref, () => ({
    getSelectKeys: () => selectKeys,
  }));

  // init request menu
  useEffect(() => {
    if (selectType) {
      requestCatalogData();
    }
  }, [selectType]);

  // columns
  const columns = [
    {
      dataIndex: 'customeRender',
      title: () => (
        <div className={styles.titleSearch}>
          <span>
            {selectType === BYCATALOG
              ? formatMessage('DOWNLOAD.MODAL.TITLECATALOG')
              : formatMessage('DOWNLOAD.MODAL.TITLEDOMAIN')}
          </span>
          <Popover
            placement="bottom"
            trigger="click"
            title={''}
            content={
              <div>
                <Input
                  style={{ width: '231px' }}
                  value={searchValue}
                  onChange={(e) => setSearchValue(e?.target?.value || '')}
                  ref={searchref}
                />
                <Divider style={{ margin: '8px 0' }} />
                <div>
                  <Space>
                    <Button
                      onClick={() => {
                        setSearchValue('');
                        setSearchFlag(false);
                        handleSearch('');
                      }}
                    >
                      {formatMessage('PROJECT.COMMON.RESET')}
                    </Button>
                    <Button
                      type="primary"
                      onClick={() => {
                        setSearchFlag(true);
                        handleSearch(searchValue);
                      }}
                    >
                      {formatMessage('PROJECT.COMMON.SEARCH')}
                    </Button>
                  </Space>
                </div>
              </div>
            }
          >
            <SearchOutlined
              className={classNames(styles.searchIcon, {
                [styles.activeSearch]: searchFlag,
              })}
            />
          </Popover>
        </div>
      ),

      render: (_: string, record: any) => {
        if (selectType === BYCATALOG) {
          return (
            record?.catalog ||
            record?.groupName ||
            (record?.subCatalog ? record?.key?.split('-')[2] : record?.subCatalog)
          );
        }
        return record?.group || record?.domainName || record?.entityName;
      },
    },
  ];

  return (
    <div className={styles.menuTreeContainer}>
      <div className={styles.menuContent}>
        {(selectType === BYCATALOG ? catalogListData : listData)?.length ? (
          <ResizableTable
            key="hasData"
            size="small"
            pagination={false}
            scroll={{
              y: 320,
            }}
            rowSelection={{
              type: 'checkbox',
              checkStrictly: false,
              onChange: (selectedRowKeys: any, selectedRows: any, info: any) => {
                let selectedKeys = [];
                if (selectType === BYCATALOG) {
                  selectedKeys = selectedRows.filter((i: any) => i.subCatalog).map((i: any) => i.subCatalog);
                } else {
                  selectedKeys = selectedRows.filter((i: any) => i.entityKey).map((i: any) => i.entityKey);
                }
                // 暂时前端判断是否选择了多个领域，通过selectedKeys的前缀判断是否选择了多个领域
                // 提取第一个 . 之前的字符串
                const prefixes = selectedKeys.map((key: string) => key.split('.')[0]);

                // 检查前缀是否一致
                const allPrefixesEqual = prefixes.every((prefix: string) => prefix === prefixes[0]);

                if (allPrefixesEqual) {
                  handleSetIsSelectedMultipleDomains(false);
                } else {
                  // 先调用父组件传入的函数，直接更新父组件状态
                  handleSetIsSelectedMultipleDomains(true);
                }
                setSelectKeys(selectedKeys);
                onSelectKeys?.(selectedKeys);
              },
            }}
            // rowKey={(record: any) => record?.key}
            expandable={{
              defaultExpandAllRows: true,
            }}
            columns={columns}
            dataSource={selectType === BYCATALOG ? catalogListDataAfterSearch : listDataAfterSearch}
          />
        ) : (
          <ResizableTable key="noData" size="small" pagination={false} columns={columns} dataSource={[]} />
        )}
      </div>
    </div>
  );
};

export default forwardRef(MenuTree);
