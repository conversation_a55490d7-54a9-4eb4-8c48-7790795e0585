import ResizableTable from '@/components/ResizableTable';
import { CheckOutlined, CloseOutlined, DeleteOutlined, FormOutlined } from '@ant-design/icons';
import { Flex, Input, message, Select } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import styles from './index.less';
import useI18n from '@/hooks/useI8n';
import getUUid from '@/utils/getUUid';

interface IODataParamsTable {
  dataToBeSent: { [k: string]: any };
  paramList: any;
  isEditAPIGService: boolean;
  getAPIGParamsOriginData: (data: any) => void;
  generateFromOData: (originData: any) => void;
  dataFromApi?: any;
}

interface IDataItem {
  key: string;
  name: string;
  value: string;
  isEdit: boolean;
}

interface IParamName {
  value: string;
  label: string;
}

const ODataParamsTable: React.FC<IODataParamsTable> = (props) => {
  const {
    dataToBeSent,
    paramList,
    isEditAPIGService = false,
    dataFromApi = [],
    getAPIGParamsOriginData,
    generateFromOData,
  } = props;
  const { formatMessage } = useI18n();
  const [dataSource, setDataSource] = useState<IDataItem[]>([]);
  const [isEditDisabled, setIsEditDisabled] = useState<boolean>(false);
  const [paramNameList, setParamNameList] = useState<IParamName[]>([]);
  const [deletedRow, setDeletedRow] = useState<string>('');
  const [isGenerateFromOData, setIsGenerateFromOData] = useState<boolean>(false);

  const [originalValues, setOriginalValues] = useState<any>({}); // 暂存的原数据

  // 改变name
  const handleNameChange = (value: any, record: IDataItem) => {
    setDataSource((prevData: IDataItem[]) =>
      prevData.map((item: IDataItem) => (item.key === record.key ? { ...item, name: value } : item)),
    );
  };

  // 改变value
  const handleValueChange = (e: any, record: IDataItem) => {
    setDataSource((prevData: IDataItem[]) =>
      prevData.map((item: IDataItem) => (item.key === record.key ? { ...item, value: e.target.value } : item)),
    );
  };

  const generateOriginData = (dataSrc: IDataItem[]) => {
    const originData = dataSrc.map((item: IDataItem) => {
      const paramItem = paramList.find((i: any) => i.name === item.name);
      return paramItem ? { ...item, reqIn: paramItem.reqIn } : { ...item };
    });
    return originData;
  };

  // 新增参数
  const handleAddParam = () => {
    setIsGenerateFromOData(true);
    if (!isEditDisabled) {
      setDataSource((prevData: IDataItem[]) => {
        return [
          ...prevData,
          {
            key: getUUid(12),
            name: '',
            value: '',
            isEdit: true,
          },
        ];
      });
    }
  };

  // 删除参数
  const handleDeleteParam = (record: IDataItem) => {
    setIsGenerateFromOData(true);
    if (!isEditDisabled) {
      setDataSource((prevData: IDataItem[]) => {
        return prevData.filter((item: IDataItem) => item.key !== record.key);
      });
      setDeletedRow(record?.name);
    }
  };

  // 修改参数
  const handleEdotParam = (record: IDataItem) => {
    setIsGenerateFromOData(true);
    if (!isEditDisabled) {
      setOriginalValues((prev: any) => ({
        ...prev,
        [record.key]: {
          name: record.name,
          value: record.value,
        },
      }));
      setDataSource((prevData: IDataItem[]) =>
        prevData.map((item: IDataItem) => (item.key === record.key ? { ...item, isEdit: true } : item)),
      );
    }
  };

  // 确认修改
  const confirmEdit = (record: IDataItem) => {
    if (record?.value && record?.name) {
      // 更新状态并删除对应的 originalValues 条目
      setDataSource((prevData: IDataItem[]) =>
        prevData.map((item: IDataItem) => (item.key === record.key ? { ...item, isEdit: false } : item)),
      );
      setOriginalValues((prev: any) => {
        const { [record.key]: _, ...rest } = prev;
        return rest;
      });

      // 更新APIGParam
      const originData = generateOriginData(dataSource);
      generateFromOData(originData);
    } else {
      message.warning(formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.NOTEMPTYTIP'));
    }
  };

  // 取消修改
  const cancelEdit = (record: IDataItem) => {
    setDataSource((prevData: IDataItem[]) =>
      prevData.map((item: IDataItem) =>
        item.key === record.key
          ? {
              ...item,
              name: originalValues[record.key]?.name,
              value: originalValues[record.key]?.value,
              isEdit: false,
            }
          : item,
      ),
    );
    // 将 value 恢复为 originalValues 中存储的值，并删除对应的 originalValues 条目
    setOriginalValues((prev: any) => {
      // [record.key]: _ 是对象解构中的一种特殊用法，用来提取并忽略某个属性
      const { [record.key]: _, ...rest } = prev;
      return rest;
    });
  };

  const columns = useMemo(() => {
    return [
      {
        dataIndex: 'name',
        title: formatMessage('PROJECT.COMMON.NAME'),
        width: '20%',
        ellipsis: true,
        render: (_: any, record: IDataItem) => {
          return (
            <Select
              value={record?.name}
              disabled={!record.isEdit}
              onChange={(value) => handleNameChange(value, record)}
            >
              {paramNameList.map((i: any) => (
                <Select.Option key={i.value} value={i.value}>
                  {i.label}
                </Select.Option>
              ))}
            </Select>
          );
        },
      },
      {
        dataIndex: 'value',
        title: formatMessage('PROJECT.COMMON.VALUE'),
        width: '55%',
        ellipsis: true,
        render: (_: any, record: IDataItem) => {
          return (
            <Input disabled={!record.isEdit} value={record.value} onChange={(e) => handleValueChange(e, record)} />
          );
        },
      },
      {
        dataIndex: '',
        title: formatMessage('PROJECT.COMMON.OPERATION'),
        width: '25%',
        ellipsis: true,
        render: (_: any, record: IDataItem) => {
          return record.isEdit ? (
            <Flex>
              {/** 确认修改 */}
              <CheckOutlined onClick={() => confirmEdit(record)} className={styles.operationIconStyle} />
              {/** 取消修改 */}
              <CloseOutlined onClick={() => cancelEdit(record)} className={styles.operationIconStyle} />
            </Flex>
          ) : (
            <Flex>
              {/** 编辑 */}
              <FormOutlined
                className={isEditDisabled ? styles.operationIconDisabledStyle : styles.operationIconStyle}
                onClick={() => handleEdotParam(record)}
              />
              {/** 删除 */}
              <DeleteOutlined
                className={isEditDisabled ? styles.operationIconDisabledStyle : styles.operationIconStyle}
                onClick={() => handleDeleteParam(record)}
              />
            </Flex>
          );
        },
      },
    ];
  }, [dataSource, originalValues, paramNameList]);

  useEffect(() => {
    // 新增APIG Service时,初始化dataSource
    if (!isEditAPIGService && dataToBeSent) {
      const newDataObj = {
        // 按需求暂时支持query和header参数
        H: dataToBeSent?.H || [],
        // P: dataToBeSent?.P || [],
        Q: dataToBeSent?.Q || [],
      };
      let newDataSource: any[] = [];
      Object.values(newDataObj).map((list: any) => {
        list.map((item: any) => {
          if (item?.value) {
            const sourceItem = {
              key: getUUid(12),
              name: item?.name,
              value: item?.value,
              isEdit: false,
            };
            newDataSource.push(sourceItem);
          }
        });
      });
      setDataSource(newDataSource);
    }
  }, [dataToBeSent]);

  useEffect(() => {
    // 新增APIG Service时,直接执行以下步骤;编辑APIG Service时,操作OData Param才执行以下步骤
    if (!isEditAPIGService || isGenerateFromOData) {
      // 编辑某一行，其他行编辑状态变为不可编辑
      if (dataSource.some((item: any) => item?.isEdit)) {
        setIsEditDisabled(true);
      } else {
        setIsEditDisabled(false);
      }
      // 传递APIG源数据给父组件
      const origindata = generateOriginData(dataSource);
      getAPIGParamsOriginData(origindata);
      // 更新paramNameList
      if (paramList) {
        const nameList = paramList
          .filter((item: any) => dataSource.every((existItem: any) => existItem?.name !== item?.name))
          .map((item: any) => ({
            value: item.name,
            label: item.name,
          }));
        setParamNameList(nameList);
      }
    }
  }, [dataSource, isGenerateFromOData]);

  useEffect(() => {
    if (deletedRow) {
      // 更新APIGParam
      const originData = generateOriginData(dataSource);
      generateFromOData(originData);
      setDeletedRow('');
    }
  }, [deletedRow, dataSource]);

  useEffect(() => {
    // 编辑APIG Service时,初始化dataSource
    if (dataFromApi.length > 0 && isEditAPIGService && !isGenerateFromOData) {
      const newData = dataFromApi.map((item: any) => ({
        key: getUUid(12),
        name: item?.paramName,
        value: item?.paramValue,
        isEdit: false,
      }));

      setDataSource(newData);
      // 传递APIG源数据给父组件
      const newOriginData = generateOriginData(newData);
      getAPIGParamsOriginData(newOriginData);
    }
  }, [dataFromApi]);

  return (
    <>
      <span className={isEditDisabled ? styles.iconDisabledStyle : styles.iconStyle} onClick={handleAddParam}>
        + {formatMessage('PROJECT.COMMON.ADD')}
      </span>
      <ResizableTable
        key="ODataParamsTable"
        size="small"
        columns={columns}
        dataSource={dataSource}
        pagination={false}
        rowKey={(record: any) => record.key}
      />
    </>
  );
};

export default ODataParamsTable;
