.responseStatus {
  display: flex;
  align-items: center;
  gap: 2rem;
  .statusSelect {
    width: 120px;
  }
  margin-bottom: 0.5rem;
}

.content {
  display: flex;
  height: 20rem;
  width: 100%;
  .leftContent {
    width: 70%;
    .codePrompt {
      height: 87%;
    }
    .buttonGroup {
      border: 1px solid rgb(223, 223, 223);
      height: 13%;
      display: flex;
      justify-content: flex-end;
      align-items: center;

      .buttonItem {
        border-radius: 5px;
        margin-right: 10px;
      }
    }
  }

  .snippets {
    overflow: auto;
    width: 30%;
    border: 1px solid rgb(223, 223, 223);
    // border-left: none; /* 左边框不设置 */
    padding: 10px;

    .title {
      font-size: 18px;
    }

    .scriptItem {
      display: flex;
      margin-top: 0.5rem;
      .iconStyle {
        color: #47e;
        cursor: pointer;
        width: 90%;
        overflow: hidden;
        margin-right: 5px;
        white-space: nowrap;
        text-overflow: ellipsis;
      }
    }

  }
}


