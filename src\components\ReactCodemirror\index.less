.codeMirrorEditor {
  height: 100%;
  border: 1px solid #dfdfdf;
  min-height: inherit;
  max-height: inherit;
  // overflow: auto;
  width: 100%;
  .codeMirrorPanel {
    width: 100%;
    padding: 8px 8px 0;
  }
  :global {
    .cm-theme {
      height: 100%;
      // overflow: auto;
      min-height: inherit;
      max-height: inherit;
    }
    .cm-editor {
      height: 100%;
      min-height: inherit;
      max-height: inherit;
      outline: none !important; // 移除codeMirror的outline显示
    }

    .cm-editor:hover {
      transition: all 200ms ease-in, border 200ms ease-in;
      // border-color: #93c5fd;
      box-shadow: 0 0 0 1px #1677FF;
      border-right-width: 1px !important;
      outline: none !important;
    }
    .cm-scroller {
      overflow: auto;
    }
    .cm-gutters {
      border-right: none;
      padding-right: 6px; /* 不显示行号时，行号与内容之间的右内边距 */
      width: 6px;
    }
    .cm-gutterElement {
      padding-left: 8px;
    }
  }
}
.codeMirrorExistHeader {
  :global {
    .cm-theme {
      height: calc(100% - 32px);
    }
  }
}

.codeMirrorReadOnly {
  :global {
    .cm-gutters {
      background-color: rgb(245,245,245); // 根据 readOnly 状态设置背景色
    }
    .cm-editor {
      background-color: rgb(245,245,245); // 根据 readOnly 状态设置背景色
    }
  }
}

.codeMirrorShowLineNumbers{
  :global {
    .cm-gutters {
      padding-right: 20px; /* 显示行号时，行号与内容之间的右内边距 */
    }
  }
}
