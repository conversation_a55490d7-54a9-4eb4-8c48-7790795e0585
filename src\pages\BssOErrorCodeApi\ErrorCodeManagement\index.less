.spinContainer {
  width: 100%;
  height: 100%;
  overflow-x: hidden;
  :global {
    .ant-spin-container {
      width: 100%;
      height: 100%;
    }
  }
}
.activeLineContainer {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  padding: 12px 16px;
  box-sizing: border-box;
}

.mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(238, 98, 98, 0.08);
  pointer-events: none; /* 事件穿透 */
  z-index: 999;
}
