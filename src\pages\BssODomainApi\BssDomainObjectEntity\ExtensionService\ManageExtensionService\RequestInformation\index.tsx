import OperationIconTitle from '@/components/OperationIconTitle';
import { DeleteOutlined, DownOutlined, FormOutlined, RightOutlined } from '@ant-design/icons';
import {
  Button,
  Flex,
  message,
  Modal,
  Popconfirm,
  PopconfirmProps,
  Select,
  Space,
  Spin,
  Tabs,
  TabsProps,
  Tooltip,
  Typography,
} from 'antd';
import { useEffect, useState } from 'react';
import styles from './index.less';
import classNames from 'classnames';
import {
  qryEnumInfoList,
  qryExtensionServiceRequestInfoByServiceId,
  operateRequestParam,
  qrySchemaDetail,
  qryBodyExample,
  batchDelete,
} from '@/services/entityService';
import AddRequestInformation from './AddRequestInformation';
import { CodeMirrorRender } from '@/components';
import { useModel } from 'umi';
import SchemaManagement from '../SchemaManagement';
import flatteningDomainSchemaList from '@/utils/flatteningDomainSchemaList';
import ResizableTable from '@/components/ResizableTable';
import { useTableFilter } from '@/hooks/useTableFilter';
import { ParamTypeSource, requestBodyFormat, RequiredSource } from '../const';
import ScriptManagement from '../ScriptManagement';
import myIcon from '@/static/iconfont/iconfont.css';
import useI18n from '@/hooks/useI8n';
import { SensitiveLeveType } from '../../../types';

const { Title } = Typography;

type ChildType = {
  reqParamName: any;
  paramType: any;
  required: any;
  avlValue: any;
  comments: any;
  refSchemaId: number;
  children?: ChildType[]; // 定义 children 为可选属性
  schemaName: any;
  reqKey: any;
};
interface RequestInformationProps {
  requestInformation: any;
  enumSource: any[];
  selectedService: any;
  domainSchemaList: any[];
  isProjectAndProductOwned: boolean;
  selectedRootNode: any; // 根节点
  isProduct: boolean;
  sensitiveLevelList: SensitiveLeveType[];
  updateDomainSchemaList: () => Promise<void>;
  queryRequestInfo: () => Promise<void>;
}

const RequestInformation = (props: RequestInformationProps) => {
  const {
    requestInformation,
    enumSource = [],
    selectedRootNode,
    isProduct = false,
    selectedService,
    domainSchemaList = [],
    updateDomainSchemaList,
    isProjectAndProductOwned = false,
    sensitiveLevelList = [],
    queryRequestInfo,
  } = props;
  const { formatMessage } = useI18n();
  const { currentDomainObjId } = useModel('manageExtensionServiceModel');
  const [spining, setSpining] = useState<boolean>(false);
  const [modalSpinning, setModalSpinning] = useState<boolean>(false);
  const [openAddRequestInformation, setOpenAddRequestInformation] = useState<boolean>(false);
  const [requestDataSource, setRequestDataSource] = useState<any>([]);
 
  const [selectedParamType, setSelectedParamType] = useState<string>('Query');
  const [operateType, setOperateType] = useState<string>('Add');
  const [selectedParam, setSelectedParam] = useState<any>({});
  const [selectedReqBodyType, setSelectedReqBodyType] = useState<string>('json');
  const [selectedSchema, setSelectedSchema] = useState<{ schemaId: number; schemaName: string }>();
  const {
    openSchemaManagementFromReq,
    setOpenSchemaManagementFromReq,
    openSchemaManagementFromResp,
    isImportBySwaggerSuccess,
    setIsImportBySwaggerSuccess,
  } = useModel('manageExtensionServiceModel');
  const [reqBodyExample, setReqBodyExample] = useState<any>({});
  const [rootSchema, setRootSchema] = useState<{ schemaId: number; schemaName: string; domainObjId: number }>();
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [pendingValue, setPendingValue] = useState<string>('');
  const [isExampleShow, setIsExampleShow] = useState<boolean>(false);
  const [openScriptManagement, setOpenScriptManagement] = useState<boolean>(false);

  // 自定义hooks，支持表格列筛选并分页展示
  const { filteredDataSource, getColumnSearchProps, getColumnEnumSearchProps, clearAllFilters } = useTableFilter(
    Array.isArray(requestDataSource) ? requestDataSource : [],
  );
  // 针对特殊表格列，自定义过滤逻辑
  const customTypeFilter = (item: any, obj: any) => {
    if (item) {
      const propertyType = item?.isArray === 'Y' ? `${item.paramType}[]` : item.paramType;
      return (propertyType || '')
        .toString()
        .toLowerCase()
        .includes((Object.values(obj)[1] as string).toLowerCase());
    }
    return true;
  };

  const customAvaValueFilter = (item: any, obj: any) => {
    if (enumSource) {
      let showData = '';
      if (item?.avlValue && item?.enumId) {
        showData = enumSource.find(
          (i: any) => `${i.value}` === `${item.enumId}` || i.label === `${item.enumId}`,
        )?.label;
      } else if (item?.typeFormat) {
        showData = `Format : ${item?.typeFormat}`;
      } else {
        showData = item.avlValue;
      }
      return (showData || '')
        .toString()
        .toLowerCase()
        .includes((Object.values(obj)[1] as string).toLowerCase());
    }
    return true; // 默认返回 true
  };

  // 请求参数类型切换
  const onTabsChange = (key: string) => {
    if (key !== 'Body') {
      setIsExampleShow(false);
    }
    if (key === 'BeforeCallServiceScript') {
      setOpenScriptManagement(true);
    } else {
      setOpenScriptManagement(false);
    }
    setSelectedParamType(key);
    // 重置表格列的筛选条件
    clearAllFilters();
  };

  

  // 查询入参/出参样例
  const queryReqBodyExample = async (schemaId: string) => {
    try {
      setSpining(true);
      const response = await qryBodyExample({ schemaId });

      if (response.success) {
        setReqBodyExample(response.data);
      } else {
        console.error('Failed to fetch request body example');
      }
    } catch (error) {
      console.error('An error occurred while fetching request body example:', error);
    } finally {
      setSpining(false);
    }
  };

  // 删除请求参数
  const deleteEntityPropConfirm: PopconfirmProps['onConfirm'] = async (e) => {
    setSpining(true);
    const param = {
      reqParamId: selectedParam?.reqParamId,
      domainObjId: currentDomainObjId,
    };
    try {
      const resultData = await operateRequestParam('delete', param);

      if (resultData?.success) {
        message.success(formatMessage('MANAGEEXTENSIONSERVICE.REQUEST.DELETE.SUCCESS'));
        // 删除后重新渲染数据
        queryRequestInfo();
        setReqBodyExample({});
      }
    } catch (error) {
      console.error(error);
      // message.success("Fail in deleting the entity property.");
    } finally {
      setSpining(false);
    }
  };

  // Body类型切换
  const handleBodyTypeChange = (value: any) => {
    setPendingValue(value);
    setIsModalVisible(true);
  };

  // 确认Body类型切换
  const handleModalOk = async () => {
    setModalSpinning(true);
    try {
      let reqIds = '';
      const reqIdList = requestInformation?.bodyList?.map((item: any) => item?.reqParamId);
      if (reqIdList.length > 0 && currentDomainObjId) {
        reqIds = reqIdList.join(',');
        const param = {
          reqIds,
          domainObjId: currentDomainObjId,
        };
        const resultData = await batchDelete(param);
        if (resultData?.success) {
          message.success(formatMessage('MANAGEEXTENSIONSERVICE.REQUEST.DELETE.SUCCESS'));
          // 删除后重新渲染数据
          queryRequestInfo();
          setIsExampleShow(false);
        }
      }
    } catch (error) {
      console.error(error);
    } finally {
      setModalSpinning(false);
      setSelectedReqBodyType(pendingValue);
      setIsModalVisible(false);
      setPendingValue('');
    }
  };

  // 取消Body类型切换
  const handleModalCancel = () => {
    setIsModalVisible(false);
    setPendingValue('');
  };

  // 递归设置children
  const mapPropListToChildren = async (propList: any[], rootKey: any): Promise<ChildType[]> => {
    const results = await Promise.all(
      propList.map(async (i: any) => {
        if (i?.hidden !== 'Y') {
          // 检查 hidden 属性
          const child: ChildType = {
            ...i,
            reqParamName: i?.name,
            paramType: i?.type,
            required: i?.required,
            avlValue: i?.avlValue,
            comments: i?.description,
            refSchemaId: i?.refSchemaId,
            schemaName: i?.schemaName,
            reqKey: `${rootKey}-${i?.name}`,
          };
          if (i.refSchemaInfo) {
            child.paramType = i.refSchemaInfo?.schemaId;
            child.schemaName = i.refSchemaInfo?.schemaName;
            if (i.refSchemaInfo.propList?.length > 0) {
              child.children = await mapPropListToChildren(i.refSchemaInfo.propList, child.reqKey);
            }
          }
          return child;
        }
        return undefined; // 显式返回 undefined
      }),
    );

    // 过滤掉 undefined 值
    return results.filter((child): child is ChildType => child !== undefined);
  };

  // param list表格
  const columns = [
    {
      dataIndex: 'reqParamName',
      title: formatMessage('PROJECT.COMMON.NAME'),
      width: '15%',
      ellipsis: true,
      ...(selectedParamType !== 'Body' || selectedReqBodyType !== 'json' ? getColumnSearchProps('reqParamName') : {}),
      render: (_: string, record: any) => {
        if (record?.hidden === 'Y') {
          return <span className={styles.hiddenParam}>{record?.reqParamName}</span>;
        }
        return record?.reqParamName;
      },
    },
    {
      dataIndex: 'paramType',
      title: formatMessage('PROJECT.COMMON.TYPE'),
      width: '15%',
      ellipsis: true,
      ...(selectedParamType !== 'Body' || selectedReqBodyType !== 'json'
        ? getColumnSearchProps('paramType', customTypeFilter)
        : {}),
      render: (_: string, record: any) => {
        if (record.paramType && !ParamTypeSource.includes(record.paramType)) {
          const allSchemaList = flatteningDomainSchemaList(domainSchemaList);
          const schemaObj = allSchemaList?.filter((i: any) => i?.schemaId === record?.refSchemaId)[0];
          return (
            <span
              className={styles.iconStyle}
              onClick={async () => {
                await updateDomainSchemaList();
                setSelectedSchema(schemaObj);
                setOpenSchemaManagementFromReq(true);
              }}
            >
              {record?.isArray === 'Y'
                ? `List<${record?.refSchemaName || record?.schemaName || schemaObj?.schemaName}>`
                : `${record?.refSchemaName || record?.schemaName || schemaObj?.schemaName}`}
            </span>
          );
        }
        return record?.isArray === 'Y' ? `${record.paramType}[]` : record.paramType;
      },
    },
    {
      dataIndex: 'required',
      title: formatMessage('PROJECT.COMMON.REQUIRED'),
      width: '10%',
      ellipsis: true,
      render: (_: string, record: any) => {
        if (record?.required) {
          const matchedItem = RequiredSource.find((i: { key: string; name: string }) => i.key === record.required);
          return matchedItem ? matchedItem.name : '';
        }
      },
      ...(selectedParamType !== 'Body' || selectedReqBodyType !== 'json'
        ? getColumnEnumSearchProps('required', RequiredSource)
        : {}),
    },
    {
      dataIndex: 'avlValue',
      title: formatMessage('MANAGEEXTENSIONSERVICE.REQUEST.PARAM.ALLOWABLE_VALUES'),
      width: '15%',
      ellipsis: true,
      ...(selectedParamType !== 'Body' || selectedReqBodyType !== 'json'
        ? getColumnSearchProps('avlValue', customAvaValueFilter)
        : {}),
      render: (_: string, record: any) => {
        if (record.avlValue && record.enumId) {
          return enumSource.find((i: any) => `${i.value}` === `${record.enumId}` || i.label === `${record.enumId}`)
            ?.label;
        }
        if (record?.typeFormat) {
          return `Format : ${record?.typeFormat}`;
        }
        return record.avlValue;
      },
    },
    {
      dataIndex: 'example',
      title: formatMessage('PROJECT.COMMON.EXAMPLE'),
      width: '10%',
      ellipsis: true,
      ...(selectedParamType !== 'Body' || selectedReqBodyType !== 'json' ? getColumnSearchProps('example') : {}),
      render: (value: any) => {
        if (typeof value === 'string') {
          return value;
        }
        return '';
      },
    },
    {
      dataIndex: 'comments',
      title: formatMessage('PROJECT.COMMON.DESCRIPTION'),
      width: '25%',
      ellipsis: true,
      ...(selectedParamType !== 'Body' || selectedReqBodyType !== 'json' ? getColumnSearchProps('comments') : {}),
    },
    {
      dataIndex: '',
      title: formatMessage('PROJECT.COMMON.OPERATION'),
      width: '9%',
      render: (_: string, record: any) => {
        // 如果是Schema类型，Schema里面的参数是递归添加的，没有reqParamId，则此处不允许修改和删除
        if (record?.reqParamId) {
          return (
            <div>
              <Tooltip title={formatMessage('PROJECT.COMMON.EDIT')}>
                <FormOutlined
                  className={classNames(styles.iconStyle, {
                    [styles.hide]: selectedRootNode?.state !== 'D',
                  })}
                  onClick={() => {
                    setOperateType('Edit');
                    setOpenAddRequestInformation(true);
                  }}
                />
              </Tooltip>
              {selectedParamType !== 'Path' && (
                <Popconfirm
                  title={formatMessage('MANAGEEXTENSIONSERVICE.REQUEST.DELETE.TITLE')}
                  description={formatMessage('MANAGEEXTENSIONSERVICE.REQUEST.DELETE.DESCRIPTION')}
                  onConfirm={deleteEntityPropConfirm}
                  okText={formatMessage('PROJECT.COMMON.YES')}
                  cancelText={formatMessage('PROJECT.COMMON.NO')}
                >
                  {isProjectAndProductOwned ? (
                    <></>
                  ) : (
                    <Tooltip title={formatMessage('PROJECT.COMMON.DELETE')}>
                      <DeleteOutlined
                        className={classNames(styles.versionIcon2, {
                          [styles.hide]: selectedRootNode?.state !== 'D',
                        })}
                        onClick={() => {}}
                      />
                    </Tooltip>
                  )}
                </Popconfirm>
              )}
            </div>
          );
        }
      },
    },
  ];

  // 渲染表格
  const renderRequestInformation = (type: string) => {
    return (
      <div>
        <Space>
          {type !== 'Body' ? (
            <span>
              {type} {formatMessage('MANAGEEXTENSIONSERVICE.REQUEST.PARAMETERS')}
            </span>
          ) : (
            <span>Content Type</span>
          )}
          {type === 'Body' && (
            <div>
              <Select
                disabled={isProjectAndProductOwned}
                style={{ width: 120 }}
                showSearch
                options={requestBodyFormat}
                value={selectedReqBodyType}
                onChange={handleBodyTypeChange}
              />
              <Modal
                title={formatMessage('MANAGEEXTENSIONSERVICE.REQUEST.BODY.CHANGE.TITLE')}
                open={isModalVisible}
                onCancel={handleModalCancel}
                footer={
                  <Flex gap="1rem" justify="end">
                    <Button onClick={handleModalCancel}>{formatMessage('PROJECT.COMMON.CANCEL')}</Button>
                    <Spin spinning={modalSpinning}>
                      <Button onClick={handleModalOk} type="primary">
                        {formatMessage('PROJECT.COMMON.CONFIRM')}
                      </Button>
                    </Spin>
                  </Flex>
                }
              >
                <p>{formatMessage('MANAGEEXTENSIONSERVICE.REQUEST.BODY.CHANGE.DESCRIPTION')}</p>
              </Modal>
            </div>
          )}
          {!isProjectAndProductOwned && type !== 'Path' && (type !== 'Body' || selectedReqBodyType !== 'json') && (
            <span
              className={classNames(styles.iconStyle, {
                [styles.hide]: selectedRootNode?.state !== 'D',
              })}
              onClick={() => {
                setOperateType('Add');
                setSelectedParam({});
                setOpenAddRequestInformation(true);
              }}
            >
              + {formatMessage('PROJECT.COMMON.ADD')}
            </span>
          )}
        </Space>
        {(type !== 'Body' || selectedReqBodyType !== 'json') && (
          <div>
            <ResizableTable
              size="small"
              style={{ marginTop: '12px' }}
              columns={columns}
              dataSource={filteredDataSource}
              rowKey={(record: any) => record?.propId}
              pagination={false}
              onRow={(record: any) => ({
                onClick: () => {
                  setSelectedParam(record);
                },
              })}
            />
          </div>
        )}
        {type === 'Body' && selectedReqBodyType === 'json' && (
          <div>
            <ResizableTable
              size="small"
              style={{ marginTop: '12px' }}
              columns={columns.filter((item: any) => item?.dataIndex !== 'example')}
              dataSource={requestDataSource}
              rowKey={(record: any) => record?.reqKey}
              pagination={false}
              onRow={(record: any) => ({
                onClick: () => {
                  setSelectedParam(record);
                },
              })}
            />
            {!isProjectAndProductOwned && !requestDataSource?.length && (
              <div>
                <Button
                  className={classNames({
                    [styles.hide]: selectedRootNode?.state !== 'D',
                  })}
                  style={{
                    width: '100%',
                    color: '#21f17f',
                    cursor: 'pointer',
                    border: '1px solid #6ce0a0',
                    backgroundColor: '#e1faea',
                  }}
                  onClick={() => {
                    setOperateType('Add');
                    setSelectedParam({});
                    setOpenAddRequestInformation(true);
                  }}
                >
                  + {formatMessage('PROJECT.COMMON.ADD')}
                </Button>
              </div>
            )}
          </div>
        )}
      </div>
    );
  };

  const items: TabsProps['items'] = [
    {
      key: 'Query',
      label: 'Query',
      children: renderRequestInformation('Query'),
    },
    ...(requestInformation?.pathList && requestInformation.pathList.length > 0
      ? [
          {
            key: 'Path',
            label: 'Path',
            children: renderRequestInformation('Path'),
          },
        ]
      : []),
    ...(selectedService?.httpMethod && selectedService?.httpMethod !== 'GET'
      ? [
          {
            key: 'Body',
            label: 'Body',
            children: renderRequestInformation('Body'),
          },
        ]
      : []),
    {
      key: 'Header',
      label: 'Header',
      children: renderRequestInformation('Header'),
    },
    {
      key: 'Cookie',
      label: 'Cookie',
      children: renderRequestInformation('Cookie'),
    },
    {
      key: 'BeforeCallServiceScript',
      label: (
        <Flex align="center" gap={5}>
          <span className={`${myIcon.iconfont} ${myIcon['icon-script']}`} />
          <span>{formatMessage('MANAGEEXTENSIONSERVICE.REQUEST.BEFORE_CALL_SERVICE_SCRIPT')}</span>
        </Flex>
      ),
      children: (
        <ScriptManagement
          selectedRootNode={selectedRootNode}
          type="req"
          selectedService={selectedService}
          open={openScriptManagement}
        />
      ),
    },
  ];

  useEffect(() => {
    if (isImportBySwaggerSuccess) {
      queryRequestInfo();
      setIsImportBySwaggerSuccess(false);
    }
  }, [isImportBySwaggerSuccess]);

  useEffect(() => {
    if (rootSchema?.schemaId) {
      queryReqBodyExample(rootSchema?.schemaId.toString());
    }
  }, [rootSchema]);

  useEffect(() => {
    // 进入扩展服务详情管理界面或关闭Schema管理界面时，查询请求信息
    if (!openSchemaManagementFromResp && !openSchemaManagementFromReq && selectedService?.serviceId) {
      queryRequestInfo();
    }
  }, [openSchemaManagementFromResp, openSchemaManagementFromReq, selectedService]);

  useEffect(() => {
    // 更新展示的请求参数数据，其中body类型的参数可能为Schema，Schema的属性可能为Schema，故递归构造树形数据
    const updateDataSource = async () => {
      if (selectedParamType === 'Header') {
        setRequestDataSource(requestInformation?.headerList || []);
      } else if (selectedParamType === 'Path') {
        setRequestDataSource(requestInformation?.pathList || []);
      } else if (selectedParamType === 'Query') {
        setRequestDataSource(requestInformation?.queryList || []);
      } else if (selectedParamType === 'Cookie') {
        setRequestDataSource(requestInformation?.cookieList || []);
      } else if (selectedParamType === 'Body') {
        if (requestInformation?.bodyList?.length > 0) {
          setSelectedReqBodyType(requestInformation?.bodyList[0]?.bodyContentType);
          const updatedBodyList = await Promise.all(
            requestInformation?.bodyList
              .filter((i: any) => i?.bodyContentType === 'json')
              .map(async (item: any) => {
                if (item?.refSchemaId) {
                  setIsExampleShow(true);
                  const resultData = await qrySchemaDetail({ schemaId: item?.refSchemaId });
                  if (resultData?.success) {
                    setRootSchema({
                      schemaId: item?.refSchemaId,
                      schemaName: item?.paramType,
                      domainObjId: resultData.data?.domainObjId,
                    });
                    if (resultData.data?.propList?.length > 0) {
                      item.children = await mapPropListToChildren(resultData.data?.propList, item?.reqParamName);
                    }
                  }
                } else {
                  setIsExampleShow(false);
                }
                return { ...item, reqKey: item?.reqParamName };
              }),
          );
          setRequestDataSource(updatedBodyList);
        }
        setRequestDataSource(requestInformation?.bodyList || []);
      }
    };

    updateDataSource();
  }, [selectedParamType, requestInformation]);

  return (
    <Spin spinning={spining}>
      <div>
        <OperationIconTitle title={formatMessage('MANAGEEXTENSIONSERVICE.REQUEST.INFORMATION')} />
        <div>
          <Tabs defaultActiveKey="HEADER" items={items} onChange={onTabsChange} />
        </div>
      </div>
      {isExampleShow && (
        <div style={{ margin: '10px 0' }}>
          <Title level={5}>{formatMessage('PROJECT.COMMON.EXAMPLE')}</Title>
          <CodeMirrorRender value={reqBodyExample} />
        </div>
      )}
      {/* 新增或修改参数信息 */}
      <AddRequestInformation
        domainSchemaList={domainSchemaList}
        isProjectAndProductOwned={isProjectAndProductOwned}
        updateDomainSchemaList={updateDomainSchemaList}
        selectedRootNode={selectedRootNode}
        selectedReqBodyType={selectedReqBodyType}
        operateType={operateType}
        selectedParam={selectedParam}
        selectedParamType={selectedParamType}
        open={openAddRequestInformation}
        enumSource={enumSource}
        selectedService={selectedService}
        onCancel={() => setOpenAddRequestInformation(false)}
        onOk={() => {
          // 新增实体属性成功后，刷新当前表格
          setOpenAddRequestInformation(false);
          queryRequestInfo();
        }}
      />

      <SchemaManagement
        sensitiveLevelList={sensitiveLevelList}
        enumSource={enumSource}
        currentSchema={selectedSchema}
        isProduct={isProduct}
        selectedRootNode={selectedRootNode}
        domainSchemaList={domainSchemaList}
        open={openSchemaManagementFromReq}
        onCancel={() => setOpenSchemaManagementFromReq(false)}
        updateDomainSchemaList={updateDomainSchemaList}
      />
    </Spin>
  );
};

export default RequestInformation;
