import OperationIconTitle from '@/components/OperationIconTitle';
import { Flex, Pagination, Switch, Tooltip } from 'antd';
import React, { useEffect, useRef, useState } from 'react';
import styles from './index.less';
import useI18n from '@/hooks/useI8n';
import ResizableTable from '@/components/ResizableTable';
import { FileTextOutlined } from '@ant-design/icons';

import { useModel } from 'umi';
import { getRecordErrorStatus, toggleRecordErrorStatus } from '@/services/errorCodeService';
import ErrorRespInfoDrawer from './ErrorRespInfoDrawer';
import { OtherErrorListItemType } from '../types';
import moment from 'moment';

interface IOtherErrorList {
  openFromBssODataApi?: boolean;
}

const OtherErrorList: React.FC<IOtherErrorList> = (props) => {
  const { formatMessage } = useI18n();
  const {
    queryOtherErrorListParams,
    setQueryOtherErrorListParams,
    queryOtherErrorList,
    otherErrorListData,
    setOtherErrorSpinning,
  } = useModel('useErrorCodeModel');
  const [selectedOtherError, setSelectedOtherError] = useState<OtherErrorListItemType>();
  const [errorRespInfoOpen, setErrorRespInfoOpen] = useState<boolean>(false);
  const [isChecked, setIsChecked] = useState<boolean>(false);
  const [expiredTime, setExpiredTime] = useState<string>(''); // 后台获取的失效时间
  const timerRef = useRef<NodeJS.Timeout>(); // 定时器引用

  // 获取开关状态
  const getRecordErrorState = async () => {
    setOtherErrorSpinning(true);
    try {
      const { data, success } = await getRecordErrorStatus();
      if (success) {
        setIsChecked(data?.status);
        if (data?.status) {
          if (data.expireDate !== null && data.expireDate !== '') {
            setExpiredTime(moment(data.expireDate).format('YYYY-MM-DD HH:mm:ss'));
          }
        } else {
          setExpiredTime('');
        }
      }
    } catch {
      console.error('Get record error status failed!');
    } finally {
      setOtherErrorSpinning(false);
    }
  };

  // 修改开关状态
  const onSwitchChange = async (checked: boolean) => {
    setOtherErrorSpinning(true);
    try {
      const { success } = await toggleRecordErrorStatus({ status: checked });
      if (success) {
        await getRecordErrorState();
      }
    } catch {
      console.error('Toggle record error status failed!');
    } finally {
      setOtherErrorSpinning(false);
    }
  };

  const columns = [
    {
      dataIndex: 'traceId',
      title: formatMessage('ERRORCODE.OTHERERRORDETAIL.TRACEID'),
      width: '25%',
      ellipsis: true,
    },
    {
      dataIndex: 'serviceName',
      title: formatMessage('ERRORCODE.OTHERERRORDETAIL.SERVICENAME'),
      width: '15%',
      ellipsis: true,
    },
    {
      dataIndex: 'servicePath',
      title: formatMessage('ERRORCODE.OTHERERRORDETAIL.SERVICEPATH'),
      width: '25%',
      ellipsis: true,
      render: (_: string, record: any) => {
        const { httpMethod, servicePath } = record;
        const newServicePath = servicePath; // TODO 可能需要拼接，等待后续需求
        switch (httpMethod) {
          case 'GET':
            return (
              <div className={styles.dyStyleWrapper} title={`${httpMethod} ${newServicePath}`}>
                <span className={styles.dyStyleGET}>{httpMethod}</span>
                <span>{newServicePath}</span>
              </div>
            );
          case 'POST':
            return (
              <div className={styles.dyStyleWrapper} title={`${httpMethod} ${newServicePath}`}>
                <span className={styles.dyStylePOST}>{httpMethod}</span>
                <span>{newServicePath}</span>
              </div>
            );
          case 'PUT':
            return (
              <div className={styles.dyStyleWrapper} title={`${httpMethod} ${newServicePath}`}>
                <span className={styles.dyStyleGET}>{httpMethod}</span>
                <span>{newServicePath}</span>
              </div>
            );
          case 'PATCH':
            return (
              <div className={styles.dyStyleWrapper} title={`${httpMethod} ${newServicePath}`}>
                <span className={styles.dyStylePATCH}>{httpMethod}</span>
                <span>{newServicePath}</span>
              </div>
            );
          case 'DELETE':
            return (
              <div className={styles.dyStyleWrapper} title={`${httpMethod} ${newServicePath}`}>
                <span className={styles.dyStyleDELETE}>{httpMethod}</span>
                <span>{newServicePath}</span>
              </div>
            );

          default:
            return (
              <div className={styles.dyStyleWrapper} title={`${httpMethod} ${newServicePath}`}>
                <span>{httpMethod}</span>
                <span>{newServicePath}</span>
              </div>
            );
        }
      },
    },
    {
      dataIndex: 'releasedSeq',
      title: formatMessage('ERRORCODE.OTHERERRORDETAIL.RELEASEDVERSION'),
      width: '10%',
      ellipsis: true,
    },
    {
      dataIndex: 'happenedDate',
      title: formatMessage('ERRORCODE.OTHERERRORDETAIL.HAPPENEDDATE'),
      width: '15%',
      ellipsis: true,
      render: (value: string, record: any) => {
        if (value) {
          return moment(value).format('YYYY-MM-DD HH:mm:ss');
        }
      },
    },
    {
      dataIndex: '',
      title: formatMessage('PROJECT.COMMON.OPERATION'),
      width: '10%',
      render: (_: string, record: any) => {
        return (
          <div>
            {/* 查看错误响应信息 */}
            <Tooltip title={formatMessage('ERRORCODE.OTHERERRORDETAIL.ERRORRESPONSEINFOMATION')}>
              <span
                onClick={() => {
                  setErrorRespInfoOpen(true);
                }}
                className={`${styles.versionIcon2}`}
              >
                <FileTextOutlined />
              </span>
            </Tooltip>
          </div>
        );
      },
    },
  ];

  useEffect(() => {
    const checkExpiration = () => {
      if (expiredTime && isChecked) {
        const expirationDate = new Date(expiredTime).getTime() + 500; // 延时500毫秒，确保所有错误都被记录
        const currentDate = Date.now();
        const timeDiff = expirationDate - currentDate;
        if (timeDiff <= 0) {
          // 立即关闭开关
          onSwitchChange(false);
        } else {
          // 设置定时器
          timerRef.current = setTimeout(() => {
            onSwitchChange(false);
          }, timeDiff);
        }
      }
    };

    checkExpiration(); // 立即执行一次检查

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [expiredTime, isChecked]); // 依赖项包含过期时间和开关状态
  useEffect(() => {
    getRecordErrorState();
  }, []);

  useEffect(() => {
    if (otherErrorListData?.totalCount && otherErrorListData?.list?.length === 0) {
      setQueryOtherErrorListParams((pre) => ({
        ...pre,
        pageNo: 1,
      }));
      queryOtherErrorList({
        ...queryOtherErrorListParams,
        pageNo: 1,
      });
    }
  }, [otherErrorListData]); // 处理特殊场景：接口返回的totalCount不为0但list为空。例如添加了过滤条件，还未点击查询按钮，直接跳转到该过滤条件下不存在的页码。

  return (
    <>
      <div>
        <Flex align="center" gap={50}>
          <OperationIconTitle title={formatMessage('ERRORCODE.OTHERERRORDETAIL.LIST.TITLE')} />
          <Flex align="center" gap={10}>
            <Switch checked={isChecked} onChange={(checked) => onSwitchChange(checked)} />
            <span className={styles.record}>
              {formatMessage('ERRORCODE.OTHERERRORDETAIL.RECORD')}
              {isChecked && formatMessage('ERRORCODE.OTHERERRORDETAIL.RECORD.EXPIRED_TIME', { expiredTime })}
            </span>
          </Flex>
        </Flex>
        <ResizableTable
          size="small"
          style={{ marginTop: '12px' }}
          columns={columns}
          dataSource={otherErrorListData.list || []}
          rowKey={(record: any) => record?.errLogIdStr}
          pagination={false}
          // 使用Table的pagination属性，dataSource为空时默认不展示分页，故使用footer
          footer={() => (
            <Flex justify="flex-end">
              <Pagination
                total={otherErrorListData?.totalCount}
                current={queryOtherErrorListParams?.pageNo}
                pageSize={queryOtherErrorListParams?.pageSize}
                pageSizeOptions={[5, 10, 15, 20]}
                showQuickJumper
                showSizeChanger
                hideOnSinglePage={false}
                showTotal={(total) => formatMessage('PROJECT.COMMON.PAGINATION.TOTAL', { total })}
                onChange={(page, pageSize) => {
                  setQueryOtherErrorListParams((pre) => ({
                    ...pre,
                    pageSize,
                    pageNo: page,
                  }));
                  queryOtherErrorList({
                    ...queryOtherErrorListParams,
                    pageSize,
                    pageNo: page,
                  });
                }}
              />
            </Flex>
          )}
          onRow={(record: any) => ({
            onClick: () => {
              setSelectedOtherError(record);
            },
          })}
        />
      </div>
      <ErrorRespInfoDrawer
        open={errorRespInfoOpen}
        setOpen={setErrorRespInfoOpen}
        selectedOtherError={selectedOtherError as OtherErrorListItemType}
      />
    </>
  );
};
export default OtherErrorList;
