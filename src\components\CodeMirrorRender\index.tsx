import React, { useEffect, useState, useContext } from 'react';
import CodeMirror from '@uiw/react-codemirror';
import UpdataRequestParamsContext from '@/context/UpdataRequestParamsContext';
import { json } from '@codemirror/lang-json';
import { html } from '@codemirror/lang-html';
import { javascript } from '@codemirror/lang-javascript';
import { foldGutter } from '@codemirror/language';
import { EditorView } from '@codemirror/view';
import { espresso } from 'thememirror';
import { closeBrackets } from '@codemirror/autocomplete';
import { keymap } from '@codemirror/view';
import { defaultKeymap } from '@codemirror/commands';

interface ICodeMirroeRender {
  value?: string;
  debugMode?: boolean;
  isHtmlReject?: boolean;
}

const CodeMirrorRender: React.FC<ICodeMirroeRender> = (props) => {
  const { value: codeValue, debugMode = false, isHtmlReject = false } = props;

  const UpdataContext = useContext(UpdataRequestParamsContext);

  //  转换codemirror格式方法
  const formatFn = (activeCodeParam: any) => {
    try {
      return JSON.stringify(JSON.parse(activeCodeParam), null, 2);
    } catch (error) {
      return JSON.stringify(activeCodeParam, null, 2);
    }
  };

  const [codemirrorValue, setCodemirrorValue] = useState('{}');

  useEffect(() => {
    setCodemirrorValue(formatFn(codeValue) || '{}');
    return () => {
      setCodemirrorValue(formatFn(codeValue) || '{}');
    };
  }, [codeValue, debugMode, isHtmlReject]);

  useEffect(() => {
    if (debugMode) {
      if (!(UpdataContext.DebugBackFillingInfo?.[0]?.paramValue === codeValue)) {
        if (UpdataContext.DebugBackFillingInfo?.[0]?.paramValue) {
          setCodemirrorValue(formatFn(UpdataContext.DebugBackFillingInfo?.[0]?.paramValue) || '{}');
        }
      }
    }
  }, [UpdataContext, debugMode, codeValue, isHtmlReject]);

  const handleOnChnage = (value: string) => {
    if (debugMode && UpdataContext?.updataRequestParamsFn) {
      UpdataContext.updataRequestParamsFn({
        BM: value,
      });
    }
  };

  // CodeMirror从5升级到6，只有当用户实际修改内容时才会触发 onChange 事件，故在组件挂载时手动触发一次 handleOnChnage
  useEffect(() => {
    // 模拟初始化时的 onChange 事件
    if (codeValue && debugMode) {
      handleOnChnage(formatFn(codeValue) || '{}');
    }
  }, []);

  // 选择适当的语言扩展
  const languageExtension = isHtmlReject ? [html()] : [javascript({ jsx: false })];

  return (
    <CodeMirror
      value={codemirrorValue}
      height="auto"
      theme={espresso}
      onChange={handleOnChnage}
      extensions={[
        foldGutter(),
        ...languageExtension,
        closeBrackets(),
        EditorView.lineWrapping,
        keymap.of(defaultKeymap),
      ]}
      readOnly={!debugMode}
      basicSetup={{
        foldGutter: true,
        lineNumbers: true,
        highlightActiveLine: false,
        autocompletion: true,
        indentOnInput: true,
        bracketMatching: true,
      }}
    />
  );
};

export default CodeMirrorRender;
