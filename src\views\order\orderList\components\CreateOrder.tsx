import { Col, DatePicker, Form, Input, Modal, Row, Select } from "antd";
import { useEffect, useImperativeHandle, useState } from "react";
import api from "../../../../api";
import { message } from "../../../../utils/AntdGlobal";

export default function CreateOrder(props: any) {
    const [form] = Form.useForm()
    const [visible, setVisible] = useState(false)
    const [cityList, setCityList] = useState<any>([])
    const [vehicleList, setVehicleList] = useState<any>([])

    const payType = [
        { label: '微信', id: 1, value: 1 },
        { label: '支付宝', id: 2, value: 2 }
    ]

    const state = [
        { label: '进行中', id: 1, value: 1 },
        { label: '已完成', id: 2, value: 2 },
        { label: '超时', id: 3, value: 3 },
        { label: '取消', id: 4, value: 4 }
    ]


    useImperativeHandle(props.ref, () => {
        return {
            open
        }
    })

    const open: any = () => {
        setVisible(true)
    }

    const handleSubmit = async () => {
        const valid = await form.validateFields()
        if (valid) {
            await api.createOrder(form.getFieldsValue())
            message.success('创建成功')
            handleCancel()
            props.update()
        }
    }

    const handleCancel = () => {
        form.resetFields()
        setVisible(false)
    }

    const getInitData = async () => {
        const cityList = await api.getCityList()
        const newCityList = cityList.map((item: any) => {
            return {
                label: item.name,
                value: item.name,
                key: item.id
            }
        })
        setCityList(newCityList)
        const vehicleList = await api.getVehicleList()
        const newVehicleList = vehicleList.map((item: any) => {
            return {
                label: item.name,
                value: item.name,
                key: item.id
            }
        })
        setVehicleList(newVehicleList)
    }

    useEffect(() => {
        getInitData()
    }, [])

    return (
        <>
            <Modal
                title="创建订单"
                width={800}
                open={visible}
                okText="确定"
                cancelText="取消"
                onOk={handleSubmit}
                onCancel={handleCancel}
            >
                <Form
                    form={form}
                    layout="horizontal"
                    labelAlign="right"
                    labelCol={{ span: 8 }}
                    wrapperCol={{ span: 16 }}
                >
                    <Row>
                        <Col span={12}>
                            <Form.Item name='cityName' label='城市名称' rules={[{ required: true }]}>
                                <Select
                                    placeholder='请选择城市名称'
                                    options={cityList}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item name='vehicleName' label='车型名称' rules={[{ required: true }]}>
                                <Select
                                    placeholder='请选择车型名称'
                                    options={vehicleList}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row>
                        <Col span={12}>
                            <Form.Item name='userName' label='用户' rules={[{ required: true }]}>
                                <Input placeholder='请输入用户名称' />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item name='mobile' label='手机号'>
                                <Input placeholder='请输入手机号' />

                            </Form.Item>
                        </Col>
                    </Row>
                    <Row>
                        <Col span={12}>
                            <Form.Item name='startAddress' label='起始地址' required>
                                <Input placeholder='请输入起始地址' />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item name='endAddress' label='结束地址' required>
                                <Input placeholder='请输入结束地址' />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row>
                        <Col span={12}>
                            <Form.Item name='orderAmount' label='下单金额' rules={[{ required: true }]}>
                                <Input type="number" placeholder='请输入下单金额' />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item name='userPayAmount' label='支付金额' rules={[{ required: true }]}>
                                <Input type="number" placeholder='请输入支付金额' />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row>
                        <Col span={12}>
                            <Form.Item name='driverName' label='司机名称' rules={[{ required: true }]}>
                                <Input placeholder='请输入司机名称' />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item name='driverAmount' label='司机金额' rules={[{ required: true }]}>
                                <Input type="number" placeholder='请输入司机金额' />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row>
                        <Col span={12}>
                            <Form.Item name='payType' label='支付方式'>
                                <Select
                                    placeholder='请选择支付方式'
                                    options={payType}
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item name='state' label='订单状态' required>
                                <Select
                                    placeholder='请选择订单状态'
                                    options={state}
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                    <Row>
                        <Col span={12}>
                            <Form.Item name='useTime' label='下单时间'>
                                <DatePicker
                                />
                            </Form.Item>
                        </Col>
                        <Col span={12}>
                            <Form.Item name='endTime' label='结束时间'>
                                <DatePicker
                                />
                            </Form.Item>
                        </Col>
                    </Row>
                </Form>
            </Modal>
        </>
    )
}