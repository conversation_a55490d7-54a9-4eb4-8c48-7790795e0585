import ResizableTable from '@/components/ResizableTable';
import { useEffect, useState } from 'react';
import styles from './index.less';
import EditParamDrawer from '../EditParamDrawer';
import getUUid from '@/utils/getUUid';
import useI18n from '@/hooks/useI8n';

interface IAPIGParamsData {
  APIGParamDataFromOData: any;
  APIGParamDataFromUrl: any;
  dataFromApi?: any;
  isEditAPIGService: boolean;
  isGenerateFromUrl?: boolean;
  getAPIGParamList: (data: any) => void;
  setAPIGParamDataFromOData: (data: any) => void;
}

const APIGParamsTable: React.FC<IAPIGParamsData> = (props) => {
  const {
    APIGParamDataFromOData,
    APIGParamDataFromUrl,
    dataFromApi = [],
    isEditAPIGService = false,
    isGenerateFromUrl = false,
    getAPIGParamList,
    setAPIGParamDataFromOData,
  } = props;
  const { formatMessage } = useI18n();
  const [dataSource, setDataSource] = useState<any>([]);
  const [openEditParamDrawer, setOpenEditParamDrawer] = useState<boolean>(false);
  const [selectedParam, setSelectedParam] = useState<any>();

  const editParamData = (data: any) => {
    setDataSource((prevData: any) => {
      return prevData.map((item: any) => (item?.key === data?.key ? data : item));
    });
    setOpenEditParamDrawer(false);
  };

  const columns = [
    {
      dataIndex: 'paramName',
      title: formatMessage('APIG.PARAM.NAME'),
      width: '20%',
      ellipsis: true,
      render: (_: any, record: any) => {
        return (
          <span
            className={styles.nameStyle}
            onClick={() => {
              setSelectedParam(record);
              setOpenEditParamDrawer(true);
            }}
          >
            {record?.paramName}
          </span>
        );
      },
    },
    {
      dataIndex: 'paramType',
      title: formatMessage('APIG.PARAM.TYPE'),
      width: '15%',
      ellipsis: true,
    },
    {
      dataIndex: 'required',
      title: formatMessage('APIG.PARAM.REQUIRED'),
      width: '15%',
      ellipsis: true,
      render: (_: string, record: any) => {
        return <span>{record?.required === 'Y' ? 'Yes' : 'No'}</span>;
      },
    },
    {
      dataIndex: 'avlValue',
      title: formatMessage('APIG.PARAM.ALLOWABLE_VALUES'),
      width: '25%',
      ellipsis: true,
      render: (_: string, record: any) => {
        if (record?.typeFormat) {
          return `Format : ${record?.typeFormat}`;
        }
        return record.avlValue;
      },
    },
    {
      dataIndex: 'comments',
      title: formatMessage('APIG.PARAM.DESCRIPTIONS'),
      width: '25%',
      ellipsis: true,
    },
  ];

  useEffect(() => {
    // 新增APIG服务时，isGenerateFromUrl为true
    // 修改APIG服务时，isGenerateFromUrl默认为false，如果url有变化，则isGenerateFromUrl为true
    if (isGenerateFromUrl) {
      setDataSource([...APIGParamDataFromOData, ...APIGParamDataFromUrl]);
    } else {
      const pathDataFromApi = dataFromApi.filter((item: any) => item?.reqIn === 'P');
      setDataSource([...APIGParamDataFromOData, ...pathDataFromApi]);
    }
  }, [APIGParamDataFromOData, APIGParamDataFromUrl, isGenerateFromUrl]);

  useEffect(() => {
    if (isEditAPIGService && dataFromApi) {
      const otherDataFromApi = dataFromApi.filter((item: any) => item?.reqIn !== 'P');
      setAPIGParamDataFromOData(otherDataFromApi); // 初始化APIGParamDataFromOData，保证修改url时不会重置其他参数
      setDataSource(dataFromApi);
    }
  }, [dataFromApi]);

  useEffect(() => {
    getAPIGParamList(dataSource);
  }, [dataSource]);

  return (
    <>
      <ResizableTable
        size="small"
        columns={columns}
        dataSource={dataSource}
        pagination={false}
        rowKey={(record: any) => record?.key}
      />
      <EditParamDrawer
        selectedParam={selectedParam}
        open={openEditParamDrawer}
        onOk={editParamData}
        onCancel={() => {
          setOpenEditParamDrawer(false);
        }}
      />
    </>
  );
};
export default APIGParamsTable;
