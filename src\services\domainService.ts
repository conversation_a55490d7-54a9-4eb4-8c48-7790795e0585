import { request, useRequest } from "umi";
import { stringify } from "qs";

//获取环境基本信息
export async function getSystemConfigInfo() {
  return request(`/odh-web/odh/web/common/info`, {
    method: "GET",
  });
}

// 查询领域信息树形结构
export async function queryODomainTreeData() {
  return request(`/odh-web/odh/web/domain/mgmt/tree`, {
    method: "GET",
  });
}

//新增领域对象
export async function addDomainObject(data: {
  domainObjCode: string;
  domainObjName: string;
  domainId: number;
  creationSrc: string;
  ignoreVer?: string;
  comments?: string;
}) {
  return request("/odh-web/odh/web/domain/mgmt/obj", {
    method: "POST",
    data,
    headers: {
      cookie: window?.top?.portal?.appGlobal?.attributes || {}
    }
  });
}

//修改领域对象
export async function editDomainObject(domainObjId: number, data: {
  domainObjCode: string;
  domainObjName: string;
  creationSrc: string;
  ignoreVer?: string;
  comments?: string;
}) {
  return request(`/odh-web/odh/web/domain/mgmt/obj/${domainObjId}`, {
    method: "PATCH",
    data,
    headers: {
      cookie: window?.top?.portal?.appGlobal?.attributes || {}
    }
  });
}

// 删除领域对象
export async function deleteDomainObject(domainObjId: number) {
  return request(`/odh-web/odh/web/domain/mgmt/obj/${domainObjId}`, {
    method: "DELETE",
  });
}


//查询领域发布信息
export async function qryReleasedInfoByDomainId(params: { domainId: number }) {
  return request(`/odh-web/odh/web/domain/released/qryReleasedInfo?${stringify(params)}`, {
    method: "GET",
  }
  );
}

//分页查询版本列表数据
export async function qryDomainReleasedList(params: {
  pageNo: number;
  pageSize: number;
  domainId: number;
}) {
  return request(`/odh-web/odh/web/domain/released/qryDomainReleasedList?${stringify(params)}`,
    {
      method: "GET",
    }
  );
}

//修改领域状态从R到D
export async function changeDomainStateToD(data: {
  domainId: number;
}) {
  return request("/odh-web/odh/web/domain/released/draftDomain", {
    method: "POST",
    data,
    headers: {
      cookie: window?.top?.portal?.appGlobal?.attributes || {}
    }
  }
  );
}

//发布领域版本
export async function releasedVersion(params: {
  domainId: number;
  comments?: string;
  autoSwitch:string;
}) {
  return request(`/odh-web/odh/web/domain/released/odhDomainReleasedNew?${stringify(params)}`, {
    method: "GET",
  }
  );
}

//查询领域对象详情
export async function queryODomainObjDetail(domainObjId: number) {
  return request(`/odh-web/odh/web/domain/mgmt/obj/${domainObjId}`, {
    method: "GET",
  });
}


//分页查询领域下的实体列表
export async function qryEntitiesByDomainObjId(domainObjId: number, params: {
  pageNo: number;
  pageSize: number;
}) {
  return request(`/odh-web/odh/web/domain/entities/${domainObjId}?${stringify(params)}`,
    {
      method: "GET",
    }
  );
}

//查询领域对象
export async function queryODomainObj(domainId: number) {
  return request(`/odh-web/odh/web/domain/mgmt/objs/${domainId}`, {
    method: "GET",
  });
}

//查询领域实体详情
export async function queryODomainEntityDetail(entityId: string) {
  return request(`/odh-web/odh/web/domain/entity/${entityId}`, {
    method: "GET",
  });
}

// 查询entity detail
export async function queryEntityDetailByEntityKey(params: {
  entityKey: string;
}) {
  const { entityKey } = params;
  return request(`/odh-web/odh/web/entity/detail?entityKey=${entityKey}`, {
    method: "GET",
  });
}

//下载领域版本jar
export async function downloadJarFile(data: { releasedId: number }) {
  return request(`/odh-web/odh/web/domain/released/downloadJar`, {
    method: "POST",
    data,
    responseType: "blob",
    getResponse: true,
  });
}

//下载领域版本Zip
export async function downloadZipFile(data: { releasedId: number }) {
  return request(`/odh-web/odh/web/domain/released/downloadSrcZip`, {
    method: "POST",
    data,
    responseType: "blob",
    getResponse: true,
  });
}

//切换领域测试版本jar包
export async function switchTestVersion(params: {
  domainId: number;
  seq: number;
}) {
  return request(`/odh-web/odh/web/domain/released/switchTestVersion?${stringify(params)}`,
    {
      method: "GET",
    }
  );
}

//重启odh-service
export async function odhServiceShutdown() {
  return request(`/odh-service/odh/shutdown`, {
    method: "GET",
  });
}

//刷新odh-service缓存
export async function reloadOdhCache() {
  return request(`/odh-service/odh/reload`, {
    method: "GET",
  });
}

//获取当前测试版本号
export async function getCurrentTestSeq(params: {artifactId: string}) {
  return request(`/odh-service/odh/domainVersion?${stringify(params)}`, {
    method: "GET",
  });
}

// 查询索引函数
export async function getIndexFunc() {
  return request('/odh-web/odh/web/common/indexFunc',{
    method: 'GET'
  })
}

// 查询在用版本
export async function getRunningVersion(params:{appContextPath:string,appCode:string}) {
  const {appContextPath,appCode} = params
  return request(`/${appContextPath}/odh/runningVersion?appCode=${appCode}`,{
    method: 'GET'
  })
}

// 查询测试版本
export async function getTestVersion(params:{appCode:string}) {
  return request(`/odh-service/odh/testVersion?${stringify(params)}`,{
    method: 'GET'
  })
}

// 查询未生效版本
export async function getDomainVersion(params:{artifactId:string}) {
  return request(`/odh-service/odh/domainVersion?${stringify(params)}`,{
    method: 'GET'
  })
}

// 导入领域定义
export async function importDomainFile(params:{domainId:number,autoRelease:string},formData:any) {
  return request(`/odh-web/odh/web/domain/page/import?${stringify(params)}`,{
    method: 'POST',
    data:formData,
  })
}

// hotfix导入领域定义
export async function hotfixImportDomainFile(params:{domainId:number,autoRelease:string},formData:any) {
  return request(`/odh-web/odh/web/domain/page/hotfix/import?${stringify(params)}`,{
    method: 'POST',
    data:formData,
  })
}

// 领域版本切换新接口
export async function switchDomainTestVersionNew(params:{domainId:number,seq:number,autoRestart:string}) {
  return request(`/odh-web/odh/web/domain/released/switchDomainTestVersionNew?${stringify(params)}`,{
    method: 'GET'
  })
}

// 查询当前操作阶段
export async function getOperateStage(params?:{domainId:number}) {
  return request(`/odh-web/odh/web/domain/released/queryOperateStage?${stringify(params)}`,{
    method: 'GET'
  })
}

// 重启odh service并刷新缓存
export async function restartOdhService() {
  return request(`/odh-web/odh/web/domain/released/restartOdhService`,{
    method: 'GET'
  })
}
