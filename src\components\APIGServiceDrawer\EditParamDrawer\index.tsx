import React, { useEffect, useState } from 'react';
import { Button, Drawer, Form, Input, Select, Space, InputNumber } from 'antd';
import useI18n from '@/hooks/useI8n';

interface IEditParamDrawer {
  open: boolean;
  onCancel: () => void;
  onOk: (data: any) => void;
  selectedParam?: any;
}

interface optionProps {
  label: string;
  value: string;
}

const EditParamDrawer: React.FC<IEditParamDrawer> = (props) => {
  const { open, onCancel, onOk, selectedParam } = props;
  const { formatMessage } = useI18n();
  const [form] = Form.useForm();
  const [formValues, setFormValues] = useState<any>({}); // formValues
  const [avlValueType, setAvlValueType] = useState<string>('');
  const [avlValueTypeSource, setAvlValueTypeSource] = useState<optionProps[]>([]);
  const [avlValueTypeValue, setAvlValueTypeValue] = useState<string>('');
  const [required, setRequired] = useState<boolean>(false);
  const [defaultValues, setDeFaultValues] = useState<any>({});

  const ParamTypeSource = ['String', 'Long', 'Integer', 'Number', 'Boolean', 'Datetime'];

  const getFormatSource = (type: string) => {
    switch (type) {
      case 'String':
        return [
          { label: 'password', value: 'password' },
          { label: 'byte', value: 'byte' },
          { label: 'email', value: 'email' },
          { label: 'binary', value: 'binary' },
        ];
      case 'Number':
        return [
          { label: 'float', value: 'float' },
          { label: 'double', value: 'double' },
        ];
      case 'Datetime':
        return [
          { label: 'date-time', value: 'date-time' },
          { label: 'date', value: 'date' },
          { label: 'time', value: 'time' },
        ];
      default:
        return [];
    }
  };

  const FormatSource = getFormatSource(avlValueType);

  const CommonSource = [
    {
      label: 'Yes',
      value: 'Y',
    },
    {
      label: 'No',
      value: 'N',
    },
  ];

  // 弹框关闭
  const onClose = async () => {
    setAvlValueTypeValue('');
    form.resetFields();
    onCancel?.();
  };

  const getStringBetween = (str: string, start: string, end: string) => {
    // 正则表达式匹配两个指定字符之间的内容
    const reg = /\[(\S*)\]/;
    if ((str.match(reg) || []).length > 1) {
      return (str.match(reg) || [])[1];
    }
    return '';
  };

  // 入参校验，构造入参
  const checkServiceReqParamData = () => {
    form.validateFields().then((values) => {
      // 构造入参
      let param = {
        ...selectedParam,
        ...values,
      };
      if (param.avlValueType) {
        switch (param.avlValueType) {
          case 'Length':
            param.avlValue = `Length[${param.min},${param.max}]`;
            break;
          case 'Empty':
            param.avlValue = '';
            break;
          case 'Range':
            param.avlValue = `Range[${param.min},${param.max}]`;
            break;
          case 'Format':
            param.avlValue = param.typeFormat;
            param = {
              ...param,
              typeFormat: param.typeFormat,
            };
            break;
          case 'Values':
            param.avlValue = param.values;
            break;

          default:
            param.avlValue = '';
            break;
        }
      }
      if (param?.avlValueType !== 'Format') {
        delete param?.typeFormat;
      }
      onOk(param);
    });
  };

  // 通用的重置表单字段函数
  const resetFormFields = (resetAvlValueType = true) => {
    // 重置所有相关字段
    const resetFields: any = {
      typeFormat: undefined,
      min: undefined,
      max: undefined,
      values: undefined,
    };

    if (resetAvlValueType) {
      resetFields.avlValueType = '';
      setAvlValueTypeValue('');
    }

    form.setFieldsValue(resetFields);
  };

  const onPropTypeChange = (value: any) => {
    setAvlValueType(value);

    // 重置avlValueTypeValue相关字段
    resetFormFields();

    setRequired(true);
    switch (value) {
      case 'String':
        let source = [
          { label: 'Empty', value: 'Empty' },
          { label: 'Length', value: 'Length' },
          { label: 'Format', value: 'Format' },
          { label: 'Values', value: 'Values' },
        ];
        setAvlValueTypeSource(source);
        form.setFieldsValue({
          avlValueType: 'Empty',
        });
        break;

      case 'Boolean':
        source = [{ label: 'Empty', value: 'Empty' }];
        setAvlValueTypeSource(source);
        form.setFieldsValue({
          avlValueType: 'Empty',
        });
        break;

      case 'Long':
        source = [
          { label: 'Empty', value: 'Empty' },
          { label: 'Length', value: 'Length' },
          { label: 'Range', value: 'Range' },
          { label: 'Values', value: 'Values' },
        ];
        setAvlValueTypeSource(source);
        form.setFieldsValue({
          avlValueType: 'Empty',
        });
        break;
      case 'Integer':
        source = [
          { label: 'Empty', value: 'Empty' },
          { label: 'Length', value: 'Length' },
          { label: 'Range', value: 'Range' },
          { label: 'Values', value: 'Values' },
        ];
        setAvlValueTypeSource(source);
        form.setFieldsValue({
          avlValueType: 'Empty',
        });
        break;
      case 'Number':
        source = [
          { label: 'Empty', value: 'Empty' },
          { label: 'Length', value: 'Length' },
          { label: 'Range', value: 'Range' },
          { label: 'Format', value: 'Format' },
          { label: 'Values', value: 'Values' },
        ];
        setAvlValueTypeSource(source);
        form.setFieldsValue({
          avlValueType: 'Empty',
        });
        break;
      case 'Datetime':
        source = [
          { label: 'Empty', value: 'Empty' },
          { label: 'Format', value: 'Format' },
          { label: 'Values', value: 'Values' },
        ];
        setAvlValueTypeSource(source);
        form.setFieldsValue({
          avlValueType: 'Empty',
        });
        break;
      default:
        source = [{ label: 'Empty', value: 'Empty' }];
        setAvlValueTypeSource(source);
        form.setFieldsValue({
          avlValueType: 'Empty',
        });
        setRequired(false);
        break;
    }
  };

  const onAvlValueTypeChange = (value: any) => {
    setAvlValueTypeValue(value);

    // 重置avlValueTypeValue相关字段，但保留avlValueType
    resetFormFields(false);
  };

  useEffect(() => {
    // 深拷贝行数据后初始化值
    const defaultValues: any = {};

    Object.assign(defaultValues, selectedParam);

    // 6种数据基本类型，处理avlValue字段的回显问题
    const { avlValue, typeFormat } = defaultValues;
    if ((avlValue === '' || avlValue === null) && !typeFormat) {
      defaultValues.avlValueType = 'Empty';
    } else if (typeof avlValue === 'string' && avlValue?.toLowerCase().startsWith('length')) {
      defaultValues.avlValueType = 'Length';
      const betweenVal = getStringBetween(avlValue, '[', ']')?.split(',');
      defaultValues.min = betweenVal[0];
      defaultValues.max = betweenVal[1];
    } else if (typeof avlValue === 'string' && avlValue?.toLowerCase().startsWith('range')) {
      defaultValues.avlValueType = 'Range';
      const betweenVal = getStringBetween(avlValue, '[', ']')?.split(',');
      defaultValues.min = betweenVal[0];
      defaultValues.max = betweenVal[1];
    } else if (typeFormat) {
      defaultValues.avlValueType = 'Format';
      defaultValues.typeFormat = typeFormat;
    } else {
      defaultValues.avlValueType = 'Values';
      defaultValues.values = avlValue;
    }
    onPropTypeChange(defaultValues.paramType);
    onAvlValueTypeChange(defaultValues.avlValueType);
    setDeFaultValues(defaultValues);
    setFormValues(defaultValues);
  }, [selectedParam, open]);

  return (
    <Drawer
      title={formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.APIGPARAMDRAWER.TITLE')}
      onClose={onClose}
      maskClosable={false}
      open={open}
      width={720}
      afterOpenChange={() => {
        form.resetFields();
      }}
      footer={
        <Space style={{ float: 'right' }}>
          <Button type="primary" onClick={checkServiceReqParamData}>
            {formatMessage('PROJECT.COMMON.CONFIRM')}
          </Button>
          <Button onClick={onClose}>{formatMessage('PROJECT.COMMON.CANCEL')}</Button>
        </Space>
      }
    >
      <Form
        form={form}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
        initialValues={defaultValues}
        onValuesChange={(cValues, alValues) => {
          setFormValues(alValues);
        }}
      >
        <Form.Item label={formatMessage('APIG.PARAM.NAME')} name="paramName" rules={[{ required: true, message: '' }]}>
          <Input disabled />
        </Form.Item>
        <Form.Item
          label={formatMessage('APIG.DRAWER.IS_ARRAY')}
          name="isArray"
          rules={[{ required: true, message: '' }]}
        >
          <Select showSearch options={CommonSource} />
        </Form.Item>
        <Form.Item
          label={formatMessage('APIG.PARAM.REQUIRED')}
          name="required"
          rules={[{ required: true, message: '' }]}
        >
          <Select showSearch options={CommonSource} />
        </Form.Item>
        <Form.Item label={formatMessage('APIG.PARAM.TYPE')} name="paramType" rules={[{ required: true, message: '' }]}>
          <Select
            options={[...ParamTypeSource.map((item) => ({ label: item, value: item, key: item }))]}
            onChange={onPropTypeChange}
          />
        </Form.Item>
        <Form.Item label={formatMessage('APIG.PARAM.ALLOWABLE_VALUES')} style={{ marginBottom: 0 }}>
          <Form.Item
            name="avlValueType"
            style={{ display: 'inline-block', width: 'calc(33% - 5px)' }}
            rules={[{ required, message: '' }]}
          >
            <Select options={avlValueTypeSource} onChange={onAvlValueTypeChange} />
          </Form.Item>
          {avlValueTypeValue === 'Length' ? (
            <Form.Item
              name="min"
              style={{ display: 'inline-block', width: 'calc(33%)', marginLeft: '5px' }}
              rules={[{ required: true, message: '' }]}
            >
              <InputNumber
                addonBefore={formatMessage('APIG.DRAWER.MIN')}
                precision={0}
                min={1}
                max={formValues.max}
                style={{ width: '100%' }}
              />
            </Form.Item>
          ) : null}
          {avlValueTypeValue === 'Length' ? (
            <Form.Item
              name="max"
              style={{ display: 'inline-block', width: 'calc(33%)', marginLeft: '5px' }}
              rules={[{ required: true, message: '' }]}
            >
              <InputNumber
                addonBefore={formatMessage('APIG.DRAWER.MAX')}
                precision={0}
                min={formValues.min}
                style={{ width: '100%' }}
              />
            </Form.Item>
          ) : null}
          {avlValueTypeValue === 'Format' ? (
            <Form.Item
              name="typeFormat"
              style={{ display: 'inline-block', width: 'calc(66% + 5px)', marginLeft: '5px' }}
              rules={[{ required: true, message: '' }]}
            >
              <Select options={FormatSource} />
            </Form.Item>
          ) : null}
          {avlValueTypeValue === 'Values' ? (
            <Form.Item
              name="values"
              style={{ display: 'inline-block', width: 'calc(66% + 5px)', marginLeft: '5px' }}
              rules={[{ required: true, message: '' }]}
            >
              <Input placeholder={formatMessage('SCHEMAMANAGEMENT.VALUES_PLACEHOLDER')} />
            </Form.Item>
          ) : null}
          {avlValueTypeValue === 'Range' ? (
            <Form.Item
              name="min"
              style={{ display: 'inline-block', width: 'calc(33%)', marginLeft: '5px' }}
              rules={[{ required: true, message: '' }]}
            >
              <InputNumber
                addonBefore={formatMessage('APIG.DRAWER.MIN')}
                precision={0}
                max={formValues.max}
                style={{ width: '100%' }}
              />
            </Form.Item>
          ) : null}
          {avlValueTypeValue === 'Range' ? (
            <Form.Item
              name="max"
              style={{ display: 'inline-block', width: 'calc(33%)', marginLeft: '5px' }}
              rules={[{ required: true, message: '' }]}
            >
              <InputNumber
                addonBefore={formatMessage('APIG.DRAWER.MAX')}
                precision={0}
                min={formValues.min}
                style={{ width: '100%' }}
              />
            </Form.Item>
          ) : null}
        </Form.Item>
        <Form.Item label={formatMessage('APIG.DRAWER.EXAMPLE')} name="example">
          <Input allowClear />
        </Form.Item>
        <Form.Item label={formatMessage('APIG.PARAM.DESCRIPTIONS')} name="comments">
          <Input.TextArea allowClear />
        </Form.Item>
      </Form>
    </Drawer>
  );
};
export default EditParamDrawer;
