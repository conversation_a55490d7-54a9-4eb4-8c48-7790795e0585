import { request } from "umi";

// 查询领域对象数据模型列表
export async function queryDomainDataModelListService(
  domainObjId: number | null,
  params: { pageNo: number; pageSize: number }
) {
  return request(`/odh-web/odh/web/domain/datamodels/${domainObjId}`, {
    method: "GET",
    params,
  });
}

interface ViewSqlList {
  verCode: null | string;
  viewSql: string;
}

interface IaddDomainDataModelListService {
  modelCode: string;
  modelName?: string;
  domainObjId: number;
  modelType?: string;
  modelClass?: string;
  creationSrc?: string;
  comments?: string;
  viewSqlList?: ViewSqlList[];
}

// 新增领域数据模型
export async function addDomainDataModelListService(
  data: IaddDomainDataModelListService
) {
  return request(`/odh-web/odh/web/domain/datamodel`, {
    method: "POST",
    data,
  });
}

// 获取环境基本信息
export async function getEnvBasicInfoService() {
  return request(`/odh-web/odh/web/common/info`, {
    method: "GET",
  });
}

// 查询领域数据模型分类列表
export async function getDomainDataModelClassesService() {
  return request(`/odh-web/odh/web/domain/datamodel/classes`, {
    method: "GET",
  });
}

// 获取领域下所有表名
export async function getDomainTableNameService(params: { domainId: number }) {
  return request(`/odh-web/odh/web/common/tableInfo`, {
    method: "GET",
    params,
  });
}

// 修改领域数据模型

export async function modifyDomainDataModelListService(
  modelId: number,
  data: IaddDomainDataModelListService
) {
  return request(`/odh-web/odh/web/domain/datamodel/${modelId}`, {
    method: "PATCH",
    data,
  });
}

// 查询领域数据模型详情
export async function getDomainDataModelDetailService(
  modelId: number | string
) {
  return request(`/odh-web/odh/web/domain/datamodel/${modelId}`, {
    method: "GET",
  });
}

// 删除领域数据模型
export async function deleteDomainDataModelListService(
  modelId: number | string
) {
  return request(`/odh-web/odh/web/domain/datamodel/${modelId}`, {
    method: "DELETE",
  });
}

// 查询产品线领域忽略版本
export async function getIgnoreVersion() {
  return request('/odh-web/odh/web/common/qryIgnoreVer', {
    method: "GET",
  });
}
