import { DeleteOutlined, DownOutlined, UpOutlined } from "@ant-design/icons";
import classNames from "classnames";
import { Input, Col, Select } from "antd";
import React, { useEffect, useMemo, useState } from "react";
import styles from "./index.less";

interface IIpuntPanel {
  value?: {
    verCode: string;
    viewSql: string;
  };
  onChange?: (value: any) => void;
  onDelete?: (value: any) => void;
  isProduct?: boolean;
  disabled?: boolean;
  type: string;
}

const versionOptions = [
  {
    value: "default",
    label: "default",
  },
  {
    value: "9E",
    label: "9E",
  },
  {
    value: "81E",
    label: "81E",
  },
];

const InputPanel: React.FC<IIpuntPanel> = ({
  value = { verCode: "", viewSql: "" },
  onChange = () => {},
  onDelete = () => {},
  disabled = false,
  isProduct = false,
}) => {
  const [expand, setExpand] = useState(true);

  const handleExpand = () => {
    setExpand(!expand);
  };

  const handleInputTitle = (val: any) => {
    const temp = {
      ...value,
      verCode: val,
    };
    onChange(temp);
  };

  const handleInputContent = (e: any) => {
    const temp = {
      ...value,
      viewSql: e.target.value,
    };
    onChange(temp);
  };

  const spans = useMemo(() => {
    if (disabled) {
      return 24;
    } else {
      if (isProduct) {
        return 22;
      }
      return 24;
    }
  }, [disabled, isProduct]);

  return (
    <div className={styles.inputPanel}>
      <Col span={spans}>
        <div className={styles["inputPanel-input"]}>
          <Select
            options={versionOptions}
            onChange={handleInputTitle}
            disabled={disabled}
            className={styles.custInput}
            value={
              value?.verCode ? value?.verCode : "default"
            }
          />
          <span
            className={styles.custSpan}
            onClick={handleExpand}
          >
            {expand ? <UpOutlined /> : <DownOutlined />}
          </span>
        </div>
        <div
          className={classNames(styles["inputPanel-inputTextArea"], {
            [styles.hiddenIt]: !expand,
          })}
        >
          <Input.TextArea
            value={value?.viewSql}
            onChange={handleInputContent}
            className={styles.custInputTextArea}
            disabled={disabled}
            autoSize={{ minRows: 2, maxRows: 5 }}
          />
        </div>
      </Col>
      {!disabled && isProduct && (
        <Col span={2} style={{ marginLeft: 10 }}>
          <DeleteOutlined style={{ cursor: "pointer" }} onClick={onDelete} />
        </Col>
      )}
    </div>
  );
};

export default InputPanel;
