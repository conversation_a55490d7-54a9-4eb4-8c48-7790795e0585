import { Button, Divider, Drawer, Flex, Form, Input, InputRef, message, Select, Space, Spin } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { useEffect, useRef, useState } from 'react';
import { CreationSource, MatchStatus } from '../../../const';
import useI18n from '@/hooks/useI8n';
import TextArea from 'antd/es/input/TextArea';
import { expReasonErrorCode, generateErrorCode, newErrorCode } from '@/services/errorCodeService';
import { DomainErrorCodesType } from '@/pages/BssODomainApi/BssDomainObjectEntity/types';
import { ErrorCodeListItemType, newErrorCodeType } from '@/pages/BssOErrorCodeApi/ErrorCodeManagement/types';
import CodePrompt from '@/components/CodePrompt';
import {
  generateRespHeaderSchema,
  generateRespSchema,
  generateStatusSchema,
  transferServiceDetailToSchema,
} from '@/utils/transferServiceDetailToSchema';
import { CommonSource } from '../const';

interface IEditExpReasonErrorCode {
  isProduct: boolean;
  domainErrorCodes: DomainErrorCodesType[];
  editOpen: boolean;
  selectedRootNode: any;
  respParam: any;
  selectedErrorCode: any;
  // allSchema: any[];
  entityServiceDetail: any;
  // setAllSchema: (schema: any[]) => void;
  selectedService: any;
  setEditOpen: (open: boolean) => void;
  queryDomainErrorCodes: (params: { domainId: number }) => void;
  queryExpReasonErrorCode: (params: { respParamId: number }) => void;
}
const EditExpReasonErrorCode: React.FC<IEditExpReasonErrorCode> = (props) => {
  const {
    isProduct,
    editOpen,
    domainErrorCodes = [],
    selectedRootNode,
    respParam,
    selectedErrorCode,
    entityServiceDetail,
    selectedService,
    setEditOpen,
    queryDomainErrorCodes,
    queryExpReasonErrorCode,
  } = props;
  const { formatMessage } = useI18n();
  const [form] = useForm();
  const [spining, setSpining] = useState<boolean>(false);
  const [newErrorMessage, setNewErrorMessage] = useState('');
  const [reasonParams, setReasonParams] = useState<
    Array<{
      paramName: string; // 参数名称，如 reasonParam0
      paramValue: string; // 用户输入的参数值
    }>
  >([]);
  const addSchemaInputRef = useRef<InputRef>(null);
  const [matchConditionsValue, setMatchConditionsValue] = useState<string>(''); // 匹配条件的值，用于存储用户输入的匹配条件表达式
  const [matchStatusValue, setMatchStatusValue] = useState<string>(''); // S:Success,E:Exception
  const [allSchema, setAllSchema] = useState<Array<any>>([]); // 当前服务的请求参数和响应参数的schema
  const [isMatchConditionsRequired, setIsMatchConditionsRequired] = useState<boolean>(false); // 匹配条件是否必填
  const [isOtherErrorsAndException, setIsOtherErrorsAndException] = useState<boolean>(false); // 是other errors并且Match Response Status是Exception
  const [curErrorCode, setCurErrorCode] = useState<ErrorCodeListItemType>(); // 当前选择的错误码
  const [isUseDefaultMatchConditions, setIsUseDefaultMatchConditions] = useState<string>('N'); // 是否使用默认匹配条件
  const [isHandleChangeErrorCodeOrUseDefaultMatchConditions, setIsHandleChangeErrorCodeOrUseDefaultMatchConditions] =
    useState<boolean>(false); // 是否手动改变了ErrorCode或者UseDefaultMatchConditions

  const editRule = selectedRootNode?.state === 'D' && (isProduct || selectedErrorCode?.creationSrc !== 'P'); // 发布状态不能操作，且项目不能修改产品定义的错误码匹配规则

  const onClose = () => {
    setIsHandleChangeErrorCodeOrUseDefaultMatchConditions(false);
    setEditOpen(false);
    setMatchStatusValue('');
    setReasonParams([]);
    setMatchConditionsValue('');
    setCurErrorCode(undefined);
    setIsUseDefaultMatchConditions('N'); // 重置为默认值
  };

  // 在当前领域生成一个异常响应码
  const generateNewErrorCodeInCurrentDomain = async (params: { domainId: number }) => {
    const { success, data } = await generateErrorCode(params);
    if (success) {
      return data;
    }
    return null;
  };

  // 在ErrorCode下拉框输入ErrorMessage
  const onAddErrorCodeFromErrorMessageChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setNewErrorMessage(value);
  };

  // 通过Error Message来新增一个当前领域下的错误码
  const addErrorCodeFromErrorMessage = async (e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>) => {
    e.preventDefault();
    if (selectedRootNode?.domainId) {
      const generatedErrorCode = await generateNewErrorCodeInCurrentDomain({ domainId: selectedRootNode?.domainId });
      const params: newErrorCodeType = {
        domainId: selectedRootNode?.domainId,
        errorCode: generatedErrorCode,
        errorReason: newErrorMessage,
      };
      const { success } = await newErrorCode(params);
      if (success) {
        message.success(formatMessage('PROJECT.COMMON.ADDSUCCESS'));
        setNewErrorMessage('');
        // 更新下拉框选项
        queryDomainErrorCodes({ domainId: selectedRootNode?.domainId });
        setTimeout(() => {
          addSchemaInputRef.current?.focus();
        }, 0);
      }
    }
  };

  // // 自定义过滤条件
  const handleErrorCodeAndMessageFilter = (input: any, option: any) => {
    if (option && typeof option.desc === 'string') {
      return option.desc.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    }
    return false;
  };

  // 匹配状态改变
  const handleMatchStatusChange = (value: string) => {
    setMatchStatusValue(value);
  };

  // 匹配条件改变
  const handleMatchConditionsChange = (value: string) => {
    setMatchConditionsValue(value);
  };

  // 根据reason生成的param的值改变
  const handleCodeChange = (index: number, newValue: string) => {
    setReasonParams((prev) => prev.map((item, i) => (i === index ? { ...item, paramValue: newValue } : item)));
  };

  // 使用正则表达式提取 {} 中的内容
  const extractContent = (str: string) => {
    const matches = str.match(/\{(.*?)\}/g);
    return matches ? matches.map((match) => match.slice(1, -1)) : [];
  };

  // 选择code后填充自动reason
  const handleErrorCodeChange = (errorCodeId: number) => {
    setIsHandleChangeErrorCodeOrUseDefaultMatchConditions(true);
    let currentErrorCode: ErrorCodeListItemType | undefined;
    domainErrorCodes.some((item) => {
      currentErrorCode = item.errorCodes.find((i) => i.errorCodeId === errorCodeId);
      setCurErrorCode(currentErrorCode);
      return currentErrorCode !== undefined;
    });

    const reasonParamsContent = extractContent(currentErrorCode?.errorReason || '');
    setReasonParams(
      reasonParamsContent.map((item) => ({
        paramName: `reasonParam${item}`,
        paramValue: '',
      })),
    );

    form.setFieldValue('errorReason', currentErrorCode?.errorReason);
  };

  const handleIsUseDefaultMatchConditionsChange = (value: any) => {
    setIsHandleChangeErrorCodeOrUseDefaultMatchConditions(true);
    setIsUseDefaultMatchConditions(value);
  };

  // 修改响应参数异常码规则
  const editExpReasonErrorCode = async (operate: string, queryParams: { domainId: number }, bodyParams: any) => {
    try {
      setSpining(true);
      const { success } = await expReasonErrorCode(operate, queryParams, bodyParams);
      if (success) {
        onClose();
        message.success(formatMessage('PROJECT.COMMON.EDITSUCCESS'));
        // 更新数据
        queryExpReasonErrorCode({ respParamId: respParam?.respParamId });
      }
    } catch (error) {
      console.error(error);
    } finally {
      setSpining(false);
    }
  };

  // 入参校验，调接口修改
  const checkErrorCodeData = async () => {
    form.validateFields().then(() => {
      const formValues = form.getFieldsValue();
      const bodyParams = {
        errorCodeId: formValues?.errorCodeId,
        matchPriority: Number(formValues?.matchPriority),
        matchStatus: formValues?.matchStatus,
        matchConditions: matchConditionsValue,
        reasonParams: reasonParams.map((param) => param.paramValue),
        respParamId: respParam?.respParamId,
        respErrorCodeId: selectedErrorCode?.respErrorCodeId,
        useErrorCodeDef: isUseDefaultMatchConditions,
      };

      editExpReasonErrorCode('update', { domainId: selectedRootNode?.domainId }, bodyParams);
    });
  };

  useEffect(() => {
    if (editOpen) {
      if (selectedErrorCode) {
        setCurErrorCode(selectedErrorCode);
        if (selectedErrorCode?.matchStatus) {
          setMatchStatusValue(selectedErrorCode?.matchStatus);
        }
        if (selectedErrorCode?.reasonParams?.length) {
          const defaultReasonParams = selectedErrorCode?.reasonParams.map((item: string, index: number) => ({
            paramName: `reasonParam${index}`,
            paramValue: item,
          }));
          setReasonParams(defaultReasonParams);
        }
        setMatchConditionsValue(selectedErrorCode?.matchConditions || '');
        form.setFieldValue('matchConditions', selectedErrorCode?.matchConditions || '');
        setIsUseDefaultMatchConditions(selectedErrorCode?.useErrorCodeDef || 'N');
        form.setFieldValue('useErrorCodeDef', selectedErrorCode?.useErrorCodeDef || 'N');
      }
    }
  }, [selectedErrorCode, editOpen]);

  useEffect(() => {
    if (entityServiceDetail && editOpen) {
      const reqSchema = transferServiceDetailToSchema(entityServiceDetail?.parameters || [], 'request');
      const respSchema = generateRespSchema(matchStatusValue, entityServiceDetail, respParam);
      const respHeaderSchema = generateRespHeaderSchema(entityServiceDetail, respParam?.respCode);
      const statusSchema = generateStatusSchema(selectedService, matchStatusValue);

      const allSchemaList = [reqSchema, respSchema, respHeaderSchema];
      if (statusSchema) {
        allSchemaList.push(statusSchema);
      }

      setAllSchema(allSchemaList);
    }
  }, [entityServiceDetail, editOpen, matchStatusValue, selectedService]);

  useEffect(() => {
    if (
      curErrorCode?.errorCodeId &&
      (curErrorCode?.errorCodeId === -1 || curErrorCode?.errorCodeId === -101) &&
      matchStatusValue === 'S'
    ) {
      setIsMatchConditionsRequired(true);
    } else {
      setIsMatchConditionsRequired(false);
    }

    if (
      curErrorCode?.errorCodeId &&
      (curErrorCode?.errorCodeId === -1 || curErrorCode?.errorCodeId === -101) &&
      matchStatusValue === 'E'
    ) {
      setIsOtherErrorsAndException(true);
    } else {
      setIsOtherErrorsAndException(false);
    }
  }, [curErrorCode, matchStatusValue]);

  useEffect(() => {
    // 更新匹配条件和匹配参数
    const updateConditionsAndParams = (
      defaultMatchConditions: string | undefined,
      defaultReasonParams: string[] | undefined,
    ) => {
      setMatchConditionsValue(defaultMatchConditions || '');
      form.setFieldValue('matchConditions', defaultMatchConditions || '');
      const paramList =
        defaultReasonParams?.map((item: string, index: number) => ({
          paramName: `reasonParam${index}`,
          paramValue: item,
        })) || [];
      setReasonParams(paramList);
    };

    // ○ 如果是Yes，下面的Match Conditions、Reason Parameter 0、Reason Parameter 1...禁止编辑，并使用Error Code定义的默认信息（如果错误码的Match Parameter Conditions为空，界面上Use Default Match Conditions就不能设置为Yes）；
    // ○ 如果是No，下面的Match Conditions、Reason Parameter 0、Reason Parameter 1...可以编辑，新增时使用Error Code定义的默认信息。修改时，使用错误码匹配定义（ODH_EXP_RESP_ERROR_CODE表）的信息
    if (isHandleChangeErrorCodeOrUseDefaultMatchConditions) {
      if (curErrorCode) {
        if (isUseDefaultMatchConditions === 'Y') {
          if (curErrorCode?.defaultMatchConditions) {
            updateConditionsAndParams(curErrorCode?.defaultMatchConditions, curErrorCode?.defaultReasonParams);
          } else {
            // 重置'是否启用默认匹配条件'并给出提示
            message.warning(formatMessage('ERRORCODE.DEFAULT.MATCHCONDITIONS.NO_VALUE'));
            setIsUseDefaultMatchConditions('N');
            form.setFieldValue('useErrorCodeDef', 'N');
          }
        } else {
          if (curErrorCode?.errorCodeId === selectedErrorCode?.errorCodeId) {
            updateConditionsAndParams(selectedErrorCode?.matchConditions, selectedErrorCode?.reasonParams);
          } else {
            setMatchConditionsValue('');
            form.setFieldValue('matchConditions', '');
            const paramList =
              reasonParams?.map((item) => ({
                paramName: item?.paramName,
                paramValue: '',
              })) || [];
            setReasonParams(paramList);
          }
        }
      }
    }
  }, [
    curErrorCode,
    selectedErrorCode,
    isUseDefaultMatchConditions,
    isHandleChangeErrorCodeOrUseDefaultMatchConditions,
  ]);

  return (
    <Drawer
      title={formatMessage('ERRORCODE.EDIT.TITLE')}
      onClose={onClose}
      maskClosable={false}
      open={editOpen}
      width={900}
      afterOpenChange={() => {
        form.resetFields();
      }}
      footer={
        <Space style={{ float: 'right' }}>
          {editRule && !isOtherErrorsAndException && (
            <Spin spinning={spining}>
              <Button type="primary" onClick={checkErrorCodeData}>
                {formatMessage('PROJECT.COMMON.SUBMIT')}
              </Button>
            </Spin>
          )}
          <Button onClick={onClose}>{formatMessage('PROJECT.COMMON.CANCEL')}</Button>
        </Space>
      }
    >
      <Form form={form} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }} initialValues={selectedErrorCode}>
        <Form.Item
          label={formatMessage('ERRORCODE.COMMON.ERRORCODE')}
          name="errorCodeId"
          rules={[{ required: true, message: '' }]}
        >
          <Select
            disabled={!editRule}
            options={[
              ...domainErrorCodes.map((item) => ({
                label: item.domainName,
                value: item.domainName,
                desc: item.domainName,
                options: item.errorCodes.map((i) => ({
                  label: i.errorCode,
                  value: i.errorCodeId,
                  key: i.errorCodeId,
                  desc: `${i.errorCode} (${i.errorReason})`,
                })),
              })),
            ]}
            // disabled={isProjectAndProductOwned}
            onChange={handleErrorCodeChange}
            showSearch
            filterOption={handleErrorCodeAndMessageFilter}
            optionRender={(option) => <span>{option.data.desc}</span>}
            dropdownRender={(menu) => (
              <>
                {menu}
                {/* 暂时屏蔽该功能 */}
                {/* <Divider style={{ margin: '8px 0' }} />
                <Flex justify="space-between">
                  <Input
                    value={newErrorMessage}
                    placeholder="The code is auto-generated, just input reason."
                    ref={addSchemaInputRef}
                    onChange={onAddErrorCodeFromErrorMessageChange}
                    onKeyDown={(e) => e.stopPropagation()}
                  />
                  <Button type="text" onClick={addErrorCodeFromErrorMessage}>
                    + New
                  </Button>
                </Flex> */}
              </>
            )}
          />
        </Form.Item>
        <Form.Item
          label={formatMessage('ERRORCODE.COMMON.ERRORREASON')}
          name="errorReason"
          rules={[{ required: true, message: '' }]}
        >
          <TextArea disabled rows={2} />
        </Form.Item>
        <Form.Item
          label={formatMessage('PROJECT.COMMON.CREATIONSOURCE')}
          name="creationSrc"
          rules={[{ required: true, message: '' }]}
        >
          <Select disabled>
            {CreationSource.map((i: any) => (
              <Select.Option key={i.key} value={i.key}>
                {i.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          label={formatMessage('ERRORCODE.EXPREASON.MATCHRESPONSESTATUS')}
          name="matchStatus"
          rules={[{ required: true, message: '' }]}
          extra={isOtherErrorsAndException ? formatMessage('ERRORCODE.EXPREASON.NO_CONFIG_NEEDED') : ''}
        >
          <Select showSearch allowClear disabled={!editRule} onChange={handleMatchStatusChange}>
            {MatchStatus.map((i: any) => (
              <Select.Option key={i.key} value={i.key}>
                {i.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          label={formatMessage('ERRORCODE.EXPREASON.USEDEFALUTMATCHCONDITIONS')}
          name="useErrorCodeDef"
          rules={[{ required: true, message: '' }]}
        >
          <Select
            disabled={!editRule}
            options={CommonSource}
            value={isUseDefaultMatchConditions}
            onChange={(value) => handleIsUseDefaultMatchConditionsChange(value)}
          />
        </Form.Item>
        <Form.Item
          label={formatMessage('ERRORCODE.EXPREASON.MATCHCONDITIONS')}
          name="matchConditions"
          rules={
            isMatchConditionsRequired
              ? [{ required: true, message: formatMessage('ERRORCODE.EXPREASON.MATCHCONDITIONS.REQUIRED') }]
              : []
          }
        >
          <CodePrompt
            language="java"
            readOnly={!editRule || isUseDefaultMatchConditions === 'Y'}
            id="matchConditionsCodePrompt"
            className="formItemCodeMirror"
            editable={true}
            // placeholder={placeholder}
            minHeight="28px"
            maxHeight="60px"
            basicSetup={{
              lineNumbers: false,
              highlightActiveLine: false,
              highlightActiveLineGutter: false,
            }}
            value={matchConditionsValue ? matchConditionsValue : ''}
            allSchema={allSchema}
            onChange={(newValue: string) => handleMatchConditionsChange(newValue)}
          />
        </Form.Item>
        {reasonParams.map((item, index) => (
          <Form.Item
            label={`${formatMessage('ERRORCODE.EXPREASON.REASONPARAMETER')} ${index}`}
            key={`${item.paramName}-${index}`}
            rules={[{ required: true, message: '' }]}
          >
            <CodePrompt
              language="java"
              readOnly={!editRule || isUseDefaultMatchConditions === 'Y'}
              id={item.paramName}
              className="formItemCodeMirror"
              editable={true}
              minHeight="28px"
              maxHeight="60px"
              basicSetup={{
                lineNumbers: false,
                highlightActiveLine: false,
                highlightActiveLineGutter: false,
              }}
              value={item.paramValue}
              allSchema={allSchema}
              onChange={(newValue: string) => handleCodeChange(index, newValue)}
            />
          </Form.Item>
        ))}
        <Form.Item
          label={formatMessage('ERRORCODE.EXPREASON.MATCHPRIORITY')}
          name="matchPriority"
          rules={[
            { required: true, message: '' },
            {
              pattern: /^(1?[0-9]|[1-9][0-9]?)$/, // 限制Priority只能输入1-99的数字
              message: formatMessage('ERRORCODE.EXPREASON.PRIORITY_LIMIT'),
            },
          ]}
          extra={formatMessage('ERRORCODE.EXPREASON.PRIORITY_NOTE')}
        >
          <Input disabled={!editRule} type="number" placeholder="1~99" />
        </Form.Item>
      </Form>
    </Drawer>
  );
};
export default EditExpReasonErrorCode;
