import { TableColumnsType } from 'antd';
import BlockTitle from '@/components/BlockTitle';
import useI18n from '@/hooks/useI8n';
import { saveAsUseCaseServicePropsType } from '@/services';
import { HttpStatusCode } from '@/utils/httpStatusCode';
import ResizableTable from '@/components/ResizableTable';

interface ResponseStatusTablePropsType {
  responseInfo?: Pick<saveAsUseCaseServicePropsType, 'serviceRespParamList'> | any;
}

const ResponseStatusTable: React.FC<ResponseStatusTablePropsType> = ({ responseInfo = [] }) => {
  const { formatMessage } = useI18n();

  const columns: TableColumnsType = [
    {
      dataIndex: 'callResult',
      title: formatMessage('DEBUGSERVICE.RESPONSESTATUSTABLE.CALLRESULT'),
      width: '25%',
      render: (text, record, index) => {
        const flag = record.respCode == 200;
        return <span style={flag ? { color: 'green' } : { color: 'red' }}>{flag ? 'Success' : 'Failed'}</span>;
      },
    },
    {
      dataIndex: 'respCode',
      title: formatMessage('DEBUGSERVICE.RESPONSESTATUSTABLE.STATUSCODE'),
      width: '25%',
    },
    {
      dataIndex: 'respTime',
      title: formatMessage('DEBUGSERVICE.RESPONSESTATUSTABLE.RESPTIME'),
      width: '25%',
      render: (text, record, index) => {
        return `${text} ms`;
      },
    },
    {
      dataIndex: 'statusText',
      title: formatMessage('DEBUGSERVICE.RESPONSESTATUSTABLE.STATUSDESC'),
      width: '25%',
      render: (text, record, index) => {
        const { respCode } = record;
        return HttpStatusCode[respCode];
      },
    },
  ];

  return (
    <div>
      <BlockTitle>{formatMessage('DEBUGSERVICE.RESPONSESTATUSTABLE.TITLE')}</BlockTitle>
      <ResizableTable
        columns={columns}
        dataSource={responseInfo?.map((res: any, index: number) => ({
          ...res,
          rowKey: `resStatusTable-row-${index}`,
        }))}
        rowKey="rowKey"
        pagination={false}
      />
    </div>
  );
};

export default ResponseStatusTable;
