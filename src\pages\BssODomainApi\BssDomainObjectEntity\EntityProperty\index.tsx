import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Popconfirm, Spin, Tooltip, message } from 'antd';
import type { PopconfirmProps, TableProps } from 'antd';
import { FormOutlined, DeleteOutlined } from '@ant-design/icons';
import {
  qryEntityPropList,
  deleteEntityProp,
  qryColumnNameList,
  qryEnumInfoList,
  qrySensitiveLevelList,
} from '@/services/entityService';
import classNames from 'classnames';
import useI18n from '@/hooks/useI8n';
import KeyIcon from '@/assets/CDP/key.svg';
import IndexIcon from '@/assets/CDP/Index.svg';
import CanFilterIcon from '@/assets/CDP/CanFilter.svg';
import MandatoryIcon from '@/assets/CDP/Mandatory.svg';
import CanOrderIcon from '@/assets/CDP/CanOrder.svg';
import styles from '../index.less';
import AddEntityPropDrawer from './AddEntityProp';
import EditEntityPropDrawer from './EditEntityProp';
import OperationIconTitle from '@/components/OperationIconTitle';
import { SelectedEntityProps, SelectedRootNode, SensitiveLeveType } from '../types';
import ResizableTable from '@/components/ResizableTable';
import { useTableFilter } from '@/hooks/useTableFilter';
import getUUid from '@/utils/getUUid';

interface IEntityProperty {
  sensitiveLevelList: SensitiveLeveType[];
  selectedEntity: SelectedEntityProps; // 当前节点
  selectedRootNode?: SelectedRootNode; // 根节点
  isProduct?: boolean;
  ignoreVersionSource: any;
  indexFunctionSource: any;
}
type OnChange = NonNullable<TableProps<any>['onChange']>;
type Filters = Parameters<OnChange>[1];

const EntityProperty: React.FC<IEntityProperty> = (props) => {
  const {
    ignoreVersionSource,
    indexFunctionSource,
    selectedEntity,
    selectedRootNode,
    isProduct,
    sensitiveLevelList = [],
  } = props;
  const { formatMessage } = useI18n();
  const [spining, setSpining] = useState<boolean>(false); // menu loading

  const [entityPropertiesSource, setEentityPropertiesSource] = React.useState<any>([]); // 领域实体属性数据源
  const [selectedProperty, setSelectedProperty] = React.useState<any>({}); // 当前选中的实体属性行数据
  const [columnNameSource, setColumnNameSource] = useState<any>([]);
  const [enumSource, setEnumSource] = useState<any>([]);

  const [openAddEntityPropDrawer, setOpenAddEntityPropDrawer] = useState<boolean>(false); // 新增领域属性
  const [openEditEntityPropDrawer, setOpenEditEntityPropDrawer] = useState<boolean>(false); // 修改实体属性

  const [filteredInfo, setFilteredInfo] = useState<Filters>({});
  const CreationSource = [
    { name: 'Product-Owned', key: 'P' },
    { name: 'Project-Customized', key: 'C' },
  ];
  const SupportSource = [
    {
      name: (
        <>
          <img src={KeyIcon} className={styles.iconItem} />
          <span style={{ marginLeft: 5 }}>{formatMessage('DOMAINDETAIL.PROPERTIES.PRIMARY')}</span>
        </>
      ),
      key: 'keyFlag',
    },
    {
      name: (
        <>
          <img src={IndexIcon} className={styles.iconItem} />
          <span style={{ marginLeft: 5 }}>{formatMessage('DOMAINDETAIL.PROPERTIES.INDEX')}</span>
        </>
      ),
      key: 'indexFlag',
    },
    {
      name: (
        <>
          <img src={CanFilterIcon} className={styles.iconItem} />
          <span style={{ marginLeft: 5 }}>{formatMessage('DOMAINDETAIL.PROPERTIES.CANFILTER')}</span>
        </>
      ),
      key: 'filterFlag',
    },
    {
      name: (
        <>
          <img src={MandatoryIcon} className={styles.iconItem} />
          <span style={{ marginLeft: 5 }}>{formatMessage('DOMAINDETAIL.PROPERTIES.MANDATORY')}</span>
        </>
      ),
      key: 'required',
    },
    {
      name: (
        <>
          <img src={CanOrderIcon} className={styles.iconItem} />
          <span style={{ marginLeft: 5 }}>{formatMessage('DOMAINDETAIL.PROPERTIES.CANORDER')}</span>
        </>
      ),
      key: 'orderFlag',
    },
  ];
  // 使用 useMemo 确保 dataSource 是稳定的引用
  const memoizedDataSource = useMemo(
    () => (Array.isArray(entityPropertiesSource) ? entityPropertiesSource : []),
    [entityPropertiesSource],
  );
  // 自定义hooks，支持表格列筛选并分页展示
  const { filteredDataSource, getColumnSearchProps, getColumnEnumSearchProps, clearAllFilters } =
    useTableFilter(memoizedDataSource);

  // 针对特殊表格列，自定义过滤逻辑
  const customTypeFilter = (item: any, obj: any) => {
    if (item) {
      const showData = item.propType === 'Enum' ? item.enumName : item.propType;
      return (showData || '')
        .toString()
        .toLowerCase()
        .includes((Object.values(obj)[1] as string).toLowerCase());
    }
    return true;
  };

  // 针对特殊表格列，自定义过滤逻辑
  const customAvlValueFilter = (item: any, obj: any) => {
    if (item) {
      let showData = item.avlValue || '';
      if (item.avlValue && item.enumId) {
        showData = enumSource.filter((i: any) => i.value === item.enumId).map((i: any) => i.label);
      }
      return showData
        .toString()
        .toLowerCase()
        .includes((Object.values(obj)[1] as string).toLowerCase());
    }
    return true;
  };

  // 针对特殊表格列，自定义过滤逻辑
  const customSupportFilter = (item: any, obj: any) => {
    if (item && obj) {
      const keyFlag = item?.keyFlag === 'Y' ? 'keyFlag' : '';
      const indexFlag = item?.indexFlag === 'Y' ? 'indexFlag' : '';
      const filterFlag = item?.filterFlag === 'Y' ? 'filterFlag' : '';
      const required = item?.required === 'Y' ? 'required' : '';
      const orderFlag = item?.orderFlag === 'Y' ? 'orderFlag' : '';
      const showData = [keyFlag, indexFlag, filterFlag, required, orderFlag];
      if (Object.values(obj)[1]) {
        const filterKeys = obj?.supportFeatures?.split(',');
        return filterKeys?.every((filterKey: any) => {
          return showData.find((showKey: any) => showKey === filterKey);
        });
      }
    }
    return true;
  };

  const deleteEntityPropConfirm: PopconfirmProps['onConfirm'] = async (e) => {
    const param = {
      propId: selectedProperty.propId,
      entityId: selectedEntity.entityId,
    };
    try {
      const resultData = await deleteEntityProp(param);

      if (resultData?.success) {
        message.success('Succeed in deleting the entity property!');
        // 删除领域对象后重新渲染数据
        queryEntityProperties();
      }
    } catch (error) {
      // message.success("Fail in deleting the entity property!");
    }
  };
  // entity list表格
  const columns = [
    {
      dataIndex: 'propName',
      title: formatMessage('PROJECT.COMMON.NAME'),
      width: '10%',
      ellipsis: true,
      ...getColumnSearchProps('propName'),
    },
    {
      dataIndex: 'columnName',
      title: formatMessage('ENTITYPROP.COLUMN_NAME'),
      width: '10%',
      ellipsis: true,
      ...getColumnSearchProps('columnName'),
    },
    {
      dataIndex: 'propType',
      title: formatMessage('PROJECT.COMMON.TYPE'),
      width: '10%',
      ellipsis: true,
      ...getColumnSearchProps('propType', customTypeFilter),
      render: (_: string, record: any) => {
        return record.propType === 'Enum' ? record.enumName : record.propType;
      },
    },
    {
      dataIndex: 'avlValue',
      title: formatMessage('ENTITYPROP.ALLOWABLE_VALUES'),
      width: '10%',
      ellipsis: true,
      ...getColumnSearchProps('avlValue', customAvlValueFilter),
      render: (_: string, record: any) => {
        if (record.avlValue && record.enumId) {
          return enumSource.filter((i: any) => i.value === record.enumId).map((i: any) => i.label);
        }
        return record.avlValue;
      },
    },
    {
      dataIndex: 'supportFeatures',
      title: formatMessage('DOMAINDETAIL.PROPERTIES.FEATURES'),
      width: '10%',
      ellipsis: true,
      render: (_: string, record: any) => {
        return (
          <div>
            {record?.keyFlag === 'Y' ? (
              <div className={styles.legendItem}>
                <img src={KeyIcon} className={styles.iconItem} alt="" />
              </div>
            ) : null}
            {record?.required === 'Y' ? (
              <div className={styles.legendItem}>
                <img src={MandatoryIcon} className={styles.iconItem} alt="" />
              </div>
            ) : null}
            {record?.indexFlag === 'Y' ? (
              <div className={styles.legendItem}>
                <img src={IndexIcon} className={styles.iconItem} alt="" />
              </div>
            ) : null}
            {record?.orderFlag === 'Y' ? (
              <div className={styles.legendItem}>
                <img src={CanOrderIcon} className={styles.iconItem} alt="" />
              </div>
            ) : null}
            {record?.filterFlag === 'Y' ? (
              <div className={styles.legendItem}>
                <img src={CanFilterIcon} className={styles.iconItem} alt="" />
              </div>
            ) : null}
          </div>
        );
      },
      ...getColumnEnumSearchProps('supportFeatures', SupportSource, customSupportFilter),
    },
    {
      dataIndex: 'creationSrc',
      title: formatMessage('PROJECT.COMMON.CREATIONSOURCE'),
      width: '13%',
      ellipsis: true,
      render: (_: string, record: any) => {
        return CreationSource.filter((i) => i.key === record.creationSrc).map((i) => i.name);
      },
      ...getColumnEnumSearchProps('creationSrc', CreationSource),
    },
    {
      dataIndex: 'comments',
      title: formatMessage('PROJECT.COMMON.DESCRIPTION'),
      width: '27%',
      ellipsis: true,
      ...getColumnSearchProps('comments'),
    },
    {
      dataIndex: '',
      title: formatMessage('PROJECT.COMMON.OPERATION'),
      width: '10%',
      render: (_: string, record: any) => {
        return (
          <div>
            <Tooltip title={formatMessage('PROJECT.COMMON.EDIT')}>
              <FormOutlined
                className={classNames(styles.iconStyle, {
                  [styles.hide]: selectedRootNode?.state !== 'D',
                })}
                onClick={() => {
                  setOpenEditEntityPropDrawer(true);
                }}
              />
            </Tooltip>

            <Popconfirm
              title="Delete Entity"
              description="Are you sure to delete the entity property?"
              onConfirm={deleteEntityPropConfirm}
              okText="Yes"
              cancelText="No"
            >
              {isProduct ? (
                <Tooltip title={formatMessage('PROJECT.COMMON.DELETE')}>
                  <DeleteOutlined
                    className={classNames(styles.versionIcon2, {
                      [styles.hide]: selectedRootNode?.state !== 'D',
                    })}
                    onClick={() => {}}
                  />
                </Tooltip>
              ) : (
                <Tooltip title={formatMessage('PROJECT.COMMON.DELETE')}>
                  <DeleteOutlined
                    className={classNames(styles.versionIcon2, {
                      [styles.hide]: record?.creationSrc === 'P' || selectedRootNode?.state !== 'D',
                    })}
                    onClick={() => {}}
                  />
                </Tooltip>
              )}
            </Popconfirm>
          </div>
        );
      },
    },
  ];

  const queryEntityProperties = async () => {
    setSpining(true);
    try {
      if (selectedEntity.entityId) {
        const { success, data } = await qryEntityPropList(selectedEntity.entityId);
        if (success) {
          setEentityPropertiesSource(data);
        } else {
          setEentityPropertiesSource([]);
        }
      }
      setSpining(false);
    } catch (error) {
      setSpining(false);
    }
  };

  const queryColumnList = async () => {
    if (selectedEntity?.modelId) {
      const { success, data } = await qryColumnNameList({ modelId: selectedEntity.modelId });
      if (success) {
        const dataSource = Object.keys(data).map((key) => ({
          label: key,
          value: key,
          type: data[key],
          key: getUUid(6),
          state: 'original',
        }));
        setColumnNameSource(dataSource);
      }
    }
  };
  // 查询枚举字段列表
  const queryEnumList = async () => {
    const param: any = {
      domainId: selectedRootNode?.domainId,
      pageNo: 1,
      pageSize: 100,
    };
    const { success, data } = await qryEnumInfoList(param);
    if (success) {
      const dataSource = data?.list.map((item: any) => ({ label: item.enumName, value: item.enumId }));
      setEnumSource(dataSource || []);
    }
  };

  useEffect(() => {
    // 查询领域对象详情
    queryEntityProperties();
    // 查询领域对象数据模型列表
    queryColumnList();
    // 查询enum列表
    queryEnumList();

    // 重置表格列的筛选条件
    clearAllFilters();
  }, [selectedEntity]);

  return (
    <Spin spinning={spining}>
      {/* 领域实体属性列表 */}
      <div>
        <OperationIconTitle
          title={formatMessage('ENTITYPROP.TITLE')}
          type={selectedRootNode?.state === 'D' ? 'add' : ''}
          opt={formatMessage('PROJECT.COMMON.ADD')}
          handleClick={() => {
            setOpenAddEntityPropDrawer(true);
          }}
        />
        <div className={styles.remarkContent}>
          <div className={styles.legendItem}>
            <img src={KeyIcon} className={styles.iconItem} alt="" />
            <span>= {formatMessage('DOMAINDETAIL.PROPERTIES.PRIMARY')}</span>
          </div>
          <div className={styles.legendItem}>
            <img src={IndexIcon} className={styles.iconItem} alt="" />
            <span>= {formatMessage('DOMAINDETAIL.PROPERTIES.INDEX')}</span>
          </div>
          <div className={styles.legendItem}>
            <img src={CanFilterIcon} className={styles.iconItem} alt="" />
            <span>= {formatMessage('DOMAINDETAIL.PROPERTIES.CANFILTER')}</span>
          </div>
          <div className={styles.legendItem}>
            <img src={MandatoryIcon} className={styles.iconItem} alt="" />
            <span>= {formatMessage('DOMAINDETAIL.PROPERTIES.MANDATORY')}</span>
          </div>
          <div className={styles.legendItem}>
            <img src={CanOrderIcon} className={styles.iconItem} alt="" />
            <span>= {formatMessage('DOMAINDETAIL.PROPERTIES.CANORDER')}</span>
          </div>
        </div>
        <div className={styles.content}>
          <ResizableTable
            size="small"
            style={{ marginTop: '12px' }}
            columns={columns}
            dataSource={filteredDataSource || []}
            rowKey={(record: any) => record?.propId}
            pagination={false}
            onRow={(record: any) => ({
              onClick: () => {
                setSelectedProperty(record);
              },
            })}
          />
        </div>
      </div>

      {/* 新增领域实体属性 */}
      <AddEntityPropDrawer
        sensitiveLevelList={sensitiveLevelList}
        indexFunctionSource={indexFunctionSource}
        ignoreVersionSource={ignoreVersionSource}
        open={openAddEntityPropDrawer}
        columnNameSource={columnNameSource}
        enumSource={enumSource}
        selectedEntity={selectedEntity}
        onCancel={() => setOpenAddEntityPropDrawer(false)}
        isProduct={isProduct as boolean}
        onOk={() => {
          // 新增实体属性成功后，刷新当前表格
          setOpenAddEntityPropDrawer(false);
          queryEntityProperties();
        }}
      />
      {/* 修改领域实体属性 */}
      <EditEntityPropDrawer
        sensitiveLevelList={sensitiveLevelList}
        indexFunctionSource={indexFunctionSource}
        ignoreVersionSource={ignoreVersionSource}
        open={openEditEntityPropDrawer}
        selectedEntity={selectedEntity}
        columnNameSource={columnNameSource}
        enumSource={enumSource}
        initValue={selectedProperty}
        onCancel={() => setOpenEditEntityPropDrawer(false)}
        isProduct={isProduct as boolean}
        onOk={() => {
          // 新增实体属性成功后，刷新当前表格
          setOpenEditEntityPropDrawer(false);
          queryEntityProperties();
        }}
      />
    </Spin>
  );
};

export default EntityProperty;
