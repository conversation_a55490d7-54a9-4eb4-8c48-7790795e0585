{"private": true, "author": "", "scripts": {"dev": "umi dev", "build": "umi build", "postinstall": "umi setup", "setup": "umi setup", "start": "npm run dev", "eslint": "eslint ./"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@codemirror/autocomplete": "^6.18.4", "@codemirror/lang-java": "^6.0.1", "@codemirror/lang-json": "^6.0.1", "@codemirror/lang-php": "^6.0.1", "@codemirror/lang-python": "^6.1.6", "@codemirror/lang-sql": "^6.8.0", "@codemirror/lang-yaml": "^6.1.2", "@codemirror/language": "^6.10.8", "@codemirror/legacy-modes": "^6.4.2", "@codemirror/view": "^6.36.1", "@codemirror/lang-html": "^6.4.8", "@codemirror/commands": "^6.3.3", "@types/qs": "^6.9.11", "@uiw/react-codemirror": "^4.23.7", "@umijs/plugins": "^4.0.87", "ahooks": "^3.7.8", "antd": "^5.12.6", "classnames": "^2.5.1", "copy-to-clipboard": "^3.3.3", "dayjs": "^1.11.11", "dnd-kit": "^0.0.2", "file-loader": "^6.2.0", "lodash": "^4.17.21", "qs": "^6.11.2", "react": "^17.0.2", "react-dom": "^17.0.2", "react-highlight-words": "^0.20.0", "react-resizable": "^3.0.5", "thememirror": "^2.0.1", "umi": "^4.0.90", "yuml-diagram": "^1.2.0", "zustand": "^4.5.4"}, "devDependencies": {"@types/lodash": "^4.14.202", "@types/react": "^18.0.33", "@types/react-dom": "^18.0.11", "@whalecloud/eslint-config": "0.0.40-beta.9", "eslint": "8.6.0", "typescript": "^5.0.3"}}