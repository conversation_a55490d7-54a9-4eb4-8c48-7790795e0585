.urlWrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  .addressSelect{
    width: 180px;
    height: 38px;
  }
  .url {
    display: flex;
    align-items: center;
    font-family: Nunito Sans;
    font-weight: 400;
    flex: 1;
    & > div:nth-child(1) {
      font-size: 16px;
      padding: 8px 0;
      width: 91px;
      line-height: 22px;
      text-align: center;
    }
    & > div:nth-child(2) {
      flex: 1;
      font-size: 14px;
      padding: 8px 0 8px 12px;
      color: #2d3040;
      line-height: 22px;
      background: #f5f6f7;
    }
    .postStyle {
      background: #00bb66;
      color: #fff;
    }
    .getStyle {
      background: #4477ee;
      color: #ffffff;
    }
    .patchStyle {
      background: #9a61ef;
      color: #ffffff;
    }
    .deleteStyle {
      background: #ec4d4d;
      color: #ffffff;
    }
  }
  .optionBtnGroup {
    display: flex;
    align-items: center;
    margin-left: 24px;
    .sendBtn {
      background: #47e;
      color: #fff;
      border: none;
      &:hover {
        background: #47e;
        color: #fff;
      }
    }
    .commonHeight {
      height: 38px;
      :global {
        button {
          height: 38px;
        }
      }
    }
  }
}
