import React, { useState, forwardRef, useImperativeHandle } from 'react';
import { Form, Select, Checkbox } from 'antd';
import { FormInstance } from 'antd/lib/form';
import useI18n from '@/hooks/useI8n';
import styles from './index.less';
import { useStore } from '@/store';

export type IFormValues = Partial<{
  fileType: string;
  version: string;
  exampleFlag: boolean;
}>;

interface IFormRef {
  formRef: FormInstance<any>;
}

const FileType: React.ForwardRefRenderFunction<IFormRef, {}> = (props, ref) => {
  const { isAICCTypeShow } = useStore();
  const DOCUMENTTYPE = [
    {
      label: 'Swagger(.json)',
      value: 'swagger',
    },
    {
      label: 'API List(.xls)',
      value: 'excel',
    },
    {
      label: 'API Protocol Document(.docx)',
      value: 'word',
    },
  ];

  if (isAICCTypeShow) {
    DOCUMENTTYPE.push({
      label: 'AICC Prompt(.txt)',
      value: 'txt',
    });
  }
  const SWAGGERVERSION = [
    {
      label: '2.0',
      value: '2.0',
    },
    {
      label: '3.0',
      value: '3.0',
    },
  ];

  const initValues = {
    fileType: 'swagger',
    version: '2.0',
  };

  useImperativeHandle(ref, () => ({
    formRef: form,
  }));

  const { formatMessage } = useI18n();
  const [form] = Form.useForm();
  const [formValues, setFormValues] = useState<IFormValues>(initValues); // formValues

  return (
    <div className={styles.fileTypeWrapper}>
      <Form
        form={form}
        labelCol={{ span: 10 }}
        wrapperCol={{ span: 14 }}
        initialValues={initValues}
        onValuesChange={(cValues, alValues) => {
          setFormValues(alValues);
        }}
      >
        <Form.Item label={formatMessage('DOWNLOAD.FORM.FILETYPE')} name="fileType">
          <Select>
            {DOCUMENTTYPE.map((i) => (
              <Select.Option key={i.value} value={i.value}>
                {i.label}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        {/* swagger version */}
        {formValues?.fileType === 'swagger' ? (
          <Form.Item label={formatMessage('DOWNLOAD.FORM.VERSION')} name="version">
            <Select>
              {SWAGGERVERSION.map((i) => (
                <Select.Option key={i.value} value={i.value}>
                  {i.label}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        ) : null}
        {/* docx example */}
        {formValues?.fileType === 'word' ? (
          <Form.Item label={formatMessage('DOWNLOAD.FORM.EXAMPLE')} name="exampleFlag" valuePropName="checked">
            <Checkbox />
          </Form.Item>
        ) : null}
      </Form>
    </div>
  );
};

export default forwardRef(FileType);
