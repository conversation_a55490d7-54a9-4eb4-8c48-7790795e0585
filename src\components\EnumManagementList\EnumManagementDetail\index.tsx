import React, { useEffect, useMemo, useRef, useState } from 'react';
import BasicInfomation from './BasicInformation';
import ConstantListTable from './ConstantListTable';
import EntitiesUsingTable from './EntitiesUsingTable';
import { EnumTypeStatus, CreationSource } from '../const';
import { useModel } from 'umi';
import styles from './index.less';
import { queryDomainDataModelListService, queryDomainEnumDetailService } from '@/services/enumListService';
import EnumManagementDrawer from '../EnumManagementDrawer';
import BssDomainTopContent from '@/pages/BssODomainApi/BssDomainTopContent';

interface IEnumManagementDetail {
  selectedDomainNode: any;
  selectedRootNode: any;
  isProduct: boolean;
  onRefresh: (currentSelectKey: string) => void;
}

const EnumManagementDetail: React.FC<IEnumManagementDetail> = ({
  selectedDomainNode = {},
  isProduct = false,
  selectedRootNode = {},
  onRefresh = () => {},
}) => {
  const { enumId = null, domainObjId = null, key = null } = selectedDomainNode;

  const [dataModelList, setDataModelList] = useState<Record<string, string>>({});

  const isDraft = useMemo(() => {
    return selectedRootNode?.state === 'D';
  }, [selectedRootNode]);

  const queryDomainDataModelListFn = async (id: number | null) => {
    if (id) {
      try {
        await queryDomainDataModelListService(id, {
          pageNo: 0,
          pageSize: 0,
        }).then((res: any) => {
          const { success = false, data = [] } = res;
          if (success) {
            const { list = [] } = data;
            let tempList: any = {};
            list.forEach((item: any) => {
              const { modelId = '', modelName = '' } = item;
              tempList[modelId] = modelName;
            });
            setDataModelList(tempList);
          }
        });
      } catch (error) {}
    }
  };

  useEffect(() => {
    if (domainObjId) {
      queryDomainDataModelListFn(domainObjId);
    }
  }, [domainObjId]);

  const [enumDetailData, setEnumDetailData] = useState<any>({});
  const enumManagementDrawerRef = useRef<any>(null);

  const handleEditClick = async () => {
    try {
      enumManagementDrawerRef?.current?.enterEditMode(enumDetailData, domainObjId);
    } catch (error) {}
  };

  const enterDetailFn = async (enumId: number | null) => {
    try {
      if (enumId) {
        await queryDomainEnumDetailService({ enumId }).then((res: any) => {
          const { success = false, data = {} } = res;
          if (success) {
            setEnumDetailData(data);
          }
        });
      }
    } catch (error) {}
  };

  useEffect(() => {
    enterDetailFn(enumId);
  }, [enumId]);

  const basicInfo = useMemo(() => {
    const { enumName = '', enumType = '', creationSrc = '', comments = '', dataModelEnum = {} } = enumDetailData;

    let dataModelInfo = {};
    let isDataModel = false;

    if (enumType === 'M') {
      isDataModel = true;
      const { valueColumnName = '', nameColumnName = '', modelId } = dataModelEnum;

      dataModelInfo = {
        dataModelName: dataModelList[modelId],
        valueColumnName,
        nameColumnName,
      };
    }

    return {
      enumName,
      enumType: EnumTypeStatus?.[enumType],
      creationSrc: CreationSource?.find((i: { key: string; name: string }) => i.key === creationSrc)?.name,
      comments,
      isDataModel,
      ...dataModelInfo,
    };
  }, [enumDetailData, dataModelList]);

  const afterSubmit = () => {
    enterDetailFn(enumId);
    onRefresh(key);
  };

  return (
    <div className={styles.enumManagementListDetail}>
      <BssDomainTopContent
        selectedDomainNode={selectedDomainNode}
        onSelectDomianData={(data) => {
          onRefresh?.(data.key);
        }}
      />
      <div className={styles.detailItem}>
        <BasicInfomation basicInfo={basicInfo} isDraft={isDraft} handleClick={handleEditClick} />
      </div>
      {enumDetailData?.enumType === 'T' && (
        <div className={styles.detailItem}>
          <ConstantListTable constantListDataSource={enumDetailData?.constEnumList} />
        </div>
      )}
      <div className={styles.detailItem}>
        <EntitiesUsingTable usingEnumEntityProp={enumDetailData?.usingEnumEntityProp} />
      </div>
      <EnumManagementDrawer
        key={`enumManagementDetail-enumManagementDrawer-${enumId}`}
        domainObjId={domainObjId}
        isProduct={isProduct}
        ref={enumManagementDrawerRef}
        afterSubmit={afterSubmit}
      />
    </div>
  );
};

export default EnumManagementDetail;
