import { DetailLayout } from '@/components';
import { IServiceDetail } from '@/services/typing';

import DetailBase from './DetailBase';
interface IServiceDetailCom {
  fromType: string; // BYCATALOG | BYDOMAIN
  serviceDetail?: IServiceDetail;
  onReturn?: () => void;
  switchToDebugMode?: () => void;
  debugMode?: boolean;
  debugResData?: { responses: [] };
  requestDataSource?: any;
  ButtonGroupFnObj?:
    | {
        [k in 'debugServiceFn' | 'onSaveAsFn']: (e: any) => any;
      }
    | any;
}

const BssDetailBase: React.FC<IServiceDetailCom> = ({
  onReturn = () => {},
  serviceDetail,
  debugMode = false,
  debugResData = { responses: [] },
  requestDataSource = {},
  ButtonGroupFnObj = {},
  fromType = '',
}) => {
  return (
    <DetailLayout
      title={`${serviceDetail?.serviceName}${debugMode ? ' Debug' : ''}` || ''}
      onReturn={() => {
        onReturn?.();
      }}
    >
      <DetailBase
        fromType={fromType}
        serviceDetail={serviceDetail}
        debugMode={debugMode}
        debugResData={debugResData}
        requestDataSource={requestDataSource}
        ButtonGroupFnObj={ButtonGroupFnObj}
      />
    </DetailLayout>
  );
};

export default BssDetailBase;
