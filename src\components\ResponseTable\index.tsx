import React, { useEffect, useState } from 'react';
import { IResponse, IServiceDetail } from '@/services/typing.d';
import SingleResponseTable, { ITableDataInterface } from './SingleResponseTabel';
import ResponseStatusTable from './ResponseStatusTable';
import { Flex, Tabs } from 'antd';
import { BlockTitle, CodeMirrorRender } from '@/components';
import useI18n from '@/hooks/useI8n';
import { saveAsUseCaseServicePropsType } from '@/services';
import ResizableTable from '../ResizableTable';
import getUUid from '@/utils/getUUid';

interface IResponseTable {
  isHtmlReject?: boolean;
  serviceDetailData?: IServiceDetail;
  debugMode?: boolean;
  responseInfo?: Pick<saveAsUseCaseServicePropsType, 'serviceRespParamList'> | any;
}

interface IResponseHeaderDataSourceType {
  name: string;
  value: any;
  rowKey: string;
}

const ResponseTable: React.FC<IResponseTable> = (props) => {
  const { serviceDetailData, debugMode = false, responseInfo = [], isHtmlReject = false } = props;
  const { formatMessage } = useI18n();

  const [responseData, setResponseData] = useState<Array<ITableDataInterface>>([]); // response数据
  const [responseHeaderData, setResponseHeaderData] = useState<Array<ITableDataInterface>>([]); // response header数据(debug前)
  const [responseHeaderDataSource, setResponseHeaderDataSource] = useState<Array<IResponseHeaderDataSourceType>>([]); // response header数据(debug后)
  const [activerKey, setActiveKey] = useState(''); // body 示例的当前tab
  const [activeCode, setActiveCode] = useState(JSON.stringify('{}')); // body tab item 对应的codemirror示例参数

  // entity properties type column
  const renderReponse = (item: IResponse) => {
    let type = item?.dataModel?.modelName || item.dataType || '';
    if (item?.isArray === 'Y') {
      return item?.dataModel?.modelName ? `List<${type}>` : `${type}[]`;
    }
    return type;
  };

  // 递归处理response数据
  const processResponseData: (data: Array<IResponse>, pName?: string, pIndex?: number) => Array<ITableDataInterface> = (
    data,
    pName,
    pIndex,
  ) => {
    return data?.map((item, index) => {
      return {
        rowKey: `${pName || ''}-${pIndex || ''}-${item.name}-${index}`,
        type: renderReponse(item),
        name: item.name,
        required: item.required,
        example: item.example,
        avlValue: item.avlValue,
        comments: item.comments,
        respCode: item.respCode,
        typeFormat: item?.typeFormat,
        dataModel: item?.dataModel,
        contentType: item?.contentType,
        children:
          item?.dataModel && item?.dataModel?.properties?.length > 0
            ? processResponseData(item.dataModel.properties, item.name, index)
            : undefined,
      };
    });
  };

  // 切换tab
  const switchTab = (key: any) => {
    setActiveKey(key);
    const filterArr = responseData.filter((item) => {
      return item.rowKey === key;
    });
    setActiveCode(filterArr[0].example || '{}');
  };

  useEffect(() => {
    // 开始筛选response数据
    if (serviceDetailData?.responses?.length) {
      const responseAfterProcess = processResponseData(
        serviceDetailData?.responses?.filter((item) => (item?.respIn ? item?.respIn === 'B' : true)), // 暂时兼容新电环境，后台完成后恢复
      );
      const responseHeaderAfterProcess = processResponseData(
        serviceDetailData?.responses?.filter((item) => item?.respIn === 'H'),
      );
      setResponseData(responseAfterProcess);
      setResponseHeaderData(responseHeaderAfterProcess);
      //   如果有返回码 设置第一个展示的tab + codemirror
      if (responseAfterProcess?.length) {
        setActiveKey(responseAfterProcess?.[0]?.rowKey);
        setActiveCode(responseAfterProcess?.[0]?.example || '{}');
      }
    }
  }, [serviceDetailData]);

  // 响应header表格列
  const responseHeaderColumns = [
    {
      dataIndex: 'name',
      title: formatMessage('PROJECT.COMMON.NAME'),
      width: '50%',
    },
    {
      dataIndex: 'value',
      title: formatMessage('PROJECT.COMMON.VALUE'),
      width: '50%',
    },
  ];

  // 从responseInfo中获取header数据
  useEffect(() => {
    if (responseInfo?.[0]?.respHeaders) {
      const dataSource = Object.entries(responseInfo?.[0]?.respHeaders || {})?.map(([name, value]) => ({
        name,
        value,
        rowKey: `${name}-${value}`, // 为表格添加唯一key
      }));
      setResponseHeaderDataSource(dataSource || []);
    }
  }, [responseInfo]);

  return (
    <div>
      {/* 特殊操作 勿删 给后面计算列的宽度能否满足字符串用的 */}
      <div style={{ visibility: 'hidden', height: 0 }}>
        <SingleResponseTable tableData={[]} />
      </div>
      {debugMode ? (
        <>
          <ResponseStatusTable responseInfo={responseInfo} />
          {/* 响应Header表格 */}
          <BlockTitle>{formatMessage('SERVICEDETAIL.RESPONSE.TITLEHEADER')}</BlockTitle>
          <ResizableTable
            key="responseHeaderTable"
            size="small"
            columns={responseHeaderColumns}
            dataSource={responseHeaderDataSource}
            pagination={false}
            rowKey={(record: any) => record?.rowKey}
          />
          {/* 响应Body，不同状态返回码对应的example codemirror */}
          <BlockTitle>{formatMessage('SERVICEDETAIL.RESPONSE.TITLEBODY')}</BlockTitle>
          <CodeMirrorRender isHtmlReject={isHtmlReject} value={responseInfo?.[0]?.respMsg} />
        </>
      ) : (
        <>
          {/* 不同状态返回码的的数据 */}
          {responseData?.length ? (
            <Tabs
              defaultActiveKey={activerKey}
              onChange={switchTab}
              activeKey={activerKey}
              size="small"
              tabBarStyle={{ marginBottom: 12 }}
              items={responseData.map((resItem) => ({
                label: resItem.respCode,
                key: resItem.rowKey,
                children: (
                  <div>
                    {/* 响应header参数 */}
                    {responseHeaderData?.filter((item) => item?.respCode === resItem?.respCode)?.length > 0 && (
                      <>
                        <BlockTitle>Header</BlockTitle>
                        <SingleResponseTable
                          isHeader={true}
                          tableData={responseHeaderData?.filter((item) => item?.respCode === resItem?.respCode)}
                        />
                      </>
                    )}
                    {/* 响应body参数 */}
                    <BlockTitle>Body</BlockTitle>
                    <div style={{ marginBottom: '0.5rem' }}>
                      <span>Content Type: {resItem?.contentType}</span>
                    </div>
                    <SingleResponseTable tableData={[resItem]} />
                    {resItem?.dataModel && (
                      <div>
                        {/* 不同状态返回码对应的example codemirror */}
                        <Flex align="center" gap={20}>
                          <BlockTitle>{formatMessage('SERVICEDETAIL.RESPONSE.TITLEEXAMPLE')}</BlockTitle>
                          <span>{formatMessage('SERVICEDETAIL.RESPONSE.EXAMPLENOTE')}</span>
                        </Flex>
                        <CodeMirrorRender isHtmlReject={isHtmlReject} value={activeCode} />
                      </div>
                    )}
                  </div>
                ),
              }))}
            />
          ) : null}
        </>
      )}
    </div>
  );
};

export default ResponseTable;
