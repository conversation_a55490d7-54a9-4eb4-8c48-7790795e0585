import React, { useState, useEffect } from 'react';
import { Button, Drawer, Form, Input, Select, Space, Spin, message } from 'antd';
import { qryEntityPropList, addDomainEntityExpand } from '@/services/entityService';
import { CloseOutlined } from '@ant-design/icons';
import { SelectedEntityProps, RelationTypeSourceProps, DomainInfoProps } from '../../types';
import useI18n from '@/hooks/useI8n';

interface IAddExpandEntityDrawer {
  isProduct: boolean;
  relationTypeSource: RelationTypeSourceProps[];
  currentDomainInfo: DomainInfoProps[];
  selectedEntity: SelectedEntityProps;
  open: boolean;
  ignoreVersionSource: any;
  onCancel: () => void;
  onOk: () => void;
}

const AddExpandEntityDrawer: React.FC<IAddExpandEntityDrawer> = (props) => {
  const {
    open,
    ignoreVersionSource,
    selectedEntity,
    isProduct,
    relationTypeSource,
    currentDomainInfo,
    onCancel,
    onOk,
  } = props;
  const { formatMessage } = useI18n();
  const [form] = Form.useForm();
  const [submitSpinning, setSubmitSpinning] = useState<boolean>(false);
  const [formValues, setFormValues] = useState<any>({}); // formValues
  const { Option, OptGroup } = Select;
  const [expandPropList, setExpandPropList] = React.useState<any>([]); // 扩展实体的属性数据源

  const [expandRelType, setExpandRelType] = React.useState<string>('');
  const [expandName, setExpandName] = React.useState<string>('');
  const [currentEntityPropList, setCurrentEntityPropList] = React.useState<any>([]); // 当前实体的属性数据源

  const CREATIONSOURCE = [
    { name: 'Product-Owned', key: 'P' },
    { name: 'Project-Customized', key: 'C' },
  ];

  const initValues = {
    creationSrc: isProduct ? 'P' : 'C',
    odhEntityRelPropList: [{ propId: '', expandPropId: '' }], // 默认展示一项
    entityName: selectedEntity?.entityName,
  };

  // modal close
  const onClose = async () => {
    form.resetFields();
    onCancel?.();
  };

  const addExpandEntity = async () => {
    setSubmitSpinning(true);
    try {
      // 调用新增实体扩展接口
      const curPostData = {
        ...formValues,
        ignoreVer: formValues?.ignoreVer?.length ? formValues?.ignoreVer?.join(',') : null, // 处理ignoreVer显示空选项的bug
        entityId: selectedEntity.entityId,
      };
      const resultData = await addDomainEntityExpand({ entityId: selectedEntity.entityId }, curPostData);

      if (resultData?.success) {
        message.success(formatMessage('EXPANDENTITY.ADD.SUCCESS'));
        form.resetFields();
        onOk?.();
      }
    } finally {
      setSubmitSpinning(false);
    }
  };

  const hasDuplicate = (relationshipArr: any) => {
    return relationshipArr.some(
      (item: any, index: number) =>
        relationshipArr.findIndex((item2: any) => JSON.stringify(item) === JSON.stringify(item2)) !== index,
    );
  };

  const checkAddExpandData = () => {
    form.validateFields().then(() => {
      // 根据对应关系，校验Associated Relationship的长度
      const relationshipLength = formValues.odhEntityRelPropList?.length;
      if (relationshipLength === 0) {
        message.info(formatMessage('EXPANDENTITY.ADD.RELATIONSHIP_REQUIRED'));
        return;
      }
      // 校验Associated Relationship中是否存在相同的关联关系
      const odhEntityRelPropArr = formValues.odhEntityRelPropList.map(
        ({ propId, expandPropId }: { propId: any; expandPropId: any }) => ({
          propId,
          expandPropId,
        }),
      );
      if (hasDuplicate(odhEntityRelPropArr)) {
        message.info(formatMessage('EXPANDENTITY.ADD.RELATIONSHIP_DUPLICATE'));
        return;
      }
      addExpandEntity();
    });
  };

  const queryExpandEntityPropList = async (value: any) => {
    if (value) {
      const { success, data } = await qryEntityPropList(value);
      if (success) {
        setExpandPropList(data);
      } else {
        setExpandPropList([]);
      }
    }
  };

  const onExpandEntityChange = (value: any, option: any) => {
    // 查询扩展实体的属性

    form.setFieldsValue({
      'odhEntityRelPropList.expandPropId': undefined,
    });
    queryExpandEntityPropList(value);
    // 设置expandName
    const expandNameVal = option.key.charAt(0).toLowerCase() + option.key.slice(1);
    setExpandName(expandNameVal);
    if (expandNameVal.endsWith('s')) {
      form.setFieldsValue({
        propName: expandNameVal,
      });
    } else if (expandRelType === 'N') {
      form.setFieldsValue({
        propName: `${expandNameVal}s`,
      });
    } else {
      form.setFieldsValue({
        propName: expandNameVal,
      });
    }
  };

  const onRelTypeChange = (value: any, option: any) => {
    const relType = option.children?.split('->');
    setExpandRelType(relType?.length > 1 ? relType[1].trim() : '');
  };

  const queryCurrentEntityPropList = async () => {
    if (selectedEntity.entityId) {
      const { success, data } = await qryEntityPropList(selectedEntity.entityId);
      if (success) {
        setCurrentEntityPropList(data);
      } else {
        setCurrentEntityPropList([]);
      }
    }
  };

  useEffect(() => {
    if (expandRelType === '1') {
      form.setFieldsValue({
        propName: expandName,
      });
    } else if (expandRelType === 'N') {
      if (expandName) {
        if (expandName.endsWith('s')) {
          form.setFieldsValue({
            propName: expandName,
          });
        } else {
          form.setFieldsValue({
            propName: `${expandName}s`,
          });
        }
      }
    }
  }, [expandRelType]);

  useEffect(() => {
    // 查询当前实体的主键字段
    if (open) {
      queryCurrentEntityPropList();
    }
  }, [open]);

  return (
    <Drawer
      title={formatMessage('EXPANDENTITY.ADD.TITLE')}
      onClose={onClose}
      maskClosable={false}
      open={open}
      width={720}
      afterOpenChange={() => {
        form.resetFields();
      }}
      footer={
        <Space style={{ float: 'right' }}>
          <Spin spinning={submitSpinning}>
            <Button type="primary" onClick={checkAddExpandData}>
              {formatMessage('EXPANDENTITY.ADD.SUBMIT')}
            </Button>
          </Spin>
          <Button onClick={onClose}>{formatMessage('EXPANDENTITY.ADD.CANCEL')}</Button>
        </Space>
      }
    >
      <Form
        form={form}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        initialValues={initValues}
        onValuesChange={(cValues, alValues) => {
          setFormValues(alValues);
        }}
      >
        <Form.Item
          label={formatMessage('EXPANDENTITY.ADD.CURRENT_ENTITY')}
          name="entityName"
          rules={[{ required: true, message: '' }]}
        >
          <Input disabled />
        </Form.Item>
        <Form.Item
          label={formatMessage('EXPANDENTITY.ADD.RELATIONSHIP_TYPE')}
          name="relType"
          rules={[{ required: true, message: '' }]}
        >
          <Select onChange={onRelTypeChange}>
            {relationTypeSource.map((i) => (
              <Select.Option key={i.relType} value={i.relType}>
                {i.relTypeName}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          label={formatMessage('EXPANDENTITY.ADD.EXPAND_ENTITY')}
          name="expandEntityId"
          rules={[{ required: true, message: '' }]}
        >
          <Select onChange={onExpandEntityChange}>
            {currentDomainInfo.map((group) => (
              <OptGroup key={group.domainObjId} label={group.domainObjName}>
                {group.entityList.map(
                  (option) =>
                    option.entityType !== 'S' && (
                      <Option
                        key={option.entityCode}
                        value={option.entityId}
                        disabled={option.entityId === selectedEntity.entityId}
                      >
                        {option.entityName}
                      </Option>
                    ),
                )}
              </OptGroup>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          // 这个Item是一个容器，加上rule属性必填样式不会生效，需手动添加样式
          label={
            <span>
              <span
                style={{
                  display: 'inline-block',
                  marginInlineEnd: 4,
                  color: '#ff4d4f',
                  fontSize: 14,
                  fontFamily: 'SimSun, sans-serif',
                  lineHeight: 1,
                }}
              >
                *
              </span>
              {formatMessage('EXPANDENTITY.ADD.ASSOCIATED_RELATIONSHIP')}
            </span>
          }
        >
          <Form.List name="odhEntityRelPropList">
            {(subFields, subOpt) => (
              <div style={{ display: 'flex', flexDirection: 'column', rowGap: 16 }}>
                {subFields.map((subField) => (
                  <Space key={subField.key}>
                    <Form.Item noStyle name={[subField.name, 'propId']} rules={[{ required: true, message: '' }]}>
                      <Select style={{ width: '150px' }}>
                        {currentEntityPropList.map((i: any) => (
                          <Select.Option key={i.propId} value={i.propId}>
                            {i.propName}
                          </Select.Option>
                        ))}
                      </Select>
                    </Form.Item>
                    <Form.Item noStyle name={[subField.name, 'expandPropId']} rules={[{ required: true, message: '' }]}>
                      <Select style={{ width: '150px', marginLeft: '5px' }}>
                        {expandPropList.map((i: any) => (
                          <Select.Option key={i.propId} value={i.propId}>
                            {i.propName}
                          </Select.Option>
                        ))}
                      </Select>
                    </Form.Item>
                    <CloseOutlined
                      onClick={() => {
                        subOpt.remove(subField.name);
                      }}
                    />
                  </Space>
                ))}
                <Button type="dashed" onClick={() => subOpt.add()} block>
                  {formatMessage('EXPANDENTITY.ADD.ADD_RELATIONSHIP')}
                </Button>
              </div>
            )}
          </Form.List>
        </Form.Item>
        <Form.Item
          label={formatMessage('EXPANDENTITY.ADD.EXPAND_NAME')}
          name="propName"
          rules={[
            { required: true, message: '' },
            {
              validator: (_, value) => {
                const regex = /^[a-z][a-zA-Z0-9]*$/;
                if (!regex.test(value)) {
                  return Promise.reject(new Error(formatMessage('EXPANDENTITY.ADD.NAME_FORMAT_ERROR')));
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          {/* 默认生成 */}
          <Input />
        </Form.Item>
        <Form.Item label={formatMessage('EXPANDENTITY.ADD.CREATION_SOURCE')} name="creationSrc">
          <Select disabled>
            {CREATIONSOURCE.map((i) => (
              <Select.Option key={i.key} value={i.key}>
                {i.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        {isProduct ? (
          <Form.Item label={formatMessage('EXPANDENTITY.ADD.IGNORE_VERSION')} name="ignoreVer">
            <Select mode="multiple">
              {ignoreVersionSource?.map((i: any) => (
                <Select.Option key={i.key} value={i.key}>
                  {i.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        ) : null}
        <Form.Item
          label={formatMessage('EXPANDENTITY.ADD.DESCRIPTIONS')}
          name="comments"
          rules={[{ required: true, message: '' }]}
        >
          <Input.TextArea allowClear />
        </Form.Item>
      </Form>
    </Drawer>
  );
};
export default AddExpandEntityDrawer;
