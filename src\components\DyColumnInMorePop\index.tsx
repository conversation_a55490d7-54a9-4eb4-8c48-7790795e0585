import React from "react";
import classNames from "classnames";
import AvlValueTableInPop from "@/components/AvlValueTableInPop";
import styles from "./index.less";

interface IDyColumnInMorePop {
  columnClaassName: string;
  value: string;
  popContentType: "array" | "string";
}

const DyColumnInMorePop: React.FC<IDyColumnInMorePop> = (props) => {
  const { columnClaassName, value, popContentType } = props;

  // 表格容器td宽度
  const containerWidth =
    (document.getElementsByClassName?.(`${columnClaassName}`)?.[0]?.scrollWidth ||
      0) - 16;
  // 模拟出一个隐藏元素(宽度随着内容变化)
  // 放入内容value
  // 计算出放入后内容的宽度
  // 销毁
  const mockContainerParent = document.querySelectorAll(
    `thead .ant-table-cell.${columnClaassName}.ant-table-cell-ellipsis`
  )?.[0];
  const mockContainer = document.createElement("div");
  mockContainer.innerHTML = value;
  mockContainer.style.fontWeight = "400";
  mockContainer.style.width = "fit-content";
  mockContainerParent.appendChild(mockContainer);
  const mockWidth = mockContainer.offsetWidth;
  mockContainer.remove();
  // 与表格容器td宽度做对比
  let overFlowFlag = false;
  if (mockWidth >= containerWidth) {
    overFlowFlag = true;
  }

  return (
    <div className={styles.avlValue}>
      <div
        className={classNames(styles.value, 'valueContainer')}
        dangerouslySetInnerHTML={{ __html: `${value}` }}
      ></div>
      {overFlowFlag ? (
        <AvlValueTableInPop title="Detail" avlValue={value} popContentType={popContentType}>
          <div className={styles.more}>more</div>
        </AvlValueTableInPop>
      ) : null}
    </div>
  );
};

export default DyColumnInMorePop;
