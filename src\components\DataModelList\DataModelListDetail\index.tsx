import BasicInfomation from './BasicInfomation';
import EntitiesUsingTable from './EntitiesUsingTable';
import ViewSqlTable from './ViewSqlTable';
import EnumsUsingTable from './EnumsUsingTable';
import styles from './index.less';
import { useEffect, useMemo, useState, useRef } from 'react';
import DataModelDrawer from '../DataModelDrawer';
import { Status, CreationSource } from '../const';
import { useModel } from 'umi';
import { getDomainDataModelDetailService } from '@/services/dataModelService';
import BssDomainTopContent from '@/pages/BssODomainApi/BssDomainTopContent';

interface IDataModelListDetail {
  selectedDomainNode: any;
  selectedRootNode: any;
  isProduct: boolean;
  onRefresh: (currentSelectKey: string) => void;
}

const DataModelListDetail: React.FC<IDataModelListDetail> = ({
  selectedDomainNode = {},
  isProduct = false,
  selectedRootNode = {},
  onRefresh = () => {},
}) => {
  // 环境基本信息
  const { classesInfo = [] } = useModel('useDomainDataModelListModel');

  const DataModelDrawerRef = useRef<any>(null);

  const { modelId = null, key = null, domainObjId = null } = selectedDomainNode;

  const { domainId = null } = selectedRootNode;

  const [dataModelDetailData, setDataModelDetailData] = useState<any>({});

  const isDraft = useMemo(() => {
    return selectedRootNode?.state === 'D';
  }, [selectedRootNode]);

  const editDrawerFn = async () => {
    try {
      DataModelDrawerRef?.current?.enterEditMode(dataModelDetailData);
    } catch (error) {}
  };

  const enterDetailfn = async (modelId: number) => {
    try {
      await getDomainDataModelDetailService(modelId).then((res) => {
        const { success = false, data = {} } = res;
        if (success) {
          setDataModelDetailData(data);
        }
      });
    } catch (error) {}
  };

  useEffect(() => {
    if (modelId) {
      enterDetailfn(modelId);
    }
  }, [modelId]);

  const basicInfo = useMemo(() => {
    const {
      modelName = '',
      modelCode = '',
      modelType = '',
      creationSrc = '',
      comments = '',
      modelClass = '',
    } = dataModelDetailData;

    const tempClass = classesInfo?.filter((el) => el?.modelClass === modelClass);

    return {
      modelName,
      modelCode,
      modelClass: tempClass?.[0]?.modelClassName,
      modelType: Status?.find((i: { key: string; name: string }) => i.key === modelType)?.name,
      creationSrc: CreationSource?.find((i: { key: string; name: string }) => i.key === creationSrc)?.name,
      comments,
    };
  }, [dataModelDetailData, classesInfo]);

  const afterSubmit = () => {
    enterDetailfn(modelId);
    onRefresh(key);
  };

  return (
    <div className={styles.dataModelListDetail}>
      <BssDomainTopContent
        selectedDomainNode={selectedDomainNode}
        onSelectDomianData={(data) => {
          onRefresh?.(data.key);
        }}
      />
      <div className={styles.detailItem}>
        <BasicInfomation basicInfo={basicInfo} isDraft={isDraft} handleClick={editDrawerFn} />
      </div>
      {dataModelDetailData?.modelType === 'V' && (
        <div className={styles.detailItem}>
          <ViewSqlTable viewDataSource={dataModelDetailData?.viewSqlList} />
        </div>
      )}
      <div className={styles.detailItem}>
        <EntitiesUsingTable entitiesDataSource={dataModelDetailData?.usingEntityList} />
      </div>
      <div className={styles.detailItem}>
        <EnumsUsingTable enumsDataSource={dataModelDetailData?.usingEnumList} />
      </div>
      <DataModelDrawer
        key={`dataModelDetail-DataModelDrawer-${modelId}`}
        domainId={domainId}
        domainObjId={domainObjId}
        ref={DataModelDrawerRef}
        afterSubmit={afterSubmit}
        isProduct={isProduct}
      />
    </div>
  );
};

export default DataModelListDetail;
