import OperationIconTitle from '@/components/OperationIconTitle';
import { Flex, FormInstance, message, Pagination, Popconfirm, Tooltip } from 'antd';
import React, { useEffect, useState } from 'react';
import styles from './index.less';
import useI18n from '@/hooks/useI8n';
import ResizableTable from '@/components/ResizableTable';
import { DeleteOutlined, FileSearchOutlined, FormOutlined } from '@ant-design/icons';
import classNames from 'classnames';

import AddErrorCode from './AddErrorCode';
import { useModel } from 'umi';
import EditErrorCode from './EditErrorCode';
import myIcon from '@/static/iconfont/iconfont.css';
import RelatedApi from './RelatedApi';
import { delErrorCode } from '@/services/errorCodeService';
import { ErrorCodeListDataType } from '../types';
import { CREATIONSOURCE } from '../constant';
import { isHotfix } from '@/global';

interface IErrorCodeList {
  odhDomainTreeData: any;
  isProduct: boolean;
  errorCodeListData: ErrorCodeListDataType;
  openFromBssODataApi?: boolean; // 是否从BssODataApi页面打开
  setIsShowOtherErrorDetail: (isShowOtherErrorDetail: boolean) => void; // 设置是否展示其他错误详情
  handleReset: (params: { isRelated: boolean }) => Promise<void>;
}

const ErrorCodeList: React.FC<IErrorCodeList> = (props) => {
  const {
    odhDomainTreeData,
    errorCodeListData,
    isProduct,
    openFromBssODataApi = false,
    setIsShowOtherErrorDetail,
    handleReset,
  } = props;

  const { formatMessage } = useI18n();
  const { queryErrorCodeListParams, setQueryErrorCodeListParams, queryErrorCodeList, setErrorCodeSpinning } =
    useModel('useErrorCodeModel');
  const { isConfigEnvironment } = useModel('useSystemConfigDataModel');
  const [selectedErrorCode, setSelectedErrorCode] = useState<any>();
  const [addOpen, setAddOpen] = useState<boolean>(false);
  const [editOpen, setEditOpen] = useState<boolean>(false);
  const [relatedOpen, setRelatedOpen] = useState<boolean>(false);

  const deleteErrorCode = async (errCodeId: number) => {
    try {
      setErrorCodeSpinning(true);
      if (errCodeId) {
        const { success } = await delErrorCode({ errorCodeId: errCodeId });
        if (success) {
          message.success(formatMessage('PROJECT.COMMON.DELETESUCCESS'));
          // 更新数据
          queryErrorCodeList({
            ...queryErrorCodeListParams,
          });
        }
      }
    } catch (error) {
      console.error(error);
    } finally {
      setErrorCodeSpinning(false);
    }
  };

  const columns = [
    {
      dataIndex: 'errorCode',
      title: formatMessage('ERRORCODE.COMMON.ERRORCODE'),
      width: '13%',
      ellipsis: true,
    },
    {
      dataIndex: 'errorReason',
      title: formatMessage('ERRORCODE.COMMON.ERRORREASON'),
      width: '30%',
      ellipsis: true,
    },
    {
      dataIndex: 'domainName',
      title: formatMessage('ERRORCODE.COMMON.APPLYDOMAIN'),
      width: '13%',
      ellipsis: true,
      render: (_: string, record: any) => {
        return record?.domainName ? record.domainName : 'All';
      },
    },
    {
      dataIndex: 'creationSrc',
      title: formatMessage('ERRORCODE.COMMON.CREATIONSOURCE'),
      width: '12%',
      ellipsis: true,
      render: (_: string, record: any) => {
        return CREATIONSOURCE.filter((i) => i.key === record.creationSrc).map((i) => i.name);
      },
    },
    {
      dataIndex: 'createdDate',
      title: formatMessage('ERRORCODE.LIST.CREATED_DATE'),
      width: '12%',
      ellipsis: true,
    },
    {
      dataIndex: 'updateDate',
      title: formatMessage('ERRORCODE.LIST.UPDATE_DATE'),
      width: '12%',
      ellipsis: true,
    },
    {
      dataIndex: '',
      title: formatMessage('PROJECT.COMMON.OPERATION'),
      width: '10%',
      render: (_: string, record: any) => {
        if (record?.errorCodeId < 0) return; // 预制的错误码，不能进行任何操作
        const showDeleteIcon = isProduct
          ? !openFromBssODataApi && isConfigEnvironment
          : !openFromBssODataApi && isConfigEnvironment && record?.creationSrc !== 'P';
        return (
          <div>
            {/* 修改按钮 */}
            {isConfigEnvironment && !openFromBssODataApi && (
              <Tooltip title={formatMessage('PROJECT.COMMON.EDIT')}>
                <FormOutlined
                  className={classNames(styles.iconStyle)}
                  onClick={() => {
                    setEditOpen(true);
                  }}
                />
              </Tooltip>
            )}
            {/* 查询关联API按钮 */}
            <Tooltip title={formatMessage('ERRORCODE.RELATED.TITLE')}>
              <span
                onClick={() => {
                  setRelatedOpen(true);
                }}
                className={`${myIcon.iconfont} ${myIcon['icon-related-api']} ${styles.versionIcon2}`}
              />
            </Tooltip>
            {/* 删除按钮及提示 */}
            <Popconfirm
              title={formatMessage('ERRORCODE.DELETE.TITLE')}
              description={formatMessage('ERRORCODE.DELETE.CONFIRM')}
              onConfirm={() => {
                deleteErrorCode(record?.errorCodeId);
              }}
              okText={formatMessage('ERRORCODE.DELETE.YES')}
              cancelText={formatMessage('ERRORCODE.DELETE.NO')}
            >
              {/* {isConfigEnvironment && !openFromBssODataApi && record?.creationSrc !== 'P' && (
                <Tooltip title={formatMessage('PROJECT.COMMON.DELETE')}>
                  <DeleteOutlined className={classNames(styles.versionIcon2)} onClick={() => {}} />
                </Tooltip>
              )} */}
              {showDeleteIcon && (
                <Tooltip title={formatMessage('PROJECT.COMMON.DELETE')}>
                  <DeleteOutlined className={classNames(styles.versionIcon2)} onClick={() => {}} />
                </Tooltip>
              )}
            </Popconfirm>
          </div>
        );
      },
    },
  ];

  useEffect(() => {
    if (errorCodeListData?.totalCount && errorCodeListData?.list?.length === 0) {
      setQueryErrorCodeListParams((pre) => ({
        ...pre,
        pageNo: 1,
      }));
      queryErrorCodeList({
        ...queryErrorCodeListParams,
        pageNo: 1,
      });
    }
  }, [errorCodeListData]); // 处理特殊场景：接口返回的totalCount不为0但list为空。例如添加了过滤条件，还未点击查询按钮，直接跳转到该过滤条件下不存在的页码。

  return (
    <>
      <div>
        <Flex align="center" gap={15}>
          <OperationIconTitle
            title={formatMessage('ERRORCODE.ERRORCODELIST.TITLE')}
            opt={formatMessage('ERRORCODE.ADD')}
            handleClick={() => {
              setAddOpen(true);
            }}
            type={!openFromBssODataApi && isConfigEnvironment ? 'add' : ''}
          />
          {!isHotfix && (
            <div>
              <span
                onClick={() => {
                  setIsShowOtherErrorDetail(true);
                }}
              >
                <FileSearchOutlined className={styles.operateText} />
                <span className={styles.operateText}>{formatMessage('ERRORCODE.OTHERERRORDETAIL.TITLE')}</span>
              </span>
            </div>
          )}
        </Flex>
        <ResizableTable
          size="small"
          style={{ marginTop: '12px' }}
          columns={columns}
          dataSource={errorCodeListData.list || []}
          rowKey={(record: any) => record?.errorCodeId}
          pagination={false}
          // 使用Table的pagination属性，dataSource为空时默认不展示分页，故使用footer
          footer={() => (
            <Flex justify="flex-end" style={{ backgroundColor: 'rgb(238, 238, 238, 0)' }}>
              <Pagination
                total={errorCodeListData?.totalCount}
                current={queryErrorCodeListParams?.pageNo}
                pageSize={queryErrorCodeListParams?.pageSize}
                pageSizeOptions={[5, 10, 15, 20]}
                showQuickJumper
                showSizeChanger
                hideOnSinglePage={false}
                showTotal={(total) => formatMessage('PROJECT.COMMON.PAGINATION.TOTAL', { total })}
                onChange={(page, pageSize) => {
                  setQueryErrorCodeListParams((pre) => ({
                    ...pre,
                    pageSize,
                    pageNo: page,
                  }));
                  queryErrorCodeList({
                    ...queryErrorCodeListParams,
                    pageSize,
                    pageNo: page,
                  });
                }}
              />
            </Flex>
          )}
          onRow={(record: any) => ({
            onClick: () => {
              if (record?.domainId) {
                setSelectedErrorCode(record);
              } else {
                // 特殊处理 all domain
                setSelectedErrorCode({
                  ...record,
                  domainId: 'all',
                  domainName: 'All',
                });
              }
            },
          })}
        />
      </div>
      <AddErrorCode
        isProduct={isProduct}
        addOpen={addOpen}
        odhDomainTreeData={odhDomainTreeData}
        setAddOpen={setAddOpen}
        handleReset={handleReset}
      />
      <EditErrorCode
        editOpen={editOpen}
        odhDomainTreeData={odhDomainTreeData}
        setEditOpen={setEditOpen}
        selectedErrorCode={selectedErrorCode}
        isProduct={isProduct}
      />
      <RelatedApi relatedOpen={relatedOpen} setRelatedOpen={setRelatedOpen} selectedErrorCode={selectedErrorCode} />
    </>
  );
};
export default ErrorCodeList;
