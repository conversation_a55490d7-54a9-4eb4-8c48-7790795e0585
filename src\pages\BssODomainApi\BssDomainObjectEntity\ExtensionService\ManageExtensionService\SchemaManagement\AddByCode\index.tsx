import OperationIconTitle from '@/components/OperationIconTitle';
import { Button, Drawer, Form, message, Space } from 'antd';
import CodeMirror from '@uiw/react-codemirror';
import { javascript } from '@codemirror/lang-javascript';
import { foldGutter } from '@codemirror/language';
import { EditorView } from '@codemirror/view';
import { espresso } from 'thememirror';
import { closeBrackets } from '@codemirror/autocomplete';
import { keymap } from '@codemirror/view';
import { defaultKeymap } from '@codemirror/commands';
import { useState } from 'react';
import { addPropByCode } from '@/services/entityService';
import { useModel } from 'umi';
import useI18n from '@/hooks/useI8n';

const AddByCode = (props: any) => {
  const { open, onCancel, selectedSchema, onAddByCodeOk } = props;
  const [form] = Form.useForm();
  const [code, setCode] = useState(''); // State to hold the code
  const { currentDomainObjId } = useModel('manageExtensionServiceModel');
  const { formatMessage } = useI18n();

  // 弹框关闭
  const onClose = async () => {
    form.resetFields();
    setCode('');
    onCancel?.();
  };

  const addCode = async () => {
    if (currentDomainObjId) {
      const param = {
        schemaId: selectedSchema?.schemaId,
        javaCode: code,
        domainObjId: currentDomainObjId,
      };

      const { success, data } = await addPropByCode(param);
      if (success) {
        message.success(formatMessage('SCHEMAMANAGEMENT.ADD_BY_CODE.SUCCESS'));
        if (data?.propList.length > 0) {
          const propList = data?.propList.map((item: any) => {
            return {
              ...item,
              id: `${Date.now()}-${Math.floor(Math.random() * 10000)}`,
            };
          });
          onAddByCodeOk(propList);
        }
      }
    }
  };
  const handleOk = () => {
    addCode();
    setCode('');
  };
  return (
    <div>
      <Drawer
        title={formatMessage('SCHEMAMANAGEMENT.ADD_BY_CODE.TITLE')}
        onClose={onClose}
        maskClosable={false}
        open={open}
        width={720}
        afterOpenChange={() => {
          form.resetFields();
        }}
        footer={
          <Space style={{ float: 'right' }}>
            <Button type="primary" onClick={() => handleOk()}>
              {formatMessage('PROJECT.COMMON.OK')}
            </Button>
            <Button onClick={onClose}>{formatMessage('SCHEMAMANAGEMENT.CANCEL')}</Button>
          </Space>
        }
      >
        <OperationIconTitle title={formatMessage('SCHEMAMANAGEMENT.ADD_BY_CODE.JAVA_CODE')} />
        <CodeMirror
          value={code}
          height="auto"
          theme={espresso}
          onChange={(value) => setCode(value)}
          extensions={[
            foldGutter(),
            javascript({ jsx: false }),
            closeBrackets(),
            EditorView.lineWrapping,
            keymap.of(defaultKeymap),
          ]}
          basicSetup={{
            foldGutter: true,
            lineNumbers: true,
            autocompletion: true,
            indentOnInput: true,
            bracketMatching: true,
          }}
        />
      </Drawer>
    </div>
  );
};
export default AddByCode;
