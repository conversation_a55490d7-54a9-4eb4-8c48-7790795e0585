import { RouterProvider } from 'react-router-dom'
import { ConfigProvider, App as AntdApp, theme } from 'antd'
import routes from './router'
import AntdGlobal from './utils/AntdGlobal'
import '@ant-design/v5-patch-for-react-19';
import './App.less'
import './styles/theme.less'
import { useStore } from './store';

function App() {
  const isDark = useStore(state => state.isDark)
  return (
    <ConfigProvider
      theme={{
        algorithm: isDark ? theme.darkAlgorithm : theme.defaultAlgorithm,
        token: {
          colorPrimary: '#ed6c00'
        }
      }}>
      <AntdApp>
        <AntdGlobal />
        <RouterProvider router={routes} />
      </AntdApp>
    </ConfigProvider>
  )
}

export default App
