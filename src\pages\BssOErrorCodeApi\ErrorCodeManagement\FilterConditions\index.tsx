import OperationIconTitle from '@/components/OperationIconTitle';
import { Button, Col, Flex, Form, FormInstance, Input, message, Select, Tooltip } from 'antd';
import React, { useEffect } from 'react';
import useI18n from '@/hooks/useI8n';

import { useModel } from 'umi';
import { CommonYesOrNoSource, CREATIONSOURCE } from '../constant';
import { DownloadOutlined } from '@ant-design/icons';
import { generateErrorCodeFile } from '@/services/errorCodeService';
import { omit } from 'lodash';

interface IFilterConditions {
  odhDomainTreeData: any;
  openFromBssODataApi?: boolean; // 是否从BssODataApi页面打开
  filterConditionsForm: FormInstance;
  handleReset: (params: { isRelated: boolean }) => Promise<void>;
}

const FilterConditions: React.FC<IFilterConditions> = (props) => {
  const { odhDomainTreeData = [], openFromBssODataApi = false, filterConditionsForm, handleReset } = props;
  const { formatMessage } = useI18n();
  const { queryErrorCodeListParams, setQueryErrorCodeListParams, queryErrorCodeList, setErrorCodeSpinning } =
    useModel('useErrorCodeModel');

  const handleSearch = async () => {
    setQueryErrorCodeListParams((pre) => ({
      ...pre,
      pageNo: 1,
      pageSize: 10,
    }));
    queryErrorCodeList({
      ...queryErrorCodeListParams,
      pageNo: 1,
      pageSize: 10,
    });
  };

  // 导出为Excel文件
  const handleExport = async () => {
    setErrorCodeSpinning(true);
    let a;
    let url;
    try {
      const { domainId } = queryErrorCodeListParams;
      const params = {
        ...omit(queryErrorCodeListParams, ['pageNo', 'pageSize']), // 移除分页参数
        // 不传domainId，查所有数据；传domainId，且domainId传空时，查询Apply Domain是All的数据；其余情况查domainId对应的数据
        ...(domainId ? { domainId: domainId === 'all' ? '' : domainId } : {}),
      };
      // 调用接口获取文件数据（确保 responseType: 'blob'）
      const response = await generateErrorCodeFile(params);

      // 从响应头中提取文件名
      const contentDisposition = response.headers['content-disposition'];
      let fileName = 'Open API Error Code List.xls'; // 默认值
      if (contentDisposition) {
        // 尝试解析文件名，如果失败则使用默认值,并处理中文文件名可能存在的编码问题
        const fileNameMatch = contentDisposition.match(/filename\*?=['"]?(?:UTF-\d['"]*)?([^;\r\n"']*)['"]?;?/i);
        if (fileNameMatch?.[1]) {
          fileName = decodeURIComponent(fileNameMatch[1]);
        } else {
          const fallbackMatch = contentDisposition.match(/filename="?(.+?)"?(;|$)/i);
          if (fallbackMatch?.[1]) fileName = fallbackMatch[1];
        }
      }

      // 创建下载链接
      url = window.URL.createObjectURL(new Blob([response.data]));
      a = document.createElement('a');
      a.href = url;
      a.download = fileName;
      document.body.appendChild(a);
      a.click();
    } catch (error) {
      console.error('Export Failed:', error);
    } finally {
      // 清理资源
      if (url) window.URL.revokeObjectURL(url);
      if (a) a.remove();
      setErrorCodeSpinning(false);
    }
  };

  const buttonGroup = (
    <Flex justify="flex-end" gap="2rem">
      <Tooltip title={formatMessage('ERRORCODE.EXPORT.TOOLTIP')}>
        <Button icon={<DownloadOutlined />} onClick={handleExport}>
          {formatMessage('ERRORCODE.EXPORT.BUTTON')}
        </Button>
      </Tooltip>
      <Button
        onClick={() => {
          handleReset({ isRelated: true });
        }}
      >
        {formatMessage('PROJECT.COMMON.RESET')}
      </Button>
      <Button type="primary" onClick={handleSearch}>
        {formatMessage('PROJECT.COMMON.SEARCH')}
      </Button>
    </Flex>
  );

  useEffect(() => {
    handleReset({ isRelated: true }); // 初始化查询
  }, []);

  return (
    <div>
      <OperationIconTitle title={formatMessage('ERRORCODE.FILTERCONDITIONS.TITLE')} />
      <Form
        form={filterConditionsForm}
        labelAlign="left"
        style={{ display: 'flex', gap: '4rem' }}
        onValuesChange={(cValues, alValues) => {
          setQueryErrorCodeListParams((pre) => ({ ...pre, ...alValues }));
        }}
      >
        {!openFromBssODataApi && (
          <Col span={7}>
            <Form.Item label={formatMessage('ERRORCODE.COMMON.APPLYDOMAIN')} name="domainId" style={{ marginLeft: 9 }}>
              <Select showSearch allowClear>
                <Select.Option key="all" value="all">
                  All
                </Select.Option>
                {odhDomainTreeData.map((i: any) => (
                  <Select.Option key={i.domainId} value={i.domainId}>
                    {i.domainName}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item label={formatMessage('ERRORCODE.COMMON.CREATIONSOURCE')} name="creationSrc">
              <Select allowClear>
                {CREATIONSOURCE.map((i: any) => (
                  <Select.Option key={i.key} value={i.key}>
                    {i.name}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
        )}
        <Col span={7}>
          <Form.Item label={formatMessage('ERRORCODE.COMMON.ERRORCODE')} name="errorCode" style={{ marginLeft: 31 }}>
            <Input allowClear />
          </Form.Item>
          <Form.Item label={formatMessage('ERRORCODE.COMMON.RELATEDSERVICE')} name="isRelated">
            <Select allowClear>
              {CommonYesOrNoSource.map((i: any) => (
                <Select.Option key={i.value} value={i.value}>
                  {i.label}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Col>
        <Col span={7}>
          <Form.Item label={formatMessage('ERRORCODE.COMMON.ERRORREASON')} name="errorReason">
            <Input allowClear />
          </Form.Item>
          {!openFromBssODataApi && buttonGroup}
        </Col>
        {openFromBssODataApi && <Col span={7}>{buttonGroup}</Col>}
      </Form>
    </div>
  );
};
export default FilterConditions;
