@font-face {
  font-family: "iconfont"; /* Project id 4585001 */
  src: url('iconfont.woff2?t=1751938207002') format('woff2'),
       url('iconfont.woff?t=1751938207002') format('woff'),
       url('iconfont.ttf?t=1751938207002') format('truetype');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-transfer:before {
  content: "\e745";
}

.icon-debug:before {
  content: "\e7f1";
}

.icon-script:before {
  content: "\e6ec";
}

.icon-err-info:before {
  content: "\e60c";
}

.icon-copy:before {
  content: "\ea64";
}

.icon-error-code:before {
  content: "\e79c";
}

.icon-related-api:before {
  content: "\e646";
}

.icon-apig-service:before {
  content: "\e66d";
}

.icon-jurassic_version:before {
  content: "\e695";
}

.icon-add-version:before {
  content: "\e653";
}

.icon-test_version:before {
  content: "\e601";
}

.icon-current_version1:before {
  content: "\e729";
}

.icon-switch_version1:before {
  content: "\e639";
}

.icon-switch_version2:before {
  content: "\e623";
}

.icon-current_version2:before {
  content: "\e62e";
}

.icon-entity:before {
  content: "\e6a7";
}

.icon-data-model:before {
  content: "\e604";
}

.icon-enum:before {
  content: "\e600";
}

.icon-domain-object:before {
  content: "\e63b";
}

.icon-domain:before {
  content: "\e84a";
}

