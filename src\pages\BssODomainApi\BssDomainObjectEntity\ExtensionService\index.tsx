import React, { useState, useEffect, useMemo } from 'react';
import { Spin, Popconfirm, message, Tooltip } from 'antd';
import type { PopconfirmProps } from 'antd';
import { DeleteOutlined, FormOutlined, SwapOutlined } from '@ant-design/icons';
import { history, useModel } from 'umi';
import classNames from 'classnames';
import ResizableTable from '@/components/ResizableTable';
import OperationIconTitle from '@/components/OperationIconTitle';
import AddExtensionServiceDrawer from './AddExtensionService';
import EditExtensionServiceDrawer from './EditExtensionService';
import TransferExtensionServiceDrawer from './TransferExtensionService';
import useI18n from '@/hooks/useI8n';
import styles from '../index.less';
import {
  qryEntityServiceList,
  qryServiceRootPath,
  queryDubboInterfaceDetails,
  deleteDomainEntityService,
  queryServiceCatgList,
  queryServiceSubCatgList,
} from '@/services/entityService';
import { useTableFilter } from '@/hooks/useTableFilter';
import { CommonOptionItemType } from '@/components/SelectWithAddButton/type';
import myIcon from '@/static/iconfont/iconfont.css';

interface IExtensionService {
  selectedEntity?: any; // 当前节点
  selectedRootNode?: any; // 根节点
  isProduct?: boolean;
  ignoreVersionSource: any;
  tfmServices: CommonOptionItemType[];
  handleManage: (data: any) => void;
}

const ExtensionService: React.FC<IExtensionService> = (props) => {
  const {
    selectedEntity,
    ignoreVersionSource,
    selectedRootNode,
    isProduct = false,
    tfmServices = [],
    handleManage,
  } = props;
  const { formatMessage } = useI18n();
  const [spining, setSpining] = useState<boolean>(false); // menu loading

  const [extensionServiceSource, setExtensionServiceSource] = React.useState<any>([]); // 领域实体属性数据源
  const [selectedExtService, setSelectedExtService] = React.useState<any>({}); // 当前选中的实体扩展服务行数据

  const [openAddExtensionServiceDrawer, setOpenAddExtensionServiceDrawer] = useState<boolean>(false); // 新增领域属性
  const [openEditExtensionServiceDrawer, setOpenEditExtensionServiceDrawer] = useState<boolean>(false); // 修改实体属性
  const [openTransferExtensionServiceDrawer, setOpenTransferExtensionServiceDrawer] = useState<boolean>(false); // 迁移扩展服务
  const [serviceCatgSource, setServiceCatgSource] = useState<any>([]); // 服务分类
  const [serviceSubCatgSource, setServiceSubCatgSource] = useState<any>([]); // 服务子分类
  const [dubboClassSource, setDubboClassSource] = useState<any>([]); // dubbo class数据源
  const [serviceRootPath, setServiceRootPath] = useState<string>('');
  const { setIsManageExtensionService } = useModel('manageExtensionServiceModel');

  const CreationSource = [
    { name: 'Product-Owned', key: 'P' },
    { name: 'Project-Customized', key: 'C' },
  ];
  // 针对特殊表格列，自定义过滤逻辑
  const serviceSubCatgFilter = (item: any, obj: any) => {
    if (serviceSubCatgSource.length > 0) {
      const matchedItem = serviceSubCatgSource.find((i: any) => i.serviceSubCatg === item.serviceSubCatg);
      const matchedItemText = matchedItem ? matchedItem.serviceSubCatgName : '';
      return matchedItemText
        .toString()
        .toLowerCase()
        .includes((Object.values(obj)[1] as string).toLowerCase());
    }
    return true; // 默认返回 true
  };
  // 针对特殊表格列，自定义过滤逻辑
  const servicePathFilter = (item: any, obj: any) => {
    if (obj && serviceCatgSource && serviceRootPath) {
      const { httpMethod, servicePath, serviceCatg, serviceVer } = item;
      const suffix = serviceCatgSource.filter((i: any) => i.serviceCatg === serviceCatg)[0]?.nameSpace || '';
      const rootPath = suffix ? `${serviceRootPath}/${suffix}` : serviceRootPath;
      let lastPath;
      let newServicePath;
      if (servicePath) {
        lastPath = servicePath.substring(serviceRootPath.length + 1, servicePath.length);
      }
      if (serviceVer) {
        newServicePath = `${httpMethod}${rootPath}/${serviceVer}/${lastPath}`;
      } else {
        newServicePath = `${httpMethod}${rootPath}/${lastPath}`;
      }
      return newServicePath
        .toString()
        .toLowerCase()
        .includes((Object.values(obj)[1] as string).toLowerCase());
    }
    return true; // 默认返回 true
  };
  // 使用 useMemo 确保 dataSource 是稳定的引用
  const memoizedDataSource = useMemo(
    () => (Array.isArray(extensionServiceSource) ? extensionServiceSource : []),
    [extensionServiceSource],
  );
  // 自定义hooks，支持表格列筛选并分页展示
  const { filteredDataSource, getColumnSearchProps, getColumnEnumSearchProps, clearAllFilters } =
    useTableFilter(memoizedDataSource);

  const deleteExtensionService: PopconfirmProps['onConfirm'] = async (e) => {
    const param = {
      serviceId: selectedExtService?.serviceId,
      entityId: selectedEntity.entityId,
    };
    try {
      const resultData = await deleteDomainEntityService(param);

      if (resultData?.success) {
        message.success(formatMessage('EXTENSIONSERVICE.DELETE.SUCCESS'));
        // 删除领域对象后重新渲染数据
        queryExtensionServices();
      }
    } catch (error) {
      message.success(formatMessage('EXTENSIONSERVICE.DELETE.FAIL'));
    }
  };

  const ImpTypeSource = [
    { name: 'Orchestration', key: 'N' },
    { name: 'Projection Dubbo API', key: 'D' },
    { name: 'Projection Restful API', key: 'R' },
    { name: 'Projection Call Service', key: 'S' },
  ];

  const gotoManagePage = (record: any) => {
    setIsManageExtensionService(true);
    handleManage(record);
  };
  // entity list表格
  const columns = [
    {
      dataIndex: 'serviceName',
      title: formatMessage('EXTENSIONSERVICE.SERVICE_NAME'),
      width: isProduct ? '18%' : '15%', // 产品环境为了显示版本标签，压缩creationSrc的宽度
      ellipsis: true,
      ...getColumnSearchProps('serviceName'),
      render: (_: string, record: any) => {
        return (
          <div className={styles.serviceNameBox}>
            <span className={styles.serviceName} title={record?.serviceName} onClick={() => gotoManagePage(record)}>
              {record?.serviceName}
            </span>
            {isProduct && (
              <div className={styles.matchVersion}>
                {record?.matchVer?.split(',')?.map((matchVersion: string) => (
                  <span
                    key={matchVersion}
                    className={`${
                      matchVersion === '9E'
                        ? styles.dyStyleV9E
                        : matchVersion === '81E'
                        ? styles.dyStyleV81E
                        : styles.dyStyleOthers
                    }`}
                  >
                    {matchVersion}
                  </span>
                ))}
              </div>
            )}
          </div>
        );
      },
    },
    {
      dataIndex: 'serviceCatg',
      title: formatMessage('EXTENSIONSERVICE.SERVICE_CATALOG'),
      width: '15%',
      ellipsis: true,
      render: (_: string, record: any) => {
        return serviceCatgSource
          .filter((i: any) => i.serviceCatg === record.serviceCatg)
          .map((i: any) => i.serviceCatgName);
      },
      ...getColumnEnumSearchProps(
        'serviceCatg',
        serviceCatgSource?.map((item: any) => {
          return {
            name: item?.serviceCatgName,
            key: item?.serviceCatg,
          };
        }),
      ),
    },
    {
      dataIndex: 'serviceSubCatg',
      title: formatMessage('EXTENSIONSERVICE.SERVICE_SUBCATALOG'),
      width: '15%',
      ellipsis: true,
      ...getColumnSearchProps('serviceSubCatg', serviceSubCatgFilter),
      render: (_: string, record: any) => {
        return serviceSubCatgSource
          .filter((i: any) => i.serviceSubCatg === record.serviceSubCatg)
          .map((i: any) => i.serviceSubCatgName);
      },
    },
    {
      dataIndex: 'creationSrc',
      title: formatMessage('EXTENSIONSERVICE.CREATION_SOURCE'),
      width: isProduct ? '12%' : '15%',
      ellipsis: true,
      render: (_: string, record: any) => {
        return CreationSource.filter((i) => i.key === record.creationSrc).map((i) => i.name);
      },
      ...getColumnEnumSearchProps('creationSrc', CreationSource),
    },
    {
      dataIndex: 'implType',
      title: formatMessage('EXTENSIONSERVICE.IMPLEMENT_TYPE'),
      width: '15%',
      ellipsis: true,
      render: (_: string, record: any) => {
        return ImpTypeSource.filter((i) => i.key === record.implType).map((i) => i.name);
      },
      ...getColumnEnumSearchProps('implType', ImpTypeSource),
    },
    {
      dataIndex: '',
      title: formatMessage('EXTENSIONSERVICE.SERVICE_PATH'),
      width: '15%',
      ellipsis: true,
      ...getColumnSearchProps('servicePath', servicePathFilter),
      render: (_: string, record: any) => {
        const { httpMethod, servicePath, serviceCatg, serviceVer } = record;
        // 拼接namespace和version
        const suffix = serviceCatgSource.filter((i: any) => i.serviceCatg === serviceCatg)[0]?.nameSpace || '';
        const rootPath = suffix ? `${serviceRootPath}/${suffix}` : serviceRootPath;
        let lastPath;
        let newServicePath;
        if (servicePath) {
          lastPath = servicePath.substring(serviceRootPath.length + 1, servicePath.length);
        }
        if (serviceVer) {
          newServicePath = `${rootPath}/${serviceVer}/${lastPath}`;
        } else {
          newServicePath = `${rootPath}/${lastPath}`;
        }
        switch (httpMethod) {
          case 'GET':
            return (
              <div className={styles.dyStyleWrapper} title={`${httpMethod} ${newServicePath}`}>
                <span className={styles.dyStyleGET}>{httpMethod}</span>
                <span>{newServicePath}</span>
              </div>
            );
          case 'POST':
            return (
              <div className={styles.dyStyleWrapper} title={`${httpMethod} ${newServicePath}`}>
                <span className={styles.dyStylePOST}>{httpMethod}</span>
                <span>{newServicePath}</span>
              </div>
            );
          case 'PUT':
            return (
              <div className={styles.dyStyleWrapper} title={`${httpMethod} ${newServicePath}`}>
                <span className={styles.dyStyleGET}>{httpMethod}</span>
                <span>{newServicePath}</span>
              </div>
            );
          case 'PATCH':
            return (
              <div className={styles.dyStyleWrapper} title={`${httpMethod} ${newServicePath}`}>
                <span className={styles.dyStylePATCH}>{httpMethod}</span>
                <span>{newServicePath}</span>
              </div>
            );
          case 'DELETE':
            return (
              <div className={styles.dyStyleWrapper} title={`${httpMethod} ${newServicePath}`}>
                <span className={styles.dyStyleDELETE}>{httpMethod}</span>
                <span>{newServicePath}</span>
              </div>
            );

          default:
            return (
              <div className={styles.dyStyleWrapper} title={`${httpMethod} ${newServicePath}`}>
                <span>{httpMethod}</span>
                <span>{newServicePath}</span>
              </div>
            );
        }
      },
    },
    {
      dataIndex: '',
      title: formatMessage('EXTENSIONSERVICE.OPERATION'),
      width: '10%',
      render: (_: string, record: any) => {
        return (
          <div>
            <Tooltip title={formatMessage('PROJECT.COMMON.EDIT')}>
              <FormOutlined
                className={classNames(styles.iconStyle, {
                  [styles.hide]: selectedRootNode?.state !== 'D',
                })}
                onClick={() => {
                  setOpenEditExtensionServiceDrawer(true);
                }}
              />
            </Tooltip>

            <Popconfirm
              title={formatMessage('EXTENSIONSERVICE.DELETE.TITLE')}
              description={formatMessage('EXTENSIONSERVICE.DELETE.DESCRIPTION')}
              onConfirm={deleteExtensionService}
              okText={formatMessage('EXTENSIONSERVICE.DELETE.YES')}
              cancelText={formatMessage('EXTENSIONSERVICE.DELETE.NO')}
            >
              {isProduct ? (
                <Tooltip title={formatMessage('PROJECT.COMMON.DELETE')}>
                  <DeleteOutlined
                    className={classNames(styles.versionIcon2, {
                      [styles.hide]: selectedRootNode?.state !== 'D',
                    })}
                    onClick={() => {}}
                  />
                </Tooltip>
              ) : (
                <Tooltip title={formatMessage('PROJECT.COMMON.DELETE')}>
                  <DeleteOutlined
                    className={classNames(styles.versionIcon2, {
                      [styles.hide]: record?.creationSrc === 'P' || selectedRootNode?.state !== 'D',
                    })}
                    onClick={() => {}}
                  />
                </Tooltip>
              )}
            </Popconfirm>
          </div>
        );
      },
    },
  ];

  const queryServiceCatg = async () => {
    const { success, data } = await queryServiceCatgList();
    if (success) {
      setServiceCatgSource(data || []);
    }
  };

  const queryServiceSubCatg = async () => {
    const { success = false, data = [] } = await queryServiceSubCatgList();
    if (success) {
      data.forEach((i: any) => {
        i.label = i.serviceSubCatgName;
        i.key = i.serviceSubCatg;
        i.value = i.serviceSubCatgName;
        i.state = 'original';
      });
      setServiceSubCatgSource(data);
    }
  };

  const queryExtensionServices = async () => {
    setSpining(true);
    try {
      const { success, data } = await qryEntityServiceList(selectedEntity.entityId);
      if (success) {
        setExtensionServiceSource(data);
      } else {
        setExtensionServiceSource([]);
      }
      setSpining(false);
    } catch (error) {
      setSpining(false);
    }
  };

  const queryDubboClassMethodList = async () => {
    const { success, data } = await queryDubboInterfaceDetails({ fuzzyKey: '' });
    if (success) {
      setDubboClassSource(data);
    } else {
      setDubboClassSource([]);
    }
  };

  const queryServiceRootPath = async () => {
    if (selectedEntity?.entityId) {
      const { success, data } = await qryServiceRootPath({ entityId: selectedEntity?.entityId });
      if (success) {
        setServiceRootPath(data);
      }
    }
  };

  useEffect(() => {
    // 查询服务分类
    queryServiceCatg();
    // 查询服务子分类
    queryServiceSubCatg();
    // 查询dubbo class和method
    queryDubboClassMethodList();
  }, []);

  useEffect(() => {
    // 查询扩展服务详情
    queryExtensionServices();
    // 查询实体服务根路径
    queryServiceRootPath();
    // 重置表格列的筛选条件
    clearAllFilters();
  }, [selectedEntity?.entityId]);

  return (
    <Spin spinning={spining}>
      {/* 领域实体扩展服务列表 */}
      <div>
        <OperationIconTitle
          title={formatMessage('EXTENSIONSERVICE.TITLE')}
          type={selectedRootNode?.state === 'D' ? 'add' : ''}
          opt={formatMessage('PROJECT.COMMON.ADD')}
          handleClick={() => {
            setOpenAddExtensionServiceDrawer(true);
          }}
          extraButtons={
            selectedRootNode?.state === 'D'
              ? [
                  {
                    text: formatMessage('EXTENSIONSERVICE.TRANSFER'),
                    onClick: () => {
                      setOpenTransferExtensionServiceDrawer(true);
                    },
                    icon: (
                      <span
                        onClick={() => {
                          setOpenTransferExtensionServiceDrawer(true);
                        }}
                        className={`${myIcon.iconfont} ${myIcon['icon-transfer']}`}
                      />
                    ),
                  },
                ]
              : []
          }
        />
        <div className={styles.content}>
          <ResizableTable
            size="small"
            style={{ marginTop: '12px' }}
            columns={columns}
            dataSource={filteredDataSource || []}
            rowKey={(record: any) => record?.propId}
            pagination={{
              hideOnSinglePage: true,
              pageSize: 10,
              showTotal: (total) => formatMessage('PROJECT.COMMON.PAGINATION.TOTAL', { total }),
            }}
            onRow={(record: any) => ({
              onClick: () => {
                setSelectedExtService(record);
              },
            })}
          />
        </div>
      </div>

      {/* 新增扩展服务 */}
      <AddExtensionServiceDrawer
        ignoreVersionSource={ignoreVersionSource}
        open={openAddExtensionServiceDrawer}
        selectedEntity={selectedEntity}
        serviceCatgSource={serviceCatgSource}
        serviceSubCatgSource={serviceSubCatgSource}
        dubboClassSource={dubboClassSource}
        serviceRootPath={serviceRootPath}
        selectedRootNode={selectedRootNode}
        tfmServices={tfmServices}
        onCancel={() => setOpenAddExtensionServiceDrawer(false)}
        isProduct={isProduct as boolean}
        onOk={() => {
          // 新增实体属性成功后，刷新当前表格
          setOpenAddExtensionServiceDrawer(false);
          queryServiceSubCatg();
          queryExtensionServices();
        }}
      />

      {/* 修改扩展服务 */}
      <EditExtensionServiceDrawer
        ignoreVersionSource={ignoreVersionSource}
        initValues={selectedExtService}
        open={openEditExtensionServiceDrawer}
        serviceCatgSource={serviceCatgSource}
        serviceSubCatgSource={serviceSubCatgSource}
        dubboClassSource={dubboClassSource}
        serviceRootPath={serviceRootPath}
        onCancel={() => setOpenEditExtensionServiceDrawer(false)}
        selectedEntity={selectedEntity}
        isProduct={isProduct as boolean}
        selectedRootNode={selectedRootNode}
        tfmServices={tfmServices}
        onOk={() => {
          // 修改扩展服务成功后，刷新当前表格
          setOpenEditExtensionServiceDrawer(false);
          queryServiceSubCatg();
          queryExtensionServices();
        }}
      />

      {/* 迁移扩展服务 */}
      <TransferExtensionServiceDrawer
        open={openTransferExtensionServiceDrawer}
        selectedEntity={selectedEntity}
        selectedRootNode={selectedRootNode}
        isProduct={isProduct as boolean}
        onCancel={() => setOpenTransferExtensionServiceDrawer(false)}
        onOk={() => {
          // 迁移扩展服务成功后，刷新当前表格
          setOpenTransferExtensionServiceDrawer(false);
          queryExtensionServices();
        }}
      />
    </Spin>
  );
};

export default ExtensionService;
