import OperationIconTitle from "@/components/OperationIconTitle";
import useI18n from "@/hooks/useI8n";
import { Col, Row } from "antd";

interface IBasicInfomation {
  basicInfo?: {
    enumName: string;
    enumType: string;
    isDataModel: boolean;
    creationSrc: string;
    comments: string;
    dataModelName?: string;
    valueColumnName?: string;
    nameColumnName?: string;
  };
  handleClick?: () => any;
  isDraft: boolean;
}

const BasicInfomation: React.FC<IBasicInfomation> = ({
  basicInfo = {},
  handleClick = () => {},
  isDraft = false,
}) => {
  const { formatMessage } = useI18n();

  const MarginTop = { marginTop: 15 };

  const ColSpan = ({ value = "" }) => (
    <span style={{ marginLeft: 16 }}>{value}</span>
  );

  const ColShow = ({ label, value }: any) => {
    return (
      <Col span={8} style={{ display: "flex", flexDirection: "row" }}>
        <Col
          style={{
            display: "flex",
            justifyContent: "flex-end",
            fontWeight: 600,
          }}
          span={9}
        >
          {label}
        </Col>
        <Col span={15}>
          <ColSpan value={value} />
        </Col>
      </Col>
    );
  };

  const LongColSpan = ({ label, value }: any) => {
    return (
      <>
        <Col
          span={3}
          style={{
            display: "flex",
            justifyContent: "flex-end",
            fontWeight: 600,
          }}
        >
          {label}
        </Col>
        <Col span={21}>
          <ColSpan value={value} />
        </Col>
      </>
    );
  };

  return (
    <div>
      <OperationIconTitle
        title={formatMessage("DATAMODELLISTDETAIL.INFO.TITLE")}
        opt={formatMessage("DATAMODELLISTDETAIL.INFO.OPT")}
        type={isDraft ? "edit" : ""}
        handleClick={handleClick}
      />
      <Row style={MarginTop}>
        <ColShow
          label={formatMessage("ENUMMANAGEMENTLIST.TABLE.NAME")}
          value={basicInfo?.enumName}
        />
        <ColShow
          label={formatMessage("ENUMMANAGEMENTLIST.TABLE.TYPE")}
          value={basicInfo?.enumType}
        />
        <ColShow
          label={formatMessage("ENUMMANAGEMENTLIST.TABLE.CREATIONSOURCE")}
          value={basicInfo?.creationSrc}
        />
      </Row>
      {basicInfo?.isDataModel && (
        <Row style={MarginTop}>
          <ColShow
            label={formatMessage("ENUMMANAGEMENTLIST.DRAWER.DATAMODEL")}
            value={basicInfo?.dataModelName}
          />
          <ColShow
            label={formatMessage("ENUMMANAGEMENTLIST.DRAWER.VALUECOLUMN")}
            value={basicInfo?.valueColumnName}
          />
          <ColShow
            label={formatMessage("ENUMMANAGEMENTLIST.DRAWER.NAMECOLUMN")}
            value={basicInfo?.nameColumnName}
          />
        </Row>
      )}
      <Row style={MarginTop}>
        <LongColSpan
          label={formatMessage("ENUMMANAGEMENTLIST.TABLE.DESC")}
          value={basicInfo?.comments}
        />
      </Row>
    </div>
  );
};

export default BasicInfomation;
