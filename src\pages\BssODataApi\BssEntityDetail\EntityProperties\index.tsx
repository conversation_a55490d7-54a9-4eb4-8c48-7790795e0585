import React, { useEffect, useMemo } from 'react';
import { IEntityDto, IEntityProperty } from '@/services/typing.d';
import useI18n from '@/hooks/useI8n';
import classNames from 'classnames';
import AvlValueTableInPop from '@/components/AvlValueTableInPop';
import CanFilterTableInPop from '@/components/CanFilterTableInPop';
import KeyIcon from '@/assets/CDP/key.svg';
import IndexIcon from '@/assets/CDP/Index.svg';
import CanFilterIcon from '@/assets/CDP/CanFilter.svg';
import MandatoryIcon from '@/assets/CDP/Mandatory.svg';
import CanOrderIcon from '@/assets/CDP/CanOrder.svg';
import EnumIcon from '@/assets/CDP/Enum.svg';
import styles from './index.less';
import ResizableTable from '@/components/ResizableTable';
import { useTableFilter } from '@/hooks/useTableFilter';

interface IEntityProperties {
  entityDtoData?: IEntityDto;
  onOtherEntityClick?: (entityKey: string) => void;
}

const SupportedFeatures: React.FC<IEntityProperty> = (props) => {
  const { keyFlag, required, index, filter, orderBy, avlValue, dataType } = props;

  // 计算avlValue 具体计算规则可以在AvlValueTableInPop中查看
  let title = '';
  const patternAll = /^(range|length)[\(\[]/;
  const patternRange = /^range[\(\[]/;
  const patternLength = /^length[\(\[]/;
  if (avlValue && patternRange.test(avlValue)) {
    title = 'Value Range';
  } else if (avlValue && patternLength.test(avlValue)) {
    title = 'Value Length';
  } else {
    title = 'Enum List';
  }

  return (
    <div>
      {keyFlag === 'Y' ? <img src={KeyIcon} className={styles.iconItem} /> : null}
      {index === 'Y' ? <img src={IndexIcon} className={styles.iconItem} /> : null}
      {filter === 'Y' ? (
        <CanFilterTableInPop dataType={dataType}>
          <img src={CanFilterIcon} className={classNames(styles.iconItem, styles.iconItemActive)} />
        </CanFilterTableInPop>
      ) : null}
      {required === 'Y' ? <img src={MandatoryIcon} className={styles.iconItem} /> : null}
      {orderBy === 'Y' ? <img src={CanOrderIcon} className={styles.iconItem} /> : null}
      {avlValue ? (
        <AvlValueTableInPop
          title={title}
          avlValue={avlValue}
          popContentType={patternAll.test(avlValue) ? 'string' : 'array'}
        >
          <img src={EnumIcon} className={classNames(styles.iconItem, styles.iconItemActive)} />
        </AvlValueTableInPop>
      ) : null}
    </div>
  );
};

const EntityProperties: React.FC<IEntityProperties> = (props) => {
  const { entityDtoData, onOtherEntityClick } = props;
  const { formatMessage } = useI18n();

  const SupportSource = [
    {
      name: (
        <>
          <img src={KeyIcon} className={styles.iconItem} />
          <span style={{ marginLeft: 5 }}>Primary Key</span>
        </>
      ),
      key: 'keyFlag',
    },
    {
      name: (
        <>
          <img src={IndexIcon} className={styles.iconItem} />
          <span style={{ marginLeft: 5 }}>Index</span>
        </>
      ),
      key: 'index',
    },
    {
      name: (
        <>
          <img src={CanFilterIcon} className={styles.iconItem} />
          <span style={{ marginLeft: 5 }}>Can Filter</span>
        </>
      ),
      key: 'filter',
    },
    {
      name: (
        <>
          <img src={MandatoryIcon} className={styles.iconItem} />
          <span style={{ marginLeft: 5 }}>Mandatory</span>
        </>
      ),
      key: 'required',
    },
    {
      name: (
        <>
          <img src={CanOrderIcon} className={styles.iconItem} />
          <span style={{ marginLeft: 5 }}>Can Order</span>
        </>
      ),
      key: 'orderBy',
    },
    {
      name: (
        <>
          <img src={EnumIcon} className={styles.iconItem} />
          <span style={{ marginLeft: 5 }}>Enum</span>
        </>
      ),
      key: 'avlValue',
    },
  ];
  // 使用 useMemo 确保 dataSource 是稳定的引用
  const memoizedDataSource = useMemo(
    () => (Array.isArray(entityDtoData?.entityProperties) ? entityDtoData?.entityProperties : []),
    [entityDtoData],
  );
  // 自定义hooks，支持表格列筛选并分页展示
  const { filteredDataSource, getColumnSearchProps, getColumnEnumSearchProps, clearAllFilters } =
    useTableFilter(memoizedDataSource);

  // 针对特殊表格列，自定义过滤逻辑
  const customTypeFilter = (item: any, obj: any) => {
    if (item) {
      let showData = '';
      if (item?.relEntityKey) {
        showData =
          item?.relShip && item?.relShip?.split(':')?.[1] === 'N'
            ? `List<${item?.relEntityName}>`
            : item?.relEntityName;
      } else {
        showData = item?.dataType;
      }
      return (showData || '')
        .toString()
        .toLowerCase()
        .includes((Object.values(obj)[1] as string).toLowerCase());
    }
    return true;
  };

  // 针对特殊表格列，自定义过滤逻辑
  const customSupportFilter = (item: any, obj: any) => {
    if (item && obj) {
      const keyFlag = item?.keyFlag === 'Y' ? 'keyFlag' : '';
      const index = item?.index === 'Y' ? 'index' : '';
      const filter = item?.filter === 'Y' ? 'filter' : '';
      const required = item?.required === 'Y' ? 'required' : '';
      const orderBy = item?.orderBy === 'Y' ? 'orderBy' : '';
      const avlValue = item?.avlValue ? 'avlValue' : '';
      const showData = [keyFlag, index, filter, required, orderBy, avlValue];
      if (Object.values(obj)[1]) {
        const filterKeys = obj?.support?.split(',');
        return filterKeys.every((filterKey: any) => {
          return showData.find((showKey: any) => showKey === filterKey);
        });
      }
    }
    return true;
  };

  const columns = [
    {
      dataIndex: 'name',
      title: formatMessage('PROJECT.COMMON.NAME'),
      width: '15%',
      ...getColumnSearchProps('name'),
    },
    {
      dataIndex: 'dataType',
      title: formatMessage('PROJECT.COMMON.TYPE'),
      width: '20%',
      ...getColumnSearchProps('dataType', customTypeFilter),
      render: (_: string, record: any) =>
        record?.relEntityKey ? (
          <div className={styles.dataTypeObject} onClick={() => onOtherEntityClick?.(record?.relEntityKey)}>
            {record?.relShip && record?.relShip?.split(':')?.[1] === 'N'
              ? `List<${record?.relEntityName}>`
              : record?.relEntityName}
          </div>
        ) : (
          record?.dataType
        ),
    },
    {
      dataIndex: 'support',
      title: formatMessage('DOMAINDETAIL.PROPERTIES.FEATURES'),
      width: '20%',
      render: (_: string, record: any) => <SupportedFeatures {...record} />,
      ...getColumnEnumSearchProps('support', SupportSource, customSupportFilter),
    },
    {
      dataIndex: 'example',
      title: formatMessage('PROJECT.COMMON.EXAMPLE'),
      width: '20%',
      ellipsis: true,
      ...getColumnSearchProps('example'),
      render: (value: any) => {
        if (typeof value === 'string') {
          return value;
        }
        return '';
      },
    },
    {
      dataIndex: 'comments',
      title: formatMessage('PROJECT.COMMON.DESC'),
      width: '25%',
      ellipsis: true,
      ...getColumnSearchProps('comments'),
    },
  ];

  useEffect(() => {
    // 重置表格列的筛选条件
    clearAllFilters();
  }, [memoizedDataSource]);

  return (
    <div className={styles.entityPropertiesContent}>
      <div className={styles.entityPropertiesLegend}>
        <div className={styles.legendItem}>
          <img src={KeyIcon} className={styles.iconItem} />
          <span>= {formatMessage('DOMAINDETAIL.PROPERTIES.PRIMARY')}</span>
        </div>
        <div className={styles.legendItem}>
          <img src={IndexIcon} className={styles.iconItem} />
          <span>= {formatMessage('DOMAINDETAIL.PROPERTIES.INDEX')}</span>
        </div>
        <div className={styles.legendItem}>
          <img src={CanFilterIcon} className={styles.iconItem} />
          <span>= {formatMessage('DOMAINDETAIL.PROPERTIES.CANFILTER')}</span>
        </div>
        <div className={styles.legendItem}>
          <img src={MandatoryIcon} className={styles.iconItem} />
          <span>= {formatMessage('DOMAINDETAIL.PROPERTIES.MANDATORY')}</span>
        </div>
        <div className={styles.legendItem}>
          <img src={CanOrderIcon} className={styles.iconItem} />
          <span>= {formatMessage('DOMAINDETAIL.PROPERTIES.CANORDER')}</span>
        </div>
        <div className={styles.legendItem}>
          <img src={EnumIcon} className={styles.iconItem} />
          <span>= {formatMessage('DOMAINDETAIL.PROPERTIES.ENUM')}</span>
        </div>
      </div>
      <ResizableTable
        size="small"
        style={{ marginTop: '12px' }}
        columns={columns}
        dataSource={filteredDataSource || []}
        rowKey={(record: any) => record?.name}
        pagination={false}
      />
    </div>
  );
};

export default EntityProperties;
