import React, { useEffect, useRef, useState } from 'react';
import {
  Button,
  Drawer,
  Form,
  Input,
  Select,
  Space,
  message,
  InputNumber,
  Divider,
  InputRef,
  Flex,
  Spin,
  Tooltip,
} from 'antd';
import { operateResponseParam, addSchemaByDomainObjId, qrySchemaListByDomainId } from '@/services/entityService';
import { EnumSourceProps, ErrorSchemaType } from '../../../../types';
import { useModel } from 'umi';
import flatteningDomainSchemaList from '@/utils/flatteningDomainSchemaList';
import { getFormatTypeSource } from '../../utils';
import { ContentTypeSource, ParamTypeSourceWithoutObject, RespHeaderOptions, ResponseDataSource } from '../../const';
import { autoFillDescByPropName } from '@/utils/common';
import useI18n from '@/hooks/useI8n';
import classNames from 'classnames';
import myIcon from '@/static/iconfont/iconfont.css';
import styles from '../index.less';
import CopySchemaDrawer from '../../SchemaManagement/CopySchemaDrawer';

interface IEditResponseInformationDrawer {
  domainSchemaList: any;
  errorSchemaList: ErrorSchemaType[];
  selectedService: any;
  enumSource: EnumSourceProps[];
  open: boolean;
  selectedParam?: any;
  selectedRootNode: any;
  httpStatusList: any;
  isProjectAndProductOwned: boolean;
  responseInformation: any;
  onTabsChange: (key: string) => void;
  onCancel: () => void;
  onOk: () => void;
  updateDomainSchemaList: () => Promise<void>;
}

interface optionProps {
  label: string;
  value: string;
}

interface DomainSchemaObj {
  domainObjId: number;
  domainObjName: string;
  schemaList: Array<any>;
}

// 局部常量，暂时屏蔽了Datetime选项
const ResponseParamTypeSourceWithoutObject = ParamTypeSourceWithoutObject.filter((type) => type !== 'Datetime');

const EditResponseInformation: React.FC<IEditResponseInformationDrawer> = (props) => {
  const {
    domainSchemaList = [],
    errorSchemaList = [],
    open,
    selectedService,
    isProjectAndProductOwned,
    enumSource,
    onCancel,
    onOk,
    selectedParam,
    selectedRootNode,
    httpStatusList,
    responseInformation = [],
    updateDomainSchemaList,
    onTabsChange,
  } = props;
  const { formatMessage } = useI18n();
  const [form] = Form.useForm();
  const [submitSpinning, setSubmitSpinning] = useState<boolean>(false);
  const [formValues, setFormValues] = useState<any>({}); // formValues
  const [avlValueType, setAvlValueType] = useState<string>('');
  const [avlValueTypeSource, setAvlValueTypeSource] = useState<optionProps[]>([]);
  const [avlValueTypeValue, setAvlValueTypeValue] = useState<string>('');
  const [disabled, setDisabled] = useState<boolean>(false);
  const [required, setRequired] = useState<boolean>(false);
  const [defaultValues, setDeFaultValues] = useState<any>({});
  const { currentDomainObjId } = useModel('manageExtensionServiceModel');
  const addSchemaInputRef = useRef<InputRef>(null);
  const [newSchemaName, setNewSchemaName] = useState('');
  const [isSchemaNameValid, setIsSchemaNameValid] = useState<boolean>(true);
  const [selectedSchemaObj, setSelectedSchemaObj] = useState<any>({});
  const [selectedErrorSchemaId, setSelectedErrorSchemaId] = useState<number>(0);
  const [copyOpen, setCopyOpen] = useState<boolean>(false); // 拷贝Schema抽屉
  const [copyingSchema, setCopyingSchema] = useState<any>({}); // 待拷贝的Schema

  const selectedHttpStatus = Form.useWatch('respCode', form);
  const respContentType = Form.useWatch('respContentType', form);
  const respData = Form.useWatch('respData', form);

  const FormatSource = getFormatTypeSource(avlValueType);

  // 弹框关闭
  const onClose = async () => {
    setAvlValueTypeValue('');
    form.resetFields();
    onCancel?.();
  };

  const getStringBetween = (str: string, start: string, end: string) => {
    // 正则表达式匹配两个指定字符之间的内容
    const reg = /\[(\S*)\]/;
    if ((str.match(reg) || []).length > 1) {
      return (str.match(reg) || [])[1];
    }
    return '';
  };
  // 调用新增或修改响应参数接口
  const optResponseParam = async (param: any) => {
    setSubmitSpinning(true);
    try {
      const resultData = await operateResponseParam('update', param);

      if (resultData?.success) {
        message.success(formatMessage('MANAGEEXTENSIONSERVICE.RESPONSE.UPDATE.SUCCESS', RespHeaderOptions.option1));
        form.resetFields();
        onTabsChange(`${param?.respCode}`);
        onOk?.();
      }
      onCancel?.();
    } catch {
      message.error(formatMessage('MANAGEEXTENSIONSERVICE.RESPONSE.UPDATE.ERROR', RespHeaderOptions.option1));
    } finally {
      setSubmitSpinning(false);
    }
  };

  const onAddSchemaNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setNewSchemaName(value);

    // 校验输入值,开头是大写字母，只能包含英文字母、数字和下划线
    const isValid = /^[A-Z][A-Za-z0-9_]*$/.test(value);
    setIsSchemaNameValid(isValid);
  };

  // 入参校验，构造入参
  const checkServiceReqParamData = () => {
    form.validateFields().then(() => {
      const formValue = form.getFieldsValue();
      // 构造入参
      let param = {
        ...formValue,
        serviceId: selectedService?.serviceId,
        domainObjId: currentDomainObjId,
        respParamId: selectedParam?.respParamId,
      };
      if (formValue?.respData === 'arrayData') {
        param = {
          ...param,
          isArray: 'Y',
        };
      } else {
        param = {
          ...param,
          isArray: 'N',
        };
      }
      if (formValue?.respData === 'noData') {
        param = {
          domainObjId: currentDomainObjId,
          serviceId: selectedService?.serviceId,
          respParamId: param?.respParamId,
          respCode: param?.respCode,
          isArray: 'N',
          paramType: 'Void',
          respContentType: param?.respContentType,
        };
      }

      if (!ResponseParamTypeSourceWithoutObject.includes(param.paramType) && formValue?.respData !== 'noData') {
        if (selectedHttpStatus >= 300) {
          param = {
            isArray: param?.isArray,
            respCode: param?.respCode,
            respContentType: param?.respContentType,
            respData: param?.respData,
            respParamName: param?.respParamName,
            comments: param?.comments,
            serviceId: param?.serviceId,
            domainObjId: param?.domainObjId,
            respParamId: param?.respParamId,
            refSchemaId: selectedErrorSchemaId,
            paramType: 'Ref',
          };
        } else {
          param = {
            // ...param,
            isArray: param?.isArray,
            respCode: param?.respCode,
            respContentType: param?.respContentType,
            respData: param?.respData,
            respParamName: param?.respParamName,
            comments: param?.comments,
            serviceId: param?.serviceId,
            domainObjId: param?.domainObjId,
            respParamId: param?.respParamId,
            refSchemaId: selectedSchemaObj?.schemaId,
            paramType: 'Ref',
          };
        }
      }
      if (param.avlValueType && formValue?.respData !== 'noData') {
        switch (param.avlValueType) {
          case 'Length':
            param.avlValue = `Length[${param.min},${param.max}]`;
            break;

          case 'Enum':
            const eId = enumSource.find(
              (i: any) => `${i.value}` === `${param.enum}` || i.label === `${param.enum}`,
            )?.value;
            param.avlValue = eId;
            param.enumId = eId;
            break;

          case 'Empty':
            param.avlValue = '';
            break;
          case 'Range':
            param.avlValue = `Range[${param.min},${param.max}]`;
            break;
          case 'Format':
            param.avlValue = param.typeFormat;
            param = {
              ...param,
              typeFormat: param.typeFormat,
            };
            break;
          case 'Values':
            param.avlValue = param.values;
            break;

          default:
            param.avlValue = '';
            break;
        }
      }
      if (ResponseParamTypeSourceWithoutObject.includes(param.paramType)) {
        delete param?.refSchemaId;
        delete param?.refSchemaName;
      }
      if (param?.avlValueType !== 'Enum') {
        delete param?.enumId;
      }
      if (param?.avlValueType !== 'Format') {
        delete param?.typeFormat;
      }
      optResponseParam(param);
    });
  };

  // httpStatus或contentType改变
  const onHttpStatusOrContentTypeChange = (httpStatus: number, contentType: string) => {
    let existSuccessStatus = ''; // 是否已经存在一个成功的状态码
    if (defaultValues?.respCode >= 300 && httpStatus >= 200 && httpStatus < 300) {
      existSuccessStatus =
        responseInformation.find((item: any) => Number(item?.respCode) >= 200 && Number(item?.respCode) < 300)
          ?.respCode || '';
    }

    if (existSuccessStatus) {
      message.warning(
        formatMessage('MANAGEEXTENSIONSERVICE.RESPONSE.SUCCESS_STATUS_EXISTS', { code: existSuccessStatus }),
      );
      form.setFieldValue('respCode', defaultValues?.respCode);
      return;
    } else {
      if (contentType === 'binary') {
        form.setFieldValue('respData', 'noData');
        form.setFieldValue('respParamName', '');
        form.setFieldValue('paramType', '');
      } else {
        // contentType === 'json'的情况
        if (httpStatus >= 300) {
          form.setFieldValue('respData', 'singleData');
          form.setFieldValue('respParamName', 'errorResp');

          const defaultErrorSchema = errorSchemaList.find((i: ErrorSchemaType) => {
            if (selectedService?.serviceCatg === 'T') {
              return i.schemaId === -2 && i?.tmfFlag;
            } else {
              return i.schemaId === -1 && !i?.tmfFlag;
            }
          });
          form.setFieldValue('paramType', defaultErrorSchema?.schemaId || '');
          setSelectedErrorSchemaId(defaultErrorSchema?.schemaId || 0);
        } else {
          onRespDataValueChange(respData);
          setSelectedErrorSchemaId(0);
        }
      }
    }
  };

  // 通用的重置表单字段函数
  const resetFormFields = (resetAvlValueType = true) => {
    // 重置所有相关字段
    const resetFields: any = {
      typeFormat: undefined,
      min: undefined,
      max: undefined,
      values: undefined,
      enum: undefined,
    };

    if (resetAvlValueType) {
      resetFields.avlValueType = '';
      setAvlValueTypeValue('');
    }

    form.setFieldsValue(resetFields);
  };

  // paramType改变
  const onPropTypeChange = (value: any) => {
    const flatDomainSchemaList = flatteningDomainSchemaList(domainSchemaList);
    const schemaObj = flatDomainSchemaList?.find((item: any) => item?.schemaId === value);
    setSelectedSchemaObj(schemaObj);
    setAvlValueType(value);

    // 重置avlValueTypeValue相关字段
    resetFormFields();

    setDisabled(false);
    setRequired(true);
    switch (value) {
      case 'String':
        setAvlValueTypeSource([
          { label: 'Empty', value: 'Empty' },
          { label: 'Length', value: 'Length' },
          { label: 'Format', value: 'Format' },
          { label: 'Enum', value: 'Enum' },
          { label: 'Values', value: 'Values' },
        ]);
        form.setFieldsValue({
          avlValueType: 'Empty',
        });
        break;

      case 'Boolean':
        setAvlValueTypeSource([{ label: 'Empty', value: 'Empty' }]);
        form.setFieldsValue({
          avlValueType: 'Empty',
        });
        break;

      case 'Long':
        setAvlValueTypeSource([
          { label: 'Empty', value: 'Empty' },
          { label: 'Length', value: 'Length' },
          { label: 'Range', value: 'Range' },
          { label: 'Enum', value: 'Enum' },
          { label: 'Values', value: 'Values' },
        ]);
        form.setFieldsValue({
          avlValueType: 'Empty',
        });
        break;
      case 'Integer':
        setAvlValueTypeSource([
          { label: 'Empty', value: 'Empty' },
          { label: 'Length', value: 'Length' },
          { label: 'Range', value: 'Range' },
          { label: 'Enum', value: 'Enum' },
          { label: 'Values', value: 'Values' },
        ]);
        form.setFieldsValue({
          avlValueType: 'Empty',
        });
        break;
      case 'Number':
        setAvlValueTypeSource([
          { label: 'Empty', value: 'Empty' },
          { label: 'Length', value: 'Length' },
          { label: 'Range', value: 'Range' },
          { label: 'Format', value: 'Format' },
          { label: 'Values', value: 'Values' },
        ]);
        form.setFieldsValue({
          avlValueType: 'Empty',
        });
        break;
      case 'Datetime':
        setAvlValueTypeSource([
          { label: 'Empty', value: 'Empty' },
          { label: 'Format', value: 'Format' },
          { label: 'Values', value: 'Values' },
        ]);
        form.setFieldsValue({
          avlValueType: 'Empty',
        });
        break;

      default:
        // TODO 可能不需要默认值
        setAvlValueTypeSource([]);
        setDisabled(true);
        setRequired(false);
        break;
    }
  };

  // avlValueType改变
  const onAvlValueTypeChange = (value: any) => {
    setAvlValueTypeValue(value);

    // 重置avlValueTypeValue相关字段，但保留avlValueType
    resetFormFields(false);
  };

  // 枚举列表搜索
  const handleEnumFilter = (input: any, option: any) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };

  // respData改变
  const onRespDataValueChange = (value: any) => {
    switch (value) {
      case 'singleData':
        form.setFieldValue('respParamName', 'resp');
        form.setFieldValue('paramType', '');
        break;
      case 'arrayData':
        form.setFieldValue('respParamName', 'respList');
        form.setFieldValue('paramType', '');
        break;
      case 'noData':
        form.setFieldValue('respParamName', '');
        break;
      default:
        form.setFieldValue('respParamName', '');
        break;
    }
  };

  // 新增Schema
  const addSchema = async (e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>) => {
    e.preventDefault();

    if (isSchemaNameValid) {
      if (currentDomainObjId) {
        const resultData = await addSchemaByDomainObjId({ schemaName: newSchemaName, domainObjId: currentDomainObjId });
        if (resultData?.success) {
          message.success(formatMessage('MANAGEEXTENSIONSERVICE.SCHEMA.ADD.SUCCESS'));
          await updateDomainSchemaList();
          setTimeout(() => {
            addSchemaInputRef.current?.focus();
          }, 0);
        }
      }
    } else {
      message.warning(formatMessage('MANAGEEXTENSIONSERVICE.SCHEMA.NAME.INVALID'));
    }
  };

  useEffect(() => {
    if (newSchemaName) {
      const flatDomainSchemaList = flatteningDomainSchemaList(domainSchemaList);
      const schemaObj = flatDomainSchemaList?.find((item: any) => item?.schemaName === newSchemaName);
      form.setFieldValue('paramType', schemaObj?.schemaId); // 新建Schema后默认选中
      onPropTypeChange(schemaObj?.schemaId);
      setNewSchemaName('');
    }
  }, [domainSchemaList]);

  useEffect(() => {
    // 深拷贝行数据后初始化值
    let defaultValues: any = {};

    Object.assign(defaultValues, selectedParam);

    if (defaultValues?.paramType && defaultValues?.paramType !== 'Void') {
      if (defaultValues?.isArray === 'Y') {
        defaultValues = {
          ...defaultValues,
          respData: 'arrayData',
        };
      } else {
        defaultValues = {
          ...defaultValues,
          respData: 'singleData',
        };
      }

      if (!ResponseParamTypeSourceWithoutObject.includes(defaultValues?.paramType)) {
        if (defaultValues?.refSchemaId < 0) {
          const schemaObj = errorSchemaList.find((i: ErrorSchemaType) => i?.schemaId === defaultValues?.refSchemaId);
          defaultValues.paramType = schemaObj?.schemaName;
          setSelectedErrorSchemaId(schemaObj?.schemaId || 0);
        } else {
          const flatDomainSchemaList = flatteningDomainSchemaList(domainSchemaList);
          const schemaObj = flatDomainSchemaList?.find((i: any) => i?.schemaId === defaultValues?.refSchemaId);
          defaultValues.paramType = schemaObj?.schemaId;
          onPropTypeChange(defaultValues.paramType);
        }
      } else {
        // 五种数据基本类型，处理avlValue字段的回显问题
        const { avlValue, typeFormat } = defaultValues;
        if ((avlValue === '' || avlValue === null) && !typeFormat) {
          defaultValues.avlValueType = 'Empty';
        } else if (avlValue === `${defaultValues.enumId}`) {
          defaultValues.avlValueType = 'Enum';
          defaultValues.enum = enumSource.find((i: any) => `${i.value}` === `${avlValue}`)?.label;
        } else if (avlValue?.toLowerCase().startsWith('length')) {
          defaultValues.avlValueType = 'Length';
          const betweenVal = getStringBetween(avlValue, '[', ']')?.split(',');
          defaultValues.min = betweenVal[0];
          defaultValues.max = betweenVal[1];
        } else if (avlValue?.toLowerCase().startsWith('range')) {
          defaultValues.avlValueType = 'Range';
          const betweenVal = getStringBetween(avlValue, '[', ']')?.split(',');
          defaultValues.min = betweenVal[0];
          defaultValues.max = betweenVal[1];
        } else if (typeFormat) {
          defaultValues.avlValueType = 'Format';
          defaultValues.typeFormat = typeFormat;
        } else {
          defaultValues.avlValueType = 'Values';
          defaultValues.values = avlValue;
        }
        onPropTypeChange(defaultValues.paramType);
        onAvlValueTypeChange(defaultValues.avlValueType);
      }
    } else {
      defaultValues = {
        ...defaultValues,
        respData: 'noData',
      };
    }
    setDeFaultValues(defaultValues);
    onRespDataValueChange(defaultValues?.respData);
  }, [selectedParam, open]);

  return (
    <>
      <Drawer
        title={formatMessage('MANAGEEXTENSIONSERVICE.RESPONSE.EDIT.TITLE', RespHeaderOptions.option1)}
        onClose={onClose}
        maskClosable={false}
        open={open}
        width={720}
        afterOpenChange={() => {
          form.resetFields();
        }}
        footer={
          <Space style={{ float: 'right' }}>
            <Spin spinning={submitSpinning}>
              <Button type="primary" onClick={checkServiceReqParamData}>
                {formatMessage('PROJECT.COMMON.SUBMIT')}
              </Button>
            </Spin>
            <Button onClick={onClose}>{formatMessage('PROJECT.COMMON.CANCEL')}</Button>
          </Space>
        }
      >
        <Form
          form={form}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
          initialValues={defaultValues}
          onValuesChange={(cValues, alValues) => {
            setFormValues(alValues);
          }}
        >
          <Form.Item
            label={formatMessage('MANAGEEXTENSIONSERVICE.RESPONSE.HTTP_STATUS')}
            name="respCode"
            rules={[{ required: true, message: '' }]}
          >
            <Select
              showSearch
              onChange={(value) => onHttpStatusOrContentTypeChange(value, respContentType)}
              disabled={isProjectAndProductOwned}
              options={[Number(selectedParam?.respCode), ...httpStatusList]
                .sort((a, b) => a - b)
                .map((item: string) => ({
                  label: item,
                  value: item,
                  key: item,
                }))}
            />
          </Form.Item>
          <Form.Item
            label={formatMessage('MANAGEEXTENSIONSERVICE.RESPONSE.CONTENT_TYPE')}
            name="respContentType"
            rules={[{ required: true, message: '' }]}
          >
            <Select
              showSearch
              options={ContentTypeSource}
              onChange={(value) => onHttpStatusOrContentTypeChange(selectedHttpStatus, value)}
              disabled={isProjectAndProductOwned}
            />
          </Form.Item>
          <Form.Item
            label={formatMessage('MANAGEEXTENSIONSERVICE.RESPONSE.DATA')}
            name="respData"
            rules={[{ required: true, message: '' }]}
          >
            <Select
              showSearch
              options={ResponseDataSource}
              onChange={onRespDataValueChange}
              disabled={respContentType === 'binary' || isProjectAndProductOwned || selectedHttpStatus >= 300}
            />
          </Form.Item>
          {respData !== 'noData' && (
            <>
              <Form.Item
                label={formatMessage('PROJECT.COMMON.NAME')}
                name="respParamName"
                rules={[{ required: true, message: '' }]}
              >
                <Input
                  disabled={isProjectAndProductOwned || selectedHttpStatus >= 300}
                  onBlur={() => autoFillDescByPropName(form, 'respParamName', 'comments')}
                />
              </Form.Item>
              <Form.Item
                label={formatMessage('PROJECT.COMMON.TYPE')}
                name="paramType"
                rules={[{ required: true, message: '' }]}
                extra={
                  !(
                    selectedHttpStatus < 300 &&
                    (!avlValueType || ResponseParamTypeSourceWithoutObject.includes(avlValueType))
                  ) && formatMessage('MANAGEEXTENSIONSERVICE.SCHEMA.EDIT.NOTES')
                }
              >
                {selectedHttpStatus >= 300 ? (
                  <Select
                    onChange={(value) => {
                      setSelectedErrorSchemaId(value);
                    }}
                    showSearch
                    optionFilterProp="children"
                    filterOption={(input, option) =>
                      (option?.children as unknown as string)?.toLowerCase().includes(input.toLowerCase())
                    }
                  >
                    {errorSchemaList
                      .filter((i: ErrorSchemaType) => {
                        if (selectedService?.serviceCatg === 'T') {
                          return i.schemaId === -2;
                        }
                        return i.schemaId === -1;
                      })
                      .map((i: ErrorSchemaType) => (
                        <Select.Option key={i.schemaId} value={i.schemaId}>
                          {i.schemaName}
                        </Select.Option>
                      ))}
                  </Select>
                ) : (
                  <Select
                    options={[
                      ...ResponseParamTypeSourceWithoutObject.map((item) => ({ label: item, value: item })),
                      ...domainSchemaList.map((item: DomainSchemaObj) => ({
                        label: <span>{item?.domainObjName}</span>,
                        value: item?.domainObjName,
                        options: item?.schemaList.map((i) => ({
                          label: i?.schemaName,
                          value: i?.schemaId,
                          key: i?.schemaId,
                        })),
                      })),
                    ]}
                    optionRender={(option) => {
                      // Schema节点
                      if (option.groupOption) {
                        return (
                          <Flex justify="space-between">
                            <div style={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                              {option.label}
                            </div>
                            {/* 拷贝功能 */}
                            <Tooltip title={formatMessage('PROJECT.COMMON.COPY')}>
                              <span
                                className={classNames(
                                  `${myIcon.iconfont} ${myIcon['icon-copy']} ${styles.deleteIcon}`,
                                  {
                                    [styles.hide]: selectedRootNode?.state !== 'D',
                                  },
                                )}
                                onClick={(e) => {
                                  e.stopPropagation();
                                  setCopyOpen(true);
                                  setCopyingSchema({ schemaName: option.label, schemaId: option.value });
                                }}
                              />
                            </Tooltip>
                          </Flex>
                        );
                      }
                      return option.label;
                    }}
                    disabled={isProjectAndProductOwned}
                    onChange={onPropTypeChange}
                    showSearch
                    filterOption={(input, option) => {
                      // 基本类型直接过滤
                      if (option?.label && typeof option.label === 'string') {
                        return option.label.toLowerCase().includes(input.toLowerCase());
                      }
                      // 组标题不参与过滤，总是显示（不需要特殊处理，Select 组件会自动处理）
                      return false;
                    }}
                    dropdownRender={(menu) => (
                      <>
                        {menu}
                        <Divider style={{ margin: '8px 0' }} />
                        <Space style={{ padding: '0 8px 4px' }}>
                          <Input
                            value={newSchemaName}
                            placeholder={formatMessage('MANAGEEXTENSIONSERVICE.SCHEMA.PLACEHOLDER')}
                            ref={addSchemaInputRef}
                            onChange={onAddSchemaNameChange}
                            onKeyDown={(e) => e.stopPropagation()}
                          />
                          <Button type="text" onClick={addSchema}>
                            + {formatMessage('MANAGEEXTENSIONSERVICE.SCHEMA.NEW')}
                          </Button>
                        </Space>
                      </>
                    )}
                  />
                )}
              </Form.Item>
              {/* 状态码小于300并且avlValueType不为复杂类型才展示'Example'和'Allowable Values' */}
              {selectedHttpStatus < 300 &&
                (!avlValueType || ResponseParamTypeSourceWithoutObject.includes(avlValueType)) && (
                  <>
                    <Form.Item
                      label={formatMessage('MANAGEEXTENSIONSERVICE.REQUEST.PARAM.ALLOWABLE_VALUES')}
                      style={{ marginBottom: 0 }}
                    >
                      <Form.Item
                        name="avlValueType"
                        style={{ display: 'inline-block', width: 'calc(33% - 5px)' }}
                        rules={[{ required, message: '' }]}
                      >
                        <Select options={avlValueTypeSource} onChange={onAvlValueTypeChange} disabled={disabled} />
                      </Form.Item>
                      {avlValueTypeValue === 'Length' ? (
                        <Form.Item
                          name="min"
                          style={{ display: 'inline-block', width: 'calc(33%)', marginLeft: '5px' }}
                          rules={[
                            { required: true, message: '' },
                            {
                              validator: (_, value) => {
                                const minValue = formValues?.required !== 'Y' ? 0 : 1;
                                if (value < minValue) {
                                  return Promise.reject(
                                    new Error(formatMessage('MANAGEEXTENSIONSERVICE.MIN.ERROR', { min: minValue })),
                                  );
                                }
                                return Promise.resolve();
                              },
                            },
                          ]}
                        >
                          <InputNumber
                            addonBefore={formatMessage('MANAGEEXTENSIONSERVICE.MIN')}
                            precision={0}
                            min={formValues?.required !== 'Y' ? 0 : 1}
                            max={formValues.max}
                            style={{ width: '100%' }}
                          />
                        </Form.Item>
                      ) : null}
                      {avlValueTypeValue === 'Length' ? (
                        <Form.Item
                          name="max"
                          style={{ display: 'inline-block', width: 'calc(33%)', marginLeft: '5px' }}
                          rules={[{ required: true, message: '' }]}
                        >
                          <InputNumber
                            addonBefore={formatMessage('MANAGEEXTENSIONSERVICE.MAX')}
                            precision={0}
                            min={formValues.min}
                            style={{ width: '100%' }}
                          />
                        </Form.Item>
                      ) : null}
                      {avlValueTypeValue === 'Enum' ? (
                        <Form.Item
                          name="enum"
                          style={{ display: 'inline-block', width: 'calc(66% + 5px)', marginLeft: '5px' }}
                        >
                          <Select showSearch options={enumSource} filterOption={handleEnumFilter} />
                        </Form.Item>
                      ) : null}
                      {avlValueTypeValue === 'Format' ? (
                        <Form.Item
                          name="typeFormat"
                          style={{ display: 'inline-block', width: 'calc(66% + 5px)', marginLeft: '5px' }}
                        >
                          <Select options={FormatSource} />
                        </Form.Item>
                      ) : null}
                      {avlValueTypeValue === 'Values' ? (
                        <Form.Item
                          name="values"
                          style={{ display: 'inline-block', width: 'calc(66% + 5px)', marginLeft: '5px' }}
                          rules={[{ required: true, message: '' }]}
                        >
                          <Input placeholder={formatMessage('MANAGEEXTENSIONSERVICE.VALUES.PLACEHOLDER')} />
                        </Form.Item>
                      ) : null}
                      {avlValueTypeValue === 'Range' ? (
                        <Form.Item
                          name="min"
                          style={{ display: 'inline-block', width: 'calc(33%)', marginLeft: '5px' }}
                          rules={[{ required: true, message: '' }]}
                        >
                          <InputNumber
                            addonBefore={formatMessage('MANAGEEXTENSIONSERVICE.MIN')}
                            precision={0}
                            max={formValues.max}
                            style={{ width: '100%' }}
                          />
                        </Form.Item>
                      ) : null}
                      {avlValueTypeValue === 'Range' ? (
                        <Form.Item
                          name="max"
                          style={{ display: 'inline-block', width: 'calc(33%)', marginLeft: '5px' }}
                          rules={[{ required: true, message: '' }]}
                        >
                          <InputNumber
                            addonBefore={formatMessage('MANAGEEXTENSIONSERVICE.MAX')}
                            precision={0}
                            min={formValues.min}
                            style={{ width: '100%' }}
                          />
                        </Form.Item>
                      ) : null}
                    </Form.Item>
                    <Form.Item
                      label={formatMessage('MANAGEEXTENSIONSERVICE.REQUEST.PARAM.DEFAULT_VALUE')}
                      name="defValue"
                    >
                      <Input />
                    </Form.Item>
                    <Form.Item label={formatMessage('PROJECT.COMMON.EXAMPLE')} name="example">
                      <Input allowClear />
                    </Form.Item>
                  </>
                )}
              <Form.Item
                label={formatMessage('PROJECT.COMMON.DESCRIPTION')}
                name="comments"
                extra={formatMessage('PROJECT.COMMON.GETDESCBYPROPNAME')}
              >
                <Input.TextArea allowClear onBlur={() => autoFillDescByPropName(form, 'respParamName', 'comments')} />
              </Form.Item>
            </>
          )}
        </Form>
      </Drawer>
      {/* 拷贝Schema */}
      <CopySchemaDrawer
        copyOpen={copyOpen}
        setCopyOpen={setCopyOpen}
        copyingSchema={copyingSchema}
        setNewSchemaName={setNewSchemaName}
        domainSchemaList={domainSchemaList}
        updateDomainSchemaList={updateDomainSchemaList}
      />
    </>
  );
};
export default EditResponseInformation;
