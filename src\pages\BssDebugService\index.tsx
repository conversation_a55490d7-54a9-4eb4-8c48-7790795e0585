import { APIGServiceCatalogItemType, IServiceDetail } from '@/services/typing';
import DebugService from './DebugService';
import BreadcrumbPublic from '@/components/BreadcrumbPublic';
import { LeftOutlined } from '@ant-design/icons';
import { breadCrumbsType } from '@/typing';

import styles from './index.less';
import { useEffect, useState } from 'react';
import { getAPIGServiceCatalog } from '@/services';

interface IServiceDetailCom {
  serviceCode: string;
  onReturn?: () => void;
  debugMode?: boolean;
  serviceDetail?: IServiceDetail;
  APIGServiceCatalogList?: APIGServiceCatalogItemType[];
}

const BssDebugService = ({
  serviceCode,
  onReturn,
  debugMode,
  serviceDetail,
  APIGServiceCatalogList = [],
}: IServiceDetailCom) => {
  const breadCrumbsItem: breadCrumbsType[] = [
    {
      level: 1,
      title: serviceDetail?.serviceName,
      onClick: () => {
        onReturn?.();
      },
      className: styles.breadCrumbsLevelOne,
    },
    {
      level: 2,
      title: 'Debug',
      onClick: () => {},
      className: styles.breadCrumbsLevelTwo,
    },
  ];

  return (
    <div className={styles.bssDebugService}>
      <div className={styles.header}>
        <LeftOutlined
          className={styles.backIcon}
          onClick={() => {
            onReturn?.();
          }}
        />
        <BreadcrumbPublic breadCrumbs={breadCrumbsItem} className={styles.breadCrumbs} />
      </div>
      <div className={styles.bottomArea}>
        <div className={styles.bottom}>
          <DebugService
            serviceCode={serviceCode}
            debugMode={debugMode}
            serviceDetail={serviceDetail}
            isEditAPIGService={false}
            APIGServiceCatalogList={APIGServiceCatalogList}
          />
        </div>
      </div>
    </div>
  );
};

export default BssDebugService;
