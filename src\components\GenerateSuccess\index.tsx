import { CheckOutlined } from "@ant-design/icons";
import useI18n from "@/hooks/useI8n";
import styles from "./index.less";

const GenerateSuccess = () => {
  const { formatMessage } = useI18n();

  return (
    <div className={styles.successWrapper}>
      <div className={styles.title}>
        <div className={styles.icon}>
          <CheckOutlined className={styles.successIcon} />
        </div>
        <div className={styles.text}>
          {formatMessage("DOWNLOAD.GENERATE.SUCCESSFULTEXT")}
        </div>
      </div>
      <div className={styles.tips}>
        {formatMessage("DOWNLOAD.GENERATE.SUCCESSFULTIPS")}
      </div>
    </div>
  );
};

export default GenerateSuccess;
