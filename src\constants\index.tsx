const BYDOMAIN = 'BYDOMAIN';
const BYCATALOG = 'BYCATALOG';

const SERVICETYPE = [
  {
    type: 'Current  Domain  Entity',
    color: '#fff5e6',
  },
  {
    type: 'Other  Domain  Entity',
    color: '#ffffff',
  },
  {
    type: 'TMF Service',
    color: '#facac6',
  },
  {
    type: 'Extended Service',
    color: '#eae3ea',
  },
  {
    type: 'System Service',
    color: '#a7c9fa',
  },
  {
    type: 'Event',
    color: '#99ced4',
  },
];

const ENTITYSERVICETYPE = [
  {
    type: 'Current  Domain  Entity',
    color: '#fff5e6',
  },
  {
    type: 'Other  Domain  Entity',
    color: '#ffffff',
  },
];

const LEGENTOTHERTYPE = [
  {
    type: 'Current Entity',
    color: 'green',
  },
];
const ENTITYLEGENTOTHERTYPE = [
  {
    type: 'AggregateRoot  Entity',
    color: 'green',
  },
];

export { BYDOMAIN, BYCATALOG, SERVICETYPE, ENTITYSERVICETYPE, LEGENTOTHERTYPE, ENTITYLEGENTOTHERTYPE };
