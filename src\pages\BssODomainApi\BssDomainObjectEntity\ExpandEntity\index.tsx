import React, { useEffect, useMemo, useState } from 'react';
import { Popconfirm, Spin, Tooltip, message } from 'antd';
import type { PopconfirmProps } from 'antd';
import { FormOutlined, DeleteOutlined } from '@ant-design/icons';
import { qryExpandEntityList, qryRelaTypeList, deleteEntityExpand } from '@/services/entityService';
import { queryODomainTreeData } from '@/services/domainService';
import classNames from 'classnames';
import styles from '../index.less';
import AddExpandEntityDrawer from './AddExpandEntity';
import EditExpandEntityDrawer from './EditExpandEntity';
import OperationIconTitle from '@/components/OperationIconTitle';
import { SelectedEntityProps, SelectedRootNode, DomainInfoProps } from '../types';
import ResizableTable from '@/components/ResizableTable';
import { useTableFilter } from '@/hooks/useTableFilter';
import useI18n from '@/hooks/useI8n';

interface IExpandEntity {
  selectedEntity: SelectedEntityProps; // 当前节点
  selectedRootNode: SelectedRootNode; // 根节点
  isProduct: boolean;
  ignoreVersionSource: any;
}

const ExpandEntity: React.FC<IExpandEntity> = (props) => {
  const { ignoreVersionSource, selectedEntity, selectedRootNode, isProduct } = props;
  const { formatMessage } = useI18n();

  const [spining, setSpining] = useState<boolean>(false); // menu loading
  const [entityPropertiesSource, setEentityPropertiesSource] = React.useState<any>([]); // 领域实体属性数据源
  const [relationTypeSource, setRelationTypeSource] = React.useState<any>([]); // 关联类型数据源
  const [selectedExpand, setSelectedExpand] = React.useState<any>({}); // 当前选中的实体属性行数据
  const [currentDomainInfo, setCurrentDomainInfo] = React.useState<DomainInfoProps[]>([]); // 当前选中的实体属性行数据

  const [openAddExpandEntityDrawer, setOpenAddExpandEntityDrawer] = useState<boolean>(false); // 新增实体扩展
  const [openEditExpandEntityDrawer, setOpenEditExpandEntityDrawer] = useState<boolean>(false); // 修改实体扩展

  const CreationSource = [
    { name: 'Product-Owned', key: 'P' },
    { name: 'Project-Customized', key: 'C' },
  ];

  const RelTypeSource = [
    { name: 'N -> 1 ( N <-> 1 )', key: '1' },
    { name: '1 -> N ( 1 <-> N )', key: '3' },
    { name: '1 -> 1', key: '5' },
  ];
  // 使用 useMemo 确保 dataSource 是稳定的引用
  const memoizedDataSource = useMemo(
    () => (Array.isArray(entityPropertiesSource) ? entityPropertiesSource : []),
    [entityPropertiesSource],
  );
  // 自定义hooks，支持表格列筛选并分页展示
  const { filteredDataSource, getColumnSearchProps, getColumnEnumSearchProps, clearAllFilters } =
    useTableFilter(memoizedDataSource);

  // 自定义过滤表格列
  const associatedRelationshipFilter = (item: any, obj: any) => {
    if (item.odhEntityRelPropList.length > 0) {
      const propItem = item.odhEntityRelPropList[0];
      const showData = `${selectedEntity?.entityName}.${propItem.propName}=${item.expandEntityName}.${propItem.expandPropName}`;
      return (showData || '')
        .toString()
        .toLowerCase()
        .includes((Object.values(obj)[1] as string).toLowerCase());
    }
    return true; // 默认返回 true
  };

  const deleteEntityExpandService: PopconfirmProps['onConfirm'] = async (e) => {
    const resultData = await deleteEntityExpand(selectedExpand.expandId);

    if (resultData?.success) {
      message.success(formatMessage('EXPANDENTITY.DELETE.SUCCESS'));
      // 删除领域对象后重新渲染数据
      queryExpandEntity();
    }
  };
  // entity list表格
  const columns = [
    {
      dataIndex: 'relTypeNameDisplay',
      title: formatMessage('EXPANDENTITY.RELATIONSHIP_TYPE'),
      width: '13%',
      ellipsis: true,
      ...getColumnEnumSearchProps('relType', RelTypeSource),
    },
    {
      dataIndex: 'expandEntityName',
      title: formatMessage('EXPANDENTITY.EXPAND_ENTITY'),
      width: '13%',
      ellipsis: true,
      ...getColumnSearchProps('expandEntityName'),
    },
    {
      dataIndex: 'odhEntityRelProp',
      title: formatMessage('EXPANDENTITY.ASSOCIATED_RELATIONSHIP'),
      width: '30%',
      ellipsis: true,
      ...getColumnSearchProps('odhEntityRelProp', associatedRelationshipFilter),
      render: (_: string, record: any) => (
        <>
          {record.odhEntityRelPropList?.map((item: any) => (
            <div>
              <span>
                {selectedEntity?.entityName}.{item.propName}
              </span>
              <span>=</span>
              <span>
                {record.expandEntityName}.{item.expandPropName}
              </span>
            </div>
          ))}
        </>
      ),
    },
    {
      dataIndex: 'propName',
      title: formatMessage('EXPANDENTITY.EXPAND_NAME'),
      width: '12%',
      ellipsis: true,
      ...getColumnSearchProps('propName'),
    },
    {
      dataIndex: 'creationSrc',
      title: formatMessage('EXPANDENTITY.CREATION_SOURCE'),
      width: '12%',
      ellipsis: true,
      render: (_: string, record: any) => {
        return CreationSource.filter((i) => i.key === record.creationSrc).map((i) => i.name);
      },
      ...getColumnEnumSearchProps('creationSrc', CreationSource),
    },
    {
      dataIndex: 'comments',
      title: formatMessage('EXPANDENTITY.DESCRIPTION'),
      width: '10%',
      ellipsis: true,
      ...getColumnSearchProps('comments'),
    },
    {
      dataIndex: '',
      title: formatMessage('EXPANDENTITY.OPERATION'),
      width: '10%',
      render: (_: string, record: any) => {
        return (
          <div>
            <Tooltip title={formatMessage('PROJECT.COMMON.EDIT')}>
              <FormOutlined
                className={classNames(styles.iconStyle, {
                  [styles.hide]: selectedRootNode?.state !== 'D',
                })}
                onClick={() => {
                  setOpenEditExpandEntityDrawer(true);
                }}
              />
            </Tooltip>

            <Popconfirm
              title={formatMessage('EXPANDENTITY.DELETE.TITLE')}
              description={formatMessage('EXPANDENTITY.DELETE.DESCRIPTION')}
              onConfirm={deleteEntityExpandService}
              okText={formatMessage('EXPANDENTITY.DELETE.YES')}
              cancelText={formatMessage('EXPANDENTITY.DELETE.NO')}
            >
              {isProduct ? (
                <Tooltip title={formatMessage('PROJECT.COMMON.DELETE')}>
                  <DeleteOutlined
                    className={classNames(styles.versionIcon2, {
                      [styles.hide]: selectedRootNode?.state !== 'D',
                    })}
                    onClick={() => {}}
                  />
                </Tooltip>
              ) : (
                <Tooltip title={formatMessage('PROJECT.COMMON.DELETE')}>
                  <DeleteOutlined
                    className={classNames(styles.versionIcon2, {
                      [styles.hide]: record?.creationSrc === 'P' || selectedRootNode?.state !== 'D',
                    })}
                    onClick={() => {}}
                  />
                </Tooltip>
              )}
            </Popconfirm>
          </div>
        );
      },
    },
  ];

  const queryExpandEntity = async () => {
    setSpining(true);
    try {
      const { success, data } = await qryExpandEntityList(selectedEntity?.entityId as number);
      if (success) {
        setEentityPropertiesSource(data);
      } else {
        setEentityPropertiesSource([]);
      }
      setSpining(false);
    } catch (error) {
      setSpining(false);
    }
  };

  const queryRelationTypeList = async () => {
    const { success, data } = await qryRelaTypeList();
    if (success) {
      setRelationTypeSource(data);
    }
  };

  const queryCurrentDomainInfo = async () => {
    const { success, data } = await queryODomainTreeData();
    if (success) {
      data.odhDomainList?.forEach((item: any) => {
        if (item.domainId === selectedRootNode?.domainId) {
          const odhDomainObjList = [...item.odhDomainObjList];
          let currentIndex = 0;
          let currentDomianObj = {};
          odhDomainObjList.forEach((odhDomainObj, index) => {
            if (odhDomainObj.domainObjId === selectedEntity?.domainObjId) {
              currentDomianObj = odhDomainObj;
              currentIndex = index;
            }
          });
          if (currentIndex !== -1) {
            odhDomainObjList.splice(currentIndex, 1);
            odhDomainObjList.unshift(currentDomianObj);
            setCurrentDomainInfo(odhDomainObjList);
          }
        }
      });
    }
  };

  useEffect(() => {
    // 查询领域对象详情
    queryExpandEntity();
    // 查询关联类型
    queryRelationTypeList();
    // 查询当前领域下的领域对象和实体
    queryCurrentDomainInfo();
    // 重置表格列的筛选条件
    clearAllFilters();
  }, [selectedEntity]);

  return (
    <Spin spinning={spining}>
      {/* 领域实体属性列表 */}
      <div>
        <OperationIconTitle
          title={formatMessage('EXPANDENTITY.TITLE')}
          type={selectedRootNode?.state === 'D' ? 'add' : ''}
          opt={formatMessage('PROJECT.COMMON.ADD')}
          handleClick={() => {
            setOpenAddExpandEntityDrawer(true);
          }}
        />
        <div className={styles.content}>
          <ResizableTable
            size="small"
            style={{ marginTop: '12px' }}
            columns={columns}
            dataSource={filteredDataSource || []}
            rowKey={(record: any) => record?.expandId}
            pagination={{
              hideOnSinglePage: true,
              pageSize: 10,
              showTotal: (total) => formatMessage('PROJECT.COMMON.PAGINATION.TOTAL', { total }),
            }}
            onRow={(record: any) => ({
              onClick: () => {
                setSelectedExpand(record);
              },
            })}
          />
        </div>
      </div>

      {/* 新增实体扩展 */}
      <AddExpandEntityDrawer
        ignoreVersionSource={ignoreVersionSource}
        open={openAddExpandEntityDrawer}
        relationTypeSource={relationTypeSource}
        currentDomainInfo={currentDomainInfo}
        selectedEntity={selectedEntity}
        onCancel={() => setOpenAddExpandEntityDrawer(false)}
        isProduct={isProduct}
        onOk={() => {
          // 新增实体扩展成功后，刷新当前表格
          setOpenAddExpandEntityDrawer(false);
          queryExpandEntity();
        }}
      />
      {/* 修改实体扩展 */}
      <EditExpandEntityDrawer
        ignoreVersionSource={ignoreVersionSource}
        open={openEditExpandEntityDrawer}
        relationTypeSource={relationTypeSource}
        currentDomainInfo={currentDomainInfo}
        selectedEntity={selectedEntity}
        initValue={selectedExpand}
        onCancel={() => setOpenEditExpandEntityDrawer(false)}
        isProduct={isProduct}
        onOk={() => {
          // 新增实体扩展成功后，刷新当前表格
          setOpenEditExpandEntityDrawer(false);
          queryExpandEntity();
        }}
      />
    </Spin>
  );
};

export default ExpandEntity;
