import React, { useEffect, useState } from 'react';
import { IAPIGCatalog } from '@/services/typing.d';
import { DeleteOutlined, DownOutlined, UpOutlined } from '@ant-design/icons';
import classNames from 'classnames';
import { recursionGetFirstAPIGCatalog } from '@/utils/recursionGetFirstSelect';
import { ICurrentActiveKeyAPIGCatalog as ICurrentActiveKey } from '@/typing.d';
import {
  filterServicesBySearchText,
  findCatalogByApigServId,
  getAllCatalogIdsAsStrings,
} from '@/utils/searchDataByKeyWords';
import styles from './index.less';
import { Popconfirm } from 'antd';
import useI18n from '@/hooks/useI8n';
import { useModel } from 'umi';
import { ODH_SUFFIX } from './const';

interface CustomerCollpaseForAPIGServiceType {
  catalogData: Array<IAPIGCatalog>; // 接口数据
  searchvalue: string; // 搜索值
  hasInitFirstSelect: boolean; // 是否初始化过默认选择第一项
  setHasInitFirstSelect: (value: boolean) => void;
  onItemChange?: (entity: ICurrentActiveKey) => void; // 选中项
  deleteApigUseCaseAndUpdateData: (params: { apigServId: number }) => void;
}

const CustomerCollpaseForAPIGService: React.FC<CustomerCollpaseForAPIGServiceType> = (props) => {
  const {
    catalogData,
    searchvalue,
    hasInitFirstSelect,
    setHasInitFirstSelect,
    onItemChange,
    deleteApigUseCaseAndUpdateData,
  } = props;
  const { formatMessage } = useI18n();
  const { isConfigEnvironment } = useModel('useSystemConfigDataModel');

  const [currentActiveKey, setCurrentActiveKey] = useState<ICurrentActiveKey>(); // 记录当前树的展开活跃key
  const [expandKeys, setExpandKeys] = useState<string[]>([]); // 记录当前树的展开活跃key
  const [catalogDataAfterSearch, setCatalogDataAfterSearch] = useState<Array<IAPIGCatalog>>([]); // 搜索之后的值

  // 目录点击
  const handleCollpase = (key: string) => {
    setExpandKeys((prev) => {
      const matchedItem = prev.find((item) => item === key);
      if (matchedItem) {
        return prev.filter((item) => item !== key);
      } else {
        return [...prev, key];
      }
    });
  };

  // 处理搜索
  const handleSearch = (catalogData: Array<IAPIGCatalog>, searchvalue: string) => {
    if (searchvalue) {
      // 关键字搜索 忽略大小写
      const resultsAfterSearch = filterServicesBySearchText(catalogData, searchvalue);
      setCatalogDataAfterSearch(resultsAfterSearch); // 注释这一行，搜索后不符合的目录也显示，只是没自动展开。可根据需求选择是否显示。
      // 搜索完默认展开符合条件的key
      const expandKeysAfterSearch = getAllCatalogIdsAsStrings(resultsAfterSearch);
      setExpandKeys(expandKeysAfterSearch);
    } else {
      // 无搜索 重置选中项的展开情况
      setCatalogDataAfterSearch(catalogData);
      setExpandKeys(currentActiveKey?.clickMenuPath || []);
    }
  };

  // 设置左边距
  const getMarginLeft = (menuLevel: number = 1) => {
    return `${(menuLevel - 1) * 7}px`;
  };

  // 渲染服务列表
  const renderServiceList = (serviceList: any[], catalogGroupItem: any, parentCatalogItem: any) => {
    if (!serviceList || serviceList.length === 0) return null;

    return (
      <div className={classNames(styles.thirdContent)}>
        {serviceList.map((subCatalogItem: any) => (
          <div
            key={subCatalogItem.apigServId}
            onClick={() =>
              setCurrentActiveKey({
                clickCatalog: parentCatalogItem,
                clickMenuPath: catalogGroupItem?.menuPath,
                clickSubCatalog: subCatalogItem,
              })
            }
            className={classNames(styles.lineContainer, styles.thirdLineContainer, {
              [styles.activeLineContainer]: subCatalogItem.apigServId === currentActiveKey?.clickSubCatalog?.apigServId,
            })}
          >
            <span
              style={{ marginLeft: getMarginLeft(catalogGroupItem?.menuPath?.length) }}
              className={classNames(styles.groupTitle, styles.groupThirdTitle)}
              title={subCatalogItem.apiMethod}
            >
              {subCatalogItem.apiMethod}
              {ODH_SUFFIX}
            </span>
            <Popconfirm
              title=""
              description={formatMessage('DEBUGSERVICE.USECASEBESELECTEDMODAL.CONFIRMTIP')}
              onConfirm={(e) => {
                e?.stopPropagation();
                deleteApigUseCaseAndUpdateData({ apigServId: subCatalogItem?.apigServId });
              }}
              onCancel={(e) => {
                e?.stopPropagation();
              }}
              okText="Yes"
              cancelText="No"
            >
              {isConfigEnvironment && (
                <DeleteOutlined
                  style={{ fontSize: '14px' }}
                  onClick={(e) => {
                    e.stopPropagation();
                  }}
                />
              )}
            </Popconfirm>
          </div>
        ))}
      </div>
    );
  };

  const renderCatalogItems = (catalogData: IAPIGCatalog[]) => {
    return catalogData.map((catalogItem) => (
      <div className={styles.firstContent} key={catalogItem?.catalogId}>
        {/* 一级标题 */}
        <div
          key={`${catalogItem?.catalogId}-header`}
          className={styles.lineContainer}
          onClick={() => handleCollpase(`${catalogItem.catalogId}`)}
        >
          <span className={classNames(styles.groupTitle, styles.groupFirstTitle)} title={catalogItem.catalogName}>
            {catalogItem.catalogName}
          </span>
          <div className={styles.groupOtherInfo}>
            <span className={styles.amount}>{catalogItem.serviceAmount}</span>
            {!expandKeys.find((item) => item === `${catalogItem.catalogId}`) ? (
              <DownOutlined className={styles.collpaseIcon} />
            ) : (
              <UpOutlined className={styles.collpaseIcon} />
            )}
          </div>
        </div>
        {/* 中间级区域 */}
        {catalogItem?.catalogChildNodes?.length ? (
          <div
            key={`${catalogItem?.catalogId}-group`}
            className={classNames(styles.secondContent, {
              [styles.hide]: !expandKeys.find((item) => item === `${catalogItem.catalogId}`),
            })}
          >
            {renderCatalogChildNodes(catalogItem.catalogChildNodes, catalogItem)}
          </div>
        ) : null}
      </div>
    ));
  };

  // 递归渲染中间级菜单和最后一级菜单
  const renderCatalogChildNodes = (catalogChildNodes: any, parentCatalogItem: any) => {
    return catalogChildNodes.map((catalogGroupItem: any) => (
      <React.Fragment key={catalogGroupItem.catalogId}>
        {/* 中间级标题 */}
        <div className={styles.lineContainer} onClick={() => handleCollpase(`${catalogGroupItem.catalogId}`)}>
          <span
            style={{ marginLeft: getMarginLeft(catalogGroupItem?.menuPath?.length) }}
            className={classNames(styles.groupTitle, styles.groupSecondTitle)}
            title={catalogGroupItem.catalogName}
          >
            {catalogGroupItem.catalogName}
          </span>
          <div className={styles.groupOtherInfo}>
            <span className={styles.amount}>{catalogGroupItem.serviceAmount}</span>
            {!expandKeys.find((item) => item === `${catalogGroupItem.catalogId}`) ? (
              <DownOutlined className={styles.collpaseIcon} />
            ) : (
              <UpOutlined className={styles.collpaseIcon} />
            )}
          </div>
        </div>

        <div
          className={classNames(styles.thirdContent, {
            [styles.hide]: !expandKeys.find((item) => item === `${catalogGroupItem.catalogId}`),
          })}
        >
          {/* 渲染服务列表，如果 isFirstLevel 为 false 且 serviceList 有数据 */}
          {!catalogGroupItem?.isFirstLevel &&
            catalogGroupItem?.serviceList?.length > 0 &&
            renderServiceList(catalogGroupItem.serviceList, catalogGroupItem, parentCatalogItem)}

          {/* 如果是中间级菜单且有子节点，递归渲染子节点 */}
          {catalogGroupItem?.isParent &&
            catalogGroupItem.catalogChildNodes &&
            renderCatalogChildNodes(catalogGroupItem.catalogChildNodes, catalogGroupItem)}
        </div>
      </React.Fragment>
    ));
  };

  // 默认展示 + 搜索
  // 初始化 展示第一个符合条件的
  // 已初始化 应用手动输入的搜索值
  // 无数据 置空
  useEffect(() => {
    if (catalogData?.length && !hasInitFirstSelect) {
      // 初始化 查找第一个符合条件的选项
      const catalogFirstResult = recursionGetFirstAPIGCatalog(catalogData);
      setCatalogDataAfterSearch(catalogData);
      setCurrentActiveKey({
        clickCatalog: catalogFirstResult?.catalog,
        clickMenuPath: catalogFirstResult?.menuPath,
        clickSubCatalog: catalogFirstResult?.subCatalog,
      });
      setExpandKeys(catalogFirstResult?.menuPath || []);
      setHasInitFirstSelect(true);
    } else if (catalogData?.length && hasInitFirstSelect) {
      handleSearch(catalogData, searchvalue);
    } else {
      // 没有数据，重置所有状态
      setCatalogDataAfterSearch([]);
      setExpandKeys([]);
      onItemChange?.({});
    }
  }, [catalogData, searchvalue]);

  // 通知父级别当前项切换
  useEffect(() => {
    if (currentActiveKey?.clickSubCatalog) {
      onItemChange?.(currentActiveKey);
    }
  }, [currentActiveKey]);

  return <>{renderCatalogItems(catalogDataAfterSearch)}</>;
};

export default CustomerCollpaseForAPIGService;
