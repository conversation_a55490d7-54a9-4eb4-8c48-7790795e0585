.commonStyle {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  box-sizing: border-box;
}

.bssApiLeftContainer {
  // padding: 8px;
  .commonStyle();

  :global {
    .ant-row {
      margin: 0 !important;
    }

    .ant-row,
    .ant-col {
      height: 100%;
    }

    .ant-row {
      width: 100%;
    }

    .ant-col:nth-child(1) {
      padding-left: 0 !important;
    }

    .ant-col:nth-child(2) {
      padding-right: 0 !important;
    }
  }
}

.bssODataApi {
  .commonStyle();
  background-color: #ebecf1;
}


