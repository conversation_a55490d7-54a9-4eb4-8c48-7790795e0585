import { request, useRequest } from "umi";
import { stringify } from "qs";
import { enCodeParamsFn, filteredZeroWidthSpace } from '@/utils/common';
import { portalLanguage } from '@/global';

// 查询catalog
export async function queryDomainMenu() {
  return request('/odh-web/odh/web/domain/catalog', {
    method: 'GET',
  });
}

// 查询domain
export async function queryCatalogMenu() {
  return request('/odh-web/odh/web/catalog/service', {
    method: 'GET',
  });
}

// 查询entity detail
export async function queryEntityDetailByEntityKey(params: { entityKey: string }) {
  const { entityKey } = params;
  return request(`/odh-web/odh/web/entity/detail?entityKey=${entityKey}`, {
    method: 'GET',
  });
}

// 查询catalog detail
export async function queryCatalogDetailByName(params: { catalog: string; group: string; subCatalog: string }) {
  return request(`/odh-web/odh/web/subCatalog/service/list?${stringify(params)}`, {
    method: 'GET',
  });
}

// 查询service detail
export async function queryServiceDetailByCode(params: { serviceCode: string }) {
  return request(`/odh-web/odh/web/service/detail?${stringify(params)}`, {
    method: 'GET',
  });
}

// 缓存刷新
export async function refreshCache() {
  return request('/odh-service/odh/reload', {
    method: 'GET',
  });
}

// 缓存刷新
export async function clearRedis(params: { type: string; id?: number }) {
  return request(`/odh-web/odh/web/common/clearRedis?${stringify(params)}`, {
    method: 'GET',
  });
}

// 后端生成文件
export async function generateFile(data: {
  selectCodes: string[];
  selectType: string;
  fileType: string;
  version?: string;
  exampleFlag?: boolean;
}) {
  return request('/odh-web/odh/web/generateFile ', {
    method: 'POST',
    data,
  });
}

// 下载文件
export async function downloadFile(params: { fileName: string }) {
  return request(`/odh-web/odh/web/downloadFile?${stringify(params)}`, {
    method: 'GET',
    responseType: 'blob',
    getResponse: true,
  });
}

// 删除文件
export async function deleteFile(params: { fileName: string }) {
  return request(`/odh-web/odh/web/deleteFile?${stringify(params)}`, {
    method: 'DELETE',
  });
}

export interface debugServiceProps {
  url: string;
  query?: { [k: string]: any };
  body?: any;
  method: string;
  prefix?: string; // 动态的域名主机号端口号
  headers?: { [key: string]: string };
}

// debug 服务
export async function debugService({ url, query, body, method, headers }: debugServiceProps) {
  let paramData: { [k: string]: any } = {};
  const language = portalLanguage === 'zh' ? 'zh-CN' : 'en-US';
  if (typeof query === 'object' && Object.keys(query)?.length) {
    paramData.params = query;
    return request(`${url}?${encodeURI(enCodeParamsFn(query))}`, {
      method,
      // ...paramData,
      data: body || null,
      getResponse: true,
      changeOrigin: true,
      headers: {
        ...(filteredZeroWidthSpace(headers) || {}),
        'Accept-Language': language,
      },
    });
  } else {
    paramData.data = body || {};
    return request(`${url}`, {
      method,
      ...paramData,
      getResponse: true,
      changeOrigin: true,
      headers: {
        ...(filteredZeroWidthSpace(headers) || {}),
        'Accept-Language': language,
      },
    });
  }
}

export interface saveAsUseCasePropsType {
  useCaseName: string;
  comments?: string;
  includeResp?: string;
}

export interface saveAsAPIGPropsType {
  apigServId?: number;
  domainName: string;
  accessRelativeUrl: string;
  odataParam?: string;
  apiMethod: string;
  apiCatalogId: number;
  serviceCode: string;
  comments?: string;
  useCase?: any;
  paramList?: any;
}

export interface APIGServiceDetailType {
  apigServId: number;
  domainName: string;
  accessRelativeUrl: string;
  odataParam: string;
  apiMethod: string;
  apiCatalogId: number;
  serviceCode: string;
  comments: string;
  apigParams: any;
}

export interface saveAsUseCaseServicePropsType extends saveAsUseCasePropsType {
  serviceCode?: string;
  useCaseId?:number;
  serviceRespParamList?: {
    respTime: number;
    succFlag: string;
    respCode: string;
  }[];
  serviceReqParamList?: {
    reqIn?: string;
    paramName?: string;
    paramValue?: string | object;
  }[];
}

// debug---save as use case
export async function saveAsUseCaseService(
  data: saveAsUseCaseServicePropsType
) {
  return request("/odh-web/odh/web/service/useCase", {
    method: "POST",
    data,
  });
}

export interface getUseCaseServicePropsType {
  pageNo: number;
  pageSize: number | string;
  useCaseName?: string;
  serviceCode: string;
}

// 查询 Use Case 服务
export async function getUseCaseService(params: getUseCaseServicePropsType) {
  return request("/odh-web/odh/web/service/useCase", {
    method: "GET",
    params,
  });
}

// Use Case, 删除
export async function deleteUseCaseService(useCaseId: string | number) {
  return request(`/odh-web/odh/web/service/useCase/${useCaseId}`, {
    method: "DELETE",
  });
}

export interface getUseCasePageListServicePropsType {
  startDate: string;
  endDate: string;
  useCaseName: string;
}

// Service Use Case Page，获取列表
export async function getUseCasePageListService(
  params: getUseCasePageListServicePropsType
) {
  return request("/odh-web/odh/web/service/useCase/list", {
    method: "GET",
    params,
  });
}

// AICC 获取 领域实体信息
export async function getAICCPrompt() {
  return request('/odh-web/odh/web/entity/getOdhPromptVo',{
    method: 'GET'
  })
}

// 查询APIG的服务分类
export async function getAPIGServiceCatalog () {
  return request('/odh-web/odh/apig/catalogs',{
    method: 'GET'
  })
}

// 按分类查询APIG服务列表
export async function getAPIGServiceList () {
  return request('/odh-web/odh/apig/serviceList',{
    method: 'GET'
  })
}

// 查询APIG服务详情
export async function getAPIGServiceDetail (params:{apigServId:number}) {
  return request(`/odh-web/odh/apig/serviceDetail?${stringify(params)}`,{
    method: 'GET'
  })
}

// 查询apig服务的usecase
export async function getAPIGUseCase (params:{apigServId:number}) {
  return request(`/odh-web/odh/apig/usecase?${stringify(params)}`,{
    method: 'GET'
  })
}

// 新增/修改APIG服务
export async function saveAsAPIGService(param:string,
  data: saveAsAPIGPropsType
) {
  return request(`/odh-web/odh/apig/serv/${param}`, {
    method: "POST",
    data,
  });
}

// 删除APIG服务的usecase
export async function deleteApigService (params:{apigServId:number}) {
  return request(`/odh-web/odh/apig/deleteApigService?${stringify(params)}`,{
    method: 'GET'
  })
}

// 根据catalogId查询已有的apig服务接口
export async function qryServiceListByCataLogId (params:{catalogId:number}) {
  return request(`/odh-web/odh/apig/qryServiceListByCataLogId?${stringify(params)}`,{
    method: 'GET'
  })
}

// 根据apiId查询apig服务基本信息
export async function qryApiBaseInfoByApiId (params:{apiId:number}) {
  return request(`/odh-web/odh/apig/qryApiBaseInfoByApiId?${stringify(params)}`,{
    method: 'GET'
  })
}

// 刷新APIG Service
export async function refreshApigService (params:{apigServId:number}) {
  return request(`/odh-web/odh/apig/refreshApigService?${stringify(params)}`,{
    method: 'GET'
  })
}

// debug 扩展服务
export async function debugExtensionService(
  serviceId: number,
  data: {
    appCode: string;
    queryParams: object;
    pathParams: object;
    headerParams: object;
    cookieParams: object;
    body: object;
  },
) {
  return request(`/odh-web/odh/web/serviceTest/${serviceId}`, {
    method: 'POST',
    data,
    getResponse: true,
  });
}