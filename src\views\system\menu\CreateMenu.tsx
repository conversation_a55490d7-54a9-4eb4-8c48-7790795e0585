import { Form, Input, InputNumber, Modal, Radio, Select, TreeSelect } from "antd";
import { InfoCircleOutlined } from '@ant-design/icons'
import { useEffect, useImperativeHandle, useState } from "react";
import type { IAction } from "../../../types/modal";
import type { Menu } from "../../../types/api";
import api from "../../../api";
import { message } from "../../../utils/AntdGlobal";

export default function CreateMenu(props: any) {
    const [form] = Form.useForm()
    const [action, setAction] = useState<IAction>('create')
    const [visible, setVisible] = useState(false)
    const [menuList, setMenuList] = useState<Menu.menuItem[]>([])
    const [typeValue, setTypeValue] = useState(1)
    const [stateValue, setStateValue] = useState(1)

    const menuType = [
        { value: 1, label: '菜单' },
        { value: 2, label: '按钮' },
        { value: 3, label: '页面' },
    ]

    const menuState = [
        { value: 1, label: '正常' },
        { value: 2, label: '停用' },
    ]


    useImperativeHandle(props.ref, () => {
        return { open }
    })
    const open = (type: IAction, data?: Menu.editParams | { parentId: string }) => {
        setVisible(true)
        setAction(type)
        getMenuList()
        if (data) {
            form.setFieldsValue(data)
        }
    }
    useEffect(() => {
        getMenuList()
    }, [])

    const getMenuList = async () => {
        const data = await api.getMenuList()
        setMenuList(data)
    }


    const handleSubmit = async () => {
        const valid = await form.validateFields()
        if (valid) {
            if (action === 'create') {
                await api.createMenu(form.getFieldsValue())
                message.success("创建成功")
            } else {
                await api.editMenu(form.getFieldsValue())
                message.success("修改成功")
            }
            handleCancel()
            props.update()
        }
    }
    const handleCancel = () => {
        setVisible(false)
        form.resetFields()
    }
    return (
        <>
            <Modal
                title={action === 'create' ? '创建菜单' : '编辑菜单'}
                width={800}
                open={visible}
                okText="确定"
                cancelText="取消"
                onOk={handleSubmit}
                onCancel={handleCancel}
            >
                <Form
                    form={form}
                    labelCol={{ span: 4 }}
                    labelAlign="right"
                    initialValues={{ menuType: 1, menuState: 1 }}
                >
                    <Form.Item name="_id" hidden><Input /></Form.Item>
                    <Form.Item name="parentId" label="上级菜单">
                        <TreeSelect
                            placeholder="请选择父级菜单"
                            allowClear
                            treeDefaultExpandAll
                            fieldNames={{ label: 'menuName', value: '_id' }}
                            treeData={menuList}
                        />
                    </Form.Item>
                    <Form.Item name="menuType" label="菜单类型">
                        <Radio.Group value={typeValue} options={menuType} />
                    </Form.Item>
                    <Form.Item name="menuName" label="菜单名称" rules={[
                        { required: true, message: "请输入菜单名称" }
                    ]}>
                        <Input placeholder="请输入菜单名称" />
                    </Form.Item>
                    <Form.Item noStyle shouldUpdate>
                        {() => {
                            return form.getFieldValue('menuType') === 2 ? (
                                <Form.Item name="menuCode" label="权限标识">
                                    <Input placeholder="请输入权限标识" />
                                </Form.Item>
                            ) : (
                                <>
                                    <Form.Item name="icon" label="菜单图标">
                                        <Input placeholder="请输入菜单图标" />
                                    </Form.Item>
                                    <Form.Item name="path" label="路由地址">
                                        <Input placeholder="请输入路由地址" />
                                    </Form.Item>
                                </>
                            )
                        }}
                    </Form.Item>
                    <Form.Item name="component" label="组件名称">
                        <Input placeholder="请输入组件名称" />
                    </Form.Item>
                    <Form.Item name="orderBy" label="排序" tooltip={{ title: "排序值越大越靠后", icon: <InfoCircleOutlined /> }}>
                        <InputNumber placeholder="请输入排序值" />
                    </Form.Item>
                    <Form.Item name="menuState" label="菜单状态">
                        <Radio.Group value={stateValue} options={menuState} />
                    </Form.Item>
                </Form>
            </Modal>
        </>
    )
}