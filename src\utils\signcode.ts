/**
 * 根据项目要求，请求中增加signcode，本文件实现生成signcode功能
 * 原始代码在signcode.bat.js 文件
 */

const getUuid = (len) => {
  var res = "",
    str = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
  var strLen = str.length;
  for (var i = 0; i < len; i++) {
    res += str.charAt(Math.floor(Math.random() * strLen));
  }
  return res;
};

const getSigUrl = function (configUrl) {
  return configUrl
    .replace(/%[0-9A-F]{2}/g, "")
    .replace(/[^a-zA-Z0-9-_.!~*()]/g, "");
};

const createSignature = function (config, token) {
  // console.log('测试数据1 config. token', config, token);
  const signatureEnable = sessionStorage.getItem("signature_enable");
  if (signatureEnable) {
    let dataStr = "";
    let url = config.url;
    if (config.params && Object.keys(config.params).length) {
      url =
        url +
        "?" +
        Object.keys(config.params)
          .map((key) => `${key}=${config.params[key]}`)
          .join("&");
    }
    let sendUrl = getSigUrl(url);
    let path = sendUrl.replace(/^odh-web/, "");

    // 排除FormData的影响
    if (config.data && !(config.data instanceof FormData)) {
      const data = config.data || {};
      dataStr = JSON.stringify(data);
    }

    const salt = sessionStorage.getItem("salt");
    const timeDiff = sessionStorage.getItem("server_time_diff") || "0";

    const appTimeStamp = new Date().valueOf() + parseInt(timeDiff, 10);
    const appNonce = getUuid(12);
    const signStr = [appTimeStamp, appNonce, path, dataStr, token, salt]
      .filter((item) => !!item)
      .join("");
    const appSignature = window.top.fish.SHA256(signStr).toString();
    // console.log('测试数据2 appTimeStamp, appNonce,path, dataStr,token, salt', appTimeStamp, appNonce,path, dataStr,token, salt);
    // console.log('测试数据3 signStr', signStr);
    // console.log('测试数据4 最终数据', {
    //   "app-timestamp": appTimeStamp,
    //   "app-nonce": appNonce,
    //   "app-signature": appSignature,
    //   });

    return {
      "app-timestamp": appTimeStamp,
      "app-nonce": appNonce,
      "app-signature": appSignature,
    };
  }
  return {};
};

export default createSignature;
