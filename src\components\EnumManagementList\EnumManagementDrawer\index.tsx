import React, { forwardRef, useEffect, useImperativeHandle, useState } from 'react';
import useI18n from '@/hooks/useI8n';
import { Button, Drawer, Space, Form, Input, Select, TableColumnsType, message } from 'antd';
import { OperationIcon } from '@/components/OperationIconTitle';
import OperationGroup from '@/components/OperationGroup';
import {
  queryDomainDataModelListService,
  addDomainEnumListService,
  modifyDomainEnumListService,
  queryColumnsService,
} from '@/services/enumListService';
import styles from './index.less';
import ResizableTable from '@/components/ResizableTable';
import SelectWithAddButton from '@/components/SelectWithAddButton';
import getUUid from '@/utils/getUUid';

const EnumTypeOptions = [
  { value: 'M', label: 'Data Model' },
  { value: 'T', label: 'Constant' },
  { value: 'D', label: 'Dubbo API' },
];

const CreationSrcOptions = [
  { value: 'P', label: 'Product-Owned' },
  { value: 'C', label: 'Project-Customized' },
];

const basicDisabledObj = {
  isEnumName: false,
  isEnumType: false,
  isCreationSrc: false,
  isComments: false,
  isModelId: false,
  isNameColumnName: false,
  isValueColumnName: false,
  isConstEnumList: false,
};

interface IEnumManagementDrawer {
  domainObjId: number | null;
  isProduct: boolean | undefined;
  afterSubmit: (el: any) => any;
}

interface OptionItemType {
  label: string;
  value: string | number;
  key: string | number;
  state: string; // new | original
}

const EnumManagementDrawer = forwardRef(
  ({ domainObjId = null, isProduct = false, afterSubmit = () => {} }: IEnumManagementDrawer, ref) => {
    const { formatMessage } = useI18n();

    const [form] = Form.useForm();

    const [open, setOpen] = useState(false);

    const [drawerTitle, setDrawerTitle] = useState('');

    // 记录当前的模式
    const [mode, setMode] = useState('');
    const [curCreationSrc, setCurCreationSrc] = useState('');

    // 保存当前的记录
    const [curRecord, setCurRecord] = useState<any>({});

    // 保存当前的枚举类型
    const [curEnumType, setCurEnumType] = useState('');

    // 控制是否可编辑
    const [isDisabledObj, setIsDisabledObj] = useState(basicDisabledObj);

    // 设置 dataModeOptions
    const [dataModelOptions, setDataModelOptions] = useState<{ name: string; value: string }[]>([]);

    const queryDomainDataModelListFn = async (id: number | null) => {
      if (id) {
        try {
          await queryDomainDataModelListService(id, {
            pageNo: 0,
            pageSize: 0,
          }).then((res) => {
            const { success = false, data = [] } = res;
            if (success) {
              const { list = [] } = data;
              let filterList: any = [];
              // 筛选 Domain Model
              if (list?.length > 0) {
                filterList = list?.filter((el: any) => el?.modelClass === 'D');
              }
              const temp = filterList?.map((el: any) => ({
                label: el?.modelName,
                value: el?.modelId,
              }));
              setDataModelOptions(temp);
            }
          });
        } catch (error) {}
      }
    };

    // 记录当前的modelId，用于查询列名
    const [curModelId, setCurModelId] = useState(null);
    // 设置列名
    const [columnOptions, setColumnOptions] = useState<OptionItemType[]>([]);

    const queryColumnsFn = async (modelId: number) => {
      try {
        await queryColumnsService({ modelId }).then((res) => {
          const { data = [], success = false } = res;
          if (success) {
            const keysList: any = Object?.keys(data) || [];
            setColumnOptions(
              keysList?.map((el: string) => ({
                label: el,
                value: el,
                key: getUUid(6),
                state: 'original',
              })),
            );
          } else {
            setColumnOptions([]);
          }
        });
      } catch (error) {
        setColumnOptions([]);
      }
    };

    useEffect(() => {
      if (curModelId) {
        queryColumnsFn(curModelId);
      }
    }, [curModelId]);

    // 关闭 Drawer 时的操作
    const onClose = () => {
      setOpen(false);
      setMode('');
      setDrawerTitle('');
      setCurEnumType('');
      form.resetFields();
    };

    const enterAddMode = (id: number | null) => {
      queryDomainDataModelListFn(id);
      setMode('add');
      setOpen(true);
      setDrawerTitle(formatMessage('ENUMMANAGEMENTLIST.DRAWER.ADDTITLE'));
    };

    const enterEditMode = (record: any, id: number | null) => {
      queryDomainDataModelListFn(id);
      setCurRecord(record);
      setMode('edit');
      setOpen(true);
      setDrawerTitle(formatMessage('ENUMMANAGEMENTLIST.DRAWER.EDITTITLE'));
      const { constEnumList = [], enumType = '', dataModelEnum = {} } = record;
      setCurEnumType(enumType);
      const temp = constEnumList.map((item: any, index: number) => {
        return {
          ...item,
          key: index,
          isEdit: false,
          isProduct,
        };
      });
      const tempDataModel = {
        modelId: dataModelEnum?.modelId,
        valueColumnName: dataModelEnum?.valueColumnName,
        nameColumnName: dataModelEnum?.nameColumnName,
      };
      setCurModelId(dataModelEnum?.modelId);
      form.setFieldsValue({ ...record, ...tempDataModel, constEnumList: temp });
    };

    useImperativeHandle(ref, () => ({
      // 暴露的方法
      enterAddMode,
      enterEditMode,
    }));

    // 传给子组件SelectWithAddButton的自定义方法，将输入自动转为大写
    const handleCustomInputChange = (value: string) => {
      return value.toUpperCase();
    };

    const onSubmit = () => {
      try {
        form.validateFields().then(async (nameList) => {
          const { enumType = '', constEnumList = [], modelId, valueColumnName, nameColumnName } = nameList;
          switch (mode) {
            case 'add':
              const addParams = {
                ...nameList,
                domainObjId,
              };

              if (enumType === 'T') {
                addParams.constEnumList = constEnumList.map((item: any) => {
                  const { constName = '', constValue = '' } = item;
                  return {
                    constName,
                    constValue,
                  };
                });
              } else if (enumType === 'M') {
                addParams.dataModelEnum = {
                  modelId,
                  valueColumnName,
                  nameColumnName,
                };
              }

              await addDomainEnumListService(addParams).then((res) => {
                const { success = false } = res;
                if (success) {
                  message.success(formatMessage('ENUMMANAGEMENTLIST.ADD.SUCCESS'));
                  onClose();
                  afterSubmit(null);
                }
              });
              break;
            case 'edit':
              const { state = '', enumId } = curRecord;
              const editParams = {
                ...nameList,
                domainObjId,
                state,
                enumId,
              };
              if (enumType === 'T') {
                editParams.constEnumList = constEnumList.map((item: any) => {
                  const { constName = '', constValue = '', enumId, enumConstId } = item;
                  return enumConstId
                    ? {
                        constName,
                        constValue,
                        enumId,
                        enumConstId,
                        operation: 'UPDATE',
                      }
                    : {
                        constName,
                        constValue,
                        enumId,
                        operation: 'ADD',
                      };
                });
              } else if (enumType === 'M') {
                editParams.dataModelEnum = {
                  enumId,
                  modelId,
                  valueColumnName,
                  nameColumnName,
                };
              }
              await modifyDomainEnumListService(editParams).then((res) => {
                const { success = false } = res;
                if (success) {
                  message.success(formatMessage('ENUMMANAGEMENTLIST.EDIT.SUCCESS'));
                  onClose();
                  afterSubmit(enumId);
                }
              });
            default:
              break;
          }
        });
      } catch (error) {}
    };

    // 定义抽屉页脚
    const DrawerButton = (
      <Space style={{ float: 'right' }}>
        <Button
          type="primary"
          onClick={() => {
            onSubmit();
          }}
        >
          {formatMessage('DATAMODELLIST.DRAWER.SUBMIT')}
        </Button>
        <Button onClick={onClose}>{formatMessage('DATAMODELLIST.DRAWER.CANCEL')}</Button>
      </Space>
    );

    const getColumns = (remove: any) => {
      return [
        {
          title: formatMessage('ENUMMANAGEMENTLIST.TABLE.VALUE'),
          dataIndex: 'constValue',
          ellipsis: true,
          render(text, field, index) {
            const record = form.getFieldValue('constEnumList')?.[index];
            const isEdit = record?.isEdit || false;
            return (
              <Form.Item
                required
                name={[field?.name, 'constValue']}
                rules={[{ required: true, message: formatMessage('ENUM.FORM.PLEASE_ENTER_VALUE') }]}
                shouldUpdate
              >
                {isEdit ? (
                  <Input allowClear placeholder={formatMessage('ENUM.FORM.PLEASE_ENTER_VALUE')} />
                ) : (
                  <div title={record?.constValue} className={styles.textSpan}>
                    {record?.constValue}
                  </div>
                )}
              </Form.Item>
            );
          },
        },
        {
          title: formatMessage('ENUMMANAGEMENTLIST.TABLE.NAME'),
          dataIndex: 'constName',
          ellipsis: true,
          render(text, field, index) {
            const record = form.getFieldValue('constEnumList')?.[index];
            const isEdit = record?.isEdit || false;
            return (
              <Form.Item required name={[field?.name, 'constName']} shouldUpdate>
                {isEdit ? (
                  <Input allowClear placeholder={formatMessage('ENUM.FORM.PLEASE_ENTER_NAME')} />
                ) : (
                  <div title={record.constName} className={styles.textSpan}>
                    {record.constName}
                  </div>
                )}
              </Form.Item>
            );
          },
        },
        {
          title: formatMessage('ENUMMANAGEMENTLIST.TABLE.OPT'),
          dataIndex: 'opt',
          width: '100px',
          render(text, field, index) {
            const record = form.getFieldValue('constEnumList')?.[index];
            const isEdit = record?.isEdit || false;
            const recordList = form.getFieldValue('constEnumList');
            if (isDisabledObj.isConstEnumList && curEnumType === 'T' && mode === 'edit') {
              return <></>;
            }
            if (!isEdit) {
              return (
                <OperationGroup
                  btnList={[
                    {
                      value: 'edit',
                      onClick: () => {
                        const newList = recordList?.map((item: any) => {
                          if (item?.key === record?.key) {
                            return { ...item, isEdit: true };
                          }
                          return item;
                        });
                        form.setFieldsValue({ ['constEnumList']: newList });
                      },
                    },
                  ]}
                />
              );
            }
            if (isEdit) {
              const saveFn = () => {
                const list = [...form.getFieldValue(['constEnumList'])];
                const tempList = list?.map((item) => {
                  if (item?.key === record?.key) {
                    return {
                      ...item,
                      isEdit: false,
                    };
                  }
                  return { ...item };
                });
                form.setFieldsValue({
                  ['constEnumList']: tempList,
                });
              };
              return (
                <OperationGroup
                  btnList={[
                    {
                      value: 'save',
                      onClick: () => {
                        form.validateFields(['constEnumList']).then(
                          (res) => {
                            if (res?.constEnumList?.every((el: any) => el?.constValue)) {
                              saveFn();
                            } else {
                              form.validateFields();
                            }
                          },
                          (params) => {
                            const isHere = params?.errorFields?.some((el: any) => el.name[0] === 'constEnumList');
                            if (!isHere) {
                              saveFn();
                            }
                          },
                        );
                      },
                    },
                    {
                      value: 'cancel',
                      onClick: () => {
                        remove(field.name);
                      },
                    },
                  ]}
                />
              );
            }
          },
        },
      ] as TableColumnsType;
    };

    useEffect(() => {
      if (mode === 'add') {
        if (isProduct) {
          form.setFieldsValue({
            creationSrc: 'P',
          });
          setCurCreationSrc('P');
        } else {
          form.setFieldsValue({
            creationSrc: 'C',
          });
          setCurCreationSrc('C');
        }
        setIsDisabledObj({
          ...basicDisabledObj,
          isCreationSrc: true,
        });
      }
      if (mode === 'edit') {
        if (isProduct) {
          setIsDisabledObj({
            ...basicDisabledObj,
            isCreationSrc: true,
          });
        } else {
          const { creationSrc = '' } = curRecord;
          switch (creationSrc) {
            case 'P':
              setIsDisabledObj({
                ...basicDisabledObj,
                isEnumName: true,
                isEnumType: true,
                isCreationSrc: true,
                isModelId: true,
                isNameColumnName: true,
                isValueColumnName: true,
                isConstEnumList: true,
              });
              break;
            case 'C':
              setIsDisabledObj({
                ...basicDisabledObj,
                isCreationSrc: true,
              });
              break;
            default:
              break;
          }
        }
      }
    }, [isProduct, mode, curRecord]);

    useEffect(() => {
      console.log('curRecord', curRecord);
    }, [curRecord]);

    return (
      <Drawer title={drawerTitle} open={open} onClose={onClose} footer={DrawerButton} width={720} maskClosable={false}>
        <Form
          form={form}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
          onValuesChange={(value) => {
            const { enumType = '' } = value;
            if (enumType) {
              setCurEnumType(enumType);
            }
          }}
        >
          <Form.Item
            label={formatMessage('ENUMMANAGEMENTLIST.TABLE.NAME')}
            name="enumName"
            rules={[{ required: true }]}
          >
            <Input disabled={isDisabledObj.isEnumName} />
          </Form.Item>
          <Form.Item
            label={formatMessage('ENUMMANAGEMENTLIST.TABLE.TYPE')}
            name="enumType"
            rules={[{ required: true }]}
          >
            <Select disabled={isDisabledObj.isEnumType} options={EnumTypeOptions} />
          </Form.Item>
          <Form.Item
            label={formatMessage('ENUMMANAGEMENTLIST.TABLE.CREATIONSOURCE')}
            name="creationSrc"
            rules={[{ required: true }]}
          >
            <Select disabled={isDisabledObj.isCreationSrc} options={CreationSrcOptions} />
          </Form.Item>
          {curEnumType === 'T' && (
            <Form.Item
              label={formatMessage('ENUMMANAGEMENTLIST.DRAWER.CONSTANTLIST')}
              rules={[{ required: true }]}
              name="constEnumList"
            >
              <Form.List name="constEnumList">
                {(fields, { add, remove }, { errors }) => (
                  <>
                    {!(isDisabledObj.isConstEnumList && curEnumType === 'T' && mode === 'edit') && (
                      <OperationIcon
                        handleClick={() => {
                          let curKey = 0;
                          let allowAdd = false;
                          if (fields.length) {
                            curKey = Number(fields[fields.length - 1].key + 1);
                          }
                          const values = form.getFieldValue('constEnumList');
                          allowAdd = values?.every((item: any) => item.isEdit === false) || fields.length === 0;
                          if (allowAdd) {
                            add({ isEdit: true, key: curKey });
                          }
                        }}
                        opt={formatMessage('DATAMODELLIST.TABLE.ADD')}
                        style={{ marginTop: 4 }}
                        type="add"
                      />
                    )}
                    <ResizableTable
                      className={styles.editTable}
                      size="small"
                      dataSource={fields}
                      columns={getColumns(remove)}
                      rowKey="key"
                      pagination={false}
                    />
                  </>
                )}
              </Form.List>
            </Form.Item>
          )}
          {curEnumType === 'M' && (
            <>
              <Form.Item
                label={formatMessage('ENUMMANAGEMENTLIST.DRAWER.DATAMODEL')}
                name="modelId"
                rules={[{ required: true }]}
              >
                <Select
                  showSearch
                  optionFilterProp="label"
                  disabled={isDisabledObj.isModelId}
                  options={dataModelOptions}
                  onChange={(value: any) => {
                    form.setFieldsValue({
                      valueColumnName: '',
                      nameColumnName: '',
                    });
                    setCurModelId(value);
                  }}
                />
              </Form.Item>
              <div className={styles.notes}>{formatMessage('ENUM.FORM.DOMAIN_MODEL_NOTE')}</div>
              <Form.Item
                label={formatMessage('ENUMMANAGEMENTLIST.DRAWER.VALUECOLUMN')}
                name="valueColumnName"
                rules={[
                  { required: true },
                  {
                    pattern: /^[A-Z0-9_]+$/,
                    message: 'Only uppercase letters, numbers and underscores (_) are allowed.',
                  },
                ]}
              >
                <SelectWithAddButton
                  disabled={isDisabledObj.isValueColumnName}
                  originalOptionsList={[
                    ...(mode === 'add' ||
                    !curRecord?.dataModelEnum?.valueColumnName ||
                    columnOptions?.find((item) => item.value === curRecord?.dataModelEnum?.valueColumnName)
                      ? []
                      : [
                          {
                            label: curRecord?.dataModelEnum?.valueColumnName,
                            value: curRecord?.dataModelEnum?.valueColumnName,
                            key: getUUid(6),
                            state: 'new',
                            // 这里主要处理通过输入"新增"的valueColumnName已经随着该Enum提交成功了，但实际该valueColumnName并没有入对应的表（非bug，后台逻辑就是不入表）。
                          },
                        ]),
                    ...columnOptions,
                  ]}
                  parentForm={form}
                  formFieldName="valueColumnName"
                  customInputChange={handleCustomInputChange}
                />
              </Form.Item>
              <Form.Item
                label={formatMessage('ENUMMANAGEMENTLIST.DRAWER.NAMECOLUMN')}
                name="nameColumnName"
                rules={[
                  {
                    pattern: /^[A-Z0-9_]+$/,
                    message: 'Only uppercase letters, numbers and underscores (_) are allowed.',
                  },
                ]}
              >
                <SelectWithAddButton
                  disabled={isDisabledObj.isNameColumnName}
                  originalOptionsList={[
                    ...(mode === 'add' ||
                    !curRecord?.dataModelEnum?.nameColumnName ||
                    columnOptions?.find((item) => item.value === curRecord?.dataModelEnum?.nameColumnName)
                      ? []
                      : [
                          {
                            label: curRecord?.dataModelEnum?.nameColumnName,
                            value: curRecord?.dataModelEnum?.nameColumnName,
                            key: getUUid(6),
                            state: 'new',
                            // 这里主要处理通过输入"新增"的nameColumnName已经随着该Enum提交成功了，但实际该nameColumnName并没有入对应的表（非bug，后台逻辑就是不入表）。
                          },
                        ]),
                    ...columnOptions,
                  ]}
                  parentForm={form}
                  formFieldName="nameColumnName"
                  customInputChange={handleCustomInputChange}
                />
              </Form.Item>
            </>
          )}
          <Form.Item
            label={formatMessage('ENUMMANAGEMENTLIST.TABLE.DESC')}
            name="comments"
            rules={[{ required: true }]}
          >
            <Input.TextArea disabled={isDisabledObj.isComments} />
          </Form.Item>
        </Form>
      </Drawer>
    );
  },
);

export default EnumManagementDrawer;
