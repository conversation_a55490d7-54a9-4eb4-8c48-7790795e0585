function Load-Nvmrc {
    # 查找 .nvmrc 文件 (从当前目录向上递归)
    $nvmrcPath = Get-ChildItem -Path $PWD -Filter ".nvmrc" -Recurse -ErrorAction SilentlyContinue | Select-Object -First 1 -ExpandProperty FullName

    if ($nvmrcPath) {
        Write-Host "🤓 找到 .nvmrc 配置，并自动切换 node 版本"
        # 读取 .nvmrc 文件的第一行
        $nodeVersion = Get-Content -Path $nvmrcPath -TotalCount 1 | ForEach-Object { $_.Trim() }
        if ($nodeVersion) {
            # 调用 nvm-windows (确保 nvm 在 PATH 中)
            & nvm use $nodeVersion
            if ($LASTEXITCODE -ne 0) {
                Write-Error "切换 Node.js 版本失败: $nodeVersion"
            }
        } else {
            Write-Error "无法从 .nvmrc 文件读取版本号。"
        }
    }
    # else: 如果没找到 .nvmrc，函数静默结束
}

# 调用函数
Load-Nvmrc