import { GetErrorCodeListType, newErrorCodeType } from "@/pages/BssOErrorCodeApi/ErrorCodeManagement/types";
import { GetOtherErrorListType } from "@/pages/BssOErrorCodeApi/OtherErrorDetail/types";
import { stringify } from "qs";
import { request } from "umi";

// EntityServiceDetail类型定义
export interface DataModelProperty {
  name: string;
  dataType: string;
  typeFormat: string | null;
  reqIn: string | null;
  isArray: string;
  required: string;
  example: string | null;
  avlValue: string | null;
  comments: string | null;
  contentType: string | null;
  sensitiveDataLevel: string | null;
  dataModel: DataModel | null;
}

export interface DataModel {
  modelName: string;
  comments: string | null;
  className: string | null;
  modelKey: string | null;
  entityCode: string | null;
  properties: DataModelProperty[];
}

export interface Parameter {
  name: string;
  dataType: string;
  typeFormat: string | null;
  reqIn: string;
  isArray: string;
  required: string;
  example: string | null;
  avlValue: string | null;
  comments: string | null;
  contentType: string;
  sensitiveDataLevel: string | null;
  dataModel: DataModel | null;
}

export interface Response {
  name: string;
  respCode: string;
  contentType: string;
  isArray: string;
  dataModel: DataModel | null;
  required: string | null;
  example: string | null;
  comments: string | null;
  dataType: string;
  typeFormat: string | null;
}

export interface EntityServiceDetail {
  comments: string;
  catalog: string | null;
  subCatalog: string | null;
  servicePath: string | null;
  odataFlag: string | null;
  entityKey: string | null;
  serviceKey: string | null;
  state: string | null;
  serviceName: string;
  serviceMethod: string;
  appCode: string | null;
  group: string | null;
  domainName: string | null;
  entityName: string | null;
  serviceCode: string | null;
  method: string;
  testPort: string | null;
  deprecated: string | null;
  serviceEntity: string | null;
  parameters: Parameter[];
  responses: Response[];
  matchVer: string | null;
  serviceId: number;
}

export interface EntityServiceDetailResponse {
  success: boolean;
  data: EntityServiceDetail;
  errorCode: string | null;
  errorMessage: string | null;
  showType: string | null;
  traceId: string | null;
}

// 分页条件查询异常响应码
export async function getErrorCodeList(params:GetErrorCodeListType) {
  return request(`/odh-web/odh/web/errorCode/list?${stringify(params)}`, {
    method: "GET",
  })
}

// 生成错误码文件
export async function generateErrorCodeFile(params:Omit<GetErrorCodeListType, 'pageNo' | 'pageSize'>) {
  return request(`/odh-web/odh/web/errorCode/generateFile?${stringify(params)}`, {
    method: "GET",
    responseType: 'blob', // 必须明确指定
    getResponse: true,    // 需要完整响应对象（包含 headers）
  })
}

// 分页条件查询其他错误
export async function getOtherErrorList(params:GetOtherErrorListType) {
  return request(`/odh-web/odh/web/errorCode/queryUnknownErrorList?${stringify(params)}`, {
    method: "GET",
  })
}

// 查询Other errors详情的开关状态
export async function getRecordErrorStatus() {
  return request('/odh-web/odh/web/getRecordErrorStatus', {
    method: "GET",
  })
}

// 修改Other errors详情的开关状态
export async function toggleRecordErrorStatus(params:{status:boolean }) {
  return request(`/odh-web/odh/web/toggleRecordErrorStatus?${stringify(params)}`, {
    method: "POST",
  })
}

// 生成一个最新异常响应码
export async function generateErrorCode(params?:{domainId?:number}) {
  return request(`/odh-web/odh/web/errorCode/generateErrorCode?${stringify(params)}`, {
    method: "GET",
  })
}

// 新增一个异常响应码
export async function newErrorCode(data:newErrorCodeType) {
  return request('/odh-web/odh/web/errorCode/new', {
    method: "POST",
    data
  })
}

// 更新异常响应码
export async function updateErrorCode(data:{errorCodeId:number,errorReason?:string}) {
  return request('/odh-web/odh/web/errorCode/update', {
    method: "POST",
    data
  })
}

// 删除异常响应码
export async function delErrorCode(params:{errorCodeId:number}) {
  return request(`/odh-web/odh/web/errorCode/delete?${stringify(params)}`, {
    method: "POST",
  })
}

// 分页查询异常响应码的关联服务
export async function getErrorCodeServices(params:{errorCodeId:number,pageNo:number,pageSize:number}) {
  return request(`/odh-web/odh/web/errorCode/errorCodeServices?${stringify(params)}`, {
    method: "GET",
  })
}

// 获取异常schema详情
export async function getErrorSchemaDetail(params:{respParamId:number}) {
  return request(`/odh-web/odh/web/errorCode/errorSchemaDetail?${stringify(params)}`, {
    method: "GET",
  })
}

// 获取异常schema的mockExample
export async function getErrorBodyExample(params:{respParamId:number}) {
  return request(`/odh-web/odh/web/errorCode/bodyExample?${stringify(params)}`, {
    method: "GET",
  })
}

// 获取异常schema列表接口
export async function getErrorSchemaList() {
  return request(`/odh-web/odh/web/errorCode/errorSchemaList`, {
    method: "GET",
  })
}

// 按领域查询领域异常码列表
export async function getDomainErrorCodes(params:{domainId:number}) {
  return request(`/odh-web/odh/web/errorCode/queryDomainErrorCodes?${stringify(params)}`, {
    method: "GET",
  })
}

// 新增/修改响应参数异常码规则
export async function expReasonErrorCode(operate:string,params:{domainId:number},data:{
  respErrorCodeId?: number; // 新增不传/修改必传
  respParamId: number; // 必需
  errorCodeId: number; // 必需
  matchPriority: number; // 必需
  matchStatus: string; // 必需
  matchConditions?: string; // 可选
  reasonParams?: string[]; // 可选
  useErrorCodeDef: string; // 必需
}) {
  return request(`/odh-web/odh/web/expErrorReason/${operate}?${stringify(params)}`, {
    method: "POST",
    data
  })
}

// 查询响应参数异常码规则列表
export async function getExpReasonErrorCode(params:{respParamId:number}) {
  return request(`/odh-web/odh/web/expErrorReason/list?${stringify(params)}`, {
    method: "GET",
  })
}

// 删除参数异常响应码规则
export async function delErrorCodeRule(params:{domainId:number,respErrorCodeId:number}) {
  return request(`/odh-web/odh/web/expErrorReason/delete?${stringify(params)}`, {
    method: "POST",
  })
}

// 查询服务全部内容
export async function getEntityServiceDetail(params: { serviceId: number }): Promise<EntityServiceDetailResponse> {
  return request(`/odh-web/odh/web/extendService/service/entityServiceDetail?${stringify(params)}`, {
    method: 'GET',
  });
}
