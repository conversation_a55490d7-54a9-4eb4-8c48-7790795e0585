import ResizableTable from '@/components/ResizableTable';
import { addDomain<PERSON>ey, addDomainKeyPlus } from '@/utils/searchDataByKeyWords';
import { message, Modal, TableColumnsType, TableProps } from 'antd';
import React, { useEffect, useState } from 'react';

interface IAICCPromptModal {
  open: boolean;
  onCancel: () => void;
  AICCPromptInformation: any;
  selectedEntity: any;
}
interface DataType {
  key: string;
  name: string;
  comments: string;
  children?: DataType[];
}

type TableRowSelection<T extends object = object> = TableProps<T>['rowSelection'];

const AICCPromptModal: React.FC<IAICCPromptModal> = (props) => {
  const { open, onCancel, AICCPromptInformation, selectedEntity } = props;
  const [dataSource, setDataSource] = useState<any>([]);
  const [selectedEntityAndProperties, setSelectedEntityAndProperties] = useState<any>({});
  const [allPropertiesMap, setAllPropertiesMap] = useState<any>();
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);

  const columns: TableColumnsType<DataType> = [
    {
      dataIndex: 'name',
      title: 'All',
      render: (_: string, record: any) => {
        return record?.group || record?.domainName || record?.entityName || record?.name;
      },
    },
    {
      dataIndex: 'comments',
      key: 'comments',
      ellipsis: true,
      render: (text: string) => <span title={text}>{text}</span>,
    },
  ];

  // 根据entityKey进行去重，并将有相同entityKey的entityProperties组合成数组
  const uniqueByEntityKey = (data: any, allPropertiesMap: Map<string, string[]>) => {
    const map = new Map();

    data.forEach((item: any) => {
      const { entityKey, entityProperties, domainKey } = item;
      if (!map.has(entityKey)) {
        // 如果map中没有这个entityKey，就添加一个新项
        map.set(entityKey, { entityKey, entityProperties: [entityProperties], domainKey });
      } else {
        // 如果map中已经有这个entityKey，就把entityProperties添加到数组中
        map.get(entityKey).entityProperties.push(entityProperties);
      }
    });

    return Array.from(map.values()).map((item) => {
      const allProperties = allPropertiesMap.get(item.entityKey) || [];
      if (allProperties.length > 0 && allProperties.every((prop) => item.entityProperties.includes(prop))) {
        // 如果所有属性都被选择了，则设置为["*"]
        item.entityProperties = ['*'];
      }
      return item;
    });
  };

  const rowSelection: TableRowSelection<DataType> = {
    selectedRowKeys,
    checkStrictly: false, // 使table子节点和父节点的选中可以联动
    onChange: (newSelectedRowKeys, selectedRows) => {
      setSelectedRowKeys(newSelectedRowKeys);

      const selectedSource = selectedRows
        ?.filter((propertyItem: any) => propertyItem?.name)
        ?.map((item: any) => {
          const entityKey = item?.key?.split('-')[0];
          return {
            entityKey: entityKey || '',
            entityProperties: item?.name,
            domainKey: entityKey?.split('.')[0] || '',
          };
        });

      const uniqueSource = uniqueByEntityKey(selectedSource, allPropertiesMap);
      setSelectedEntityAndProperties(uniqueSource);
    },
    // onSelect: (record, selected, selectedRows) => {
    //   console.log(record, selected, selectedRows);
    // },
    // onSelectAll: (selected, selectedRows, changeRows) => {
    //   console.log(selected, selectedRows, changeRows);
    // },
  };

  // 提交选择的数据
  const submitData = () => {
    if (selectedEntityAndProperties?.length > 0) {
      const isCrossDomain = selectedEntityAndProperties?.some(
        (item: any) => item.domainKey !== selectedEntityAndProperties[0].domainKey,
      );
      if (isCrossDomain) {
        message.warning('Entity and properties cannot be selected across domains.');
      } else {
        const params = selectedEntityAndProperties?.map((item: any) => {
          return {
            entityKey: item?.entityKey,
            entityProperties: item?.entityProperties,
          };
        });
        console.log('AICC入参', params);
      }
    }
  };

  useEffect(() => {
    if (AICCPromptInformation?.length > 0) {
      const source = addDomainKeyPlus(AICCPromptInformation);

      // 初始化allPropertiesMap
      const tempAllPropertiesMap = new Map<string, string[]>();

      // 遍历多层嵌套结构
      source.forEach((groupItem) => {
        groupItem.children?.forEach((domainItem) => {
          domainItem.children?.forEach((entityItem) => {
            const entityKey = entityItem.key || '';
            const properties = entityItem.children?.map((child: any) => child.name) || [];
            tempAllPropertiesMap.set(entityKey, properties);
          });
        });
      });

      setDataSource(source);
      setAllPropertiesMap(tempAllPropertiesMap); // 保存到state中
    }
  }, [AICCPromptInformation, selectedEntity]);

  useEffect(() => {
    setSelectedEntityAndProperties({});
    setSelectedRowKeys([]); // 清空选中的行
  }, [open]);

  return (
    <Modal width={800} title="Select Entity And Properties" open={open} onCancel={onCancel} onOk={submitData}>
      <ResizableTable
        size="small"
        scroll={{
          y: 360,
        }}
        sticky={true}
        rowSelection={{ ...rowSelection }}
        columns={columns}
        dataSource={dataSource}
        pagination={false}
      />
    </Modal>
  );
};
export default AICCPromptModal;
