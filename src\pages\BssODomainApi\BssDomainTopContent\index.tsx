import React, { useEffect, useState } from 'react';
import { ArrowRightOutlined } from '@ant-design/icons';
import styles from './index.less';
import { useModel } from 'umi';

interface IBssDomainTopContent {
  selectedDomainNode?: object; //当前节点
  onSelectDomianData: (data: any) => void;
  serviceInfo?: any;
}

const BssDomainTopContent: React.FC<IBssDomainTopContent> = (props) => {
  const { selectedDomainNode, onSelectDomianData, serviceInfo } = props;
  const [currentSelectData, setCurrentSelectData] = useState<any>({});
  const { isManageExtensionService, setIsManageExtensionService } = useModel('manageExtensionServiceModel');
  const onClickTop = (e) => {
    const level = e.target.getAttribute('level');
    if (level === 5) {
      setIsManageExtensionService(true);
    } else {
      setIsManageExtensionService(false);
      setPathList(selectedDomainNode.path);
      const data = e.target.getAttribute('data');
      const currentSelectData = selectedDomainNode?.path?.filter((i) => i.key === data).map((i) => i.data);
      onSelectDomianData(currentSelectData?.[0]);
    }
  };

  // 扩展服务详情管理
  const [pathList, setPathList] = useState(() => {
    return Array.isArray(selectedDomainNode.path) ? [...selectedDomainNode.path] : [];
  });

  useEffect(() => {
    if (isManageExtensionService) {
      const newDomainNode = {
        level: 5,
        key: 'DOMAIN_OBJ_1_ENTITY_4_2',
        name: serviceInfo?.serviceName,
      };
      setPathList([...selectedDomainNode.path, newDomainNode]);
    } else {
      setPathList(selectedDomainNode.path);
    }
  }, [serviceInfo, isManageExtensionService, selectedDomainNode.path]);

  return (
    <div className={styles.activeLineContainer}>
      {/* 头部区域 */}
      {Array.isArray(pathList) && pathList.length > 0
        ? pathList.map((item) => (
            <div className={styles.topContent}>
              {item.level === 1 ? (
                <>
                  <span className={styles.contentName} data={item.key} onClick={onClickTop}>
                    {item.name}
                  </span>
                  <span> ( {item.domainState === 'D' ? 'Draft' : 'Released'} ) </span>
                </>
              ) : null}
              {item.level === 2 || item.level === 4 ? (
                <>
                  <ArrowRightOutlined className={styles.arrowRight} />
                  <span className={styles.contentName} data={item.key} onClick={onClickTop}>
                    {item.name}
                  </span>
                </>
              ) : null}
              {item.level === 5 && item.name ? (
                <>
                  <ArrowRightOutlined className={styles.arrowRight} />
                  <span className={styles.contentName} data={item.key}>
                    {item.name}
                  </span>
                </>
              ) : null}
            </div>
          ))
        : null}
    </div>
  );
};

export default BssDomainTopContent;
