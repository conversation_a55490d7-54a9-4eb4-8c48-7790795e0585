import { MenuUnfoldOutlined, MenuFoldOutlined } from '@ant-design/icons'
import { Dropdown, Switch, type MenuProps } from "antd"
import styles from './index.module.less'
import { useStore } from '../../store'
import storage from '../../utils/storage'
import BreadCrumb from './breadCrumb'
import { useEffect } from 'react'

const NavHeader = () => {
    // const collapsed = useStore(state => state.collapsed)
    // const handleCollapsed = useStore(state => state.updateCollapsed)
    // const userInfo = useStore(state => state.userInfo)

    useEffect(() => {
        handleSwitchTheme(isDark)
    }, [])

    const { userInfo, collapsed, isDark, updateCollapsed, updateTheme } = useStore()

    const onClick: MenuProps['onClick'] = ({ key }) => {
        if (+key == 2) {
            storage.remove('token')
            location.href = '/login?callback=' + encodeURIComponent(location.href)
        }
    };

    const items: MenuProps['items'] = [
        {
            key: '1',
            label: '邮箱：' + userInfo.userEmail
        },
        {
            key: '2',
            label: '退出'
        }
    ]

    const handleCollapsed = () => {
        updateCollapsed()
    }

    const handleSwitchTheme = (isDark: boolean) => {
        if (isDark) {
            document.documentElement.dataset.theme = 'dark'
            document.documentElement.classList.add('dark')
        } else {
            document.documentElement.dataset.theme = 'light'
            document.documentElement.classList.remove('dark')
        }
        storage.set('isDark', isDark)
        updateTheme(isDark)
    }

    return (
        <div className={styles.navHeader}>
            <div className={styles.left}>
                <div onClick={handleCollapsed}>
                    {collapsed ? <MenuUnfoldOutlined /> : <MenuFoldOutlined />}
                </div>
                <BreadCrumb />
            </div>
            <div className={styles.right}>
                <Switch checked={isDark} checkedChildren="暗黑" unCheckedChildren="默认" onChange={handleSwitchTheme} />
                <Dropdown menu={{ items, onClick }} trigger={['click']}>
                    <span className={styles.nickName}>{userInfo.userName}</span>
                </Dropdown>
            </div>
        </div>
    )
}

export default NavHeader