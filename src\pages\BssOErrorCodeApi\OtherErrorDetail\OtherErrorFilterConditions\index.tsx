import OperationIconTitle from '@/components/OperationIconTitle';
import { Button, Col, DatePicker, Flex, Form, Input, message } from 'antd';
import React, { useEffect, useState } from 'react';
import useI18n from '@/hooks/useI8n';
import { useModel } from 'umi';
import dayjs from 'dayjs';
import { formatLocalDate, getTimeRange } from '@/utils/aboutTime';

interface IFilterConditions {
  openFromBssODataApi?: boolean; // 是否从BssODataApi页面打开
  otherErrorFilterConditionsForm: any;
}

const OtherErrorFilterConditions: React.FC<IFilterConditions> = (props) => {
  const { otherErrorFilterConditionsForm } = props;
  const { formatMessage } = useI18n();
  const { queryOtherErrorListParams, setQueryOtherErrorListParams, queryOtherErrorList } =
    useModel('useErrorCodeModel');
  const [dateList, setDateList] = useState<string[]>(getTimeRange(3));

  // 日期范围选择
  const handleDateChange = (dateStrings: string[]) => {
    // 边界情况处理
    if (!Array.isArray(dateStrings) || dateStrings.length !== 2) {
      console.error('Invalid dateStrings.');
      return;
    }

    // 日期解析
    const parseDate = (str: string) => {
      const [datePart, timePart] = str.split(' ');
      const [year, month, day] = datePart.split('-').map(Number);
      const [hours = 0, minutes = 0, seconds = 0] = timePart?.split(':').map(Number) || [];
      return new Date(year, month - 1, day, hours, minutes, seconds);
    };

    const startDate = parseDate(dateStrings[0]);
    const endDate = parseDate(dateStrings[1]);

    // 时间间隔计算（毫秒）
    const timeDiff = endDate.getTime() - startDate.getTime();
    const sevenDaysMs = 7 * 24 * 60 * 60 * 1000;

    // 业务规则处理
    if (timeDiff > sevenDaysMs) {
      // 创建新的起始日期（保持原始结束日期不变）
      const adjustedStart = new Date(endDate);
      adjustedStart.setDate(adjustedStart.getDate() - 6);
      adjustedStart.setHours(0, 0, 0, 0); // 固定凌晨时间

      // 生成合规日期范围
      const validRange = [formatLocalDate(adjustedStart), formatLocalDate(endDate)];

      message.warning(`The date range has exceeded seven days and is automatically adjusted to:${validRange}`);

      // 更新状态
      setDateList(validRange);
    } else {
      // 直接使用原始值
      setDateList(dateStrings);
    }
  };

  // 搜索
  const handleSearch = async () => {
    setQueryOtherErrorListParams((pre) => ({
      ...pre,
      pageNo: 1,
      pageSize: 10,
    }));
    queryOtherErrorList({
      ...queryOtherErrorListParams,
      pageNo: 1,
      pageSize: 10,
    });
  };

  // 重置查询条件
  const handleReset = async () => {
    const newDateList = getTimeRange(3);
    setDateList(newDateList);
    otherErrorFilterConditionsForm.resetFields();
    setQueryOtherErrorListParams({
      pageNo: 1,
      pageSize: 10,
      beginDate: newDateList[0],
      endDate: newDateList[1],
    });
    queryOtherErrorList({
      pageNo: 1,
      pageSize: 10,
      beginDate: newDateList[0],
      endDate: newDateList[1],
    });
  };

  useEffect(() => {
    setQueryOtherErrorListParams((pre) => ({
      ...pre,
      beginDate: dateList[0],
      endDate: dateList[1],
    }));
  }, [dateList]);

  useEffect(() => {
    handleReset(); // 初始化查询
  }, []);

  return (
    <div>
      <OperationIconTitle title={formatMessage('ERRORCODE.FILTERCONDITIONS.TITLE')} />
      <Form
        form={otherErrorFilterConditionsForm}
        labelAlign="left"
        // labelCol={{ span: 8 }}
        // wrapperCol={{ span: 16 }}
        style={{ display: 'flex', gap: '4rem' }}
        onValuesChange={(cValues, alValues) => {
          setQueryOtherErrorListParams((pre) => ({
            ...pre,
            ...alValues,
          }));
        }}
      >
        <Col span={6}>
          <Form.Item
            label={formatMessage('ERRORCODE.OTHERERRORDETAIL.TRACEID')}
            name="traceId"
            style={{ marginLeft: 45 }} // 微调，使上下Form.Item冒号对齐
          >
            <Input allowClear />
          </Form.Item>
          <Form.Item label={formatMessage('ERRORCODE.OTHERERRORDETAIL.ERRORRESPONSE')} name="errResp">
            <Input allowClear />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            label={formatMessage('ERRORCODE.OTHERERRORDETAIL.SERVICENAME')}
            name="serviceName"
            style={{ marginLeft: 12 }} // 微调，使上下Form.Item冒号对齐
          >
            <Input allowClear />
          </Form.Item>
          <Form.Item
            label={formatMessage('ERRORCODE.OTHERERRORDETAIL.HAPPENEDDATE')}
            // labelCol={{ span: 4 }}
            // wrapperCol={{ span: 20 }}
          >
            <DatePicker.RangePicker
              onChange={(dates, dateStrings) => handleDateChange(dateStrings)}
              value={
                dateList.length === 2
                  ? [dayjs(dateList[0], 'YYYY-MM-DD HH:mm:ss'), dayjs(dateList[1], 'YYYY-MM-DD HH:mm:ss')]
                  : [null, null]
              }
              allowClear={false}
              showTime
              format="YYYY-MM-DD HH:mm:ss"
            />
          </Form.Item>
        </Col>
        <Col span={7}>
          <Form.Item label={formatMessage('ERRORCODE.OTHERERRORDETAIL.SERVICEPATH')} name="servicePath">
            <Input allowClear />
          </Form.Item>
          <Flex justify="flex-end" gap="2rem">
            <Button onClick={handleReset}> {formatMessage('PROJECT.COMMON.RESET')}</Button>
            <Button type="primary" onClick={handleSearch}>
              {formatMessage('PROJECT.COMMON.SEARCH')}
            </Button>
          </Flex>
        </Col>
      </Form>
    </div>
  );
};
export default OtherErrorFilterConditions;
