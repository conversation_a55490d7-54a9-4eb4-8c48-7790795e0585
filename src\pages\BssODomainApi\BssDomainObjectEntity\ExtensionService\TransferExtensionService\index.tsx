import React, { useState, useEffect } from 'react';
import { Button, Drawer, Form, Space, Spin, message, Select, Transfer, Alert } from 'antd';
import type { TransferDirection } from 'antd/es/transfer';
import type { Key } from 'react';
import { queryODomainTreeData } from '@/services/domainService';
import { qryEntityServiceList, transferEntityServices } from '@/services/entityService';
import { SelectedEntityProps, SelectedRootNode } from '../../types';
import useI18n from '@/hooks/useI8n';
import styles from './index.less';

interface ITransferExtensionServiceDrawer {
  selectedEntity: SelectedEntityProps; // 当前节点
  selectedRootNode: SelectedRootNode; // 根节点
  isProduct: boolean;
  open: boolean;
  onCancel: () => void;
  onOk: () => void;
}

interface TransferItem {
  key: string;
  title: string;
  description: string;
  disabled: boolean;
  creationSrc: string;
  serviceId: number;
}

const TransferExtensionServiceDrawer: React.FC<ITransferExtensionServiceDrawer> = (props) => {
  const { open, selectedEntity, selectedRootNode, isProduct, onCancel, onOk } = props;
  const { formatMessage } = useI18n();
  const [form] = Form.useForm();
  const [submitSpinning, setSubmitSpinning] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [domainInfo, setDomainInfo] = useState<any[]>([]);
  const [targetEntityId, setTargetEntityId] = useState<number | null>(null);
  const [sourceServices, setSourceServices] = useState<TransferItem[]>([]); // 源实体服务
  const [targetServices, setTargetServices] = useState<TransferItem[]>([]); // 目标实体服务
  const [targetKeys, setTargetKeys] = useState<Key[]>([]);
  const { Option, OptGroup } = Select;

  // 关闭抽屉
  const onClose = async () => {
    form.resetFields();
    setTargetEntityId(null);
    setTargetKeys([]);
    setSourceServices([]);
    setTargetServices([]);
    onCancel?.();
  };

  // 查询当前领域下的领域对象和实体
  const queryCurrentDomainInfo = async () => {
    setLoading(true);
    try {
      const { success, data } = await queryODomainTreeData();
      if (success) {
        data.odhDomainList?.forEach((item: any) => {
          if (item.domainId === selectedRootNode?.domainId) {
            setDomainInfo(item.odhDomainObjList || []);
          }
        });
      }
    } finally {
      setLoading(false);
    }
  };

  // 查询源实体的服务列表
  const querySourceServices = async () => {
    setLoading(true);
    try {
      const { success, data } = await qryEntityServiceList(selectedEntity.entityId);
      if (success && Array.isArray(data)) {
        const serviceItems: TransferItem[] = data.map((service: any) => ({
          key: service.serviceId.toString(),
          title: service.serviceName,
          description: service.comments || '',
          // 在项目环境下，产品拥有的服务不能被选择
          disabled: !isProduct && service.creationSrc === 'P',
          creationSrc: service.creationSrc,
          serviceId: service.serviceId,
        }));
        setSourceServices(serviceItems);
      }
    } finally {
      setLoading(false);
    }
  };

  // 查询目标实体的服务列表
  const queryTargetServices = async (entityId: number) => {
    if (!entityId) return;

    setLoading(true);
    try {
      const { success, data } = await qryEntityServiceList(entityId);
      if (success && Array.isArray(data)) {
        const serviceItems: TransferItem[] = data.map((service: any) => ({
          key: `target_${service.serviceId.toString()}`, // 为目标实体服务添加前缀，避免与源实体服务ID冲突
          title: service.serviceName,
          description: service.comments || '',
          disabled: true, // 目标实体的服务不能被选择
          creationSrc: service.creationSrc,
          serviceId: service.serviceId,
        }));
        setTargetServices(serviceItems);

        // 将目标实体的服务添加到已选择的服务列表中
        const targetServiceKeys = serviceItems.map((item) => item.key);
        setTargetKeys(targetServiceKeys);
      }
    } finally {
      setLoading(false);
    }
  };

  // 处理目标实体变更
  const handleTargetEntityChange = (value: number, option: any) => {
    setTargetEntityId(value);
    setTargetKeys([]); // 清空已选择的服务
    queryTargetServices(value);
  };

  // 处理穿梭框变更
  const handleTransferChange = (nextTargetKeys: Key[], direction: TransferDirection, moveKeys: Key[]) => {
    // 获取当前目标实体的服务键值（以target_开头）
    const targetEntityServiceKeys = targetServices.map((item) => item.key);

    // 确保目标实体原有服务始终保留在右侧
    const updatedTargetKeys = [
      ...targetEntityServiceKeys,
      ...nextTargetKeys.filter((key) => !key.toString().startsWith('target_')),
    ];

    setTargetKeys(updatedTargetKeys);
  };

  // 过滤穿梭框左侧显示的内容（只显示源实体服务）
  const filterSourceOption = (inputValue: string, item: TransferItem) => {
    return (
      !item.key.toString().startsWith('target_') && item.title.toLowerCase().indexOf(inputValue.toLowerCase()) !== -1
    );
  };

  // 过滤穿梭框右侧显示的内容（只显示目标实体服务和已选择的源实体服务）
  const filterTargetOption = (inputValue: string, item: TransferItem) => {
    return (
      (item.key.toString().startsWith('target_') || targetKeys.includes(item.key)) &&
      item.title.toLowerCase().indexOf(inputValue.toLowerCase()) !== -1
    );
  };

  // 提交表单
  const handleSubmit = async () => {
    if (!targetEntityId) {
      message.error(formatMessage('EXTENSIONSERVICE.TRANSFER.TARGET_ENTITY_REQUIRED'));
      return;
    }

    // 只获取不是以target_开头的服务ID（即源实体中选择的服务）
    const selectedSourceServiceKeys = targetKeys.filter((key) => !key.toString().startsWith('target_'));

    if (selectedSourceServiceKeys.length === 0) {
      message.error(formatMessage('EXTENSIONSERVICE.TRANSFER.SERVICE_REQUIRED'));
      return;
    }

    // 查找目标实体所属的领域对象信息
    const targetEntityObj = domainInfo.find((obj) =>
      obj.entityList?.some((entity: any) => entity.entityId === targetEntityId),
    );
    const targetEntity = domainInfo
      .flatMap((obj) => obj.entityList || [])
      .find((entity: any) => entity.entityId === targetEntityId);

    if (!targetEntityObj || !targetEntity) {
      message.error(formatMessage('EXTENSIONSERVICE.TRANSFER.TARGET_ENTITY_REQUIRED'));
      return;
    }

    setSubmitSpinning(true);
    try {
      // 准备请求数据
      const transferData = selectedSourceServiceKeys.map((key) => ({
        serviceId: parseInt(key.toString(), 10),
        bfDomainObjCode: domainInfo.find((obj) => obj.domainObjId === selectedEntity.domainObjId)?.domainObjCode || '',
        bfEntityCode: selectedEntity.entityCode,
        afDomainObjCode: targetEntityObj.domainObjCode,
        afEntityCode: targetEntity.entityCode,
        afEntityId: targetEntityId,
      }));

      // 调用API进行服务迁移
      const result = await transferEntityServices(transferData);

      if (result && result.success) {
        message.success(formatMessage('EXTENSIONSERVICE.TRANSFER.SUCCESS'));
        onOk?.();
        onClose();
      } else {
        message.error(result?.msg || formatMessage('EXTENSIONSERVICE.TRANSFER.FAIL'));
      }
    } catch (error) {
      console.error('Transfer service error:', error);
      message.error(formatMessage('EXTENSIONSERVICE.TRANSFER.FAIL'));
    } finally {
      setSubmitSpinning(false);
    }
  };

  useEffect(() => {
    if (open) {
      queryCurrentDomainInfo();
      querySourceServices();
    }
  }, [open]);

  return (
    <Drawer
      title={formatMessage('EXTENSIONSERVICE.TRANSFER.TITLE')}
      onClose={onClose}
      maskClosable={false}
      open={open}
      width={720}
      footer={
        <Space style={{ float: 'right' }}>
          <Spin spinning={submitSpinning}>
            <Button type="primary" onClick={handleSubmit}>
              {formatMessage('EXTENSIONSERVICE.TRANSFER.SUBMIT')}
            </Button>
          </Spin>
          <Button onClick={onClose}>{formatMessage('EXTENSIONSERVICE.TRANSFER.CANCEL')}</Button>
        </Space>
      }
    >
      <Spin spinning={loading}>
        <Form form={form} labelCol={{ span: 4 }} wrapperCol={{ span: 20 }}>
          {/* <Form.Item
            label={formatMessage('EXTENSIONSERVICE.TRANSFER.CURRENT_ENTITY')}
            name="entityName"
            initialValue={selectedEntity?.entityName}
          >
            <span>{selectedEntity?.entityName}</span>
          </Form.Item> */}

          <Form.Item
            label={formatMessage('EXTENSIONSERVICE.TRANSFER.TARGET_ENTITY')}
            name="targetEntityId"
            rules={[{ required: true, message: formatMessage('EXTENSIONSERVICE.TRANSFER.TARGET_ENTITY_REQUIRED') }]}
          >
            <Select onChange={handleTargetEntityChange}>
              {domainInfo.map((group) => (
                <OptGroup key={group.domainObjId} label={group.domainObjName}>
                  {group.entityList?.map(
                    (option: any) =>
                      <Option
                        key={option.entityCode}
                        value={option.entityId}
                        disabled={option.entityId === selectedEntity.entityId}
                      >
                        {option.entityName}
                      </Option>
                  )}
                </OptGroup>
              ))}
            </Select>
          </Form.Item>

          <Form.Item label={formatMessage('EXTENSIONSERVICE.TRANSFER.SERVICES')} name="services">
            <Transfer
              dataSource={[...sourceServices, ...targetServices]}
              titles={[
                formatMessage('EXTENSIONSERVICE.TRANSFER.SOURCE_SERVICES'),
                formatMessage('EXTENSIONSERVICE.TRANSFER.TARGET_SERVICES'),
              ]}
              targetKeys={targetKeys}
              onChange={handleTransferChange}
              render={(item) => (
                <span
                  title={item.title}
                  className={item.key.toString().startsWith('target_') ? styles.disabledItem : ''}
                >
                  {item.title}
                </span>
              )}
              oneWay
              showSearch
              locale={{
                itemsUnit: formatMessage('EXTENSIONSERVICE.TRANSFER.ITEMS_UNIT'),
                itemUnit: formatMessage('EXTENSIONSERVICE.TRANSFER.ITEM_UNIT'),
              }}
              filterOption={(inputValue, item, direction) =>
                direction === 'left' ? filterSourceOption(inputValue, item) : filterTargetOption(inputValue, item)
              }
              listStyle={{
                flex: 1,
                width: '45%',
                height: 300,
              }}
            />
          </Form.Item>

          <Alert
            message={
              <span>
                {formatMessage('EXTENSIONSERVICE.TRANSFER.WARNING')}
                <span className={styles.warningHighlight}>
                  {formatMessage('EXTENSIONSERVICE.TRANSFER.WARNING_HIGHLIGHT')}
                </span>
              </span>
            }
            type="warning"
            showIcon
          />
        </Form>
      </Spin>
    </Drawer>
  );
};

export default TransferExtensionServiceDrawer;
