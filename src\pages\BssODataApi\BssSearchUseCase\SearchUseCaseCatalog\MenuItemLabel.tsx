import classNames from 'classnames';
import styles from './index.less';
import { Col, Popconfirm } from 'antd';
import useI18n from '@/hooks/useI8n';
import { UseCaseBeQueriedListPropsType } from '@/services/typing';
import { DeleteOutlined } from '@ant-design/icons';

const MenuItemLabel = ({
  menuItemData,
  onDeleteConfirm = () => {},
  onMenuItemClick = () => {},
}: {
  menuItemData: UseCaseBeQueriedListPropsType;
  onDeleteConfirm: (el: UseCaseBeQueriedListPropsType) => void;
  onMenuItemClick: (el: UseCaseBeQueriedListPropsType) => void;
}) => {
  const { formatMessage } = useI18n();
  const { method } = menuItemData;

  return (
    <div className={styles.menuItemLabel} onClick={() => onMenuItemClick(menuItemData)}>
      <div className={styles.menuTitle}>
        <span
          className={classNames(styles.methodText, {
            [styles.postStyle]: method === 'POST',
            [styles.getStyle]: method === 'GET' || method === 'PUT',
            [styles.patchStyle]: method === 'PATCH',
            [styles.deleteStyle]: method === 'DELETE',
          })}
        >
          {menuItemData.method}
        </span>
        <span title={menuItemData.useCaseName}>{menuItemData.useCaseName}</span>
      </div>
      <div className={styles.deleteBtn}>
        <Popconfirm
          title=""
          description={formatMessage('DEBUGSERVICE.USECASEBESELECTEDMODAL.CONFIRMTIP')}
          onConfirm={(e) => {
            e?.stopPropagation();
            onDeleteConfirm(menuItemData);
          }}
          onCancel={(e) => {
            e?.stopPropagation();
          }}
          okText="Yes"
          cancelText="No"
        >
          <DeleteOutlined
            onClick={(e) => {
              e.stopPropagation();
            }}
          />
        </Popconfirm>
      </div>
    </div>
  );
};

export default MenuItemLabel;
