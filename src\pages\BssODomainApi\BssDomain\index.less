.spinContainer {
  width: 100%;
  height: 100%;
  :global {
    .ant-spin-container {
      width: 100%;
      height: 100%;
    }
    .ant-tree-node-content-wrapper{
      line-height: 30px;
      text-overflow: ellipsis;
      overflow: hidden;
      white-space: nowrap;
      .ant-tree-iconEle{
        line-height: 30px;
        width: 25px;
        height: 25px;
      }
    }
    .ant-tree-switcher{
      line-height: 30px;
    }
    .ant-tree-treenode{
      padding: 0 0 2px 0;
    }
  }
}

.bssApiLeftContent {
    position: relative;
    width: 100%;
    height: 100%;
    border: 1px solid #e0e0e0;
    box-sizing: border-box;
    background-color: #fff;
    display: flex;
    flex-direction: column
}

.mask {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(238, 98, 98, 0.08);
  pointer-events: none; /* 事件穿透 */
  z-index: 999;
}

.bssApiLeftContentSearch {
  padding: 12px 12px;
  border-bottom: 1px solid #e0e0e0;
}
.bssApiLeftContentList {
  overflow-y: auto;
  margin-top: 5px;
}

.noData{
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}



