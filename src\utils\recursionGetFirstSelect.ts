import { I<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>in<PERSON>ist, IEntityList,  ICatalog, ICatalogGroupList, ISubCatalogList, IAPIGCatalog, IAPIGCatalogGroupList, IAPIGSubCatalogList } from '@/services/typing.d';

interface IRecursionReturnDomain {
    group?: IGroupList;
    domain?: IDomainList;
    entity?: IEntityList;
}

interface IRecursionReturnCatalog {
    catalog?: ICatalog;
    group?: ICatalogGroupList;
    subCatalog?: ISubCatalogList;
}

interface IRecursionReturnAPIGCatalog {
  catalog?: IAPIGCatalog;
  menuPath?: string[];
  subCatalog?: IAPIGSubCatalogList;
}

// 从domian中返回第一个存在entity的数据
const recursionGetFirstDomain: (data: Array<IGroupList>) => IRecursionReturnDomain = (data) => {
    let domainResult = {};
    let reduceCycleFlag = false;
    (data || []).forEach(groupItem => {
        if(reduceCycleFlag) return;

        const { domainList } = groupItem;
        (domainList || []).forEach(domainItem => {
            if(reduceCycleFlag) return

            const { entityList } = domainItem;
            if(entityList?.length) {
                reduceCycleFlag = true;
                domainResult = {
                    group: groupItem,
                    domain: domainItem,
                    entity: entityList?.[0]
                };
                return;
            }
        });
    });
    return domainResult;
};


const recursionGetFirstCatalog: (data: Array<ICatalog>) => IRecursionReturnCatalog = (data) => {
    let domainResult = {};
    let reduceCycleFlag = false;
    (data || []).forEach(catalogItem => {
        if(reduceCycleFlag) return;

        const { groupList } = catalogItem;
        (groupList || []).forEach(groupItem => {
            if(reduceCycleFlag) return

            const { subCatalogList } = groupItem;
            if(subCatalogList?.length) {
                reduceCycleFlag = true;
                domainResult = {
                    catalog: catalogItem,
                    group: groupItem,
                    subCatalog: subCatalogList[0]
                };
                return;
            }
        });
    });
    return domainResult;
};

const recursionGetFirstAPIGCatalog = (catalogList: Array<IAPIGCatalog>,menuPath: string[] = []):IRecursionReturnAPIGCatalog => {
  let catalogResult:any = null;
  (catalogList || []).some(catalogItem => {
    if(catalogResult) {
      return true
    }
    // 记录当前节点的catalogId
    const currentPath = [...menuPath, catalogItem.catalogId.toString()];
    // 如果当前节点有serviceList且不为空，返回第一条service
    if (catalogItem?.serviceList?.length > 0) {
      catalogResult = {
        catalog: catalogItem,
        subCatalog: catalogItem.serviceList[0],
        menuPath: currentPath,
      };
      return true;
    }
    // 递归遍历子节点
    if (catalogItem?.catalogChildNodes?.length > 0) {
      catalogResult = recursionGetFirstAPIGCatalog(catalogItem.catalogChildNodes, currentPath);
    }
  })

  return catalogResult;
};

export {
    recursionGetFirstDomain,
    recursionGetFirstCatalog,
    recursionGetFirstAPIGCatalog
}
