import { <PERSON><PERSON>, <PERSON>er, <PERSON>, Spin } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { useEffect, useState } from 'react';
import { ErrorCodeServiceListDataType, ErrorCodeServiceListItemType } from '../../types';
import useI18n from '@/hooks/useI8n';
import { getErrorCodeServices } from '@/services/errorCodeService';
import ResizableTable from '@/components/ResizableTable';

interface IRelatedApi {
  relatedOpen: boolean;
  selectedErrorCode: any;
  setRelatedOpen: (open: boolean) => void;
}
const RelatedApi: React.FC<IRelatedApi> = (props) => {
  const { relatedOpen, selectedErrorCode, setRelatedOpen } = props;
  const { formatMessage } = useI18n();
  const [form] = useForm();
  const [spining, setSpining] = useState<boolean>(false);
  const [errorCodeServiceListData, setErrorCodeServiceListData] = useState<ErrorCodeServiceListDataType>();

  const columns = [
    {
      dataIndex: 'serviceName',
      title: formatMessage('ERRORCODE.RELATED.SERVICENAME'),
      width: '35%',
      ellipsis: true,
    },
    {
      dataIndex: 'respCode',
      title: formatMessage('ERRORCODE.RELATED.HTTPSTATUS'),
      width: '25%',
      ellipsis: true,
    },
    {
      dataIndex: 'entityName',
      title: formatMessage('ERRORCODE.RELATED.DOMAINENTITY'),
      width: '40%',
      ellipsis: true,
      render: (_: any, record: ErrorCodeServiceListItemType) => {
        return `${record.domainName} -> ${record.domainObjName} -> ${record.entityName}`;
      },
    },
  ];

  const onClose = () => {
    setRelatedOpen(false);
  };

  const queryErrorCodeServices = async (params: { errorCodeId: number; pageNo: number; pageSize: number }) => {
    try {
      setSpining(true);
      const { success, data } = await getErrorCodeServices(params);
      if (success) {
        setErrorCodeServiceListData(data);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setSpining(false);
    }
  };

  useEffect(() => {
    if (selectedErrorCode?.errorCodeId && relatedOpen) {
      queryErrorCodeServices({
        errorCodeId: selectedErrorCode?.errorCodeId,
        pageNo: 1,
        pageSize: 10,
      });
    }
  }, [selectedErrorCode, relatedOpen]);

  return (
    <Drawer
      title={formatMessage('ERRORCODE.RELATED.TITLE')}
      onClose={onClose}
      maskClosable={false}
      open={relatedOpen}
      width={800}
      afterOpenChange={() => {
        form.resetFields();
      }}
      footer={
        <Space style={{ float: 'right' }}>
          <Button onClick={onClose}>Cancel</Button>
        </Space>
      }
    >
      <Spin spinning={spining}>
        <ResizableTable
          size="small"
          style={{ marginTop: '12px' }}
          columns={columns}
          dataSource={errorCodeServiceListData?.list || []}
          rowKey={(record: ErrorCodeServiceListItemType) =>
            `${record.domainId}-${record.domainObjId}-${record.entityId}-${record.serviceId}`
          }
          pagination={{
            hideOnSinglePage: true,
            showSizeChanger: true,
            pageSizeOptions: [5, 10, 15, 20],
            total: errorCodeServiceListData?.totalCount,
            current: errorCodeServiceListData?.pageNo,
            pageSize: errorCodeServiceListData?.pageSize,
            showTotal: (total) => formatMessage('PROJECT.COMMON.PAGINATION.TOTAL', { total }),
            onChange: (page: any, pageSize: any) => {
              queryErrorCodeServices({ errorCodeId: selectedErrorCode?.errorCodeId, pageSize, pageNo: page });
            },
          }}
        />
      </Spin>
    </Drawer>
  );
};
export default RelatedApi;
