import React, { useEffect, useState } from "react";
import { IGroupList, IEntityList } from "@/services/typing.d";
import { DownOutlined, UpOutlined } from "@ant-design/icons";
import classNames from "classnames";
import { recursionGetFirstDomain } from "@/utils/recursionGetFirstSelect";
import { ICurrentActiveKey } from "./type";
import { judgeExistInEntity } from "@/utils/judgeCurrentKeyInData";
import { searchDomainByText } from "@/utils/searchDataByKeyWords";
import styles from "./index.less";

interface CustomerCollpaseForDomain {
  menuRefreshFlag: any; // 目录刷新控制
  completeRefreash: () => void; // 目录刷新结束响应事件
  otherEntitykey?: string; // 其他途径的选中项目
  groupData: Array<IGroupList>; // 接口数据
  searchvalue: string; // 搜索值
  onItemChange?: (entity: IEntityList) => void; // 选中项
  onRefresh: (currentActiveKey: ICurrentActiveKey) => void; // 刷新缓存 通知上级刷新缓存
}

const CustomerCollpaseForDomain: React.FC<CustomerCollpaseForDomain> = (props) => {
  const { otherEntitykey, groupData, searchvalue, menuRefreshFlag, onItemChange, onRefresh, completeRefreash } = props;

  const [hasInitFirstSelect, setHasInitFirstSelect] = useState<boolean>(false); // 是否初始化过默认选择第一项
  const [currentActiveKey, setCurrentActiveKey] = useState<ICurrentActiveKey>(); // 当前活跃的item
  const [collpaseKey, setCollpaseKey] = useState<Record<string, Record<string, boolean>>>({}); // 记录当前树的展开活跃key
  const [groupDataAfterSearch, setGroupDataAfterSearch] = useState<Array<IGroupList>>([]); // 搜索之后的值

  // 一级目录点击
  const handleFirstCollpase = (firstKey: string) => {
    if (collpaseKey[firstKey]) {
      const { [firstKey]: deleteKey, ...otherKey } = collpaseKey;
      setCollpaseKey(otherKey);
    } else {
      setCollpaseKey({
        ...collpaseKey,
        [firstKey]: {},
      });
    }
  };

  // 二级目录的点击
  const handleSecondCollpase = (firstKey: string, secondKey: string) => {
    setCollpaseKey({
      ...collpaseKey,
      [firstKey]: {
        ...(collpaseKey?.[firstKey] || {}),
        [secondKey]: !collpaseKey?.[firstKey]?.[secondKey],
      },
    });
  };

  // 通过关键字搜索
  const searchByText = (searchvalue: string) => {
    const resultsAfterSearch = searchDomainByText(searchvalue, groupData);
    return resultsAfterSearch;
  };

  // 处理搜索
  const handleSearch = (groupData: Array<IGroupList>, searchvalue: string) => {
    if (searchvalue) {
      // 关键字搜索 忽略大小写
      const resultsAfterSearch = searchByText(searchvalue);
      setGroupDataAfterSearch(resultsAfterSearch);
      // 搜索完默认展开所有的key
      let collpasekeyAfterSearch: Record<string, Record<string, boolean>> = {};
      resultsAfterSearch.forEach((groupItem) => {
        collpasekeyAfterSearch[groupItem?.group] = {};
        groupItem.domainList.forEach((domainItem) => {
          collpasekeyAfterSearch[groupItem?.group][domainItem?.domainKey] = true;
        });
      });
      setCollpaseKey(collpasekeyAfterSearch);
    } else {
      // 无搜索 重置选中项的展开情况
      setGroupDataAfterSearch(groupData);
      if (!(currentActiveKey?.clickGroup?.group && currentActiveKey?.clickDomain?.domainKey && currentActiveKey?.clickEntity?.entityKey)) {
        return;
      }
      setCollpaseKey({
        [currentActiveKey?.clickGroup?.group]: {
          [currentActiveKey?.clickDomain?.domainKey]: true,
        },
      });
    }
  };

  // 默认展示 + 搜索
  // 初始化 展示第一个符合条件的
  // 已初始化 应用手动输入的搜索值
  // 无数据 置空
  useEffect(() => {
    if (groupData?.length && !hasInitFirstSelect) {
      // 初始化 查找第一个符合条件的选项
      const domainFirstResult = recursionGetFirstDomain(groupData);
      setGroupDataAfterSearch(groupData);
      setCollpaseKey({
        [domainFirstResult?.group?.group || ""]: {
          [domainFirstResult?.domain?.domainKey || ""]: true,
        },
      });
      setCurrentActiveKey({
        clickGroup: domainFirstResult?.group,
        clickDomain: domainFirstResult?.domain,
        clickEntity: domainFirstResult?.entity,
      });
      setHasInitFirstSelect(true);
    } else if (groupData?.length && hasInitFirstSelect) {
      handleSearch(groupData, searchvalue);
    } else {
      setGroupDataAfterSearch([]);
      setCollpaseKey({});
    }
  }, [groupData, searchvalue]);

  // 未通过点击选中 通过其他页面的entity点击
  // 1、需要重新设置活跃entity
  // 2、并打开相对应的上级
  useEffect(() => {
    if (otherEntitykey) {
      groupData.forEach((groupItem) => {
        const { domainList } = groupItem;
        domainList.forEach((domainItem) => {
          const { entityList } = domainItem;
          const belongEntity = (entityList || []).find((i) => i.entityKey === otherEntitykey);
          if (belongEntity?.entityKey) {
            // 设置活跃集合
            setCurrentActiveKey({
              clickGroup: groupItem,
              clickDomain: domainItem,
              clickEntity: belongEntity,
            });
            // 设置对应的父集
            setCollpaseKey({
              ...collpaseKey,
              [groupItem.group]: {
                ...(collpaseKey[groupItem.group] || {}),
                [domainItem.domainKey]: true,
              },
            });
          }
        });
      });
    }
  }, [otherEntitykey]);

  // 计算当前选中和选中目录的关系 =》刷新
  // 刷新后data + key 都存在 => 1
  //   当前的选中项目是不是在新的数据里面
  //     存在 刷新 Tip: 存在的条件是父集目录+子集完全一致，防止调整目录位置引起的数据对不上的场景
  //     不在 重新取第一项
  // 刷新后data + !key => 2
  //   重新取第一项
  // 刷新后!data => 3
  //   无数据 置空
  useEffect(() => {
    if (!menuRefreshFlag?.flag) return;
    // 计算当前选中和选中目录的关系 =》刷新
    const groupData = menuRefreshFlag?.data || [];
    if (currentActiveKey?.clickEntity?.entityKey && groupData?.length) {
      // 选中项是不是在新的数据里面
      const isExist = judgeExistInEntity(currentActiveKey, groupData);
      if (isExist) {
        // 存在 刷新详情
        onRefresh?.(currentActiveKey);
      } else {
        const domainFirstResult = recursionGetFirstDomain(groupData);
        // 不存在 重新取第一项
        setCollpaseKey({
          ...(collpaseKey || {}),
          [domainFirstResult?.group?.group || ""]: {
            ...(collpaseKey?.[domainFirstResult?.group?.group || ""] || {}),
            [domainFirstResult?.domain?.domainKey || ""]: true,
          },
        });
        setCurrentActiveKey({
          clickGroup: domainFirstResult?.group,
          clickDomain: domainFirstResult?.domain,
          clickEntity: domainFirstResult?.entity,
        });
      }
    } else if (!currentActiveKey?.clickEntity?.entityKey && groupData?.length) {
      // 刷新后data + !key
      const domainFirstResult = recursionGetFirstDomain(groupData);
      // 重新取第一项
      setCollpaseKey({
        ...(collpaseKey || {}),
        [domainFirstResult?.group?.group || ""]: {
          ...(collpaseKey?.[domainFirstResult?.group?.group || ""] || {}),
          [domainFirstResult?.domain?.domainKey || ""]: true,
        },
      });
      setCurrentActiveKey({
        clickGroup: domainFirstResult?.group,
        clickDomain: domainFirstResult?.domain,
        clickEntity: domainFirstResult?.entity,
      });
    } else {
      // 无数据 置空详情
      onItemChange?.({});
      setCurrentActiveKey({});
    }
    completeRefreash?.();
  }, [menuRefreshFlag]);

  // 通知父级别当前项切换
  useEffect(() => {
    if (currentActiveKey?.clickEntity?.entityKey) {
      onItemChange?.(currentActiveKey?.clickEntity);
    }
  }, [currentActiveKey]);

  return (
    <>
      {groupDataAfterSearch.map((groupItem) => (
        //   一级区域
        <div className={styles.firstContent} key={groupItem.group}>
          {/* 一级title */}
          <div key={`${groupItem.group}-header`} className={styles.lineContainer} onClick={() => handleFirstCollpase(groupItem.group)}>
            <span className={classNames(styles.groupTitle, styles.groupFirstTitle)} title={groupItem.group}>
              {groupItem.group}
            </span>
            <div className={styles.groupOtherInfo}>
              <span className={styles.amount}>{groupItem.serviceAmount}</span>
              {!collpaseKey?.[groupItem.group] ? (
                <DownOutlined className={styles.collpaseIcon} />
              ) : (
                <UpOutlined className={styles.collpaseIcon} />
              )}
            </div>
          </div>
          {/* 二级区域 */}
          {groupItem?.domainList?.length ? (
            <div
              key={`${groupItem.group}-domain`}
              className={classNames(styles.secondContent, {
                [styles.hide]: !collpaseKey?.[groupItem.group],
              })}
            >
              {groupItem.domainList.map((domainItem) => (
                <React.Fragment key={domainItem.domainKey}>
                  {/* 二级title */}
                  <div className={styles.lineContainer} onClick={() => handleSecondCollpase(groupItem.group, domainItem.domainKey)}>
                    <span className={classNames(styles.groupTitle, styles.groupSecondTitle)} title={domainItem.domainName}>
                      {domainItem.domainName}
                    </span>
                    <div className={styles.groupOtherInfo}>
                      <span className={styles.amount}>{domainItem.serviceAmount}</span>
                      {!collpaseKey?.[groupItem.group]?.[domainItem.domainKey] ? (
                        <DownOutlined className={styles.collpaseIcon} />
                      ) : (
                        <UpOutlined className={styles.collpaseIcon} />
                      )}
                    </div>
                  </div>
                  {domainItem?.entityList?.length ? (
                    <div
                      className={classNames(styles.thirdContent, {
                        [styles.hide]: !collpaseKey?.[groupItem.group]?.[domainItem.domainKey],
                      })}
                    >
                      {/* 三级title */}
                      {domainItem.entityList.map((entityItem) => (
                        <div
                          key={entityItem?.entityKey}
                          onClick={() =>
                            setCurrentActiveKey({
                              clickGroup: groupItem,
                              clickDomain: domainItem,
                              clickEntity: entityItem,
                            })
                          }
                          className={classNames(styles.lineContainer, styles.thirdLineContainer, {
                            [styles.activeLineContainer]: entityItem.entityKey === currentActiveKey?.clickEntity?.entityKey,
                          })}
                        >
                          <span className={classNames(styles.groupTitle, styles.groupThirdTitle)} title={entityItem.entityName}>
                            {entityItem.entityName}
                          </span>
                          <span className={classNames(styles.amount, styles.thirdAmount)}>{entityItem.serviceAmount}</span>
                        </div>
                      ))}
                    </div>
                  ) : null}
                </React.Fragment>
              ))}
            </div>
          ) : null}
        </div>
      ))}
    </>
  );
};

export default CustomerCollpaseForDomain;
