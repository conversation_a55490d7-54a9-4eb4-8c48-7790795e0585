import React, { useState, useEffect } from 'react';
import { Button, Drawer, Form, Input, Select, Space, Spin, message } from 'antd';
import { qryEntityPropList, modifyDomainEntityExpand } from '@/services/entityService';
import { CloseOutlined } from '@ant-design/icons';
import { SelectedEntityProps, RelationTypeSourceProps, DomainInfoProps } from '../../types';
import useI18n from '@/hooks/useI8n';

interface IEditExpandEntityDrawer {
  selectedEntity: SelectedEntityProps;
  isProduct: boolean;
  relationTypeSource: RelationTypeSourceProps[];
  currentDomainInfo: DomainInfoProps[];
  open: boolean;
  initValue: any;
  ignoreVersionSource: any;
  onCancel: () => void;
  onOk: () => void;
}

const EditExpandEntityDrawer: React.FC<IEditExpandEntityDrawer> = (props) => {
  const {
    open,
    ignoreVersionSource,
    selectedEntity,
    isProduct,
    initValue,
    relationTypeSource,
    currentDomainInfo,
    onCancel,
    onOk,
  } = props;
  const { formatMessage } = useI18n();
  const [form] = Form.useForm();
  const [submitSpinning, setSubmitSpinning] = useState<boolean>(false);
  const [formValues, setFormValues] = useState<any>({}); // formValues

  const [defaultValues, setDeFaultValues] = useState<any>({});

  const { Option, OptGroup } = Select;
  const [expandPropList, setExpandPropList] = React.useState<any>([]); // 扩展实体的属性数据源

  const [expandRelType, setExpandRelType] = React.useState<string>('');
  const [expandName, setExpandName] = React.useState<string>('');
  const [currentEntityPropList, setCurrentEntityPropList] = React.useState<any>([]); // 当前实体的属性数据源

  const CREATIONSOURCE = [
    { name: 'Product-Owned', key: 'P' },
    { name: 'Project-Customized', key: 'C' },
  ];

  // modal close
  const onClose = async () => {
    onCancel?.();
    form.resetFields();
  };

  const editExpandEntity = async () => {
    setSubmitSpinning(true);
    try {
      // 调用修改实体扩展接口
      const curPostData = {
        ...formValues,
        ignoreVer: formValues?.ignoreVer?.length ? formValues?.ignoreVer?.join(',') : null, // 处理ignoreVer显示空选项的bug
        entityId: selectedEntity.entityId,
      };
      const resultData = await modifyDomainEntityExpand(initValue.expandId, curPostData);
      if (resultData) {
        message.success(formatMessage('EXPANDENTITY.EDIT.SUCCESS'));
        onOk?.();
        form.resetFields();
      }
    } finally {
      setSubmitSpinning(false);
    }
  };

  const hasDuplicate = (relationshipArr: any) => {
    return relationshipArr?.some(
      (item: any, index: number) =>
        relationshipArr?.findIndex((item2: any) => JSON.stringify(item) === JSON.stringify(item2)) !== index,
    );
  };

  const checkEditExpandData = async () => {
    form.validateFields().then(async (values) => {
      // 根据对应关系，校验Associated Relationship的长度
      const relationshipLength = formValues.odhEntityRelPropList?.length;
      if (relationshipLength === 0) {
        message.info(formatMessage('EXPANDENTITY.ADD.RELATIONSHIP_REQUIRED'));
        return;
      }
      formValues.odhEntityRelPropList?.forEach((item: any) => {
        item.expandId = defaultValues.expandId;
      });
      // 校验Associated Relationship中是否存在相同的关联关系
      const odhEntityRelPropArr = formValues.odhEntityRelPropList?.map(
        ({ propId, expandPropId }: { propId: any; expandPropId: any }) => ({
          propId,
          expandPropId,
        }),
      );
      if (hasDuplicate(odhEntityRelPropArr)) {
        message.info(formatMessage('EXPANDENTITY.ADD.RELATIONSHIP_DUPLICATE'));
        return;
      }
      editExpandEntity();
    });
  };

  const queryExpandEntityPropList = async (value: any) => {
    if (value) {
      const { success, data } = await qryEntityPropList(value);
      if (success) {
        setExpandPropList(data);
      } else {
        setExpandPropList([]);
      }
    }
  };

  const onExpandEntityChange = (value: any, option: any) => {
    // 查询扩展实体的属性

    form.setFieldsValue({
      'odhEntityRelPropList.expandPropId': undefined,
    });
    queryExpandEntityPropList(value);
    // 设置expandName
    const expandNameVal = option.key.charAt(0).toLowerCase() + option.key.slice(1);
    setExpandName(expandNameVal);
    if (expandNameVal.endsWith('s')) {
      form.setFieldsValue({
        propName: expandNameVal,
      });
    } else if (expandRelType === 'N') {
      form.setFieldsValue({
        propName: `${expandNameVal}s`,
      });
    } else {
      form.setFieldsValue({
        propName: expandNameVal,
      });
    }
  };

  const onRelTypeChange = (value: any, option: any) => {
    const relType = option.children?.split('->');
    setExpandRelType(relType?.length > 1 ? relType[1].trim() : '');
  };

  const queryCurrentEntityPropList = async () => {
    if (selectedEntity.entityId) {
      const { success, data } = await qryEntityPropList(selectedEntity.entityId);
      if (success) {
        setCurrentEntityPropList(data);
      } else {
        setCurrentEntityPropList([]);
      }
    }
  };

  useEffect(() => {
    if (expandRelType === '1') {
      form.setFieldsValue({
        propName: expandName,
      });
    } else if (expandRelType === 'N') {
      if (expandName) {
        if (expandName.endsWith('s')) {
          form.setFieldsValue({
            propName: expandName,
          });
        } else {
          form.setFieldsValue({
            propName: `${expandName}s`,
          });
        }
      }
    } else {
      setExpandPropList([]);
    }
  }, [expandRelType]);

  useEffect(() => {
    if (open) {
      // 深拷贝行数据后初始化值
      const defaultValues: any = {};
      Object.assign(defaultValues, initValue);
      queryExpandEntityPropList(defaultValues.expandEntityId);
      if (defaultValues?.ignoreVer) {
        defaultValues.ignoreVer = defaultValues?.ignoreVer?.split(',');
      }
      defaultValues.entityName = selectedEntity?.entityName;
      setExpandName(defaultValues.propName);
      setDeFaultValues(defaultValues);
      const data = form.getFieldsValue();
      // 提取 formValues 中的属性
      const filteredValues = Object.keys(data).reduce((acc: any, key) => {
        if (key in defaultValues) {
          acc[key] = defaultValues[key];
        }
        return acc;
      }, {});

      // 设置过滤后的属性
      setFormValues(filteredValues);
    }
  }, [initValue, open]);

  useEffect(() => {
    // 查询当前实体的主键字段
    if (open) {
      queryCurrentEntityPropList();
    }
  }, [open]);

  return (
    <Drawer
      title={formatMessage('EXPANDENTITY.EDIT.TITLE')}
      onClose={onClose}
      maskClosable={false}
      open={open}
      width={720}
      afterOpenChange={() => {
        form.resetFields();
      }}
      footer={
        <Space style={{ float: 'right' }}>
          <Spin spinning={submitSpinning}>
            <Button type="primary" onClick={checkEditExpandData}>
              {formatMessage('EXPANDENTITY.ADD.SUBMIT')}
            </Button>
          </Spin>
          <Button onClick={onClose}>{formatMessage('EXPANDENTITY.ADD.CANCEL')}</Button>
        </Space>
      }
    >
      <Form
        form={form}
        labelCol={{ span: 8 }}
        wrapperCol={{ span: 16 }}
        initialValues={defaultValues}
        onValuesChange={(cValues, alValues) => {
          setFormValues(alValues);
        }}
      >
        <Form.Item
          label={formatMessage('EXPANDENTITY.ADD.CURRENT_ENTITY')}
          name="entityName"
          rules={[{ required: true, message: '' }]}
        >
          <Input allowClear disabled />
        </Form.Item>
        <Form.Item
          label={formatMessage('EXPANDENTITY.ADD.RELATIONSHIP_TYPE')}
          name="relType"
          rules={[{ required: true, message: '' }]}
        >
          <Select onChange={onRelTypeChange} disabled={!isProduct && initValue?.creationSrc === 'P'}>
            {relationTypeSource.map((i) => (
              <Select.Option key={i.relType} value={i.relType}>
                {i.relTypeName}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          label={formatMessage('EXPANDENTITY.ADD.EXPAND_ENTITY')}
          name="expandEntityId"
          rules={[{ required: true, message: '' }]}
        >
          <Select onChange={onExpandEntityChange} disabled={!isProduct && initValue?.creationSrc === 'P'}>
            {currentDomainInfo.map((group) => (
              <OptGroup key={group.domainObjId} label={group.domainObjName}>
                {group.entityList.map(
                  (option) =>
                    option.entityType !== 'S' && (
                      <Option
                        key={option.entityCode}
                        value={option.entityId}
                        disabled={option.entityId === selectedEntity.entityId}
                      >
                        {option.entityName}
                      </Option>
                    ),
                )}
              </OptGroup>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          // 这个Item是一个容器，加上rule属性必填样式不会生效，需手动添加样式
          label={
            <span>
              <span
                style={{
                  display: 'inline-block',
                  marginInlineEnd: 4,
                  color: '#ff4d4f',
                  fontSize: 14,
                  fontFamily: 'SimSun, sans-serif',
                  lineHeight: 1,
                }}
              >
                *
              </span>
              {formatMessage('EXPANDENTITY.ADD.ASSOCIATED_RELATIONSHIP')}
            </span>
          }
        >
          <Form.List name="odhEntityRelPropList">
            {(subFields, subOpt) => (
              <div style={{ display: 'flex', flexDirection: 'column', rowGap: 16 }}>
                {subFields.map((subField) => (
                  <Space key={subField.key}>
                    <Form.Item noStyle name={[subField.name, 'propId']} rules={[{ required: true, message: '' }]}>
                      <Select style={{ width: '150px' }} disabled={!isProduct && initValue?.creationSrc === 'P'}>
                        {currentEntityPropList.map((i: any) => (
                          <Select.Option key={i.propId} value={i.propId}>
                            {i.propName}
                          </Select.Option>
                        ))}
                      </Select>
                    </Form.Item>
                    <Form.Item noStyle name={[subField.name, 'expandPropId']} rules={[{ required: true, message: '' }]}>
                      <Select
                        style={{ width: '150px', marginLeft: '5px' }}
                        disabled={!isProduct && initValue?.creationSrc === 'P'}
                      >
                        {expandPropList.map((i: any) => (
                          <Select.Option key={i.propId} value={i.propId}>
                            {i.propName}
                          </Select.Option>
                        ))}
                      </Select>
                    </Form.Item>
                    <CloseOutlined
                      onClick={() => {
                        subOpt.remove(subField.name);
                      }}
                      disabled={!isProduct && initValue?.creationSrc === 'P'}
                    />
                  </Space>
                ))}
                <Button
                  type="dashed"
                  onClick={() => subOpt.add()}
                  block
                  disabled={!isProduct && initValue?.creationSrc === 'P'}
                >
                  {formatMessage('EXPANDENTITY.ADD.ADD_RELATIONSHIP')}
                </Button>
              </div>
            )}
          </Form.List>
        </Form.Item>
        <Form.Item
          label={formatMessage('EXPANDENTITY.ADD.EXPAND_NAME')}
          name="propName"
          rules={[
            { required: true, message: '' },
            {
              validator: (_, value) => {
                const regex = /^[a-z][a-zA-Z0-9]*$/;
                if (!regex.test(value)) {
                  return Promise.reject(new Error(formatMessage('EXPANDENTITY.ADD.NAME_FORMAT_ERROR')));
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          {/* 默认生成 */}
          <Input disabled={!isProduct && initValue?.creationSrc === 'P'} />
        </Form.Item>
        <Form.Item label={formatMessage('EXPANDENTITY.ADD.CREATION_SOURCE')} name="creationSrc">
          <Select disabled>
            {CREATIONSOURCE.map((i) => (
              <Select.Option key={i.key} value={i.key}>
                {i.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        {isProduct ? (
          <Form.Item label={formatMessage('EXPANDENTITY.ADD.IGNORE_VERSION')} name="ignoreVer">
            <Select mode="multiple">
              {ignoreVersionSource?.map((i: any) => (
                <Select.Option key={i.key} value={i.key}>
                  {i.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        ) : null}
        <Form.Item
          label={formatMessage('EXPANDENTITY.ADD.DESCRIPTIONS')}
          name="comments"
          rules={[{ required: true, message: '' }]}
        >
          <Input.TextArea allowClear />
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default EditExpandEntityDrawer;
