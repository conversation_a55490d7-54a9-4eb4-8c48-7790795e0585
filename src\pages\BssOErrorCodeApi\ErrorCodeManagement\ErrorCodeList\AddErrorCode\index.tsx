import { Button, Drawer, Form, Input, message, Select, Space, Spin, Tooltip, FormInstance } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { useEffect, useState } from 'react';

import useI18n from '@/hooks/useI8n';

import TextArea from 'antd/es/input/TextArea';
import { RedoOutlined } from '@ant-design/icons';
import { generateErrorCode, newErrorCode } from '@/services/errorCodeService';
import { useModel } from 'umi';
import styles from '../index.less';
import { debounce } from 'lodash';
import CodePrompt from '@/components/CodePrompt';
import { newErrorCodeType } from '../../types';
import { CREATIONSOURCE } from '../../constant';

interface IAddErrorCode {
  isProduct: boolean; // true:产品，false:项目
  addOpen: boolean;
  odhDomainTreeData: any;
  setAddOpen: (open: boolean) => void;
  handleReset: (params: { isRelated: boolean }) => Promise<void>;
}

const AddErrorCode: React.FC<IAddErrorCode> = (props) => {
  const { addOpen, odhDomainTreeData, setAddOpen, isProduct, handleReset } = props;
  const { formatMessage } = useI18n();
  const [form] = useForm();
  const { setQueryErrorCodeListParams, queryErrorCodeList } = useModel('useErrorCodeModel');
  const [spining, setSpining] = useState<boolean>(false);
  const [selectedDomainId, setSelectedDomainId] = useState<number | string>();
  const [defaultMatchConditions, setDefaultMatchConditions] = useState<string>('');
  const [reasonParams, setReasonParams] = useState<Array<{ paramName: string; paramValue: string }>>([]);

  const onClose = () => {
    setDefaultMatchConditions('');
    setReasonParams([]);
    setSelectedDomainId('');
    setAddOpen(false);
  };
  // apply domain 搜索
  const onInputToSearch = (input: any, option: any) => {
    return option?.children?.toLowerCase()?.indexOf(input?.toLowerCase()) >= 0;
  };

  const changeDomain = (value: number) => {
    setSelectedDomainId(value);
  };

  const handleErrorCodeChange = (e: any) => {
    const value = e.target.value.trim(); // 去除前后空格
    form.setFieldsValue({ errorCode: value }); // 更新表单的值
  };

  const addErrorCode = async (params: newErrorCodeType) => {
    try {
      setSpining(true);
      const { success } = await newErrorCode(params);
      if (success) {
        message.success(formatMessage('PROJECT.COMMON.ADDSUCCESS'));
        onClose();
        // 更新数据
        handleReset({ isRelated: false });
      }
    } catch (error) {
      console.error(error);
    } finally {
      setSpining(false);
    }
  };

  const checkErrorCodeData = async () => {
    form.validateFields().then(() => {
      const params = {
        errorCode: form.getFieldValue('errorCode'),
        errorReason: form.getFieldValue('errorReason'),
        ...(form.getFieldValue('domainId') !== 'all' ? { domainId: form.getFieldValue('domainId') } : {}),
        ...(!isProduct
          ? {
              defaultMatchConditions: defaultMatchConditions,
              defaultReasonParams: reasonParams.map((param) => param.paramValue), // 只传递 paramValue 数组
            }
          : {}),
      };
      addErrorCode(params);
    });
  };

  // 生成一个异常响应码
  const generateNewErrorCode = async () => {
    const params = selectedDomainId && typeof selectedDomainId === 'number' ? { domainId: selectedDomainId } : {};
    const { success, data } = await generateErrorCode(params);
    if (success) {
      form.setFieldValue('errorCode', data);
    } else {
      form.setFieldValue('errorCode', '');
    }
  };

  // 刷新生成新的异常响应码
  const handleRefreshCode = () => {
    generateNewErrorCode();
  };

  // 根据reason生成的param的值改变
  const handleCodeChange = (index: number, newValue: string) => {
    setReasonParams((prev) => prev.map((item, i) => (i === index ? { ...item, paramValue: newValue } : item)));
  };

  // 使用正则表达式提取 {} 中的内容
  const extractContent = (str: string) => {
    const matches = str.match(/\{(.*?)\}/g);
    return matches ? matches.map((match) => match.slice(1, -1)) : [];
  };

  // 防抖处理的 change 事件
  const handleErrorReasonChange = debounce((value: string) => {
    const reasonParamsContent = extractContent(value); // 提取内容

    // 创建一个新的 reasonParams 数组
    const newReasonParams = reasonParamsContent.map((item, index) => {
      const existingParam = reasonParams.find((param) => param.paramName === `reasonParam${index}`);

      // 如果存在相同的 paramName，保留原有的 paramValue，否则创建新的对象
      return existingParam ? { ...existingParam } : { paramName: `reasonParam${index}`, paramValue: '' };
    });

    setReasonParams(newReasonParams);
  }, 300); // 300ms 防抖

  useEffect(() => {
    if (selectedDomainId) {
      generateNewErrorCode();
    }
  }, [selectedDomainId]);

  return (
    <Drawer
      title={formatMessage('ERRORCODE.ADD.TITLE')}
      onClose={onClose}
      maskClosable={false}
      open={addOpen}
      width={900}
      afterOpenChange={() => {
        form.resetFields();
      }}
      footer={
        <Space style={{ float: 'right' }}>
          <Spin spinning={spining}>
            <Button type="primary" onClick={checkErrorCodeData}>
              {formatMessage('PROJECT.COMMON.SUBMIT')}
            </Button>
          </Spin>
          <Button onClick={onClose}>{formatMessage('PROJECT.COMMON.CANCEL')}</Button>
        </Space>
      }
    >
      <Form form={form} labelCol={{ span: 6 }} wrapperCol={{ span: 18 }}>
        <Form.Item
          label={formatMessage('ERRORCODE.COMMON.APPLYDOMAIN')}
          name="domainId"
          rules={[{ required: true, message: '' }]}
          extra={formatMessage('ERRORCODE.COMMON.APPLYDOMAIN.NOTE')}
        >
          <Select showSearch allowClear onChange={changeDomain} filterOption={onInputToSearch}>
            <Select.Option key="all" value="all" disabled={!isProduct}>
              All
            </Select.Option>
            {odhDomainTreeData.map((i: any) => (
              <Select.Option key={i.domainId} value={i.domainId} disabled={isProduct}>
                {i.domainName}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          label={formatMessage('ERRORCODE.COMMON.ERRORCODE')}
          name="errorCode"
          rules={[{ required: true, message: '' }]}
        >
          <Input
            readOnly
            onChange={handleErrorCodeChange} // 处理输入变化
            suffix={
              <Tooltip title="If Error Code already exists, click to get a new Error Code.">
                <RedoOutlined onClick={handleRefreshCode} />
              </Tooltip>
            }
          />
        </Form.Item>
        <Form.Item label={formatMessage('ERRORCODE.COMMON.CREATIONSOURCE')} name="creationSrc">
          <Select defaultValue={isProduct ? CREATIONSOURCE[0] : CREATIONSOURCE[1]} disabled>
            {CREATIONSOURCE.map((i: any) => (
              <Select.Option key={i.key} value={i.key}>
                {i.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          label={formatMessage('ERRORCODE.COMMON.ERRORREASON')}
          name="errorReason"
          rules={[{ required: true, message: '' }]}
          extra={formatMessage('ERRORCODE.COMMON.REASONPARAMETER.NOTE')}
        >
          <TextArea rows={4} onChange={(e) => handleErrorReasonChange(e.target.value)} />
        </Form.Item>
        {!isProduct && (
          <>
            <Form.Item label={`${formatMessage('ERRORCODE.DEFAULT.MATCHCONDITIONS')}`} name="defaultMatchConditions">
              <CodePrompt
                language="java"
                id="defaultMatchConditions"
                className="formItemCodeMirror"
                editable={true}
                minHeight="28px"
                maxHeight="60px"
                basicSetup={{
                  lineNumbers: false,
                  highlightActiveLine: false,
                  highlightActiveLineGutter: false,
                }}
                value={defaultMatchConditions ? defaultMatchConditions : ''}
                allSchema={[]}
                onChange={(codeValue: string) => setDefaultMatchConditions(codeValue)}
              />
            </Form.Item>
            {reasonParams.map((item, index) => {
              return (
                <Form.Item
                  label={`${formatMessage('ERRORCODE.DEFAULT.REASONPARAMETER')} ${index}`}
                  key={item.paramName}
                  name={item.paramName}
                  rules={defaultMatchConditions ? [{ required: true, message: 'Please input.' }] : []}
                >
                  <CodePrompt
                    language="java"
                    id={item.paramName}
                    className="formItemCodeMirror"
                    editable={true}
                    minHeight="28px"
                    maxHeight="60px"
                    basicSetup={{
                      lineNumbers: false,
                      highlightActiveLine: false,
                      highlightActiveLineGutter: false,
                    }}
                    value={item.paramValue}
                    allSchema={[]}
                    onChange={(newValue: string) => handleCodeChange(index, newValue)}
                  />
                </Form.Item>
              );
            })}
          </>
        )}
      </Form>
    </Drawer>
  );
};

export default AddErrorCode;
