import { RequestTable, ResponseTable, UrlHeader, Title, BlockTitle } from '@/components';
import useI18n from '@/hooks/useI8n';
import styles from './index.less';
import { IServiceDetail } from '@/services/typing';
import { useMemo } from 'react';
import { saveAsUseCaseServicePropsType } from '@/services';
import { BYCATALOG } from '@/constants';
import { Input } from 'antd';

interface IServiceDetailCom {
  wholeURL?: string;
  fromType?: string; // BYCATALOG | BYDOMAIN
  isHtmlReject?: boolean;
  serviceDetail?: IServiceDetail;
  switchToDebugMode?: () => void;
  debugMode?: boolean;
  requestDataSource?: any;
  ButtonGroupFnObj?:
    | {
        [k in 'debugServiceFn' | 'onSaveAsFn']: (e: any) => any;
      }
    | any;
  hiddenSelectBtn?: boolean;
  isEditAPIGService?: boolean;
  saveUsType?: string;
  responseInfo?: Pick<saveAsUseCaseServicePropsType, 'serviceRespParamList'> | any;
  refreshAPIGService?: () => void;
}

const DetailBase: React.FC<IServiceDetailCom> = ({
  wholeURL = '',
  fromType = '',
  serviceDetail,
  debugMode = false,
  requestDataSource = {},
  ButtonGroupFnObj = {},
  hiddenSelectBtn = false,
  responseInfo = [],
  isHtmlReject = false,
  isEditAPIGService = false,
  saveUsType = '',
  refreshAPIGService = () => {},
}) => {
  const { formatMessage } = useI18n();

  const ResponseArea = useMemo(() => {
    if (debugMode) {
      if (responseInfo?.length > 0) {
        return (
          <div key="debugResponseTable" className={styles.responseContent}>
            <Title>{formatMessage('SERVICEDETAIL.RESPONSE.TITLE')}</Title>
            <ResponseTable debugMode responseInfo={responseInfo} isHtmlReject={isHtmlReject} />
          </div>
        );
      }
      return null;
    }
    return (
      <div key="nomalResponseTable" className={styles.responseContent}>
        <Title>{formatMessage('SERVICEDETAIL.RESPONSE.TITLE')}</Title>
        <ResponseTable serviceDetailData={serviceDetail} />
      </div>
    );
  }, [serviceDetail, debugMode, responseInfo, isHtmlReject]);

  return (
    <>
      {/* url content */}
      <UrlHeader
        serviceDetail={serviceDetail}
        style={{ marginBottom: 24 }}
        shwoBtnGroup={debugMode}
        ButtonGroupFnObj={ButtonGroupFnObj}
        hiddenSelectBtn={hiddenSelectBtn}
        isEditAPIGService={isEditAPIGService}
        saveUsType={saveUsType}
        refreshAPIGService={refreshAPIGService}
      />
      {!debugMode && (
        <>
          {/* catalog content */}
          <div className={styles.catalog}>
            <Title>
              {formatMessage(fromType === BYCATALOG ? 'PROJECT.COMMON.DOMAINENTITY' : 'PROJECT.COMMON.SERVICECATALOG')}
            </Title>
            <p className={styles.description}>
              {fromType === BYCATALOG
                ? serviceDetail?.serviceEntity
                : `${serviceDetail?.catalog} -> ${serviceDetail?.group} -> ${serviceDetail?.subCatalog}`}
            </p>
          </div>
          <div className={styles.descContent}>
            <Title>{formatMessage('PROJECT.COMMON.DESC')}</Title>
            <p className={styles.description}>{serviceDetail?.comments}</p>
          </div>
        </>
      )}
      {/* request Information */}
      <div className={styles.requestContent}>
        <Title>{formatMessage('SERVICEDETAIL.REQUEST.TITLE')}</Title>
        {wholeURL && (
          <div>
            <BlockTitle>{formatMessage('SERVICEDETAIL.REQUEST.URL')}</BlockTitle>
            <div title={wholeURL}>
              <Input readOnly variant="borderless" value={wholeURL} />
            </div>
          </div>
        )}
        <RequestTable
          requestDataSource={requestDataSource}
          servicemethod={serviceDetail?.method}
          debugMode={debugMode}
          isEditAPIGService={isEditAPIGService}
        />
      </div>
      {ResponseArea}
    </>
  );
};

export default DetailBase;
