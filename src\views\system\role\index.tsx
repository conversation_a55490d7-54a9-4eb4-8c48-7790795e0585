import { Button, Form, Input, Space, Table } from "antd";
import { useForm } from "antd/es/form/Form";
import type { ColumnsType } from "antd/es/table";
import { useRef } from "react";
import api from "../../../api";
import type { IAction } from "../../../types/modal";
import { formatDate } from "../../../utils";
import { useAntdTable } from 'ahooks';
import type { Role } from "../../../types/api";
import CreateRole from "./CreateRole";
import { message, modal } from "../../../utils/AntdGlobal";
import SetPermission from "./SetPermission";

export default function RoleList() {
    const [form] = useForm()
    const roleRef = useRef<{
        open: (type: IAction, data?: Role.EditParams | { parentId: string }) => void
    }>(null)

    const permissionRef = useRef<{
        open: (type: IAction, data?: Role.EditParams | { parentId: string }) => void
    }>(null)

    const getTableData = async ({ current, pageSize }: { current: number, pageSize: number }) => {
        const data = await api.getRoleList({
            pageNum: current,
            pageSize
        })
        return {
            total: data.page.total,
            list: data.list
        }
    };
    const { tableProps, search } = useAntdTable(getTableData, {
        form,
        defaultPageSize: 10
    });

    const handleCreate = () => {
        roleRef.current?.open('create')
    }

    const handleEdit = (record: Role.RoleItem) => {
        roleRef.current?.open('edit', record)
    }

    const handlePermission = (record: any) => {
        permissionRef.current?.open('edit', record)
    }

    const handleDelete = (_id: string) => {
        modal.confirm({
            title: "删除确认",
            content: <span>确认删除所选用户吗？</span>,
            onOk: async () => {
                await api.delRole({ _id })
                message.success('删除成功')
                search.submit()
            }
        })
    }

    const columns: ColumnsType<Role.RoleItem> = [
        {
            title: '角色名称',
            dataIndex: 'roleName',
            key: 'roleName',
            align: 'center'
        },
        {
            title: '备注',
            dataIndex: 'remark',
            key: 'remark',
            align: 'center'
        },
        {
            title: '更新时间',
            dataIndex: 'updateTime',
            key: 'updateTime',
            align: 'center',
            render(updateTime: string) {
                return formatDate(updateTime)
            }
        },
        {
            title: '创建时间',
            dataIndex: 'createTime',
            key: 'createTime',
            align: 'center',
            render(createTime: string) {
                return formatDate(createTime)
            }
        },
        {
            title: '操作',
            dataIndex: 'action', //如果该列为空，下方render的第一个value可以省略
            key: 'action',
            align: 'center',
            render(_, record) {
                return (
                    <Space>
                        <Button type='text' onClick={() => handleEdit(record)}>
                            编辑
                        </Button>
                        <Button type='text' onClick={() => handlePermission(record)}>
                            设置权限
                        </Button>
                        <Button type='text' danger onClick={() => handleDelete(record._id)}>
                            删除
                        </Button>
                    </Space>
                )
            }
        }
    ]
    return (
        <>
            <Form className="search" layout="inline" form={form}>
                <Form.Item name="roleName" label="角色名称">
                    <Input placeholder="请输入角色名称" />
                </Form.Item>
                <Form.Item >
                    <Button type="primary" className="mr10" onClick={search.submit}>搜索</Button>
                    <Button type="default" onClick={search.reset}>重置</Button>
                </Form.Item>
            </Form>
            <div className="table">
                <div className="header">
                    <div className="title">角色列表</div>
                    <div className="action">
                        <Button type="primary" onClick={handleCreate}>新增</Button>
                    </div>
                </div>
                <Table
                    bordered
                    rowKey='_id'
                    columns={columns}
                    {...tableProps}
                />
            </div>
            <CreateRole ref={roleRef} update={search.submit} />
            <SetPermission ref={permissionRef} update={search.submit} />
        </>
    )
}