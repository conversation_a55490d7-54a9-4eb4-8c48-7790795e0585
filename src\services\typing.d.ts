// domain 相关
export type IEntityList = Partial<{
  entityKey: string;
  entityCode: string;
  entityName: string;
  entityType: string;
  comments: string;
  serviceAmount: number;
  entityProperties?: any;
}>;

export interface IDomainList {
  domainKey: string;
  domainName: string;
  domainCode: string;
  comments: string;
  serviceAmount?: number;
  entityList: IEntityList[];
  children?: Array<IEntityList>;
}

export interface IGroupList {
  group: string;
  serviceAmount?: number;
  domainList: IDomainList[];
  children?: Array<IDomainList>;
}

// entity detail相关
export interface IEntityProperty {
  name: string;
  dataType: string;
  relEntityKey?: any;
  relType?: any;
  relShip?: any;
  keyFlag: string;
  required: string;
  length: number;
  orderBy?: any;
  searchType?: any;
  index?: string;
  filter?: string;
  example: string;
  comments: string;
  relEntityName?: any;
  avlValue?: string;
}

export type IEntityDto = Partial<{
  entityKey: string;
  entityCode: string;
  entityName: string;
  entityType: string;
  comments: string;
  serviceAmount?: any;
  entityProperties: IEntityProperty[];
  domainObjectType: string;
}>;

export type IDomainEntityRel = Partial<{
  entityKey: string;
  entityName: string;
  entityCode: string;
  entityType: string;
  keyProperties: string[];
  subEntityList: IDomainEntityRel[];
  relType?: any;
  relShip?: any;
}>;

export interface IServiceList {
  comments: string;
  catalog: string;
  subCatalog: string;
  servicePath: string;
  entityKey: string;
  serviceKey: string;
  state: string;
  serviceName: string;
  serviceMethod: string;
  group: string;
  domainName?: any;
  entityName?: any;
  serviceCode?: any;
  method?: any;
  parameters?: any;
  responses?: any;
}

export interface IEntityDetail {
  basePath: string;
  entityDto: IEntityDto;
  domainEntityRel: IDomainEntityRel;
  serviceList: IServiceList[];
}

// catalog相关
export interface IServiceBaseList {
  comments: string;
  odataFlag: string;
  serviceCode: string;
  serviceMethod: string;
  serviceName: string;
  servicePath: string;
  serviceEntity: string;
}


export interface IOriginalCatalogList {
  catalogName: string;
  domainServiceList: {
    domainName: string;
    subCatalogServiceList: {
      subCatalogName: string;
      serviceBaseList: IServiceBaseList[];
    }[];
  }[];
}

export interface ISubCatalogList {
  serviceAmount: number;
  currentPathList?: string[];
  subCatalog: string;
  serviceBaseList?: IServiceBaseList[]
}

export interface ICatalogGroupList {
  groupName: string;
  serviceAmount?: number;
  currentPathList?: string[];
  subCatalogList: ISubCatalogList[];
  children?: Array<ISubCatalogList>;
}

export interface ICatalog {
  catalog: string;
  serviceAmount?: number;
  currentPathList?: string[]; // 添加currentPathList，解决点击同名目录产生的一系列问题
  groupList: ICatalogGroupList[];
  children?: Array<ICatalogGroupList>;
}

// catalog detail
export interface ICatalogDetailResult {
  comments: string;
  catalog: string;
  subCatalog: string;
  servicePath: string;
  entityKey: string;
  serviceKey: string;
  state: string;
  serviceName: string;
  serviceMethod: string;
  group: string;
  domainName?: any;
  entityName?: any;
  serviceCode?: any;
  method?: any;
  parameters?: any;
  responses?: any;
}

// service detail相关
export interface IDataModel {
  modelName: string;
  comments: string;
  className: string;
  properties: IParameter[];
  modelKey: string;
}

export interface IParameter {
  comments: string;
  reqIn: 'H' | 'P' | 'Q' | 'B' | 'C';
  dataType: string;
  name: string;
  dataModel: IDataModel;
  isArray: string;
  avlValue: string;
  required: string;
  hidden?: string;
  example: string;
  respCode: string;
  typeFormat?: string;
  contentType?: string;
  respIn: string;
}

export interface IResponse {
  comments: string;
  name: string;
  dataModel: IDataModel;
  isArray: string;
  respCode: string;
  required: string;
  example: string;
  avlValue?: string;
  dataType?: string;
  typeFormat?: string;
  contentType?: string;
  respIn: string;
}

export interface IServiceDetail {
  comments: string;
  method: string;
  serviceCode: string;
  catalog: string;
  entityKey: string;
  serviceName: string;
  subCatalog: string;
  entityName: string;
  servicePath: string;
  domainName: string;
  responses: IResponse[];
  state: string;
  parameters: IParameter[];
  group: string;
  serviceEntity: string;
  serviceMethod: string;
}

export interface UseCaseBeQueriedPropsType {
  totalPage?: number;
  pageNo?: number;
  pageSize?: number | string;
  totalCount?: number;
  list?: UseCaseBeQueriedListPropsType[];
}

export interface UseCaseBeQueriedListPropsType {
  useCaseName: string;
  comments: string;
  serviceCode: string;
  useCaseId: number | string;
  respParamList?: RespParamList[];
  reqParamList?: ReqParamList[];
  includeResp: string;
  method?: string;
  createdDate?: string;
}

export interface IEventList {
  eventName: string;
  eventCode: string;
  comments: string;
}

interface ReqParamList {
  reqIn: string;
  useCaseId: number;
  paramName: string;
  reqParamId: number;
  paramValue: string;
}

interface RespParamList {
  useCaseId: number;
  respParamId: number;
  respTime: number;
  respMsg: string;
  succFlag: string;
  respCode: string;
}

export interface IAPIGSubCatalogList {
  apigServId: number;
  apiMethod: string;
  serviceCode: string;
}

export interface IAPIGCatalogGroupList {
  catalogId: number;
  catalogName: string;
  serviceAmount?: number;
  serviceList: IAPIGSubCatalogList[];
  children?: Array<IAPIGSubCatalogList>;
}

export interface IAPIGCatalog {
  catalogId: number;
  catalogName: string;
  serviceAmount?: number;
  menuLevel?: number;
  menuPath?: string[];
  catalogChildNodes: IAPIGCatalog[];
  isParent: boolean;
  serviceList: any;
  children?: Array<IAPIGCatalogGroupList>;
}

export interface APIGServiceCatalogItemType {
  catalogId: number;
  catalogName: string;
  isParent: boolean;
  serviceList: any;
  serviceAmount?: number;
  catalogChildNodes: APIGServiceCatalogItemType[];
}

interface Window {
  top?: {
    portal?: {}
  }
}