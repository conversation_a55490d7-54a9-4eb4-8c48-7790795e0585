.bssSearchUseCase {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .header {
    display: flex;
    align-items: center;
    height: 44px;
    background: #ffffff;
    box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.12);
    padding: 0 12px;
    width: 100%;
    .returnIcon {
      font-size: 18px;
      color: #2d3040;
      cursor: pointer;
    }
    .returnText {
      font-size: 18px;
      font-family: Nunito Sans-Bold;
      font-weight: bold;
      color: #2d3040;
      line-height: 28px;
      margin-left: 8px;
      cursor: pointer;
    }
  }
  .spinContainer {
    width: 100%;
    height: 100%;
    overflow-x: hidden;
    :global {
      .ant-spin-container {
        width: 100%;
        height: 100%;
      }
    }
  }
  .activeLineContainer {
    width: 100%;
    height: 100%;
    overflow-y: auto;
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    padding: 12px 16px;
    box-sizing: border-box;
  }
}
