import React, { useEffect, useState, useRef, useMemo } from 'react';
import { Input, Select, Popconfirm, Form, Row, Col, message, Space, Button, Tooltip } from 'antd';
import type { InputRef, PopconfirmProps, TableColumnType } from 'antd';
import {
  FormOutlined,
  PlusOutlined,
  ArrowRightOutlined,
  DeleteOutlined,
  SearchOutlined,
  FunnelPlotOutlined,
} from '@ant-design/icons';
import { IDomainEntityRel, IEntityDto, IServiceList } from '@/services/typing.d';
import {
  queryODomainObjDetail,
  queryEntityDetailByEntityKey,
  qryEntitiesByDomainObjId,
} from '@/services/domainService';
import { deleteDomainEntity, qryEntityRelByDomainObjId } from '@/services/entityService';
import classNames from 'classnames';
import useI18n from '@/hooks/useI8n';
import styles from './index.less';
import AddDomainEntityDrawer from './AddDomainEntity';
import EditDomainEntityDrawer from './EditDomainEntity';
import EditDomainObjectDrawer from '../BssDomainDetail/EditDomainObject';
import EntityReleationShip from '../../BssODataApi/BssEntityDetail/EntityReleationShip';
import DataModelList from '@/components/DataModelList';
import EnumManagementList from '@/components/EnumManagementList';
import OperationIconTitle from '@/components/OperationIconTitle';
import BssDomainTopContent from './../BssDomainTopContent';
import { useModel } from 'umi';
import { getIgnoreVersion } from '@/services/dataModelService';
import ResizableTable from '@/components/ResizableTable';
import { FilterDropdownProps } from 'antd/es/table/interface';
import Highlighter from 'react-highlight-words';
import { useTableFilter } from '@/hooks/useTableFilter';

interface IBssDomainObjectDetail {
  selectedDomainNode?: object; //当前节点
  selectedRootNode?: object; //根节点
  scrollNode?: object;
  isProduct?: boolean;
  ignoreVersionSource: any;
  onSelectMenuObj: (entityData: any) => void; // 菜单选择
  onRefresh: (currentSelectKey) => void; // 刷新缓存 通知上级刷新缓存
}

const BssDomainObjectDetail: React.FC<IBssDomainObjectDetail> = (props) => {
  const {
    ignoreVersionSource,
    selectedDomainNode,
    selectedRootNode,
    scrollNode,
    isProduct = false,
    onSelectMenuObj,
    onRefresh,
  } = props;

  const { formatMessage } = useI18n();
  const rightPanelRef = useRef(null);
  const [form] = Form.useForm();
  const [formValues, setFormValues] = useState<any>({}); // formValues
  const [openDownloadModel, setOpenDownloadModel] = useState<boolean>(false); // download版本
  const [selectedEntityObj, setSelectedEntityObj] = useState<any>({}); // 当前选择的领域实体行数据
  const [domainObjDetail, setDomainObjDetail] = React.useState<object>(); // 当前领域对象详情信息
  const { setCurrentDomainObjId } = useModel('manageExtensionServiceModel');

  //实体关系相关参数
  const [domainEntityRelData, setDomainEntityRelData] = useState<Partial<IDomainEntityRel>>(); // entity relationship data
  const [entityDtoData, setEntityDto] = useState<Partial<IEntityDto>>(); // entity dto data
  const [serviceListData, setServiceListData] = useState<Array<IServiceList>>([]); // entity serviceList data

  const [openEditDomainObjectDrawer, setOpenEditDomainObjectDrawer] = useState<boolean>(false); //修改领域对象
  const [openAddDomainEntityDrawer, setOpenAddDomainEntityDrawer] = useState<boolean>(false); // 新增领域实体
  const [openEditDomainEntityDrawer, setOpenEditDomainEntityDrawer] = useState<boolean>(false); // 修改领域实体
  const [infoUsedforSearching, setInfoUsedforSearching] = useState<any>({
    pageNo: 1,
    pageSize: 5,
  }); // 当前用于查询实体列表的分页信息
  const [basicInfo, setBasicInfo] = useState<any>({});
  const [entitySource, setEntitySource] = useState<any>({}); //领域对象下的分页展示的实体列表数据源
  const [allEntitySource, setAllEntitySource] = useState<any>({}); //领域对象下的所有实体列表数据源

  const ODomainStateType = [
    { name: 'Draft', key: 'D' },
    { name: 'Released', key: 'R' },
  ];

  const CreationSource = [
    { name: 'Product-Owned', key: 'P' },
    { name: 'Project-Customized', key: 'C' },
  ];

  const TYPESOURCE = [
    {
      name: 'Aggregate',
      key: 'A',
    },
    {
      name: 'Collection',
      key: 'C',
    },
  ];

  const EntityTypeSource = [
    // A: AggregateRoot, E: Entity, S: ServiceHolder
    { name: 'ServiceHolder', key: 'S' },
    { name: 'AggregateRoot', key: 'A' },
    { name: 'Entity', key: 'E' },
  ];
  // 使用 useMemo 确保 dataSource 是稳定的引用
  const memoizedDataSource = useMemo(
    () => (Array.isArray(allEntitySource?.list) ? allEntitySource?.list : []),
    [allEntitySource],
  );
  // 自定义hooks，支持表格列筛选并分页展示
  const { filteredDataSource, getColumnSearchProps, getColumnEnumSearchProps, clearAllFilters } =
    useTableFilter(memoizedDataSource);
  //基础信息
  const MarginTop = { marginTop: 15 };

  const ColSpan = ({ value = '' }) => (
    <p
      style={{ marginLeft: 16, marginBottom: 0, textOverflow: 'ellipsis', overflow: 'hidden', whiteSpace: 'nowrap' }}
      title={value}
    >
      {value}
    </p>
  );

  const ColShow = ({ label, value }: any) => {
    return (
      <Col span={8} style={{ display: 'flex', flexDirection: 'row' }}>
        <Col
          style={{
            display: 'flex',
            justifyContent: 'flex-end',
            fontWeight: 600,
          }}
          span={9}
        >
          {label}
        </Col>
        <Col span={15}>
          <ColSpan value={value} />
        </Col>
      </Col>
    );
  };

  const LongColSpan = ({ label, value }: any) => {
    return (
      <>
        <Col
          span={3}
          style={{
            display: 'flex',
            justifyContent: 'flex-end',
            fontWeight: 600,
          }}
        >
          {label}
        </Col>
        <Col span={21}>
          <ColSpan value={value} />
        </Col>
      </>
    );
  };
  //entity list表格
  const entityColumns = [
    {
      dataIndex: 'entityName',
      title: formatMessage('PROJECT.COMMON.NAME'),
      width: '15%',
      ellipsis: true,
      ...getColumnSearchProps('entityName'),
      render: (_: string, record: any) => (
        <div
          className={styles.domainObjName}
          onClick={() => {
            record.module = 'Entity';
            onSelectMenuObj?.(record);
          }}
        >
          {record?.entityName}
        </div>
      ),
    },
    {
      dataIndex: 'entityCode',
      title: formatMessage('PROJECT.COMMON.CODE'),
      width: '10%',
      ellipsis: true,
      ...getColumnSearchProps('entityCode'),
    },
    {
      dataIndex: 'creationSrc',
      title: formatMessage('PROJECT.COMMON.CREATIONSOURCE'),
      width: '15%',
      ellipsis: true,
      render: (_: string, record: any) => {
        return CreationSource.filter((i) => i.key === record.creationSrc).map((i) => i.name);
      },
      ...getColumnEnumSearchProps('creationSrc', CreationSource),
    },
    {
      dataIndex: 'entityType',
      title: formatMessage('DOMAIN.ENTITY.TYPE'),
      width: '15%',
      ellipsis: true,
      render: (_: string, record: any) => {
        return EntityTypeSource.filter((i) => i.key === record.entityType).map((i) => i.name);
      },
      ...getColumnEnumSearchProps('entityType', EntityTypeSource),
    },
    {
      dataIndex: 'comments',
      title: formatMessage('PROJECT.COMMON.DESCRIPTION'),
      width: '35%',
      ellipsis: true,
      ...getColumnSearchProps('comments'),
    },
    {
      dataIndex: '',
      title: formatMessage('DOMAIN.OPERATION'),
      width: '10%',
      ellipsis: true,
      render: (_: string, record: any) => {
        return (
          <div>
            <Tooltip title={formatMessage('PROJECT.COMMON.EDIT')}>
              <FormOutlined
                className={classNames(styles.iconStyle, {
                  [styles.hide]: selectedRootNode?.state !== 'D',
                })}
                onClick={() => {
                  setOpenEditDomainEntityDrawer(true);
                }}
              />
            </Tooltip>

            <Popconfirm
              title={formatMessage('DOMAIN.ENTITY.DELETE_CONFIRM')}
              description={formatMessage('DOMAIN.ENTITY.DELETE_DESCRIPTION')}
              onConfirm={deleteDomainEntityService}
              okText={formatMessage('PROJECT.COMMON.CONFIRM')}
              cancelText={formatMessage('PROJECT.COMMON.CANCEL')}
            >
              {isProduct ? (
                <Tooltip title={formatMessage('PROJECT.COMMON.DELETE')}>
                  <DeleteOutlined
                    className={classNames(styles.versionIcon2, {
                      [styles.hide]: selectedRootNode?.state !== 'D',
                    })}
                    onClick={() => {}}
                  />
                </Tooltip>
              ) : (
                <Tooltip title={formatMessage('PROJECT.COMMON.DELETE')}>
                  <DeleteOutlined
                    className={classNames(styles.versionIcon2, {
                      [styles.hide]: record?.creationSrc === 'P' || selectedRootNode?.state !== 'D',
                    })}
                    onClick={() => {}}
                  />
                </Tooltip>
              )}
            </Popconfirm>
          </div>
        );
      },
    },
  ];

  const deleteDomainEntityService: PopconfirmProps['onConfirm'] = async (e) => {
    const entityId = selectedEntityObj?.entityId;
    // 调用删除领域实体接口
    try {
      const resultData = await deleteDomainEntity(entityId);

      if (resultData?.success) {
        message.success(formatMessage('DOMAIN.ENTITY.DELETE_SUCCESS'));
        //删除领域对象后重新渲染数据
        onRefresh(selectedDomainNode?.key);
        qryEntitiesByObjId();
      }
    } catch (error) {
      // message.success("Fail in deleting domain entity!");
    }
  };

  const queryDomainObjDetail = async () => {
    const { success, data } = await queryODomainObjDetail(selectedDomainNode?.domainObjId);
    form.resetFields();
    if (success) {
      setDomainObjDetail(data);
      setBasicInfo(data);
    } else {
      setDomainObjDetail({});
      setBasicInfo({});
    }
  };

  const qryEntitiesByObjId = async () => {
    // const param = {
    //   ...infoUsedforSearching,
    // };
    const param = {
      pageNo: 1,
      pageSize: 1000, // 按需求改为前端分页
    };
    await qryEntitiesByDomainObjId(selectedDomainNode?.domainObjId, param).then((res) => {
      const { success, data } = res;
      if (success) {
        // setFilteredEntitySource(data);
        setAllEntitySource(data);
        const { pageNo, pageSize } = infoUsedforSearching;
        const showData = data?.list?.slice((pageNo - 1) * pageSize, pageNo * pageSize);
        setEntitySource({
          list: showData,
          pageNo,
          pageSize,
          totalCount: data?.totalCount,
        });
        if (data?.list?.length > 0) {
          setSelectedEntityObj(data?.list[0]);
        }
      }
    });
  };

  // 查询entity detail
  const queryEntityDetail = async () => {
    const domainObjId = selectedDomainNode?.domainObjId;
    if (!domainObjId) {
      setDomainEntityRelData({});
      setEntityDto({});
      setServiceListData([]);
      return;
    }
    const { data, success } = await qryEntityRelByDomainObjId({
      domainObjId: domainObjId,
    });
    if (success) {
      setDomainEntityRelData(data || {});
      setEntityDto(data || {});
    }
  };

  const scrollToElement = (scrollNode) => {
    let element = null;
    if (scrollNode?.title === 'Entities') {
      element = document.getElementById('entityContent');
    } else if (scrollNode?.title === 'Data Models') {
      element = document.getElementById('dataModelContent');
    } else if (scrollNode?.title === 'Enums') {
      element = document.getElementById('enumContent');
    } else {
      element = document.getElementById('totleContent');
    }

    if (element && rightPanelRef.current) {
      rightPanelRef.current.scrollTop = element.offsetTop - rightPanelRef.current.offsetTop;
    }
  };

  useEffect(() => {
    // 分页触发查询实体列表
    // qryEntitiesByObjId();
    if (filteredDataSource) {
      const { pageNo, pageSize } = infoUsedforSearching;
      const showData = filteredDataSource.slice((pageNo - 1) * pageSize, pageNo * pageSize);
      setEntitySource({
        list: showData,
        pageNo,
        pageSize,
        totalCount: filteredDataSource.length,
      });
    }
  }, [infoUsedforSearching, filteredDataSource]);

  useEffect(() => {
    if (scrollNode) {
      scrollToElement(scrollNode);
    }
  }, [scrollNode]);

  useEffect(() => {
    //查询领域对象详情
    queryDomainObjDetail();
    //分页查询领域对象下的实体列表
    qryEntitiesByObjId();
    queryEntityDetail();
    setCurrentDomainObjId(selectedDomainNode?.domainObjId);
    // 重置表格列的筛选条件
    clearAllFilters();
  }, [selectedDomainNode]);

  return (
    <div className={styles.activeLineContainer} ref={rightPanelRef} id="totleContent">
      {/* 头部区域 */}
      <BssDomainTopContent
        selectedDomainNode={selectedDomainNode}
        onSelectDomianData={(data) => {
          onSelectMenuObj?.(data);
        }}
      />

      {/* 领域对象基础信息 */}
      <div className={styles.domainObjContent}>
        <OperationIconTitle
          title={formatMessage('DOMAIN.BASIC_INFO')}
          type={selectedRootNode?.state === 'D' ? 'edit' : ''}
          opt={formatMessage('PROJECT.COMMON.EDIT')}
          handleClick={() => {
            setOpenEditDomainObjectDrawer(true);
          }}
        />
        <div className={styles.content}>
          <Row style={MarginTop}>
            <ColShow label={formatMessage('PROJECT.COMMON.NAME')} value={basicInfo?.domainObjName} />
            <ColShow label={formatMessage('PROJECT.COMMON.CODE')} value={basicInfo?.domainObjCode} />
            <ColShow
              label={formatMessage('PROJECT.COMMON.TYPE')}
              value={
                TYPESOURCE.filter((item: { name: string; key: string }) => item.key === basicInfo?.domainObjType)[0]
                  ?.name
              }
            />
          </Row>
          <Row style={MarginTop}>
            <ColShow
              label={formatMessage('PROJECT.COMMON.CREATIONSOURCE')}
              value={CreationSource.filter((i) => i.key === basicInfo?.creationSrc).map((i) => i.name)}
            />
          </Row>
          <Row style={MarginTop}>
            <LongColSpan label={formatMessage('PROJECT.COMMON.DESCRIPTION')} value={basicInfo?.comments} />
          </Row>
        </div>
      </div>
      {/* 领域对象下的实体列表 */}
      <div className={styles.domainObjContent} id="entityContent">
        <OperationIconTitle
          title={formatMessage('DOMAIN.ENTITY_LIST')}
          type={selectedRootNode?.state === 'D' ? 'add' : ''}
          opt={formatMessage('PROJECT.COMMON.ADD')}
          handleClick={() => {
            setOpenAddDomainEntityDrawer(true);
          }}
        />
        <div className={styles.content}>
          <ResizableTable
            onRow={(record: any) => ({
              onClick: () => {
                setSelectedEntityObj(record);
              },
            })}
            size="small"
            style={{ marginTop: '12px' }}
            columns={entityColumns}
            dataSource={entitySource?.list || []}
            rowKey={(record: any) => record?.entityId}
            pagination={{
              hideOnSinglePage: true,
              showSizeChanger: true,
              pageSizeOptions: [5, 10, 15, 20],
              total: entitySource?.totalCount,
              current: entitySource?.pageNo,
              pageSize: entitySource?.pageSize as any,
              showTotal: (total) => formatMessage('PROJECT.COMMON.PAGINATION.TOTAL', { total }),
              onChange: (page: any, pageSize: any) => {
                setInfoUsedforSearching({
                  ...infoUsedforSearching,
                  pageSize,
                  pageNo: page,
                });
              },
            }}
            // onRow={(record:any) => ({
            //   onClick: () => {
            //     setSelectedEntityObj(record);
            //   },
            // })}
          />
        </div>
      </div>
      {/* 领域对象下实体的关系 */}
      {basicInfo?.domainObjType !== 'C' && (
        <div className={styles.domainObjContent}>
          <OperationIconTitle title={formatMessage('DOMAIN.ENTITY.RELATIONSHIP')} />
          <EntityReleationShip
            domainEntityRelData={domainEntityRelData}
            entityDtoData={entityDtoData}
            serviceListData={serviceListData}
            entityFlag={true}
          />
        </div>
      )}
      {/* 领域对象下的数据模型 */}
      <div id="dataModelContent">
        <DataModelList
          selectedDomainNode={selectedDomainNode} //当前节点
          selectedRootNode={selectedRootNode} //根节点
          isProduct={isProduct}
          onSelectModelObj={(data) => {
            data.module = 'MODEL';
            onSelectMenuObj?.(data);
          }}
          refreshData={() => {
            onRefresh(selectedDomainNode?.key); //刷新缓存
          }}
        />
      </div>
      {/* 领域对象下的枚举 */}

      <div id="enumContent">
        <EnumManagementList
          selectedDomainNode={selectedDomainNode} //当前节点
          selectedRootNode={selectedRootNode} //根节点
          isProduct={isProduct}
          onSelectModelObj={(data) => {
            data.module = 'ENUM';
            onSelectMenuObj?.(data);
          }}
          refreshData={() => {
            onRefresh(selectedDomainNode?.key); //刷新缓存
          }}
        />
      </div>

      {/* 新增领域实体 */}
      <AddDomainEntityDrawer
        ignoreVersionSource={ignoreVersionSource}
        domainObjType={basicInfo?.domainObjType}
        open={openAddDomainEntityDrawer}
        domainObjId={selectedDomainNode?.domainObjId}
        onCancel={() => setOpenAddDomainEntityDrawer(false)}
        isProduct={isProduct}
        onOk={() => {
          //新增实体成功后操作
          setOpenAddDomainEntityDrawer(false);
          onRefresh(selectedDomainNode?.key);
          qryEntitiesByObjId();
        }}
      />

      {/* 修改领域实体 */}
      <EditDomainEntityDrawer
        ignoreVersionSource={ignoreVersionSource}
        domainObjType={basicInfo?.domainObjType}
        open={openEditDomainEntityDrawer}
        domainObjId={selectedDomainNode?.domainObjId}
        initValue={selectedEntityObj}
        onCancel={() => setOpenEditDomainEntityDrawer(false)}
        isProduct={isProduct}
        onOk={() => {
          //修改实体成功后操作
          setOpenEditDomainEntityDrawer(false);
          onRefresh(selectedDomainNode?.key);
          qryEntitiesByObjId();
        }}
      />

      {/* 修改领域对象 */}
      <EditDomainObjectDrawer
        ignoreVersionSource={ignoreVersionSource}
        initValue={domainObjDetail}
        open={openEditDomainObjectDrawer}
        onCancel={() => setOpenEditDomainObjectDrawer(false)}
        domainId={domainObjDetail?.domainId}
        isProduct={isProduct}
        onOk={() => {
          //修改对象成功后操作
          setOpenEditDomainObjectDrawer(false);
          onRefresh(selectedDomainNode?.key);
          queryDomainObjDetail();
        }}
      />
    </div>
  );
};

export default BssDomainObjectDetail;
