import { Link, Outlet } from 'umi';
import styles from './index.less';
import { openDomainApiHotfixPath, openErrorCodeApiHotfixPath } from '@/global';

export default function Layout() {
  return (
    <div className={styles.navs}>
      <ul>
        <li>
          <Link to="/">Home</Link>
        </li>
        <li>
          <Link to="/openDataApi">bssODataApi</Link>
        </li>
        <li>
          <Link to="/openDomainApi">openDomainApi</Link>
        </li>
        <li>
          <Link to="/openErrorCodeApi">openErrorCodeApi</Link>
        </li>
        <li>
          <Link to={openDomainApiHotfixPath}>Open Data Domain Hotfix</Link>
        </li>
        <li>
          <Link to={openErrorCodeApiHotfixPath}>Open API Error Code Hotfix</Link>
        </li>
        <li>
          <a href="https://github.com/umijs/umi">Github</a>
        </li>
      </ul>
      <Outlet />
    </div>
  );
}
