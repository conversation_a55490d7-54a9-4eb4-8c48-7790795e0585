.useCaseListForm {
  height: 64px;
  :global {
    .ant-form-item {
      margin-bottom: 8px; /* 设置表单项之间的垂直间距 */
    }
  }
}

.searchUseCaseCatalog {
  height: 100%;
  padding-right: 16px;
  .searchUseCaseMenuList {
    height: calc(100% - 64px);
    overflow: auto;
  }
}

.menuItemLabel {
  width: 100%;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  .menuTitle {
    width: 90%;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
    .methodText {
      margin-right: 8px;
      &.postStyle {
        color: #00bb66;
      }
      &.getStyle {
        color: #4477ee;
      }
      &.patchStyle {
        color: #efe5ff;
      }
      &.deleteStyle {
        color: #ab1d1d;
      }
    }
  }
  .deleteBtn {
    width: 10%;
  }
}
