import React, { useEffect } from 'react';
import { Popover, Collapse } from 'antd';
import styles from './index.less';
import useI18n from '@/hooks/useI8n';
import ResizableTable from '../ResizableTable';

interface ICanFilterTableInPop {
  dataType: any;
  children: React.ReactNode;
}

const CanFilterTableInPop: React.FC<ICanFilterTableInPop> = (props) => {
  const { dataType, children } = props;

  const { Panel } = Collapse;

  const { formatMessage } = useI18n();

  /**
   * dataType 为Long/Integer/Datetime类型：展示Comparison Operators(comparisonData),Logical Operators(logicalData);
   * dataType 为Boolean类型：展示Comparison Operators(comparisonData1);
   * dataType 为String类型：展示Comparison Operators(comparisonData),Logical Operators(logicalData),String Functions(stringData);
   */

  const columns = [
    {
      dataIndex: 'operator',
      title: formatMessage('FILTER.COLUMN.OPERATOR'),
      ellipsis: true,
    },
    {
      dataIndex: 'desc',
      title: formatMessage('FILTER.COLUMN.DESCRIPTION'),
      width: 180,
      ellipsis: true,
    },
  ];

  const stringColumns = [
    {
      dataIndex: 'operator',
      title: formatMessage('FILTER.COLUMN.FUNCTION'),
      ellipsis: true,
    },
    {
      dataIndex: 'desc',
      title: formatMessage('FILTER.COLUMN.DESCRIPTION'),
      width: 180,
      ellipsis: true,
    },
  ];

  const comparisonData = [
    {
      operator: 'eq',
      desc: formatMessage('FILTER.COMPARISON.EQUAL'),
    },
    {
      operator: 'ne',
      desc: formatMessage('FILTER.COMPARISON.NOT_EQUAL'),
    },
    {
      operator: 'gt',
      desc: formatMessage('FILTER.COMPARISON.GREATER_THAN'),
    },
    {
      operator: 'ge',
      desc: formatMessage('FILTER.COMPARISON.GREATER_EQUAL'),
    },
    {
      operator: 'lt',
      desc: formatMessage('FILTER.COMPARISON.LESS_THAN'),
    },
    {
      operator: 'le',
      desc: formatMessage('FILTER.COMPARISON.LESS_EQUAL'),
    },
    {
      operator: 'in',
      desc: formatMessage('FILTER.COMPARISON.IS_MEMBER'),
    },
  ];

  const comparisonData1 = [
    {
      operator: 'eq',
      desc: formatMessage('FILTER.COMPARISON.EQUAL'),
    },
    {
      operator: 'ne',
      desc: formatMessage('FILTER.COMPARISON.NOT_EQUAL'),
    },
    {
      operator: 'in',
      desc: formatMessage('FILTER.COMPARISON.IS_MEMBER'),
    },
  ];

  const logicalData = [
    {
      operator: 'and',
      desc: formatMessage('FILTER.LOGICAL.AND'),
    },
    {
      operator: 'or',
      desc: formatMessage('FILTER.LOGICAL.OR'),
    },
    {
      operator: 'not',
      desc: formatMessage('FILTER.LOGICAL.NOT'),
    },
  ];

  const stringData = [
    {
      operator: 'concat',
      desc: formatMessage('FILTER.STRING.CONCAT'),
    },
    {
      operator: 'contains',
      desc: formatMessage('FILTER.STRING.CONTAINS'),
    },
    {
      operator: 'startswith',
      desc: formatMessage('FILTER.STRING.STARTSWITH'),
    },
    {
      operator: 'endswith',
      desc: formatMessage('FILTER.STRING.ENDSWITH'),
    },
    {
      operator: 'indexof',
      desc: formatMessage('FILTER.STRING.INDEXOF'),
    },
    {
      operator: 'length',
      desc: formatMessage('FILTER.STRING.LENGTH'),
    },
    {
      operator: 'substring',
      desc: formatMessage('FILTER.STRING.SUBSTRING'),
    },
    {
      operator: 'tolower',
      desc: formatMessage('FILTER.STRING.TOLOWER'),
    },
    {
      operator: 'toupper',
      desc: formatMessage('FILTER.STRING.TOUPPER'),
    },
    {
      operator: 'trim',
      desc: formatMessage('FILTER.STRING.TRIM'),
    },
  ];

  const title = (
    <span>
      {formatMessage('DOWNLOAD.TITLE.SUPPORTED')}{' '}
      <a
        href="https://docs.oasis-open.org/odata/odata/v4.01/odata-v4.01-part1-protocol.html#sec_SystemQueryOptionfilter"
        target="_blank"
      >
        [{formatMessage('DOWNLOAD.TITLE.SEEMORE')}]
      </a>
    </span>
  );

  useEffect(() => {
    // 具体解析规则:
  }, [dataType]);

  return (
    <Popover
      arrow={{
        pointAtCenter: true,
      }}
      placement="bottomLeft"
      overlayClassName={styles.popOverClass}
      getPopupContainer={() => document.getElementById('root') || document.body}
      content={
        <Collapse defaultActiveKey={['1']} ghost>
          <Panel header={formatMessage('DOWNLOAD.TITLE.COMPARISON')} key="1">
            <ResizableTable
              style={{ width: '300px' }}
              scroll={{ y: 200 }}
              pagination={false}
              size="small"
              columns={columns}
              dataSource={dataType === 'Long' || 'Integer' || 'Datetime' ? comparisonData : comparisonData1}
              rowKey={(record: any) => record.operator}
            />
          </Panel>
          {dataType !== 'Boolean' && (
            <Panel header={formatMessage('DOWNLOAD.TITLE.LOGICAL')} key={dataType === 'Boolean' ? '1' : '2'}>
              <ResizableTable
                style={{ width: '300px' }}
                scroll={{ y: 200 }}
                pagination={false}
                size="small"
                columns={columns}
                dataSource={logicalData}
                rowKey={(record: any) => record.operator}
              />
            </Panel>
          )}
          {dataType === 'String' && (
            <Panel header={formatMessage('DOWNLOAD.TITLE.STRINGFUNCTIONS')} key="3">
              <ResizableTable
                style={{ width: '300px' }}
                scroll={{ y: 200 }}
                pagination={false}
                size="small"
                columns={stringColumns}
                dataSource={stringData}
                rowKey={(record: any) => record.operator}
              />
            </Panel>
          )}
        </Collapse>
      }
      title={title}
      trigger="hover"
    >
      {children}
    </Popover>
  );
};

export default CanFilterTableInPop;
