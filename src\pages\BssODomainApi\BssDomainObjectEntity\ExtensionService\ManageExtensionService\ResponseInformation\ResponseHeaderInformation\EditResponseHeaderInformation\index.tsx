import React, { useEffect, useState } from 'react';
import { Button, Drawer, Form, Input, Select, Space, message, InputNumber, Spin } from 'antd';
import { operateResponseParam } from '@/services/entityService';
import { EnumSourceProps } from '../../../../../types';
import { useModel } from 'umi';
import { CommonYesOrNoSource, RespHeaderOptions } from '../../../const';
import { getFormatTypeSource } from '../../../utils';
import { autoFillDescByPropName } from '@/utils/common';
import useI18n from '@/hooks/useI8n';

interface IEditResponseHeaderInformation {
  selectedStatus: string;
  selectedService: any;
  enumSource: EnumSourceProps[];
  open: boolean;
  isProjectAndProductOwned: boolean;
  selectedParam?: any;
  onCancel: () => void;
  onOk: () => void;
}

interface optionProps {
  label: string;
  value: string;
}

const EditResponseHeaderInformation: React.FC<IEditResponseHeaderInformation> = (props) => {
  const { open, selectedService, isProjectAndProductOwned, enumSource, selectedStatus, selectedParam, onCancel, onOk } =
    props;
  const { formatMessage } = useI18n();
  const [form] = Form.useForm();
  const [submitSpinning, setSubmitSpinning] = useState<boolean>(false);
  const [formValues, setFormValues] = useState<any>({});
  const [avlValueType, setAvlValueType] = useState<string>('');
  const [avlValueTypeSource, setAvlValueTypeSource] = useState<optionProps[]>([]);
  const [avlValueTypeValue, setAvlValueTypeValue] = useState<string>('');
  const [disabled, setDisabled] = useState<boolean>(false);
  const [required, setRequired] = useState<boolean>(false);
  const [defaultValues, setDeFaultValues] = useState<any>({});
  const { currentDomainObjId } = useModel('manageExtensionServiceModel');

  const [FormatTypeSource, setFormatTypeSource] = useState<any>([]);

  useEffect(() => {
    setFormatTypeSource(getFormatTypeSource(avlValueType));
  }, [avlValueType]);

  // 弹框关闭
  const onClose = async () => {
    setAvlValueType('');
    setAvlValueTypeValue('');
    form.resetFields();
    onCancel?.();
  };

  // 调用修改响应参数接口
  const optResponseParam = async (param: any) => {
    setSubmitSpinning(true);
    try {
      const resultData = await operateResponseParam('update', param);

      if (resultData?.success) {
        message.success(formatMessage('MANAGEEXTENSIONSERVICE.RESPONSE.UPDATE.SUCCESS', RespHeaderOptions.option3));
        form.resetFields();
        onOk?.();
      }
      onCancel?.();
    } catch {
      message.error(formatMessage('MANAGEEXTENSIONSERVICE.RESPONSE.UPDATE.ERROR', RespHeaderOptions.option3));
    } finally {
      setSubmitSpinning(false);
    }
  };

  // 入参校验，构造入参
  const checkServiceReqParamData = () => {
    form.validateFields().then(() => {
      const formValue = form.getFieldsValue();
      // 构造入参
      let param = {
        ...formValue,
        serviceId: selectedService?.serviceId,
        domainObjId: currentDomainObjId,
        respIn: 'H',
        respContentType: '',
        respCode: selectedStatus,
        respParamId: selectedParam?.respParamId,
      };

      if (param.avlValueType) {
        switch (param.avlValueType) {
          case 'Length':
            param.avlValue = `Length[${param.min},${param.max}]`;
            break;
          case 'Enum':
            const eId = enumSource.find(
              (i: any) => `${i.value}` === `${param.enum}` || i.label === `${param.enum}`,
            )?.value;
            param.avlValue = eId;
            param.enumId = eId;
            break;
          case 'Empty':
            param.avlValue = '';
            break;
          case 'Range':
            param.avlValue = `Range[${param.min},${param.max}]`;
            break;
          case 'Format':
            param.avlValue = param.typeFormat;
            param = {
              ...param,
              typeFormat: param.typeFormat,
            };
            break;
          case 'Values':
            param.avlValue = param.values;
            break;
          default:
            param.avlValue = '';
            break;
        }
      }
      if (param?.avlValueType !== 'Enum') {
        delete param?.enumId;
      }
      if (param?.avlValueType !== 'Format') {
        delete param?.typeFormat;
      }

      optResponseParam(param);
    });
  };

  // 通用的重置表单字段函数
  const resetFormFields = (resetAvlValueType = true) => {
    const resetFields: any = {
      typeFormat: undefined,
      min: undefined,
      max: undefined,
      values: undefined,
      enum: undefined,
    };

    if (resetAvlValueType) {
      resetFields.avlValueType = '';
      setAvlValueTypeValue('');
    }

    form.setFieldsValue(resetFields);
  };

  // paramType改变
  const onPropTypeChange = (value: any) => {
    setAvlValueType(value);
    resetFormFields();
    setDisabled(false);
    setRequired(true);

    switch (value) {
      case 'String':
        setAvlValueTypeSource([
          { label: 'Empty', value: 'Empty' },
          { label: 'Length', value: 'Length' },
          { label: 'Format', value: 'Format' },
          { label: 'Enum', value: 'Enum' },
          { label: 'Values', value: 'Values' },
        ]);
        break;

      case 'Boolean':
        setAvlValueTypeSource([{ label: 'Empty', value: 'Empty' }]);
        break;

      case 'Long':
        setAvlValueTypeSource([
          { label: 'Empty', value: 'Empty' },
          { label: 'Length', value: 'Length' },
          { label: 'Range', value: 'Range' },
          { label: 'Enum', value: 'Enum' },
          { label: 'Values', value: 'Values' },
        ]);
        break;
      case 'Integer':
        setAvlValueTypeSource([
          { label: 'Empty', value: 'Empty' },
          { label: 'Length', value: 'Length' },
          { label: 'Range', value: 'Range' },
          { label: 'Enum', value: 'Enum' },
          { label: 'Values', value: 'Values' },
        ]);
        break;
      case 'Number':
        setAvlValueTypeSource([
          { label: 'Empty', value: 'Empty' },
          { label: 'Length', value: 'Length' },
          { label: 'Range', value: 'Range' },
          { label: 'Format', value: 'Format' },
          { label: 'Values', value: 'Values' },
        ]);
        break;
      case 'Datetime':
        setAvlValueTypeSource([
          { label: 'Empty', value: 'Empty' },
          { label: 'Format', value: 'Format' },
          { label: 'Values', value: 'Values' },
        ]);
        break;

      default:
        // TODO 可能不需要默认值
        setAvlValueTypeSource([]);
        setDisabled(true);
        setRequired(false);
        break;
    }
  };

  // avlValueType改变
  const onAvlValueTypeChange = (value: any) => {
    setAvlValueTypeValue(value);
    resetFormFields(false);
  };

  // 枚举列表搜索
  const handleEnumFilter = (input: any, option: any) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };

  const getStringBetween = (str: string, start: string, end: string) => {
    const reg = /\[(\S*)\]/;
    if ((str.match(reg) || []).length > 1) {
      return (str.match(reg) || [])[1];
    }
    return '';
  };

  useEffect(() => {
    if (open && selectedParam) {
      // 深拷贝行数据后初始化值
      let defaultValues: any = {};

      Object.assign(defaultValues, selectedParam);
      // 五种数据基本类型，处理avlValue字段的回显问题
      const { avlValue, typeFormat } = defaultValues;

      if ((avlValue === '' || avlValue === null) && !typeFormat) {
        defaultValues.avlValueType = 'Empty';
      } else if (avlValue === `${defaultValues.enumId}`) {
        defaultValues.avlValueType = 'Enum';
        defaultValues.enum = enumSource.find((i: any) => `${i.value}` === `${avlValue}`)?.label;
      } else if (avlValue?.toLowerCase().startsWith('length')) {
        defaultValues.avlValueType = 'Length';
        const betweenVal = getStringBetween(avlValue, '[', ']')?.split(',');
        defaultValues.min = betweenVal[0];
        defaultValues.max = betweenVal[1];
      } else if (avlValue?.toLowerCase().startsWith('range')) {
        defaultValues.avlValueType = 'Range';
        const betweenVal = getStringBetween(avlValue, '[', ']')?.split(',');
        defaultValues.min = betweenVal[0];
        defaultValues.max = betweenVal[1];
      } else if (typeFormat) {
        defaultValues.avlValueType = 'Format';
        defaultValues.typeFormat = typeFormat;
      } else {
        defaultValues.avlValueType = 'Values';
        defaultValues.values = avlValue;
      }
      onPropTypeChange(defaultValues.paramType);
      onAvlValueTypeChange(defaultValues.avlValueType);
      setDeFaultValues(defaultValues);
      setFormValues(defaultValues);
    }
  }, [selectedParam, open]);

  return (
    <>
      <Drawer
        title={formatMessage('MANAGEEXTENSIONSERVICE.RESPONSE.EDIT.TITLE', RespHeaderOptions.option2)}
        onClose={onClose}
        maskClosable={false}
        open={open}
        width={720}
        afterOpenChange={() => {
          form.resetFields();
        }}
        footer={
          <Space style={{ float: 'right' }}>
            <Spin spinning={submitSpinning}>
              <Button type="primary" onClick={checkServiceReqParamData}>
                {formatMessage('PROJECT.COMMON.SUBMIT')}
              </Button>
            </Spin>
            <Button onClick={onClose}>{formatMessage('PROJECT.COMMON.CANCEL')}</Button>
          </Space>
        }
      >
        <Form
          form={form}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
          initialValues={defaultValues}
          onValuesChange={(cValues, alValues) => {
            setFormValues(alValues);
          }}
        >
          <Form.Item
            label={formatMessage('PROJECT.COMMON.NAME')}
            name="respParamName"
            rules={[
              { required: true, message: '' },
              {
                validator: (_, value) => {
                  if (value?.includes(' ')) {
                    return Promise.reject(
                      new Error(formatMessage('MANAGEEXTENSIONSERVICE.REQUEST.PARAM.NAME.SPACE.ERROR')),
                    );
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <Input
              allowClear
              disabled={isProjectAndProductOwned}
              onBlur={() => autoFillDescByPropName(form, 'respParamName', 'comments')}
            />
          </Form.Item>
          <Form.Item
            label={formatMessage('MANAGEEXTENSIONSERVICE.REQUEST.PARAM.IS_ARRAY')}
            name="isArray"
            rules={[{ required: true, message: '' }]}
          >
            <Select showSearch options={CommonYesOrNoSource} disabled={isProjectAndProductOwned} />
          </Form.Item>
          <Form.Item
            label={formatMessage('PROJECT.COMMON.HIDDEN')}
            name="hidden"
            rules={[{ required: true, message: '' }]}
          >
            <Select showSearch options={CommonYesOrNoSource} />
          </Form.Item>
          <Form.Item
            label={formatMessage('PROJECT.COMMON.TYPE')}
            name="paramType"
            rules={[{ required: true, message: '' }]}
          >
            <Select
              disabled={isProjectAndProductOwned}
              options={[
                { label: 'String', value: 'String' },
                { label: 'Boolean', value: 'Boolean' },
                { label: 'Long', value: 'Long' },
                { label: 'Number', value: 'Number' },
                { label: 'Datetime', value: 'Datetime' },
                { label: 'Integer', value: 'Integer' },
              ]}
              onChange={onPropTypeChange}
              showSearch
              filterOption={(input, option) => (option?.label as string)?.toLowerCase().includes(input.toLowerCase())}
            />
          </Form.Item>
          <Form.Item
            label={formatMessage('MANAGEEXTENSIONSERVICE.REQUEST.PARAM.ALLOWABLE_VALUES')}
            style={{ marginBottom: 0 }}
          >
            <Form.Item
              name="avlValueType"
              style={{ display: 'inline-block', width: 'calc(33% - 5px)' }}
              rules={[{ required, message: '' }]}
            >
              <Select options={avlValueTypeSource} onChange={onAvlValueTypeChange} disabled={disabled} />
            </Form.Item>
            {avlValueTypeValue === 'Length' ? (
              <Form.Item
                name="min"
                style={{ display: 'inline-block', width: 'calc(33%)', marginLeft: '5px' }}
                rules={[
                  { required: true, message: '' },
                  {
                    validator: (_, value) => {
                      if (value !== undefined && value < 0) {
                        return Promise.reject(new Error(''));
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <InputNumber
                  addonBefore={formatMessage('MANAGEEXTENSIONSERVICE.MIN')}
                  precision={0}
                  min={formValues?.required !== 'Y' ? 0 : 1}
                  max={formValues.max}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            ) : null}
            {avlValueTypeValue === 'Length' ? (
              <Form.Item
                name="max"
                style={{ display: 'inline-block', width: 'calc(33%)', marginLeft: '5px' }}
                rules={[{ required: true, message: '' }]}
              >
                <InputNumber
                  addonBefore={formatMessage('MANAGEEXTENSIONSERVICE.MAX')}
                  precision={0}
                  min={formValues.min}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            ) : null}
            {avlValueTypeValue === 'Enum' ? (
              <Form.Item name="enum" style={{ display: 'inline-block', width: 'calc(66% + 5px)', marginLeft: '5px' }}>
                <Select showSearch options={enumSource} filterOption={handleEnumFilter} />
              </Form.Item>
            ) : null}
            {avlValueTypeValue === 'Format' ? (
              <Form.Item
                name="typeFormat"
                style={{ display: 'inline-block', width: 'calc(66% + 5px)', marginLeft: '5px' }}
              >
                <Select options={FormatTypeSource} />
              </Form.Item>
            ) : null}
            {avlValueTypeValue === 'Values' ? (
              <Form.Item
                name="values"
                style={{ display: 'inline-block', width: 'calc(66% + 5px)', marginLeft: '5px' }}
                rules={[{ required: true, message: '' }]}
              >
                <Input placeholder={formatMessage('MANAGEEXTENSIONSERVICE.VALUES.PLACEHOLDER')} />
              </Form.Item>
            ) : null}
            {avlValueTypeValue === 'Range' ? (
              <Form.Item
                name="min"
                style={{ display: 'inline-block', width: 'calc(33%)', marginLeft: '5px' }}
                rules={[{ required: true, message: '' }]}
              >
                <InputNumber
                  addonBefore={formatMessage('MANAGEEXTENSIONSERVICE.MIN')}
                  precision={0}
                  max={formValues.max}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            ) : null}
            {avlValueTypeValue === 'Range' ? (
              <Form.Item
                name="max"
                style={{ display: 'inline-block', width: 'calc(33%)', marginLeft: '5px' }}
                rules={[{ required: true, message: '' }]}
              >
                <InputNumber
                  addonBefore={formatMessage('MANAGEEXTENSIONSERVICE.MAX')}
                  precision={0}
                  min={formValues.min}
                  style={{ width: '100%' }}
                />
              </Form.Item>
            ) : null}
          </Form.Item>
          <Form.Item label={formatMessage('MANAGEEXTENSIONSERVICE.REQUEST.PARAM.DEFAULT_VALUE')} name="defValue">
            <Input />
          </Form.Item>
          <Form.Item label={formatMessage('PROJECT.COMMON.EXAMPLE')} name="example">
            <Input allowClear />
          </Form.Item>
          <Form.Item
            label={formatMessage('PROJECT.COMMON.DESCRIPTION')}
            name="comments"
            extra={formatMessage('PROJECT.COMMON.GETDESCBYPROPNAME')}
          >
            <Input.TextArea allowClear onBlur={() => autoFillDescByPropName(form, 'respParamName', 'comments')} />
          </Form.Item>
        </Form>
      </Drawer>
    </>
  );
};

export default EditResponseHeaderInformation;
