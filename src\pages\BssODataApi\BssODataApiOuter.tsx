import React, { useState, useRef } from 'react';
import BssCatalogLeft from './BssCatalog';
import BssEntityDetail from './BssEntityDetail';
import { Row, Col } from 'antd';
import { IEntityList } from '@/services/typing.d';
import { ICurrentActiveKeyCatalog } from '@/typing.d';
import BssCatalogDetail from './BssCatalogDetail';
import ServiceDetail from '../BssServiceDetail';
import { BYCATALOG, BYDOMAIN } from '@/constants';
import styles from './index.less';

const BssODataApiOuter = ({
  hidden = false,
  onOpenSelectUseCase = () => {},
  onOpenSelectAPIGService = () => {},
  onOpenSelectErrorCode = () => {},
}: {
  hidden: boolean;
  onOpenSelectUseCase: () => void;
  onOpenSelectAPIGService: () => void;
  onOpenSelectErrorCode: () => void;
}) => {
  const [currentEntity, setCurrentEntity] = useState<Partial<IEntityList>>(); // domain当前选中项
  const [currentCatalogMenu, setCurrentCatalogMenu] = useState<ICurrentActiveKeyCatalog>(); // catalog 当前选中项
  const [selectType, setSelectType] = useState<string>(BYDOMAIN); // domain catalog类型选择
  const [serviceDetailData, setServiceDetailData] = useState<{
    show: boolean;
    serviceCode: string;
    fromType: string; // BYCATALOG | BYDOMAIN
  }>({
    show: false,
    serviceCode: '',
    fromType: '',
  }); // catalog 服务详情
  const [otherEntitykey, setOtherEntitykey] = useState(''); // domain entity详情中点击了实体  记录

  const catalogDetailRef = useRef<any>(); // catalog detail ref
  const domainDetailRef = useRef<any>(); // domain detail ref

  return (
    <div className={styles.bssApiLeftContainer} id="bssApiLeftContainer" hidden={hidden}>
      {/* service detail 详情 */}
      {serviceDetailData?.show && serviceDetailData?.serviceCode ? (
        <ServiceDetail
          serviceCode={serviceDetailData?.serviceCode}
          fromType={serviceDetailData?.fromType}
          onReturn={() => {
            setServiceDetailData({
              show: false,
              serviceCode: '',
              fromType: '',
            });
          }}
        />
      ) : null}
      {/* 用隐藏保持原状态 */}
      <Row gutter={8} style={serviceDetailData?.show ? { display: 'none' } : {}}>
        <Col span={6}>
          {/* 目录 */}
          <BssCatalogLeft
            otherEntitykey={otherEntitykey}
            onSelectMenu={(entityData) => {
              setOtherEntitykey('');
              setCurrentEntity(entityData);
            }}
            onSelectCatalogMenu={(catalogMenu) => setCurrentCatalogMenu(catalogMenu)}
            onSelectType={(type) => {
              setSelectType(type);
              setOtherEntitykey('');
              setCurrentEntity({});
              setCurrentCatalogMenu({});
            }}
            onRefresh={() => {
              // 目录刷新 刷新详情
              if (selectType === BYCATALOG) {
                catalogDetailRef?.current?.reload();
              } else if (selectType === BYDOMAIN) {
                domainDetailRef?.current?.reload();
              }
            }}
            onOpenSelectUseCase={onOpenSelectUseCase}
            onOpenSelectAPIGService={onOpenSelectAPIGService}
            onOpenSelectErrorCode={onOpenSelectErrorCode}
          />
        </Col>
        <Col span={18}>
          {
            // domain 右侧查询详情
            selectType === BYDOMAIN ? (
              <BssEntityDetail
                ref={domainDetailRef}
                entityKey={currentEntity?.entityKey}
                // key={`bssEntityDetail-${currentEntity?.entityKey}`}
                onSelectService={(serviceCode) =>
                  setServiceDetailData({
                    show: true,
                    serviceCode,
                    fromType: BYDOMAIN,
                  })
                }
                onOtherEntityClick={(entityKey) => {
                  setOtherEntitykey(entityKey);
                }}
              />
            ) : null
          }
          {
            // catalog右侧详情
            selectType === BYCATALOG ? (
              <BssCatalogDetail
                ref={catalogDetailRef}
                currentCatalog={currentCatalogMenu}
                key={`bssCatalogDetail`}
                onSelectService={(serviceCode) =>
                  setServiceDetailData({
                    show: true,
                    serviceCode,
                    fromType: BYCATALOG,
                  })
                }
              />
            ) : null
          }
        </Col>
      </Row>
    </div>
  );
};

export default BssODataApiOuter;
