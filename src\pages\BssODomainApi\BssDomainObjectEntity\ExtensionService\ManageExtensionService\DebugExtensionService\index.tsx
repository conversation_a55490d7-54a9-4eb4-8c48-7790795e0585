import styles from './index.less';
import useI18n from '@/hooks/useI8n';
import { LeftOutlined } from '@ant-design/icons';
import DebugDetail from './DebugDetail';
import { useEffect, useState } from 'react';
import { EntityServiceDetail, getEntityServiceDetail } from '@/services/errorCodeService';
import { breadCrumbsType } from '@/typing';
import BreadcrumbPublic from '@/components/BreadcrumbPublic';
import { Button, message, Spin } from 'antd';
import classNames from 'classnames';
import { changeDataToBeSentToUsedFn, getRequestFormattingData, processParams } from '@/utils/modifyTableValue';
import { debugExtensionService, debugService, saveAsUseCaseServicePropsType } from '@/services';
import { filteredZeroWidthSpace } from '@/utils/common';
import { qryBodyExample } from '@/services/entityService';
import UpdataRequestParamsContext from '@/context/UpdataRequestParamsContext';

interface IDebugExtensionService {
  onClose: () => void;
  selectedService: any;
  requestInformation: any;
  selectedRootNode: any;
}

const DebugExtensionService: React.FC<IDebugExtensionService> = (props) => {
  const { onClose, selectedService, requestInformation, selectedRootNode } = props;
  const { formatMessage } = useI18n();
  const [entityServiceDetail, setEntityServiceDetail] = useState<EntityServiceDetail>(); // 接口查询的不完整的服务数据
  const [serviceDetail, setServiceDetail] = useState<EntityServiceDetail>(); // 拼接后的完整的服务数据
  const [spining, setSpining] = useState<boolean>(false);
  const [requestDataSource, setRequestDataSource] = useState({});

  // 记录当前接口的 path 和 method
  const [servPath, setServPath] = useState('');
  const [servMethod, setServMethod] = useState('');
  const [isHtmlReject, setIsHtmlReject] = useState<boolean>(false); // 接口的响应是否是HTML格式
  const [wholeURL, setWholeURL] = useState<string>('');

  // 以其他格式接收 paramValueList
  const [paramValueList, setParamValueList] = useState<
    Pick<saveAsUseCaseServicePropsType, 'serviceReqParamList'> | any
  >([]);

  // 获取接口的响应时间等信息
  const [responseInfo, setResponseInfo] = useState<Pick<saveAsUseCaseServicePropsType, 'serviceRespParamList'> | any>(
    [],
  );

  const [dataToBeSent, setDataToBeSent] = useState<{ [k: string]: any }>({});

  // 用于格式化校验
  const [arrUsedforVerification, setArrUsedforVerification] = useState<string[]>([]);

  const breadCrumbsItem: breadCrumbsType[] = [
    {
      level: 1,
      title: selectedService?.serviceName,
      onClick: () => {
        onClose?.();
      },
    },
    {
      level: 2,
      title: 'Debug',
      onClick: () => {},
    },
  ];

  // 查询服务全部内容
  const queryEntityServiceDetail = async () => {
    try {
      setSpining(true);
      const params = {
        serviceId: selectedService?.serviceId,
      };
      const { success, data } = await getEntityServiceDetail(params);
      if (success) {
        setEntityServiceDetail(data);
      }
    } finally {
      setSpining(false);
    }
  };

  // 查询入参/出参样例
  const fillServiceDetail = async (requestInformation: any, entityServiceDetail: any) => {
    try {
      setSpining(true);

      let filledData = {
        ...entityServiceDetail,
        method: selectedService?.httpMethod,
        serviceId: selectedService?.serviceId,
      };
      const reqBodyParam = requestInformation?.bodyList?.find((item: any) => item?.refSchemaId);
      if (reqBodyParam) {
        const response = await qryBodyExample({ schemaId: reqBodyParam?.refSchemaId });

        if (response.success) {
          filledData = {
            ...filledData,
            parameters: entityServiceDetail?.parameters?.map((item: any) => {
              if (item?.name === reqBodyParam?.reqParamName && item?.dataModel) {
                return {
                  ...item,
                  example: JSON.parse(response.data),
                };
              }
              return item;
            }),
          };
        } else {
          console.error('Failed to fetch request body example');
        }
      }
      setServiceDetail(filledData);
    } catch (error) {
      console.error('An error occurred while fetching request body example:', error);
    } finally {
      setSpining(false);
    }
  };

  // debug 接口调用，格式化 value
  const formatValuefn = (el: any) => {
    const { value, isArray } = el;
    let curValue = value;
    if (Array.isArray(value)) {
      if (isArray === 'N') {
        curValue = value?.[0];
      }
    }
    return curValue;
  };

  // debug 接口调用
  const debugServiceFn = async () => {
    const startTime = new Date().getTime();
    let recordRequiedParams: string[] = [];

    Object.values(dataToBeSent).forEach((item: any) => {
      if (Array.isArray(item)) {
        item.forEach((el: any) => {
          if (el?.required === 'Y' && (!el?.value || el?.value?.length === 0)) {
            recordRequiedParams.push(el?.name);
          }
        });
      }
    });
    // setArrUsedforVerification(recordRequiedParams);
    if (Array.isArray(recordRequiedParams) && recordRequiedParams.length > 0) {
      const msgInfo = recordRequiedParams.join(',');
      message.error(`${msgInfo} ${formatMessage('DEBUGSERVICE.ERROR.CANNOT_BE_NULL')}`);
    } else {
      setSpining(true);

      // 获取body数据，直接使用B数组中第一条数据的value
      const bodyData = dataToBeSent.B && dataToBeSent.B.length > 0 ? dataToBeSent.B[0].value : {};

      await debugExtensionService(serviceDetail?.serviceId, {
        appCode: selectedRootNode?.appCode,
        queryParams: filteredZeroWidthSpace(processParams(dataToBeSent['Q'])),
        pathParams: filteredZeroWidthSpace(processParams(dataToBeSent['P'])),
        headerParams: filteredZeroWidthSpace(processParams(dataToBeSent['H'])),
        cookieParams: filteredZeroWidthSpace(processParams(dataToBeSent['C'])),
        body: bodyData,
      })
        .then(
          (res: any) => {
            const { data = {}, status, headers = {} } = res;
            const endTime = new Date().getTime();
            const responseTime = endTime - startTime;
            setIsHtmlReject(false);
            setResponseInfo([
              {
                respTime: responseTime,
                succFlag: status == 200 ? 'Y' : 'N',
                respCode: String(status),
                respMsg: typeof data === 'object' ? JSON.stringify(data) : data,
                respHeaders: headers, // 添加响应头信息
              },
            ]);
          },
          (rej: any) => {
            const { response = {} } = rej;
            const { status = '', data = {}, headers = {} } = response;
            const { errorMessage = '' } = data;
            if (typeof data === 'string') {
              // 如果是string类型，即是HTML格式
              setIsHtmlReject(true);
            } else {
              setIsHtmlReject(false);
            }
            const endTime = new Date().getTime();
            const responseTime = endTime - startTime;
            setResponseInfo([
              {
                respTime: responseTime,
                succFlag: status == 200 ? 'Y' : 'N',
                respCode: String(status),
                respMsg: data,
                respHeaders: headers, // 添加响应头信息
              },
            ]);
          },
        )
        .catch((error) => {
          console.log(error);
        })
        .finally(() => {
          setSpining(false);
        });
    }
  };

  // debug 模式更新查询参数
  const updataRequestParamsFn = (value: any) => {
    // 特殊处理BM（CodeMirror中的body内容）
    if (value.BM !== undefined) {
      try {
        // 尝试解析JSON内容
        const parsedBody = JSON.parse(value.BM);

        // 更新B数组中的第一个元素的value属性
        if (dataToBeSent.B && dataToBeSent.B.length > 0) {
          const updatedB = [...dataToBeSent.B];
          updatedB[0] = {
            ...updatedB[0],
            value: parsedBody,
          };

          // 更新状态，同时保存BM和更新后的B
          setDataToBeSent({
            ...dataToBeSent,
            BM: value.BM,
            B: updatedB,
          });
          return; // 已经处理完毕，不需要继续执行
        }
      } catch (error) {
        console.error('Failed to parse body JSON:', error);
        // 即使解析失败，也保存原始字符串
        setDataToBeSent({ ...dataToBeSent, BM: value.BM });
        return;
      }
    }

    // 处理其他类型的参数更新
    setDataToBeSent({ ...dataToBeSent, ...value });
  };

  useEffect(() => {
    if (entityServiceDetail) {
      // 过滤掉hidden==='Y'的数据
      const filteredRequestInformation = {
        ...requestInformation,
        headerList: requestInformation?.headerList?.filter((item: any) => item?.hidden !== 'Y') || [],
        pathList: requestInformation?.pathList?.filter((item: any) => item?.hidden !== 'Y') || [],
        queryList: requestInformation?.queryList?.filter((item: any) => item?.hidden !== 'Y') || [],
        cookieList: requestInformation?.cookieList?.filter((item: any) => item?.hidden !== 'Y') || [],
        bodyList: requestInformation?.bodyList?.filter((item: any) => item?.hidden !== 'Y') || [],
      };
      // 过滤掉hidden==='Y'的数据
      const filteredEntityServiceDetail = {
        ...entityServiceDetail,
        parameters: entityServiceDetail?.parameters?.filter((item: any) => item?.hidden !== 'Y') || [],
      };
      fillServiceDetail(filteredRequestInformation, filteredEntityServiceDetail);
    }
  }, [entityServiceDetail, requestInformation, selectedService]);

  useEffect(() => {
    queryEntityServiceDetail();
  }, [selectedService]);

  useEffect(() => {
    if (serviceDetail) {
      setServMethod(serviceDetail?.serviceMethod);
      setServPath(`odh-web/odh/web/serviceTest/${serviceDetail?.serviceId}`);
      const { newArrObj, newObj } = getRequestFormattingData(serviceDetail);
      setRequestDataSource(newArrObj);
      setDataToBeSent(newObj);
    }
  }, [serviceDetail]);

  // Update paramValueList when dataToBeSent changes
  useEffect(() => {
    const { serviceReqParamList = [] } = changeDataToBeSentToUsedFn({
      dataToBeSent,
      formatValuefn,
    });
    setParamValueList(serviceReqParamList);
    return () => {};
  }, [dataToBeSent]);

  return (
    <div className={styles.bssSearchUseCase}>
      <div className={styles.header}>
        <LeftOutlined className={styles.returnIcon} onClick={onClose} />
        <BreadcrumbPublic breadCrumbs={breadCrumbsItem} className={styles.returnText} />
      </div>
      <div className={styles.bottom}>
        <Spin spinning={spining}>
          <UpdataRequestParamsContext.Provider value={{ updataRequestParamsFn, arrUsedforVerification }}>
            <DebugDetail
              serviceDetail={serviceDetail}
              wholeURL={wholeURL}
              isHtmlReject={isHtmlReject}
              requestDataSource={requestDataSource}
              debugServiceFn={debugServiceFn}
              responseInfo={responseInfo}
              servPath={servPath}
            />
          </UpdataRequestParamsContext.Provider>
        </Spin>
      </div>
    </div>
  );
};

export default DebugExtensionService;
