import React, { useEffect, useState } from 'react';
import { Popover } from 'antd';
import { parseLinkFromString } from '@/utils/common';
import styles from './index.less';
import ResizableTable from '../ResizableTable';

interface IAvlValueTableInPop {
  title: string;
  avlValue?: string;
  children: React.ReactNode;
  popContentType: 'array' | 'string';
}

const AvlValueTableInPop: React.FC<IAvlValueTableInPop> = (props) => {
  const { avlValue, children, title, popContentType } = props;

  const [tableData, setTableData] = useState<
    Array<{
      name: string;
      desc: string;
    }>
  >([]);
  const [stringModeContent, setStringModeContent] = useState('');

  const columns = [
    {
      dataIndex: 'name',
      title: 'Value',
      ellipsis: true,
      width: '45%',
    },
    {
      dataIndex: 'desc',
      title: 'Description',
      width: '50%',
      ellipsis: true,
    },
  ];

  useEffect(() => {
    // 具体解析规则:
    // array 规则:  解析规则A1: "A: A1, B: B1" => [{name: "A", desc: 'A1'},{name: 'B', desc: "B1"}]
    // array 规则:  解析规则A2: "A, B" => [{name: "A", desc: ''},{name: 'B', desc: ""}]
    // string 规则: 解析规则B1: 以这四种字符串开头的"length[ 、length( 、range[ 、range(" 直接展示内容
    // string 规则: 解析规则B2: 转换规则如下
    // 转还前 "Show only the first n items, see [Paging - Top](http://docs.oasis-open.org/odata/odata/v4.01/odata-v4.01-part1-protocol.html#sec_SystemQueryOptiontop)"
    // 转换后 "Show only the first n items, see <a target="http://docs.oasis-open.org/odata/odata/v4.01/odata-v4.01-part1-protocol.html#sec_SystemQueryOptiontop" target="_blank">Paging - Top</a>"
    if (popContentType === 'array') {
      // array 模式
      const firstSplit = (avlValue || '').split(/,\u200B/); // 正则匹配英文逗号+零宽度空格：后端在英文逗号后传了一个零宽度空格，前端不可见，但是可以用来过滤，以解决描述中存在英文逗号的情况
      const tableTempData = (firstSplit || [])
        .filter((i) => !!i)
        .map((i) => {
          const secondSplit = i.split(':');
          return {
            name: secondSplit?.[0]?.trim(),
            desc: secondSplit?.[1]?.trim() || '',
          };
        });
      setTableData(tableTempData);
    } else if (popContentType === 'string') {
      // string 模式
      const pattern = /^(range|length)[\(\[]/;
      if (avlValue && !pattern.test(avlValue)) {
        // range 、length字符
        setStringModeContent(avlValue);
      } else {
        // link字符
        setStringModeContent(parseLinkFromString(avlValue || ''));
      }
    }
  }, [avlValue]);

  return (
    <Popover
      arrow={{
        pointAtCenter: true,
      }}
      placement="bottomLeft"
      overlayClassName={styles.popOverClass}
      getPopupContainer={() => document.getElementById('root') || document.body}
      content={
        popContentType === 'array' ? (
          <ResizableTable
            style={{ width: '300px' }}
            scroll={{ y: 200 }}
            pagination={false}
            size="small"
            columns={columns}
            dataSource={tableData}
            rowKey={(record: any) => record.name}
          />
        ) : (
          <div
            style={{ maxWidth: '180px', wordWrap: 'break-word' }}
            dangerouslySetInnerHTML={{
              __html: `${avlValue}`,
            }}
          ></div>
        )
      }
      title={title}
      trigger="hover"
    >
      {children}
    </Popover>
  );
};

export default AvlValueTableInPop;
