import { createBrowserRouter, Navigate } from 'react-router-dom'
import NotFound404 from '../views/Error404'
import NotFound403 from '../views/Error403'
import Login from '../views/login'
import Welcome from '../views/welcome'
// import DashBoard from '../views/dashboard'
import Layout from '../layout/index'
import UserList from '../views/system/user'
import DeptList from '../views/system/dept'
import MenuList from '../views/system/menu'
import AuthLoader from './AuthLoader'
import RoleList from '../views/system/role'
import OrderList from '../views/order/orderList'
import React from 'react'
import LazyLoad from './LazyLoad'

const DashBoard = React.lazy(() => import('../views/dashboard'))

const routes = [
    {
        path: '/',
        element: <Navigate to='/welcome' />
    },
    {
        path: '/login',
        element: <Login />
    },
    {
        id: 'layout',
        element: <Layout />,
        loader: <PERSON><PERSON><PERSON><PERSON><PERSON>,
        children: [
            {
                path: '/welcome',
                element: <Welcome />
            },
            {
                path: '/dashBoard',
                element: LazyLoad(DashBoard)
            },
            {
                path: '/userList',
                element: <UserList />
            },
            {
                path: '/deptList',
                element: <DeptList />
            },
            {
                path: '/menuList',
                element: <MenuList />
            },
            {
                path: '/roleList',
                element: <RoleList />
            },
            {
                path: '/orderList',
                element: <OrderList />
            },
        ]
    },
    {
        path: '/404',
        element: <NotFound404 />
    },
    {
        path: '/403',
        element: <NotFound403 />
    },
    {
        path: '*',
        element: <Navigate to='/404' />
    }
]

export default createBrowserRouter(routes)