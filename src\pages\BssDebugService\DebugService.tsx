import { useState, useEffect, useMemo, useRef } from 'react';
import { Spin, message } from 'antd';
import styles from './index.less';
import useI18n from '@/hooks/useI8n';
import {
  debugService,
  saveAsUseCaseService,
  saveAsUseCasePropsType,
  saveAsUseCaseServicePropsType,
  saveAsAPIGPropsType,
  saveAsAPIGService,
} from '@/services';
import { APIGServiceCatalogItemType, IServiceDetail } from '@/services/typing';
import { AddUseCaseDrawer, EditUseCaseDrawer, UseCaseBeSelectedModal } from '@/components';
import { getRequestFormattingData } from '@/utils/modifyTableValue';
import UpdataRequestParamsContext from '@/context/UpdataRequestParamsContext';
import { changeDataToBeSentToUsedFn, changeParamsListToDataToBeSent } from '@/utils/modifyTableValue';
import DetailBase from '../BssDetailBase/DetailBase';
import AddAPIGServiceDrawer from '@/components/APIGServiceDrawer/AddAPIGServiceDrawer';
import EditAPIGServiceDrawer from '@/components/APIGServiceDrawer/EditAPIGServiceDrawer';
import { enCodeParamsFn } from '@/utils/common';

interface IServiceDetailCom {
  serviceCode: string;
  debugMode?: boolean;
  serviceDetail?: IServiceDetail;
  hiddenSelectBtn?: boolean;
  initSeletedValue?: any;
  isEditAPIGService?: boolean;
  isEditUseCase?: boolean;
  saveUsType?: string;
  APIGServiceDetail?: any;
  APIGServiceCatalogList?: APIGServiceCatalogItemType[];
  otherFnInUseCaseModal?: (extensionData?: { isEditUseCase: boolean; currentUseCaseId: number }) => void;
  onEditAPIGOk?: () => void;
  refreshAPIGService?: () => void;
}

const DebugService = ({
  serviceCode,
  debugMode,
  serviceDetail,
  hiddenSelectBtn = false,
  initSeletedValue,
  isEditAPIGService = false,
  isEditUseCase = false,
  saveUsType = '',
  APIGServiceDetail = {},
  APIGServiceCatalogList = [],
  otherFnInUseCaseModal = () => {},
  onEditAPIGOk = () => {},
  refreshAPIGService = () => {},
}: IServiceDetailCom) => {
  const { formatMessage } = useI18n();
  const [spinning, setSpinning] = useState(false);
  const [requestDataSource, setRequestDataSource] = useState({});

  // 记录当前接口的 path 和 method
  const [servPath, setServPath] = useState('');
  const [servMethod, setServMethod] = useState('');
  const [isHtmlReject, setIsHtmlReject] = useState<boolean>(false); // 接口的响应是否是HTML格式
  const [wholeURL, setWholeURL] = useState<string>('');

  // 以其他格式接收 paramValueList
  const [paramValueList, setParamValueList] = useState<
    Pick<saveAsUseCaseServicePropsType, 'serviceReqParamList'> | any
  >([]);

  // 获取接口的响应时间等信息
  const [responseInfo, setResponseInfo] = useState<Pick<saveAsUseCaseServicePropsType, 'serviceRespParamList'> | any>(
    [],
  );

  const [dataToBeSent, setDataToBeSent] = useState<{ [k: string]: any }>({});

  // 用于格式化校验
  const [arrUsedforVerification, setArrUsedforVerification] = useState<string[]>([]);

  // debug 模式更新查询参数
  const updataRequestParamsFn = (value: any) => {
    // setResponseInfo([]); // 注释这一行，解决Service Use Case List中切换两个不同的服务时，Response Information数据不更新的问题
    setDataToBeSent({ ...dataToBeSent, ...value });
  };

  // debug 接口调用，格式化 value
  const formatValuefn = (el: any) => {
    const { value, isArray } = el;
    let curValue = value;
    if (Array.isArray(value)) {
      if (isArray === 'N') {
        curValue = value?.[0];
      }
    }
    return curValue;
  };

  const replaceUrlPrefix = (url: string) => {
    // 定义正则表达式，用于匹配第一个 /odh/
    const regex = /^(.*?)(\/odh\/)/;

    // 使用正则表达式进行替换
    const newUrl = url.replace(regex, '/odh-service$2');

    return newUrl;
  };

  // debug 接口调用
  const debugServiceFn = async (selectedAddressType?: string) => {
    const startTime = new Date().getTime();
    const { debugparamsData, serviceReqParamList, recordRequiedParams } = changeDataToBeSentToUsedFn({
      dataToBeSent,
      servMethod,
      servPath,
      formatValuefn,
    });
    // setArrUsedforVerification(recordRequiedParams);
    if (Array.isArray(recordRequiedParams) && recordRequiedParams.length > 0) {
      const msgInfo = recordRequiedParams.join(',');
      message.error(`${msgInfo} ${formatMessage('DEBUGSERVICE.ERROR.CANNOT_BE_NULL')}`);
    } else {
      setSpinning(true);
      setParamValueList(serviceReqParamList);
      if (selectedAddressType === 'testing') {
        debugparamsData.url = replaceUrlPrefix(debugparamsData.url);
      }
      setWholeURL(
        `${window?.location?.origin}${debugparamsData?.url}${
          typeof debugparamsData?.query === 'object' && Object.keys(debugparamsData?.query).length
            ? `?${enCodeParamsFn(debugparamsData?.query)}`
            : ''
        }`,
      );
      await debugService(debugparamsData)
        .then(
          (res: any) => {
            const { data = {}, status, headers = {} } = res;
            const endTime = new Date().getTime();
            const responseTime = endTime - startTime;
            setIsHtmlReject(false);
            setResponseInfo([
              {
                respTime: responseTime,
                succFlag: status == 200 ? 'Y' : 'N',
                respCode: String(status),
                respMsg: typeof data === 'object' ? JSON.stringify(data) : data,
                respHeaders: headers, // 添加响应头信息
              },
            ]);
          },
          (rej: any) => {
            const { response = {} } = rej;
            const { status = '', data = {}, headers = {} } = response;
            const { errorMessage = '' } = data;
            if (typeof data === 'string') {
              // 如果是string类型，即是HTML格式
              setIsHtmlReject(true);
            } else {
              setIsHtmlReject(false);
            }
            const endTime = new Date().getTime();
            const responseTime = endTime - startTime;
            setResponseInfo([
              {
                respTime: responseTime,
                succFlag: status == 200 ? 'Y' : 'N',
                respCode: String(status),
                respMsg: data,
                respHeaders: headers, // 添加错误响应的头信息
              },
            ]);
          },
        )
        .catch((error) => {
          console.log(error);
        })
        .finally(() => {
          setSpinning(false);
        });
    }
  };

  // Use Case, 控制 Drawer
  const [open, setOpen] = useState<boolean>(false);
  const [loading, setLoading] = useState(false);
  const useCaseModalRef = useRef({ onCancel: () => {} } as {
    onCancel: () => void;
  });

  // APIG Service, 控制 Drawer
  const [APIGOpen, setAPIGOpen] = useState<boolean>(false);
  const [APIGLoading, setAPIGLoading] = useState(false);
  const APIGDrawerRef = useRef({ onCancel: () => {} } as {
    onCancel: () => void;
  });

  // Use Case 信息发送
  const onUseCaseModalSend = async (val: saveAsUseCasePropsType) => {
    const tempParamValueList = (paramValueList || []).map((el: any) => {
      const { paramValue } = el;
      if (Array.isArray(paramValue)) {
        return {
          ...el,
          paramValue: paramValue.join(','),
        };
      }
      return {
        ...el,
      };
    });

    let curPostData: saveAsUseCaseServicePropsType = {
      ...val,
      serviceCode,
      serviceRespParamList: val?.includeResp === 'Y' ? responseInfo : [],
      serviceReqParamList: tempParamValueList,
    };

    setLoading(true);
    await saveAsUseCaseService(curPostData)
      .then(() => {
        useCaseModalRef.current.onCancel();
        if (isEditUseCase && curPostData?.useCaseId) {
          otherFnInUseCaseModal({ isEditUseCase, currentUseCaseId: curPostData?.useCaseId });
        } else {
          otherFnInUseCaseModal();
        }

        message.success(
          formatMessage(isEditUseCase ? 'DEBUGSERVICE.USECASE.EDIT_SUCCESS' : 'DEBUGSERVICE.USECASE.ADD_SUCCESS'),
        );
      })
      .finally(() => {
        setLoading(false);
      });
  };

  // APIG Service 信息发送
  const onAPIGServiceDrawerSend = async (val: saveAsAPIGPropsType) => {
    const tempParamValueList = (paramValueList || []).map((el: any) => {
      const { paramValue } = el;
      if (Array.isArray(paramValue)) {
        return {
          ...el,
          paramValue: paramValue.join(','),
        };
      }
      return {
        ...el,
      };
    });

    const useCaseObj = responseInfo.some((item: any) => item.succFlag === 'Y')
      ? {
          useCase: {
            includeResp: 'Y',
            useCaseName: val?.apiMethod,
            serviceCode,
            serviceRespParamList: responseInfo.filter((item: any) => item.succFlag === 'Y'),
            serviceReqParamList: tempParamValueList,
            comments: '',
          },
        }
      : {};
    let curPostData = {
      ...val,
      serviceCode,
      ...useCaseObj,
    };
    const operateType = isEditAPIGService ? 'update' : 'new';

    try {
      setAPIGLoading(true);
      await saveAsAPIGService(operateType, curPostData).then(() => {
        APIGDrawerRef.current.onCancel();
        onEditAPIGOk?.();
        message.success(
          formatMessage(
            isEditAPIGService ? 'DEBUGSERVICE.APIGSERVICE.EDIT_SUCCESS' : 'DEBUGSERVICE.APIGSERVICE.ADD_SUCCESS',
          ),
        );
      });
    } finally {
      setAPIGLoading(false);
    }
  };

  // Select Use Case, 控制 Modal
  const [selOpen, setSelOpen] = useState<boolean>(false);

  const initQueryValue = useMemo(
    () => ({
      pageNo: 1,
      pageSize: 5,
      useCaseName: '',
      serviceCode,
    }),
    [serviceCode],
  ); // 初始化查询数据

  // Select Use Case，回填信息格式化处理为 dataToBeSent 的信息
  const [DebugBackFillingInfo, setDebugBackFillingInfo] = useState<{ [k: string]: any }[]>([]);
  const saveDebugBackFillingInfo = (
    value: Pick<saveAsUseCaseServicePropsType, 'serviceReqParamList' | 'serviceRespParamList'>,
  ) => {
    const { serviceReqParamList = [], serviceRespParamList = [] } = value;
    setDebugBackFillingInfo(serviceReqParamList);
    setDataToBeSent(changeParamsListToDataToBeSent(serviceReqParamList as any));
    setResponseInfo(serviceRespParamList);
  };

  useEffect(() => {
    const { serviceReqParamList = [] } = changeDataToBeSentToUsedFn({
      dataToBeSent,
      formatValuefn,
    });
    setParamValueList(serviceReqParamList);
    // setDebugBackFillingInfo(serviceReqParamList);
    return () => {};
  }, [dataToBeSent]);

  // 按钮组，saveAs 按钮函数
  const onSaveAsFn = ({ key }: { key: string }) => {
    switch (key) {
      case 'USECASE':
        setOpen(!open);
        break;
      case 'APIGSERVICE':
        setAPIGOpen(!APIGOpen);
        break;
      default:
        break;
    }
  };

  // 按钮组，设置按钮组的函数
  const ButtonGroupFnObj = {
    debugServiceFn,
    onSaveAsFn,
    onSelectUseCaseFn() {
      setSelOpen(!selOpen);
    },
  };

  useEffect(() => {
    if (serviceDetail) {
      setServMethod(serviceDetail?.method);
      setServPath(`${serviceDetail?.servicePath}`);
      const { newArrObj, newObj } = getRequestFormattingData(serviceDetail);
      setRequestDataSource(newArrObj);
      setDataToBeSent(newObj);
    }
    if (initSeletedValue?.serviceReqParamList?.length > 0) {
      saveDebugBackFillingInfo(initSeletedValue);
    }
  }, [serviceDetail, initSeletedValue]);

  // 数据初始化
  useEffect(() => {
    setParamValueList([]);
    return () => {};
  }, [debugMode]);

  return (
    <Spin wrapperClassName={styles.spinContainer} spinning={spinning}>
      <UpdataRequestParamsContext.Provider
        value={{ updataRequestParamsFn, DebugBackFillingInfo, arrUsedforVerification }}
      >
        <>
          <DetailBase
            wholeURL={wholeURL}
            isHtmlReject={isHtmlReject}
            requestDataSource={requestDataSource}
            serviceDetail={serviceDetail}
            debugMode={debugMode}
            ButtonGroupFnObj={ButtonGroupFnObj}
            hiddenSelectBtn={hiddenSelectBtn}
            responseInfo={responseInfo}
            isEditAPIGService={isEditAPIGService}
            saveUsType={saveUsType}
            refreshAPIGService={refreshAPIGService}
          />

          {isEditUseCase ? (
            <EditUseCaseDrawer
              key="EditUseCaseDrawer"
              ref={useCaseModalRef}
              title={formatMessage('DEBUGSERVICE.USECASEMODAL.EDITTITLE')}
              open={open}
              setOpen={setOpen}
              initSeletedValue={initSeletedValue}
              disabled={responseInfo?.length === 0}
              onUseCaseModalSend={onUseCaseModalSend}
              loading={loading}
            />
          ) : (
            <AddUseCaseDrawer
              key="AddUseCaseDrawer"
              ref={useCaseModalRef}
              title={formatMessage('DEBUGSERVICE.USECASEMODAL.TITLE')}
              open={open}
              setOpen={setOpen}
              disabled={responseInfo?.length === 0}
              onUseCaseModalSend={onUseCaseModalSend}
              loading={loading}
            />
          )}

          {isEditAPIGService ? (
            <EditAPIGServiceDrawer
              key="EditAPIGServiceDrawer"
              ref={APIGDrawerRef}
              title={formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.EDITTITLE')}
              open={APIGOpen}
              setOpen={setAPIGOpen}
              onAPIGServiceDrawerSend={onAPIGServiceDrawerSend}
              loading={APIGLoading}
              dataToBeSent={dataToBeSent}
              serviceDetail={serviceDetail}
              APIGServiceCatalogList={APIGServiceCatalogList}
              APIGServiceDetail={APIGServiceDetail}
            />
          ) : (
            <AddAPIGServiceDrawer
              key="AddAPIGServiceDrawer"
              ref={APIGDrawerRef}
              title={formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.TITLE')}
              open={APIGOpen}
              setOpen={setAPIGOpen}
              onAPIGServiceDrawerSend={onAPIGServiceDrawerSend}
              loading={APIGLoading}
              dataToBeSent={dataToBeSent}
              serviceDetail={serviceDetail}
              APIGServiceCatalogList={APIGServiceCatalogList}
            />
          )}

          {!hiddenSelectBtn && (
            <UseCaseBeSelectedModal
              key="useCaseBeSelectedModal"
              open={selOpen}
              setOpen={() => setSelOpen(false)}
              title={formatMessage('DEBUGSERVICE.USECASEBESELECTEDMODAL.SELECTUSECASE')}
              formatMessage={formatMessage}
              initQueryValue={initQueryValue}
              saveDebugBackFillingInfo={saveDebugBackFillingInfo}
            />
          )}
        </>
      </UpdataRequestParamsContext.Provider>
    </Spin>
  );
};

export default DebugService;
