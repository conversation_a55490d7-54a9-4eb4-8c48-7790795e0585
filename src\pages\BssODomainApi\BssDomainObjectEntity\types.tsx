import { ErrorCodeListItemType } from '@/pages/BssOErrorCodeApi/ErrorCodeManagement/types';

export interface RlcEventCatgSourceProps {
  catalogName: string;
  comments: null;
  catalogId: number;
  catalogType: string;
  parentCatalogId: null;
  class: string;
  events: RlcEventCatgSourceEventProps[];
}

export interface RlcEventCatgSourceEventProps {
  eventId: number;
  updateDate: null;
  comments: null;
  eventTrigger: null;
  eventType: null;
  eventCode: string;
  eventCnctMethod: string;
  createdDate: null;
  catalogId: number;
  excludeRepetition: null;
  updateBy: null;
  createdBy: null;
  eventName: string;
  isProduct: null;
  state: string;
  ruleExeMode: null;
  class: string;
}

export interface SelectedEntityProps {
  comments: string;
  createdBy: number;
  createdDate: string;
  creationSrc: string;
  defaultPage: number;
  domainObjId: number;
  entityCode: string;
  entityId: number;
  entityName: string;
  entityType: string;
  ignoreVer: null;
  key: string;
  level: number;
  modelCode: null;
  modelId: number;
  modelName: null;
  module: string;
  path: Path[];
  showOrder: number;
  state: string;
  title: string;
  updateBy: null;
  updateDate: null;
  value: string;
}

interface Path {
  data: Data;
  domainState: string;
  isDisable: boolean;
  key: string;
  level: number;
  name: string;
}

interface Data {}

export interface ColumnNameSourceProps {
  label: string;
  value: string;
  type: string;
  key: string | number;
  state: string; // new | original
}

export interface EnumSourceProps {
  label: string;
  value: number;
}

export interface SelectedRootNode {
  appCode: string;
  children: any[];
  comments: string;
  createdBy: number;
  createdDate: string;
  creationSrc: string;
  domainCode: string;
  domainId: number;
  domainName: string;
  key: string;
  level: number;
  module: string;
  path: any[];
  showOrder: number;
  state: string;
  title: string;
  updateBy: number;
  updateDate: string;
  value: string;
}

export interface RelationTypeSourceProps {
  relType: string;
  relTypeName: string;
  bidirectional: string;
  comments: string;
}

export interface DomainInfoProps {
  domainObjId: number;
  domainObjCode: string;
  domainObjName: string;
  domainId: number;
  creationSrc: string;
  ignoreVer: null;
  state: string;
  showOrder: number;
  createdDate: string;
  createdBy: number;
  updateDate: string;
  updateBy: null;
  comments: string;
  aggregateRootEntity: AggregateRootEntity;
  entityList: EntityList[];
  dataModelList: DataModelList[];
  enumList: EnumList[];
  objDetailList: ObjDetailList[];
}

interface ObjDetailList {
  detailName: string;
  detailValue: DetailValue[];
}

interface DetailValue {
  entityId?: number;
  entityCode?: string;
  entityName?: string;
  domainObjId: number;
  modelId?: number;
  modelCode?: null | string;
  modelName?: null | string;
  entityType?: string;
  defaultPage?: number;
  creationSrc: string;
  ignoreVer?: null;
  state: string;
  showOrder?: number;
  createdDate: string;
  createdBy: number;
  updateDate: null | string | string;
  updateBy: null | number | number;
  comments: string;
  modelType?: string;
  modelClass?: string;
  tableName?: null | string;
  viewSqlList?: null;
  usingEntityList?: null;
  usingEnumList?: null;
  enumId?: number;
  enumName?: string;
  enumType?: string;
  constEnumList?: null;
  dataModelEnum?: null;
  usingEnumEntityProp?: null;
}

interface EnumList {
  enumId: number;
  enumName: string;
  enumType: string;
  domainObjId: number;
  creationSrc: string;
  state: string;
  createdDate: string;
  createdBy: number;
  updateDate: string;
  updateBy: number;
  comments: string;
  constEnumList: null;
  dataModelEnum: null;
  usingEnumEntityProp: null;
}

interface DataModelList {
  modelId: number;
  modelCode: string;
  modelName: string;
  domainObjId: number;
  modelType: string;
  modelClass: string;
  tableName: null | string;
  creationSrc: string;
  state: string;
  createdDate: string;
  createdBy: number;
  updateDate: string;
  updateBy: number;
  comments: string;
  viewSqlList: null;
  usingEntityList: null;
  usingEnumList: null;
}

interface EntityList {
  entityId: number;
  entityCode: string;
  entityName: string;
  domainObjId: number;
  modelId: number;
  modelCode: null;
  modelName: null;
  entityType: string;
  defaultPage: number;
  creationSrc: string;
  ignoreVer: null;
  state: string;
  showOrder: number;
  createdDate: string;
  createdBy: number;
  updateDate: null | string;
  updateBy: null | number;
  comments: string;
}

interface AggregateRootEntity {
  entityId: number;
  entityCode: string;
  entityName: string;
  domainObjId: number;
  modelId: number;
  modelCode: null;
  modelName: null;
  entityType: string;
  defaultPage: number;
  creationSrc: string;
  ignoreVer: null;
  state: string;
  showOrder: number;
  createdDate: string;
  createdBy: number;
  updateDate: string;
  updateBy: number;
  comments: string;
}

export interface ExpReasonErrorCodeType {
  respErrorCodeId: number;
  respParamId: number;
  errorCodeId: number;
  matchPriority: number;
  matchStatus: string;
  matchConditions: string | null;
  reasonParams: string[];
  errorCode: string;
  errorReason: string;
}

export interface DomainErrorCodesType {
  domainId: number | null;
  domainName: string;
  errorCodes: ErrorCodeListItemType[];
}

export interface ScripSnippetItem {
  snippetId: number;
  snippetName: string;
  exePhase: string;
  snippetContent: string;
  comments: string;
}

export interface ScripItem {
  exeScriptId: number;
  serviceId: number;
  creationSrc: string;
  exePhase: string;
  exeScript: string;
  createdDate: string;
  createdBy: number;
  updateDate: string;
  updateBy: number;
  projectExtend: string;
}

export interface ServiceDetailType {
  comments: string;
  catalog: string;
  subCatalog: string;
  servicePath: string;
  odataFlag: string;
  entityKey: string;
  serviceKey: string;
  state: string;
  serviceName: string;
  serviceMethod: string;
  appCode: string;
  group: string;
  domainName: string;
  entityName: string;
  serviceCode: string;
  method: string;
  testPort: string;
  deprecated: string;
  parameters: any[];
  responses: any[];
}

export interface SensitiveLeveType {
  comments: string;
  sensitiveLevelCode: string;
  sensitiveLevelName: string;
  sensitiveLevelId: number;
};

export interface ErrorSchemaType {
  schemaId: number;
  schemaName: string;
  tmfFlag: boolean;
};