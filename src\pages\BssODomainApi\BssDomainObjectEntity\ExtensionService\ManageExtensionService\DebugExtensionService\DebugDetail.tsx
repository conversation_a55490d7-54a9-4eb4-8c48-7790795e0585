import { RequestTable, ResponseTable, Title, BlockTitle } from '@/components';
import useI18n from '@/hooks/useI8n';
import styles from './index.less';
import { IServiceDetail } from '@/services/typing';
import { useMemo } from 'react';
import { saveAsUseCaseServicePropsType } from '@/services';
import { Input } from 'antd';
import DebugUrlHeader from '@/components/DebugUrlHeader';

interface IServiceDetailCom {
  wholeURL?: string;
  isHtmlReject?: boolean;
  serviceDetail?: IServiceDetail;
  responseInfo?: Pick<saveAsUseCaseServicePropsType, 'serviceRespParamList'> | any;
  requestDataSource?: any;
  servPath: string;
  debugServiceFn: () => Promise<void>;
}

const DebugDetail: React.FC<IServiceDetailCom> = ({
  wholeURL = '',
  serviceDetail,
  responseInfo = [],
  isHtmlReject = false,
  requestDataSource = {},
  servPath = '',
  debugServiceFn,
}) => {
  const { formatMessage } = useI18n();

  const addressSource = [
    {
      label: 'Example Address',
      value: 'testing',
    },
  ];
  const ResponseArea = useMemo(() => {
    if (responseInfo?.length > 0) {
      return (
        <div key="debugResponseTable" className={styles.responseContent}>
          <Title>{formatMessage('SERVICEDETAIL.RESPONSE.TITLE')}</Title>
          <ResponseTable debugMode={true} responseInfo={responseInfo} isHtmlReject={isHtmlReject} />
        </div>
      );
    }
    return null;
  }, [serviceDetail, responseInfo, isHtmlReject]);

  return (
    <div className={styles.debugDetailContainer}>
      {/* url content */}
      <DebugUrlHeader
        servPath={servPath}
        addressSource={addressSource}
        serviceDetail={serviceDetail}
        style={{ marginBottom: 24 }}
        debugServiceFn={debugServiceFn}
      />

      {/* request Information */}
      <div className={styles.requestContent}>
        <Title>{formatMessage('SERVICEDETAIL.REQUEST.TITLE')}</Title>
        {wholeURL && (
          <div>
            <BlockTitle>{formatMessage('SERVICEDETAIL.REQUEST.URL')}</BlockTitle>
            <div title={wholeURL}>
              <Input readOnly variant="borderless" value={wholeURL} />
            </div>
          </div>
        )}
        <RequestTable requestDataSource={requestDataSource} servicemethod={serviceDetail?.method} debugMode={true} />
      </div>
      {ResponseArea}
    </div>
  );
};

export default DebugDetail;
