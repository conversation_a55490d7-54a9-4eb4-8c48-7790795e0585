.spinContainer {
  width: 100%;
  height: 100%;
  overflow: hidden;
  :global {
    .ant-spin-container {
      width: 100%;
      height: 100%;
      overflow: hidden;
    }
  }
}

.bssApiLeftContent {
  width: 100%;
  height: 100%;
  border: 1px solid #e0e0e0;
  box-sizing: border-box;
  background-color: white;
  // padding: 12px;
  display: flex;
  flex-direction: column;
  .titleContainer {
    padding: 12px 12px 0 12px;
    margin-bottom: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    & > div {
      display: flex;
    }
    .title {
      font-size: 16px;
      font-family: Nunito Sans;
      font-weight: bold;
      color: #2d3040;
      line-height: 24px;
    }
    .downLoad {
      width: 30px;
      height: 28px;
      display: flex;
      align-items: center;
      justify-content: center;
      border: 1px solid #e0e0e0;
      border-radius: 2px;
      margin-left: 12px;
      cursor: pointer;
    }
  }
  .bssApiLeftContentSearch {
    display: flex;
    padding: 0 12px;
    .search {
      flex: 1;
    }
  }
  .bssApiLeftContentList {
    flex: 1;
    overflow-y: auto;
    margin-top: 12px;
  }
}
.judgeSmButton {
  height: 24px !important;
}