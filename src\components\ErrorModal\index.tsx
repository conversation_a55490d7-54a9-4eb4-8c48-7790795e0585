import React, { useCallback, useEffect, useState } from "react";
import ReactDOM from "react-dom";
import { Modal, Button, message, Typography } from "antd";
import copy from "copy-to-clipboard";
import styles from "./index.less";

const BULB_PNG = require("@/assets/CDP/bulb.png");
const WARNING_PNG = require("@/assets/CDP/<EMAIL>");

const { modalBottom, modalContent, errorModalStyle } = styles;

const ErrorDialog = (props: any) => {
  const { modalKey, errorInfo, onDestory } = props;
  const { errMessage = "", errCode = "", traceId } = errorInfo || {};
  const [visible, setVisible] = useState(true);
  const onOkBtn = () => {
    setVisible(false);
    onDestory && onDestory(modalKey);
  };
  const onCancel = () => {
    setVisible(false);
    onDestory && onDestory(modalKey);
  };
  const handleCopy = () => {
    copy(errCode ? `[${errCode}]${errMessage}` : errMessage);
    message.success("Copy Success!");
  };

  return (
    <>
      <Modal
        title={
          <div>
            <img src={WARNING_PNG} alt="" style={{ width: "21px" }} />
            <span style={{ paddingLeft: "12px" }}>Error</span>
          </div>
        }
        closable={false}
        maskClosable={false}
        onOk={onOkBtn}
        // centered
        width={600}
        open={visible}
        onCancel={onCancel}
        wrapClassName={errorModalStyle}
        destroyOnClose
        footer={[
          <div key="bottom" className={modalBottom}>
            <div>
              <img src={BULB_PNG} alt="" width={16} />
              <span style={{ paddingLeft: "4px" }}>
                You can
                {"\u00A0"}
              </span>
              <span
                onClick={handleCopy}
                style={{ color: "#4477EE", cursor: "pointer" }}
              >
                copy error log
              </span>
              <span>
                {"\u00A0"}
                and contact administrator if needed
              </span>
            </div>
            <Button key="OK" type="primary" onClick={() => onOkBtn()}>
              Ok
            </Button>
          </div>,
        ]}
      >
        <div className={modalContent}>
          Traced ID:{" "}
          <Typography.Paragraph
            style={{
              display: "inline-block",
              color: "#4478ee",
              margin: 0,
            }}
            copyable
          >
            {traceId}
          </Typography.Paragraph>
          <br />
          <Typography.Paragraph
            ellipsis={{ rows: 6, expandable: true, symbol: "more" }}
          >
            Stack: {errCode ? `[${errCode}]${errMessage}` : errMessage}
          </Typography.Paragraph>
        </div>
      </Modal>
    </>
  );
};

const ErrorModal = () => {
  const [modalList, SetModalList] = useState<Array<any>>([]);

  useEffect(() => {
    // 初始
    (Modal as any).create = (params: any) => {
      const key = `${new Date().getTime()}`;
      const newModalList = modalList;
      newModalList.push(
        <ErrorDialog
          modalKey={key}
          errorInfo={{
            errCode: params?.errCode || "",
            errMessage: params?.errMessage || "",
            traceId: params.traceId || "",
          }}
          onDestory={(keyIndex: string) => {
            modalList.splice(
              modalList.findIndex((v) => v.key === keyIndex),
              1
            );
            SetModalList(modalList);
          }}
        />
      );
      SetModalList([...newModalList]);
    };

    (Modal as any).cloase = () => {
      SetModalList([]);
    };
  }, []);

  return <div id="modalErrorId">{modalList}</div>;
};

export default ErrorModal;
