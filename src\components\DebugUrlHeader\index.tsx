import { CSSProperties, useEffect, useState } from 'react';
import classNames from 'classnames';
import styles from './index.less';
import { Button, Select } from 'antd';
import useI18n from '@/hooks/useI8n';

interface UrlHeaderPropsType {
  servPath: string;
  addressSource: { label: string; value: string | number }[];
  serviceDetail: any;
  style?: CSSProperties;
  debugServiceFn: () => Promise<void>;
}

const DebugUrlHeader = ({
  servPath = '',
  addressSource = [],
  serviceDetail = {},
  style = {},
  debugServiceFn,
}: UrlHeaderPropsType) => {
  const { formatMessage } = useI18n();
  const [selectedAddressType, setSelectedAddressType] = useState<string>('testing');

  const onAddressChange = (value: string) => {
    setSelectedAddressType(value);
  };
  return (
    <div style={style}>
      <div className={styles.urlWrapper}>
        <div>
          <Select
            options={addressSource}
            className={styles.addressSelect}
            onChange={onAddressChange}
            defaultValue={selectedAddressType}
            disabled
          />
        </div>
        <div className={styles.url}>
          <div
            className={classNames({
              [styles.postStyle]: serviceDetail?.method === 'POST',
              [styles.getStyle]: serviceDetail?.method === 'GET' || serviceDetail?.method === 'PUT',
              [styles.patchStyle]: serviceDetail?.method === 'PATCH',
              [styles.deleteStyle]: serviceDetail?.method === 'DELETE',
            })}
          >
            {serviceDetail?.method}
          </div>
          <div>{`${window.location.origin}${serviceDetail?.servicePath}`}</div>
        </div>
        <div className={styles.optionBtnGroup}>
          <Button
            className={classNames(styles.sendBtn, styles.commonHeight)}
            onClick={() => {
              if (debugServiceFn) {
                debugServiceFn();
              }
            }}
          >
            {formatMessage('DEBUGSERVICE.URL.SEND')}
          </Button>
        </div>
      </div>
      <div>
        <span className={styles.note}>{formatMessage('DEBUGSERVICE.URL.NOTE')}</span>
      </div>
    </div>
  );
};

export default DebugUrlHeader;
