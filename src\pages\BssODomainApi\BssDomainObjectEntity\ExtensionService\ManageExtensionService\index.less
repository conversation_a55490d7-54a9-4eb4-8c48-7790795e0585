
.activeLineContainer {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  padding: 12px 16px;
  box-sizing: border-box;
}

.topContent {
  margin-top: 5px;
  display: inline-block;
}

.hide {
  display: none;
}

.selectContent {
  margin-left: 20px;
}

.domainObjContent {
  .title {
    font-size: 16px;
    font-family: Nunito Sans-Bold;
    font-weight: bold;
    color: #2d3040;
    line-height: 24px;
  }
}

.domainObjName {
  color: #47e;
  cursor: pointer;
}

.iconStyle {
  color: #47e;
  cursor: pointer;
}

.versionIcon1 {
  margin-left: 20px;
  color: #21f17f;
  cursor: pointer;
}

.versionIcon2 {
  margin-left: 20px;
  color: #47e;
  cursor: pointer;
}
.uploadBySwagger {
  color: #47e;
  cursor: pointer;
  font-size: 16px;
}
.arrowRight {
  padding: 0 10px;
}

.remarkContent{
  margin-top: 10px;
  border: 1px solid #9cc0ff;
  border-radius: 2px;
  height: 46px;
  opacity: 1;
  display: flex;
  align-items: center;
  border-radius: 5px;
  background: #edf5ff;
  padding: 0 12px;
}

.legendItem {
  font-size: 14px;
  font-family: Nunito Sans;
  font-weight: 400;
  color: #2d3040;
  display: inline-block;
  align-items: center;
  & > span:nth-child(2) {
    margin-left: 4px;
  }
}

.legendItem:not(:first-child) {
  margin-left: 12px;
}
.special{
  color: #47e;
}
.iconItem {
  width: 12px;
}
.iconItemActive {
  cursor: pointer;
}
.iconItem:not(:first-child) {
  margin-left: 12px;
}


.dyStyleWrapper {

  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  span:nth-child(1) {
    border-radius: 2px 2px 2px 2px;
    opacity: 1;
    font-size: 14px;
    font-family: Nunito Sans;
    font-weight: 400;
    line-height: 22px;
    padding: 0 12px;
  }
  span:nth-child(2) {
    margin-left: 8px;
  }
  .dyStylePOST {
    //  margin-top: -3px;
    background: #e1faea;
    border: 1px solid #6ce0a0;
    color: #00bb66;
  }
  .dyStyleGET {
    border: 1px solid #709efa;
    background: #edf5ff;
    color: #4477ee;
  }
  .dyStylePATCH {
    background: #f9f0ff;
    border: 1px solid #d3adf7;
    color: #531dab;
  }
  .dyStyleDELETE {
    background: #f9f0ff;
    border: 1px solid #f7adc3;
    color: #ab1d1d;
  }
}


