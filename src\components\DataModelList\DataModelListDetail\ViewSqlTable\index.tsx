import OperationIconTitle from '@/components/OperationIconTitle';
import ResizableTable from '@/components/ResizableTable';
import useI18n from '@/hooks/useI8n';
import { useTableFilter } from '@/hooks/useTableFilter';
import { TableColumnsType, Tree } from 'antd';
import { useEffect, useMemo, useState } from 'react';

interface IViewSqlTable {
  viewDataSource: any;
}
interface IViewDataSource {
  modelId: number;
  verCode: string | null;
  viewSql: string;
  viewSqlId: number;
}

const ViewSqlTable: React.FC<IViewSqlTable> = ({ viewDataSource = [] }) => {
  const [newViewDataSource, setNewViewDataSource] = useState<any>();
  const { formatMessage } = useI18n();
  // 使用 useMemo 确保 dataSource 是稳定的引用
  const memoizedDataSource = useMemo(
    () => (Array.isArray(newViewDataSource) ? newViewDataSource : []),
    [newViewDataSource],
  );
  // 自定义hooks，支持表格列筛选并分页展示
  const { filteredDataSource, getColumnSearchProps, clearAllFilters } = useTableFilter(memoizedDataSource);

  useEffect(() => {
    if (viewDataSource.length) {
      const newSource = viewDataSource.map((item: IViewDataSource) => {
        return {
          ...item,
          verCode: item.verCode ? item.verCode : 'Default', // 补充verCode数据，便于verCode表格列筛选
        };
      });
      setNewViewDataSource(newSource);
    }
    // 重置表格列的筛选条件
    clearAllFilters();
  }, [viewDataSource]);

  const columns: TableColumnsType = [
    {
      dataIndex: 'verCode',
      width: '30%',
      ellipsis: true,
      title: formatMessage('DATAMODELLISTDETAIL.VIEWSQL.PROVIEW'),
      ...getColumnSearchProps('verCode'),
    },
    {
      dataIndex: 'viewSql',
      width: '70%',
      ellipsis: true,
      title: formatMessage('DATAMODELLISTDETAIL.VIEWSQL.QUERYSQL'),
      ...getColumnSearchProps('viewSql'),
    },
  ];

  return (
    <div>
      <OperationIconTitle title={formatMessage('DATAMODELLISTDETAIL.VIEWSQL.TITLE')} />
      <ResizableTable
        size="small"
        rowKey="key"
        columns={columns}
        pagination={{
          hideOnSinglePage: true,
          pageSize: 5,
          showTotal: (total) => formatMessage('PROJECT.COMMON.PAGINATION.TOTAL', { total }),
        }}
        dataSource={filteredDataSource?.map((el: any, index: number) => ({
          ...el,
          key: `viewSqlTable-${index}-${el.verCode}-${el?.viewSql}`,
        }))}
      />
    </div>
  );
};

export default ViewSqlTable;
