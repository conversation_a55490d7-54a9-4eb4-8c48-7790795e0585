@bgColr: #edf5ff;

.rowSelectClassName {
  background: @bgColr;
  background-color: @bgColr;
  //   &:hover {
  //     background: @bgColr;
  //     background-color: @bgColr;
  //   }
}

.tableWrapper {
  :global {
    .ant-table-row:hover {
      background-color: @bgColr;
    }
    .ant-table-tbody > tr.ant-table-row:hover > td,
    .ant-table-tbody > tr > td.ant-table-cell-row-hover {
      background: @bgColr;
    }
    .ant-table-body {
      overflow-y: auto !important;
    }
  }
}
