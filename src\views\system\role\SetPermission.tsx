import { Form, Input, Modal, Tree } from "antd";
import type { IAction } from "../../../types/modal";
import { useEffect, useImperativeHandle, useState } from "react";
import type { Menu, Role } from "../../../types/api";
import api from "../../../api";
import { message } from "../../../utils/AntdGlobal";

export default function SetPermission(props: any) {
    const [form] = Form.useForm()
    const [action, setAction] = useState<IAction>('create')
    const [visible, setVisible] = useState(false)
    const [checkedKeys, setCheckedKeys] = useState<string[]>([])
    const [menuList, setMenuList] = useState<Menu.menuItem[]>([])


    const [roleInfo, setRoleInfo] = useState<Role.RoleItem>()
    const [permission, setPermission] = useState<Role.Permission>()

    useEffect(() => {
        getMenuList()
    }, [])
    const getMenuList = async () => {
        const data = await api.getMenuList()
        setMenuList(data)
    }

    useImperativeHandle(props.ref, () => {
        return { open }
    })
    const open = (type: IAction, data?: Role.RoleItem) => {
        setAction(type)
        setVisible(true)
        setRoleInfo(data)
        setCheckedKeys(data?.permissionList.checkedKeys || [])
    }

    const handleSubmit = async () => {
        if (permission) {
            await api.updatePermission(permission)
            message.success('权限设置成功')
            handleCancel()
            props.update()
        }
    }
    const handleCancel = () => {
        setVisible(false)
        form.resetFields()
    }

    const onCheck = (checkedKeysValue: any, item: any) => {
        setCheckedKeys(checkedKeysValue)
        const checkedKeys: string[] = []
        const parentKeys: string[] = []
        item.checkedNodes.map((node: Menu.menuItem) => {
            if (node.menuType === 2) {
                checkedKeys.push(node._id)
            } else {
                parentKeys.push(node._id)
            }
        })
        setPermission({
            _id: roleInfo?._id || '',
            permissionList: {
                checkedKeys,
                halfCheckedKeys: parentKeys.concat(item.halfCheckedKeys)
            }
        })
    }

    return (
        <>
            <Modal
                title='设置权限'
                width={600}
                open={visible}
                okText="确定"
                cancelText="取消"
                onOk={handleSubmit}
                onCancel={handleCancel}
            >
                <Form
                    form={form}
                    labelCol={{ span: 4 }}
                    labelAlign="right"
                >
                    <Form.Item name="_id" hidden><Input /></Form.Item>
                    <Form.Item label="角色名称" >
                        {roleInfo?.roleName}
                    </Form.Item>
                    <Form.Item label="权限" >
                        <Tree
                            checkable
                            defaultExpandAll
                            onCheck={onCheck}
                            checkedKeys={checkedKeys}
                            treeData={menuList}
                            fieldNames={{
                                title: 'menuName',
                                key: '_id',
                                children: 'children'
                            }}
                        />
                    </Form.Item>
                </Form>
            </Modal>
        </>
    )
}