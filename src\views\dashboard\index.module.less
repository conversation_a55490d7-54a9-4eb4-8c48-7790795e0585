.container {
    background-color: var(--dark-bg-color);
    color: var(--dark-color);
    padding: 20px;

    .userInfo {
        display: flex;
        align-items: center;

        .userImg {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            margin-right: 25px;
        }
    }

    .report {
        display: flex;
        margin-top: 20px;

        .card {
            flex: 1;
            height: 100px;
            padding: 10px;
            margin-right: 20px;
            border-radius: 5px;
            color: #fff;
            font-size: 14px;

            &:nth-child(1) {
                background-color: #f4864f;
            }

            &:nth-child(2) {
                background-color: #887edc;
            }

            &:nth-child(3) {
                background-color: #4f95e5;
            }

            &:nth-child(4) {
                background-color: #6dc3d7;
            }

            &:nth-last-child(1) {
                margin-right: 0;
            }

            .title {
                text-align: left;
            }

            .data {
                text-align: center;
                font-size: 24px;
            }
        }
    }
}

.chart {
    margin-top: 50px;

    .itemLine {
        height: 400px;
    }

    .pie<PERSON>hart {
        display: flex;

        .itemPie {
            height: 400px;
            flex: 1;
        }
    }
}