import { Button, Form, Input, Space, Table } from "antd";
import { useForm } from "antd/es/form/Form";
import type { ColumnsType } from "antd/es/table";
import { useEffect, useRef, useState } from "react";
import api from "../../../api";
import type { Dept } from "../../../types/api";
import CreateDept from "./CreateDept";
import type { IAction } from "../../../types/modal";
import { formatDate } from "../../../utils";
import { message, modal } from "../../../utils/AntdGlobal";

export default function DeptList() {
    const [form] = useForm()
    const deptRef = useRef<{
        open: (type: IAction, data?: Dept.editParams | { parentId: string }) => void
    }>(null)
    const [data, setData] = useState<Dept.deptItem[]>()

    useEffect(() => {
        getDeptList()
    }, [])

    const getDeptList = async () => {
        try {
            const data = await api.getDeptList(form.getFieldsValue())
            setData(data)
        } catch (error) {
            console.log(error);
        }
    }
    const handleReset = () => {
        form.resetFields()
    }

    const handleCreate = () => {
        deptRef.current?.open('create')
    }

    const handleSubCreate = (parentId: string) => {
        deptRef.current?.open('create', { parentId })
    }

    const handleEdit = (record: Dept.deptItem) => {
        deptRef.current?.open('edit', record)
    }

    const handleDelete = (id: string) => {
        modal.confirm({
            title: "删除确认",
            content: <span>确认删除该部门吗？</span>,
            onOk: () => {
                handleUserDelSubmit(id)
            }
        })
    }
    const handleUserDelSubmit = async (id: string) => {
        await api.DelDept({ _id: id })
        message.success('删除成功')
        getDeptList()
    }

    const columns: ColumnsType<Dept.deptItem> = [
        {
            title: "部门名称",
            dataIndex: 'deptName',
            key: "deptName",
            width: 200,
            align: 'center'
        },
        {
            title: "负责人",
            dataIndex: 'userName',
            key: "userName",
            width: 100,
            align: 'center'
        },
        {
            title: "更新时间",
            dataIndex: 'updateTime',
            key: "updateTime",
            align: 'center',
            render(updateTime: string) {
                return formatDate(updateTime)
            }
        },
        {
            title: "创建时间",
            dataIndex: 'createTime',
            key: "createTime",
            align: 'center',
            render(createTime: string) {
                return formatDate(createTime)
            }
        },
        {
            title: "操作",
            key: "action",
            width: 200,
            align: 'center',
            render(record) {
                return (
                    <Space>
                        <Button type="text" onClick={() => handleSubCreate(record._id)}>新增</Button>
                        <Button type="text" onClick={() => handleEdit(record)}>编辑</Button>
                        <Button type="text" danger onClick={() => handleDelete(record._id)}>删除</Button>
                    </Space>
                )
            }
        },
    ]
    return (
        <>
            <Form className="search" layout="inline" form={form}>
                <Form.Item name="deptName" label="部门名称">
                    <Input placeholder="请输入部门名称" />
                </Form.Item>
                <Form.Item >
                    <Button type="primary" className="mr10" onClick={getDeptList}>搜索</Button>
                    <Button type="default" onClick={handleReset}>重置</Button>
                </Form.Item>
            </Form>
            <div className="table">
                <div className="header">
                    <div className="title">部门列表</div>
                    <div className="action">
                        <Button type="primary" onClick={handleCreate}>新增</Button>
                    </div>
                </div>
                <Table
                    bordered
                    pagination={false}
                    rowKey='_id'
                    columns={columns}
                    dataSource={data}
                />
            </div>
            <CreateDept ref={deptRef} update={getDeptList} />
        </>
    )
}