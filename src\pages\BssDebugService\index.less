.spinContainer {
  width: 100%;
  height: 100%;
  overflow: auto;

  :global {
    .ant-spin-container {
      width: 100%;
      height: 100%;
      overflow: auto;
    }
  }
}

.bssDebugService {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;

  .header {
    display: flex;
    align-items: center;
    height: 44px;
    background: #ffffff;
    box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.12);
    padding: 0 12px;
    font-size: 18px;
    width: 100%;

    .breadCrumbs {
      margin-left: 8px;
      font-size: 18px;

      .breadCrumbsLevelOne,
      .breadCrumbsLevelTwo {
        color: #2d3040;
        font-family: Nunito Sans-Bold;
        font-weight: bold;
      }

      .breadCrumbsLevelOne {
        cursor: pointer;
      }
    }
  }

  .bottomArea {
    height: calc(100% - 52px);
    background: #fff;
    overflow-y: auto;
    .bottom {
      padding: 12px;
    }
  }
}
