# odh92-frontend-core

使用 yarn install，安装依赖包
node:18.17.0

使用 yarn start，启动项目

进入 http://localhost:端口号/portal-web/，在菜单栏搜索‘open’

74 环境 http://************/portal-web/ 测试账号：admin/Uportal_15

14 环境 http://************/portal-web/ 测试账号：dev000/Ngportal_15

105（印尼）环境 http://*************/portal-web/ 测试账号：admin/abc@123A

bol 环境 http://*************/portal-web/ 测试账号：admin/Ngportal_15

V9DC 环境 http://*************:80/portal-web/ 测试账号：admin/Ngportal_15

V81E 环境 'http://************:8086/portal/' 测试账号：admin/11

GOMO (新电) 环境 'http://*************:1080/portal-web/' 测试账号：admin/Ngportal_15

203 环境 'http://*************/portal-web/' 测试账号：admin/11