import useI18n from '@/hooks/useI8n';
import OperationIconTitle from '../OperationIconTitle';
import OperationGroup from '../OperationGroup';
import { TableColumnsType, Spin, message } from 'antd';
import DataModelDrawer from './DataModelDrawer';
import { useModel } from 'umi';
import {
  queryDomainDataModelListService,
  getDomainDataModelDetailService,
  deleteDomainDataModelListService,
} from '@/services/dataModelService';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import { Status, CreationSource } from './const';
import ResizableTable from '../ResizableTable';
import { useTableFilter } from '@/hooks/useTableFilter';

interface IDataModelListPropsType {
  selectedDomainNode: any;
  selectedRootNode: any; // 根节点
  isProduct: boolean;
  onSelectModelObj: any;
  refreshData: () => any;
}

const DataModelList: React.FC<IDataModelListPropsType> = ({
  selectedDomainNode = {},
  selectedRootNode = {},
  isProduct = false,
  onSelectModelObj = () => {},
  refreshData = () => {},
}) => {
  const { formatMessage } = useI18n();

  const { dataModelaQueryObj = { pageNo: 1, pageSize: 5 }, setDataModelaQueryObj = () => {} } = useModel(
    'useDataModelTableQueryObjModel',
  );

  const { classesInfo = [] } = useModel('useDomainDataModelListModel');

  const isDraft = useMemo(() => {
    return selectedRootNode?.state === 'D';
  }, [selectedRootNode]);

  const ModelClassSource = useMemo(() => {
    const source = classesInfo?.map((item) => {
      return {
        name: item.modelClassName,
        key: item.modelClass,
      };
    });
    return source;
  }, [classesInfo]);

  const { domainObjId = null } = selectedDomainNode;
  const { domainId = null } = selectedRootNode;

  // loading...
  const [loading, setLoading] = useState(false);

  // 保存table数据
  const [tableDataSource, setTableDataSource] = useState<any>([]);
  const [allTableDataSource, setAllTableDataSource] = useState<any>([]);

  // 保存查询参数
  const [queryParam, setQueryParam] = useState<{
    pageNo: number;
    pageSize: number;
  }>({
    pageNo: 1,
    pageSize: 5,
  });

  // 保存单词查询的所有数据总数
  const [total, setTotal] = useState(0);

  // Add Drawer 受控
  const DataModelDrawerRef = useRef({
    enterAddMode: () => {},
    enterEditMode: (el: any) => {},
  });
  // 使用 useMemo 确保 dataSource 是稳定的引用
  const memoizedDataSource = useMemo(
    () => (Array.isArray(allTableDataSource) ? allTableDataSource : []),
    [allTableDataSource],
  );
  // 自定义hooks，支持表格列筛选并分页展示
  const { filteredDataSource, getColumnSearchProps, getColumnEnumSearchProps, clearAllFilters } =
    useTableFilter(memoizedDataSource);

  // 接口数据查询
  const getDataModelListDataFn = async (id: number | null, params: any) => {
    setLoading(true);
    try {
      await queryDomainDataModelListService(id, { pageNo: 1, pageSize: 1000 }).then((res) => {
        const { success = false, data = {} } = res;
        if (success) {
          const { totalCount = 1, list = [] } = data;
          setAllTableDataSource(list);
          setQueryParam({
            pageNo: params?.pageNo,
            pageSize: params?.pageSize,
          });
          setTotal(totalCount);
        }
      });
    } catch (error) {
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (domainObjId) {
      setQueryParam({
        pageNo: dataModelaQueryObj?.pageNo,
        pageSize: dataModelaQueryObj?.pageSize,
      });
      // getDataModelListDataFn(domainObjId, dataModelaQueryObj);
    }
    return () => {};
  }, [dataModelaQueryObj]);

  useEffect(() => {
    if (domainObjId && dataModelaQueryObj) {
      getDataModelListDataFn(domainObjId, dataModelaQueryObj);
    }
  }, [domainObjId]);

  useEffect(() => {
    return () => {
      setDataModelaQueryObj({
        pageNo: 1,
        pageSize: 5,
      });
    };
  }, [domainObjId]);

  useEffect(() => {
    if (filteredDataSource) {
      setTableDataSource(filteredDataSource);
      setTotal(filteredDataSource.length);
    }
  }, [filteredDataSource]);

  const onClickGroup = {
    add: () => {
      DataModelDrawerRef?.current?.enterAddMode();
    },
    edit: async (modelId: any) => {
      try {
        await getDomainDataModelDetailService(modelId).then((res) => {
          const { success = false, data = {} } = res;
          if (success) {
            DataModelDrawerRef?.current?.enterEditMode(data);
          }
        });
      } catch (error) {}
    },
    delete: async (modelId: any) => {
      try {
        await deleteDomainDataModelListService(modelId).then((res) => {
          const { success = false } = res;
          if (success) {
            if (allTableDataSource.length === 1 && queryParam.pageNo > 1) {
              setDataModelaQueryObj({
                pageNo: queryParam.pageNo - 1,
                pageSize: queryParam.pageSize,
              });
            } else {
              getDataModelListDataFn(domainObjId, dataModelaQueryObj);
            }
            message.success(formatMessage('DATAMODELLISTDETAIL.DELETE.SUCCESS'));
            refreshData();
          }
        });
      } catch (error) {}
    },
  };

  const columns: TableColumnsType = [
    {
      dataIndex: 'modelName',
      title: formatMessage('DATAMODELLIST.TABLE.NAME'),
      width: '15%',
      ellipsis: true,
      ...getColumnSearchProps('modelName'),
      render: (text, record, index) => {
        return (
          <span style={{ color: '#47e', cursor: 'pointer' }} onClick={() => onSelectModelObj(record)}>
            {text}
          </span>
        );
      },
    },
    {
      dataIndex: 'modelCode',
      width: '10%',
      ellipsis: true,
      title: formatMessage('DATAMODELLIST.TABLE.CODE'),
      ...getColumnSearchProps('modelCode'),
    },
    {
      dataIndex: 'modelType',
      title: formatMessage('DATAMODELLIST.TABLE.TYPE'),
      width: '15%',
      ellipsis: true,
      render: (_: string, record: any) => {
        const matchedItem = Status.find((i: { key: string; name: string }) => i.key === record.modelType);
        return matchedItem ? matchedItem.name : '';
      },
      ...getColumnEnumSearchProps('modelType', Status),
    },
    {
      dataIndex: 'creationSrc',
      title: formatMessage('DATAMODELLIST.TABLE.CREATIONSOURCE'),
      width: '15%',
      ellipsis: true,
      render: (_: string, record: any) => {
        const matchedItem = CreationSource.find((i: { key: string; name: string }) => i.key === record.creationSrc);
        return matchedItem ? matchedItem.name : '';
      },
      ...getColumnEnumSearchProps('creationSrc', CreationSource),
    },
    {
      dataIndex: 'modelClass',
      title: formatMessage('DATAMODELLIST.DRAWER.CLASSIFICATION'),
      width: '15%',
      ellipsis: true,
      render: (text, record, index) => {
        const tempClass = classesInfo?.filter((el) => el?.modelClass === text);
        return <>{tempClass?.[0]?.modelClassName}</>;
      },
      ...getColumnEnumSearchProps('modelClass', ModelClassSource),
    },
    {
      dataIndex: 'comments',
      width: '20%',
      ellipsis: true,
      title: formatMessage('DATAMODELLIST.TABLE.DESC'),
      ...getColumnSearchProps('comments'),
    },
    {
      dataIndex: 'opt',
      title: formatMessage('DATAMODELLIST.TABLE.OPT'),
      width: '10%',
      ellipsis: true,
      render: (text, record, index) => {
        const { modelId } = record;
        if (isDraft) {
          if (isProduct) {
            return (
              <OperationGroup
                btnList={[
                  {
                    value: 'edit',
                    onClick: () => {
                      onClickGroup?.edit(modelId);
                    },
                  },
                  {
                    value: 'delete',
                    title: formatMessage('DATAMODELLIST.TABLE.DELTIP'),
                    onClick: () => {
                      onClickGroup?.delete(modelId);
                    },
                  },
                ]}
              />
            );
          }
          if (record?.creationSrc === 'C') {
            return (
              <OperationGroup
                btnList={[
                  {
                    value: 'edit',
                    onClick: () => {
                      onClickGroup?.edit(modelId);
                    },
                  },
                  {
                    value: 'delete',
                    title: formatMessage('DATAMODELLIST.TABLE.DELTIP'),
                    onClick: () => {
                      onClickGroup?.delete(modelId);
                    },
                  },
                ]}
              />
            );
          }
          return (
            <OperationGroup
              btnList={[
                {
                  value: 'edit',
                  onClick: () => {
                    onClickGroup?.edit(modelId);
                  },
                },
              ]}
            />
          );
        }
        return <></>;
      },
    },
  ];

  const afterSubmit = () => {
    if (domainObjId) {
      getDataModelListDataFn(domainObjId, dataModelaQueryObj);
    }
    refreshData();
  };

  useEffect(() => {
    // 重置表格列的筛选条件
    clearAllFilters();
  }, [selectedDomainNode]);

  return (
    <React.Fragment>
      <div>
        <OperationIconTitle
          title={formatMessage('DATAMODELLIST.TABLE.TITLE')}
          opt={formatMessage('DATAMODELLIST.TABLE.ADD')}
          handleClick={onClickGroup.add}
          type={isDraft ? 'add' : ''}
        />
        <Spin spinning={loading}>
          <ResizableTable
            size="small"
            dataSource={(tableDataSource || [])?.map((el: any, index: number) => ({
              ...el,
              key: `dataModelListTable-${index}-${el.modelId}`,
            }))}
            columns={columns}
            rowKey="key"
            pagination={{
              hideOnSinglePage: true,
              showSizeChanger: true,
              pageSizeOptions: [5, 10, 15, 20],
              total,
              current: queryParam?.pageNo,
              pageSize: queryParam?.pageSize,
              showTotal: (total) => formatMessage('PROJECT.COMMON.PAGINATION.TOTAL', { total }),
              onChange: (page: any, pageSize: any) => {
                setDataModelaQueryObj({
                  pageSize,
                  pageNo: page,
                });
              },
            }}
          />
        </Spin>
      </div>
      <DataModelDrawer
        ref={DataModelDrawerRef}
        domainObjId={domainObjId}
        domainId={domainId}
        afterSubmit={afterSubmit}
        isProduct={isProduct}
      />
    </React.Fragment>
  );
};

export default DataModelList;
