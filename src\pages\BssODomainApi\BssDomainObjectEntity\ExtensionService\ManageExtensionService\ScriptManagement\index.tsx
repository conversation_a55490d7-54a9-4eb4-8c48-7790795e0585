import { Button, Flex, message, Select, Spin, Tooltip } from 'antd';
import { MatchStatus } from '../const';
import CodePrompt from '@/components/CodePrompt';
import styles from './index.less';
import { CloseCircleOutlined, FormOutlined, InfoCircleOutlined, SaveOutlined, EditOutlined } from '@ant-design/icons';
import { useEffect, useState } from 'react';
import { ScripItem, ScripSnippetItem } from '../../../types';
import { operateServiceScript, qryScriptListById, qryScriptSnippetList } from '@/services/entityService';
import {
  generateRespHeaderSchema,
  generateRespSchema,
  generateStatusSchema,
  transferServiceDetailToSchema,
} from '@/utils/transferServiceDetailToSchema';
import { EntityServiceDetail, getEntityServiceDetail } from '@/services/errorCodeService';
import useI18n from '@/hooks/useI8n';

interface IScriptManagement {
  open: boolean;
  type: string; // req | resp
  selectedService: any;
  selectedRootNode: any;
}
const ScriptManagement: React.FC<IScriptManagement> = (props) => {
  const { type, selectedService, open = false, selectedRootNode } = props;
  const { formatMessage } = useI18n();
  const [loading, setLoading] = useState<boolean>(false);
  const [scriptSnippetList, setScriptSnippetList] = useState<ScripSnippetItem[]>([]); // 脚本片段
  const [scriptList, setScriptList] = useState<ScripItem[]>([]); // 脚本列表,包含BF_REQ、SUCC_RESP、EXP_RESP对应的脚本信息
  const [allSchema, setAllSchema] = useState<Array<any>>([]); // 当前服务的请求参数和响应参数的schema
  const [matchStatusValue, setMatchStatusValue] = useState<string>(''); // SUCC_RESP:Success,EXP_RESP:Exception,且type==='resp'时才有值
  const [codeValue, setCodeValue] = useState<string>('');
  const [selectedScript, setSelectedScrip] = useState<ScripItem>(); // 当前操作的script,可能为null(该服务还没有配置脚本)
  const [isEdit, setIsEdit] = useState<boolean>(false);
  const [entityServiceDetail, setEntityServiceDetail] = useState<EntityServiceDetail>(); // 服务全部内容

  const BF_REQ = 'BF_REQ';
  const SUCC_RESP = 'SUCC_RESP';
  const EXP_RESP = 'EXP_RESP';
  const AfterResponseStatus = [
    {
      name: 'Success',
      key: SUCC_RESP,
    },
    {
      name: 'Exception',
      key: EXP_RESP,
    },
  ];

  // 匹配状态改变
  const handleMatchStatusChange = (value: string) => {
    setMatchStatusValue(value);
    const findItem = scriptList.find(
      (item: ScripItem) => item.serviceId === selectedService?.serviceId && item.exePhase === value,
    );
    setSelectedScrip(findItem);
  };

  // code值改变
  const handleCodeChange = (newValue: string) => {
    setCodeValue(newValue);
  };

  // 点击脚本代码片段
  const handleAddSnippet = (snippet: string) => {
    if (isEdit) {
      setCodeValue((prev: string) => {
        if (prev) {
          return `${prev}
${snippet}`; // 此处不要格式化，目的是让新加的snippet另起一行
        } else {
          return snippet;
        }
      });
    }
  };

  // 查询服务全部内容
  const queryEntityServiceDetail = async (param: { serviceId: number }) => {
    try {
      setLoading(true);
      const { success, data } = await getEntityServiceDetail(param);
      if (success) {
        setEntityServiceDetail(data);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  //查询脚本代码片段
  const getScriptSnippetList = async (param: { exePhase: string }) => {
    try {
      setLoading(true);
      const { success, data } = await qryScriptSnippetList(param);
      if (success) {
        const filteredData = data.filter((item: ScripSnippetItem) => item.exePhase.includes(param.exePhase));
        setScriptSnippetList(filteredData);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  //查询执行脚本
  const getScriptList = async (param: { serviceId: number }) => {
    try {
      setLoading(true);
      const { success, data } = await qryScriptListById(param);
      if (success) {
        setScriptList(data);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // 保存执行脚本
  const handleSave = async () => {
    try {
      setLoading(true);
      const param = {
        serviceId: selectedService?.serviceId,
        exePhase: type === 'req' ? BF_REQ : matchStatusValue,
        exeScript: codeValue,
        ...(selectedScript?.exeScriptId ? { exeScriptId: selectedScript?.exeScriptId } : {}), // 新增时不传exeScriptId
      };
      const { success } = await operateServiceScript(param);
      if (success) {
        message.success(formatMessage('SCRIPT.SAVE.SUCCESS'));
        // 获取最新脚本列表
        await getScriptList({ serviceId: selectedService?.serviceId });
        setIsEdit(false);
      }
    } catch (error) {
      message.error(formatMessage('SCRIPT.SAVE.FAILURE'));
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // 取消编辑
  const handleCancel = () => {
    setIsEdit(false);
    // 还原为编辑之前的信息
    setCodeValue(selectedScript?.exeScript || '');
  };

  useEffect(() => {
    if (open && selectedService?.serviceId) {
      // 查询服务全部内容
      queryEntityServiceDetail({ serviceId: selectedService?.serviceId });
      //查询执行脚本
      getScriptList({ serviceId: selectedService?.serviceId });
    }
  }, [selectedService, open]);

  useEffect(() => {
    if (open) {
      if (matchStatusValue && type === 'resp') {
        getScriptSnippetList({ exePhase: matchStatusValue });
      }
      if (!matchStatusValue && type === 'req') {
        getScriptSnippetList({ exePhase: BF_REQ });
      }
    }
  }, [matchStatusValue, open]);

  useEffect(() => {
    setCodeValue(selectedScript?.exeScript || '');
  }, [selectedScript]);

  useEffect(() => {
    if (open) {
      if (type === 'resp') {
        let findItem = null;
        if (matchStatusValue) {
          // 响应参数脚本初始化后，切换matchStatusValue,'Edit' => 'Save'
          findItem = scriptList.find(
            (item: ScripItem) => item.serviceId === selectedService?.serviceId && item.exePhase === matchStatusValue,
          );
        } else {
          // 响应参数脚本初始化时:
          // 1. 若Success和Exception都未设置，则默认展示Success
          // 2. 若Success和Exception只设置其一，则默认展示已设置的
          // 3. 若Success和Exception都已设置，则默认展示在scriptList中靠前的一项
          findItem = scriptList.find(
            (item: ScripItem) => item.serviceId === selectedService?.serviceId && item.exePhase !== BF_REQ,
          );
        }
        if (findItem) {
          setMatchStatusValue(findItem.exePhase);
          setSelectedScrip(findItem);
        } else {
          setMatchStatusValue(SUCC_RESP);
        }
      } else {
        const findItem = scriptList.find(
          (item: ScripItem) => item.serviceId === selectedService?.serviceId && item.exePhase === BF_REQ,
        );
        setSelectedScrip(findItem);
      }
    }
  }, [scriptList, open, type]);

  // 设置allSchema,用于代码提示
  useEffect(() => {
    if (entityServiceDetail) {
      const reqSchema = transferServiceDetailToSchema(entityServiceDetail?.parameters || [], 'request');
      const respSchema = generateRespSchema(
        matchStatusValue === SUCC_RESP ? 'S' : 'E',
        entityServiceDetail,
        selectedService,
      );
      const respHeaderSchema = generateRespHeaderSchema(entityServiceDetail);
      const statusSchema = generateStatusSchema(selectedService, matchStatusValue === EXP_RESP ? 'E' : 'S');

      // 如果是req,仅将请求参数存放上脚本上下文中
      const allSchemaList = [reqSchema, ...(type === 'resp' ? [respSchema, respHeaderSchema] : [])];
      if (statusSchema && type === 'resp') {
        allSchemaList.push(statusSchema);
      }

      setAllSchema(allSchemaList);
    }
  }, [entityServiceDetail, matchStatusValue, selectedService]);

  return (
    <Spin spinning={loading}>
      {type === 'resp' && (
        <div className={styles.responseStatus}>
          <span>{formatMessage('SCRIPT.EXECUTE_AFTER_RESPONSE_STATUS')}</span>
          <Select
            className={styles.statusSelect}
            onChange={handleMatchStatusChange}
            value={AfterResponseStatus.find((item: any) => item.key === matchStatusValue)?.name}
          >
            {AfterResponseStatus.map((i: any) => (
              <Select.Option key={i.key} value={i.key}>
                {i.name}
              </Select.Option>
            ))}
          </Select>
        </div>
      )}
      <div className={styles.content}>
        <div className={styles.leftContent}>
          <div className={styles.codePrompt}>
            <CodePrompt
              language="java"
              readOnly={!isEdit}
              id={type}
              editable={true}
              basicSetup={{
                lineNumbers: true,
                autoCloseTags: true,
                highlightActiveLine: false,
                highlightActiveLineGutter: false,
              }}
              value={codeValue}
              allSchema={allSchema}
              onChange={(newValue: string) => handleCodeChange(newValue)}
            />
          </div>
          <div className={styles.buttonGroup}>
            {isEdit ? (
              <>
                <Button
                  disabled={selectedRootNode?.state !== 'D'}
                  className={styles.buttonItem}
                  type="primary"
                  icon={<SaveOutlined />}
                  onClick={handleSave}
                >
                  {formatMessage('PROJECT.COMMON.SAVE')}
                </Button>
                <Button className={styles.buttonItem} icon={<CloseCircleOutlined />} onClick={handleCancel}>
                  {formatMessage('PROJECT.COMMON.CANCEL')}
                </Button>
              </>
            ) : (
              <Button
                disabled={selectedRootNode?.state !== 'D'}
                className={styles.buttonItem}
                type="primary"
                icon={<EditOutlined />}
                onClick={() => setIsEdit(true)}
              >
                {formatMessage('PROJECT.COMMON.EDIT')}
              </Button>
            )}
          </div>
        </div>

        <div className={styles.snippets}>
          <span>
            {formatMessage('SCRIPT.DESCRIPTION', {
              timing: type === 'req' ? formatMessage('SCRIPT.BEFORE') : formatMessage('SCRIPT.AFTER'),
            })}
          </span>
          <div className={styles.title}>{formatMessage('SCRIPT.SNIPPETS_TITLE')}</div>
          {scriptSnippetList.map((item: ScripSnippetItem) => (
            <div className={styles.scriptItem} key={item.snippetId}>
              <div
                className={styles.iconStyle}
                title={item.snippetName}
                onClick={() => handleAddSnippet(item.snippetContent)}
              >
                {item.snippetName}
              </div>
              <Tooltip title={item.comments}>
                <InfoCircleOutlined />
              </Tooltip>
            </div>
          ))}
        </div>
      </div>
    </Spin>
  );
};

export default ScriptManagement;
