.bssSearchUseCase {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  position: fixed;
  left: 0;
  top: 0;
  z-index: 999;
  .header {
    display: flex;
    align-items: center;
    height: 44px;
    background: #ffffff;
    box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.12);
    padding: 0 12px;
    width: 100%;
    .returnIcon {
      font-size: 18px;
      color: #2d3040;
      cursor: pointer;
    }
    .returnText {
      font-size: 18px;
      font-family: Nunito Sans-Bold;
      font-weight: bold;
      color: #2d3040;
      line-height: 28px;
      margin-left: 8px;
      cursor: pointer;
    }
  }
  .bottom {
    height: calc(100%);
    background: #fff;
    display: flex;
    overflow-y: auto;
    align-items: stretch; /* 使子项高度匹配容器高度 */
    padding: 1rem;
    max-width: 100%;
    overflow-x: hidden;
    
    & > div {
      width: 100%;
      max-width: 100%;
    }
  }
}

.debugDetailContainer {
  width: 100%;
  max-width: 100%;
}

.requestContent {
  width: 100%;
  max-width: 100%;
}

.responseContent {
  width: 100%;
  max-width: 100%;
}
