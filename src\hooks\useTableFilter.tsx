import { useState, useEffect, useRef } from 'react';
import { Input, Button, Space, InputRef } from 'antd';
import { SearchOutlined, FunnelPlotOutlined } from '@ant-design/icons';
import Highlighter from 'react-highlight-words';

// 自定义hooks，用于处理表格的搜索和过滤功能
export const useTableFilter = (dataSource: any[] = []) => {
  // 状态管理：存储搜索文本、已搜索列、过滤后的数据源和过滤对象映射
  const [searchText, setSearchText] = useState('');
  const [searchedColumn, setSearchedColumn] = useState('');
  const [filteredDataSource, setFilteredDataSource] = useState(dataSource);
  const [filteredObjMap, setFilteredObjMap] = useState<any[]>([]);
  const searchInput = useRef<InputRef>(null);

  // 处理普通文本搜索
  const handleSearch = (
    selectedKeys: string[],
    confirm: () => void,
    dataIndex: any,
    customFilter?: (item: any, obj: any) => boolean,
  ) => {
    confirm(); // 确认过滤
    setSearchText(selectedKeys[0]); // 设置搜索文本
    setSearchedColumn(dataIndex); // 设置已搜索列
    updateFilteredObjMap(dataIndex, selectedKeys[0] || '', false, customFilter); // 更新过滤对象映射
  };

  // 处理枚举类型搜索
  const handleEnumSearch = (
    selectedKeys: string[],
    confirm: () => void,
    dataIndex: any,
    customFilter?: (item: any, obj: any) => boolean,
  ) => {
    confirm(); // 确认过滤
    // 将选中的枚举项拼接为字符串
    // const newValue = selectedKeys.map((item: any) => item.split('-')[1]).join(',') || '';
    const newValue = selectedKeys[0]?.split('-')[1] || '';
    updateFilteredObjMap(dataIndex, newValue, true, customFilter); // 更新过滤对象映射
  };

  // 更新过滤对象映射
  const updateFilteredObjMap = (
    dataIndex: any,
    value: string,
    isEnum: boolean,
    customFilter?: (item: any, obj: any) => boolean,
  ) => {
    // 查找现有过滤对象映射中匹配 dataIndex 的索引
    const existingIndex = filteredObjMap.findIndex((item: any) => Object.keys(item)[1] === dataIndex);

    let updatedFilteredMap;

    if (value === '') {
      // 如果 value 为空，从过滤对象映射中移除匹配项
      updatedFilteredMap = filteredObjMap.filter((item: any, index: any) => index !== existingIndex);
    } else {
      // 根据是否存在索引，更新或添加新的过滤对象
      updatedFilteredMap =
        existingIndex !== -1
          ? filteredObjMap.map((item: any, index: any) =>
              index === existingIndex ? { ...item, [dataIndex]: value, customFilter } : item,
            )
          : [...filteredObjMap, { isEnum, [dataIndex]: value, customFilter }];
    }

    setFilteredObjMap(updatedFilteredMap); // 更新过滤对象映射状态
  };

  // 重置搜索
  const handleReset = (clearFilters: () => void) => {
    clearFilters(); // 清除过滤器
  };

  // 重置枚举搜索
  const handleEnumReset = (clearFilters: () => void) => {
    clearFilters(); // 清除过滤器
  };

  // 清除所有过滤条件
  const clearAllFilters = () => {
    setSearchText('');
    setFilteredObjMap([]);
    setFilteredDataSource(dataSource);
  };

  // 使用 useEffect 处理数据源的过滤逻辑
  useEffect(() => {
    // 根据过滤条件过滤数据源
    const filteredData = dataSource.filter((item: any) => {
      return filteredObjMap.every((obj: any) => {
        const isEnum = Object.values(obj)[0]; // 判断是否为枚举类型
        const key = Object.keys(obj)[1]; // 获取过滤字段
        const value = (Object.values(obj)[1] as string).toLowerCase(); // 获取过滤值并转为小写
        if (obj.customFilter) {
          // 使用回调函数进行特殊处理
          return obj.customFilter(item, obj);
        }
        if (isEnum && value !== '') {
          // 如果是枚举类型且值不为空，进行枚举过滤
          return value.includes(item[key].toString().toLowerCase());
        } else {
          // 否则进行普通文本过滤
          const data = item[key] || '';
          return data.toString().toLowerCase().includes(value);
        }
      });
    });
    setFilteredDataSource(filteredData); // 更新过滤后的数据源
  }, [filteredObjMap, dataSource]); // 依赖于 filteredObjMap 和 dataSource

  // 获取普通文本搜索的列属性
  const getColumnSearchProps = (dataIndex: any, customFilter?: (item: any, obj: any) => boolean) => ({
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: any) => (
      <div style={{ padding: 8 }} onKeyDown={(e) => e.stopPropagation()}>
        <Input
          ref={searchInput}
          placeholder={`Search ${dataIndex}`} // 搜索框的占位符
          value={selectedKeys[0]} // 输入框的值
          onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])} // 输入变化时更新选中键
          onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex, customFilter)} // 按下回车时触发搜索
          style={{ marginBottom: 8, display: 'block' }}
        />
        <Space>
          <Button onClick={() => clearFilters && handleReset(clearFilters)} size="small" style={{ width: 90 }}>
            Reset
          </Button>
          <Button
            onClick={() => {
              clearAllFilters();
              confirm();
            }}
            size="small"
            style={{ width: 90 }}
          >
            Reset All
          </Button>
          <Button
            type="primary"
            onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex, customFilter)}
            icon={<SearchOutlined />}
            size="small"
            style={{ width: 90 }}
          >
            Search
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered: boolean) => <SearchOutlined style={{ color: filtered ? '#1677ff' : undefined }} />,
    render: (text: string) =>
      searchedColumn === dataIndex ? (
        <Highlighter
          highlightStyle={{ backgroundColor: '#ffc069', padding: 0 }}
          searchWords={[searchText]}
          autoEscape
          textToHighlight={text ? text.toString() : ''}
        />
      ) : (
        text
      ),
    filteredValue: filteredObjMap.find((obj) => Object.keys(obj)[1] === dataIndex)
      ? [filteredObjMap.find((obj) => Object.keys(obj)[1] === dataIndex)[dataIndex]]
      : null, // 使用 filteredValue 控制过滤状态
  });

  // 获取枚举类型搜索的列属性
  const getColumnEnumSearchProps = (
    dataIndex: any,
    enumTypeSource: any[],
    customFilter?: (item: any, obj: any) => boolean,
  ) => ({
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: any) => (
      <div style={{ padding: 8 }} onKeyDown={(e) => e.stopPropagation()}>
        <div>
          <ul style={{ listStyle: 'none', padding: 0 }}>
            {enumTypeSource.map((item: any) => (
              <li key={item.key}>
                <label>
                  <input
                    type="checkbox"
                    checked={selectedKeys.some((i: any) => i.split('-')[1]?.includes(item.key))}
                    onChange={() => {
                      if (selectedKeys?.length > 0) {
                        const firstKey = selectedKeys[0].split('-')[0];
                        const secondKeyList = selectedKeys[0].split('-')[1].split(',');
                        if (selectedKeys[0].split('-')[1]?.includes(item.key)) {
                          const newSecondKeyList = secondKeyList?.filter(
                            (secondKeyItem: any) => secondKeyItem !== item.key,
                          );
                          const newKeys = [`${firstKey}-${newSecondKeyList.join(',')}`];
                          setSelectedKeys(newKeys);
                        } else {
                          const newSecondKeyList = [...secondKeyList, item.key];
                          const newKeys = [`${firstKey}-${newSecondKeyList?.join(',')}`];
                          setSelectedKeys(newKeys);
                        }
                      } else {
                        const newKeys = [`${dataIndex}-${item.key}`];
                        setSelectedKeys(newKeys);
                      }
                    }}
                    style={{ marginRight: 5 }}
                  />
                  {item.name}
                </label>
              </li>
            ))}
          </ul>
        </div>
        <Space>
          <Button onClick={() => clearFilters && handleEnumReset(clearFilters)} size="small" style={{ width: 90 }}>
            Reset
          </Button>
          <Button
            type="primary"
            onClick={() => handleEnumSearch(selectedKeys as string[], confirm, dataIndex, customFilter)}
            icon={<SearchOutlined />}
            size="small"
            style={{ width: 90 }}
          >
            Search
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered: boolean) => <FunnelPlotOutlined style={{ color: filtered ? '#1677ff' : undefined }} />,
    filteredValue: filteredObjMap.find((obj) => Object.keys(obj)[1] === dataIndex)
      ? [
          `${Object.keys(filteredObjMap.find((obj) => Object.keys(obj)[1] === dataIndex))[1]}-${
            Object.values(filteredObjMap.find((obj) => Object.keys(obj)[1] === dataIndex))[1]
          }`,
        ]
      : null, // 使用 filteredValue 控制过滤状态
  });

  // 返回过滤后的数据源和列搜索属性
  return {
    filteredDataSource,
    getColumnSearchProps,
    getColumnEnumSearchProps,
    clearAllFilters,
  };
};
