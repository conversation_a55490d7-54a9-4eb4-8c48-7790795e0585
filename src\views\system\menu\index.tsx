import { Button, Form, Input, Select, Space, Table } from "antd";
import { useForm } from "antd/es/form/Form";
import type { ColumnsType } from "antd/es/table";
import { useEffect, useRef, useState } from "react";
import api from "../../../api";
import type { Menu } from "../../../types/api";
import type { IAction } from "../../../types/modal";
import { formatDate } from "../../../utils";
import { message, modal } from "../../../utils/AntdGlobal";
import CreateMenu from "./CreateMenu";


export default function MenuList() {
    const [form] = useForm()
    const menuRef = useRef<{
        open: (type: IAction, data?: Menu.editParams | { parentId?: string, orderBy?: number }) => void
    }>(null)

    const menuState = [
        { value: 1, label: '正常' },
        { value: 2, label: '停用' },
    ]

    const menuType: any = {
        1: '菜单',
        2: '按钮',
        3: '页面'
    }


    const [data, setData] = useState<Menu.menuItem[]>([])

    useEffect(() => {
        getMenuList()
    }, [])

    const getMenuList = async () => {
        try {
            const data = await api.getMenuList(form.getFieldsValue())
            setData(data)
        } catch (error) {
            console.log(error);
        }
    }
    const handleReset = () => {
        form.resetFields()
    }

    const handleCreate = () => {
        menuRef.current?.open('create', { orderBy: data.length || 0 })
    }

    const handleSubCreate = (record: Menu.menuItem) => {
        menuRef.current?.open('create', { parentId: record._id, orderBy: record.children?.length || 0 })
    }

    const handleEdit = (record: Menu.menuItem) => {
        menuRef.current?.open('edit', record)
    }

    const handleDelete = (record: Menu.menuItem) => {
        modal.confirm({
            title: "删除确认",
            content: <span>{`确认删除该${menuType[record.menuType]}吗？`}</span>,
            onOk: () => {
                handleUserDelSubmit(record._id)
            }
        })
    }
    const handleUserDelSubmit = async (id: string) => {
        await api.delMenu({ _id: id })
        message.success('删除成功')
        getMenuList()
    }

    const columns: ColumnsType<Menu.menuItem> = [
        {
            title: "菜单名称",
            dataIndex: 'menuName',
            key: "menuName",
            align: 'center'
        },
        {
            title: "图标",
            dataIndex: 'icon',
            key: "icon",
            align: 'center'
        },
        {
            title: "菜单类型",
            dataIndex: 'menuType',
            key: "menuType",
            align: 'center',
            render(menuType: number) {
                return {
                    1: '菜单',
                    2: '按钮',
                    3: '页面'
                }[menuType]
            }
        },
        {
            title: "权限标识",
            dataIndex: 'menuCode',
            key: "menuCode",
            align: 'center',
        },
        {
            title: "路由地址",
            dataIndex: 'path',
            key: "path",
            align: 'center',
        },
        {
            title: "组件名称",
            dataIndex: 'component',
            key: "component",
            align: 'center',
        },
        {
            title: "创建时间",
            dataIndex: 'createTime',
            key: "createTime",
            align: 'center',
            render(createTime: string) {
                return formatDate(createTime)
            }
        },
        {
            title: "操作",
            key: "action",
            width: 200,
            align: 'center',
            render(record) {
                return (
                    <Space>
                        <Button type="text" onClick={() => handleSubCreate(record)}>新增</Button>
                        <Button type="text" onClick={() => handleEdit(record)}>编辑</Button>
                        <Button type="text" danger onClick={() => handleDelete(record)}>删除</Button>
                    </Space>
                )
            }
        },
    ]
    return (
        <>
            <Form className="search" layout="inline" form={form} initialValues={{ menuState: 1 }}>
                <Form.Item name="menuName" label="菜单名称">
                    <Input placeholder="请输入菜单名称" />
                </Form.Item>
                <Form.Item name="menuState" label="菜单状态">
                    <Select style={{ width: 100 }} options={menuState} />
                </Form.Item>
                <Form.Item >
                    <Button type="primary" className="mr10" onClick={getMenuList}>搜索</Button>
                    <Button type="default" onClick={handleReset}>重置</Button>
                </Form.Item>
            </Form>
            <div className="table">
                <div className="header">
                    <div className="title">菜单列表</div>
                    <div className="action">
                        <Button type="primary" onClick={handleCreate}>新增</Button>
                    </div>
                </div>
                <Table
                    bordered
                    pagination={false}
                    rowKey='_id'
                    columns={columns}
                    dataSource={data}
                />
            </div>
            <CreateMenu ref={menuRef} update={getMenuList} />
        </>
    )
}