import { Menu } from "antd";
import { useMemo, useState } from "react";
import { useModel } from "umi";
import { UseCaseBeQueriedListPropsType } from "@/services/typing";
import MenuItemLabel from "./MenuItemLabel";

const SearchUseCaseMenuList = () => {
  const {
    useCaseMenuListInModel,
    setServiceCodeInModel,
    setUseCaseIdInModel,
    setMenuItemInModel,
    deleteUseCasePageList,

    openKeysInModel,
    setOpenKeysInModel,
    selectedKeysInModel,
    setSelectedKeysInModel,
  } = useModel("useSearchUseCaseModel");

  // 删除 menuItem
  const onDeleteConfirm = (el: UseCaseBeQueriedListPropsType) => {
    deleteUseCasePageList(String(el?.useCaseId));
  };

  // 点击 item 触发
  const onMenuItemClick = (values: UseCaseBeQueriedListPropsType) => {
    setServiceCodeInModel(values.serviceCode);
    setUseCaseIdInModel(values.useCaseId);
    setMenuItemInModel(values);
  };

  // 数据归并，按照日期合并
  const mergedArrFn = (useCaseList: UseCaseBeQueriedListPropsType[]) => {
    if (useCaseList?.length > 0) {
      let arr: any = {};
      // 生成日期为key的对象
      useCaseList?.forEach((el) => {
        const { createdDate = "" } = el;
        if (createdDate && arr?.hasOwnProperty?.(createdDate)) {
          arr[createdDate].push(el);
        } else {
          arr[createdDate] = [el];
        }
      });

      // 根据日期排序
      const sortedDates = Object.keys(arr)?.sort((a, b) => {
        return new Date(b).getTime() - new Date(a).getTime();
      });

      if (selectedKeysInModel?.length === 0) {
        const curVal = sortedDates?.[0];
        setOpenKeysInModel([curVal]);
      }

      // 标记查找selectedKeys
      let isSelected: boolean = false;
      const mergedArr = sortedDates.map((el: string, index: number) => {
        return {
          key: el,
          label: el,
          children: arr[el].map(
            (els: UseCaseBeQueriedListPropsType, idx: number) => {
              if (String(els?.useCaseId) === String(selectedKeysInModel?.[0])) {
                isSelected = true;

                // 筛选公共 openKeys
                const newOpenKeys = openKeysInModel?.filter((el) => {
                  return sortedDates.includes(el);
                });
                if (!newOpenKeys.includes(el)) {
                  setOpenKeysInModel([...newOpenKeys, el]);
                }
              }
              return {
                key: els.useCaseId,
                label: (
                  <MenuItemLabel
                    key={els.useCaseId}
                    menuItemData={els}
                    onDeleteConfirm={onDeleteConfirm}
                    onMenuItemClick={(val) => {
                      onMenuItemClick(val);
                      setSelectedKeysInModel([String(els.useCaseId)]);
                    }}
                  />
                ),
              };
            }
          ),
        };
      });

      if (!isSelected) {
        const curVal = sortedDates?.[0];
        onMenuItemClick(arr[curVal]?.[0]);
        setSelectedKeysInModel([String(arr[curVal]?.[0].useCaseId)]);
        if (!openKeysInModel.includes(curVal)) {
          setOpenKeysInModel([...openKeysInModel, curVal]);
        }
      }

      return mergedArr;
    }
    setUseCaseIdInModel("");
    return [];
  };

  const menuList = useMemo(() => {
    // 按日期进行归并
    return mergedArrFn(useCaseMenuListInModel);
  }, [useCaseMenuListInModel]);

  return (
    <Menu
      mode="inline"
      items={menuList}
      openKeys={openKeysInModel}
      onOpenChange={(values) => {
        setOpenKeysInModel(values);
        // console.log('values', values);
      }}
      selectedKeys={selectedKeysInModel}
    />
  );
};

export default SearchUseCaseMenuList;
