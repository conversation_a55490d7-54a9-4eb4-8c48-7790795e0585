import request from "../utils/request";
import type { DashBoardType, Login, ResultData, User, Dept, Menu, Role, Order } from '../types/api';

export default {
    //登录
    login(params: Login.params) {
        return request.post('/api/users/login', params)
    },
    //获取用户信息
    getUserInfo() {
        return request.get<User.userItem>('/api/users/getUserInfo')
    },
    //获取工作台汇总数据
    getReportData() {
        return request.get<DashBoardType.ReportData>('/api/order/dashboard/getReportData')
    },
    //获取折线图数据
    getLineData() {
        return request.get<DashBoardType.LineData>('/api/order/dashboard/getLineData')
    },
    //获取饼图数据
    getPieCityData() {
        return request.get<DashBoardType.PieData[]>('/api/order/dashboard/getPieCityData')
    },
    //获取饼图数据
    getPieAgeData() {
        return request.get<DashBoardType.PieData[]>('/api/order/dashboard/getPieAgeData')
    },
    //获取雷达图数据
    getRadarData() {
        return request.get<DashBoardType.RadarData>('/api/order/dashboard/getRadarData')
    },
    //获取用户列表
    getUserList(params: User.Params) {
        return request.get<ResultData<User.userItem>>('/api/users/list', params)
    },
    //获取所有用户列表
    getAllUserList() {
        return request.get<User.userItem[]>('/api/users/all/list')
    },
    //创建用户
    createUser(params: User.createParams) {
        return request.post('/api/users/create', params)
    },
    //编辑用户
    editUser(params: User.editParams) {
        return request.post('/api/users/edit', params)
    },
    //删除用户
    delUser(params: { userIds: number[] }) {
        return request.post('/api/users/delete', params)
    },
    //获取用户权限列表
    getPermissionList() {
        return request.get<{ buttonList: string[]; menuList: Menu.menuItem[] }>('/api/users/getPermissionList')
    },
    //部门管理
    //部门列表
    getDeptList(params?: Dept.deptParams) {
        return request.get<Dept.deptItem[]>('/api/dept/list', params)
    },
    //创建部门
    createDept(params: Dept.createParams) {
        return request.post('/api/dept/create', params)
    },
    //修改部门
    editDept(params: Dept.editParams) {
        return request.post('/api/dept/edit', params)
    },
    //删除部门
    DelDept(params: { _id: string }) {
        return request.post('/api/dept/delete', params)
    },
    //菜单管理
    //菜单列表
    getMenuList(params?: Menu.menuParams) {
        return request.get<Menu.menuItem[]>('/api/menu/list', params)
    },
    //创建菜单
    createMenu(params: Menu.createParams) {
        return request.post('/api/menu/create', params)
    },
    //编辑菜单
    editMenu(params: Menu.editParams) {
        return request.post('/api/menu/edit', params)
    },
    //删除菜单
    delMenu(params: { _id: string }) {
        return request.post('/api/menu/delete', params)
    },
    //获取角色列表
    getRoleList(params: Role.Params) {
        return request.get<ResultData<Role.RoleItem>>('/api/roles/list', params)
    },
    //创建角色
    createRole(params: Role.CreateParams) {
        return request.post('/api/roles/create', params)
    },
    //修改角色
    editRole(params: Role.EditParams) {
        return request.post('/api/roles/edit', params)
    },
    //删除角色
    delRole(params: Role.DelParams) {
        return request.post('/api/roles/delete', params)
    },
    //设置权限
    updatePermission(params: Role.Permission) {
        return request.post('/api/roles/update/permission', params)
    },
    //获取所有角色列表
    getAllRoleList() {
        return request.get<Role.RoleItem[]>('/api/roles/allList')
    },
    //订单
    //获取订单列表
    getOrderList(params: Order.Params) {
        return request.get<ResultData<Order.OrderItem>>('/api/order/list', params)
    },
    //获取城市列表
    getCityList() {
        return request.get<any>('/api/order/cityList')
    },
    //获取车型列表
    getVehicleList() {
        return request.get<any>('/api/order/vehicleList')
    },
    //创建订单
    createOrder(params: Order.CreateParams) {
        return request.post('/api/order/create', params)
    },
    //删除订单
    deleteOrder(params: { _id: string }) {
        return request.post('/api/order/delete', params)
    },
    //获取订单详情
    getOrderDetail(orderId: string) {
        return request.get<any>(`/api/order/detail/${orderId}`)
    },
    //更新打点信息
    updateOrderInfo(params: Order.OrderRoute) {
        return request.post('/api/order/edit', params)
    },
    //下载文件
    exportFile(params: Order.SearchParams) {
        return request.downLoad('/api/order/orderExport', params, '订单列表')
    },
}