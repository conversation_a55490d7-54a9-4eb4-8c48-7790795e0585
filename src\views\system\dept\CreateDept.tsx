import { Form, Input, Modal, Select, TreeSelect } from "antd";
import { useEffect, useImperativeHandle, useState } from "react";
import type { IAction } from "../../../types/modal";
import type { Dept } from "../../../types/api";
import api from "../../../api";
import { message } from "../../../utils/AntdGlobal";

export default function CreateDept(props: any) {
    const [form] = Form.useForm()
    const [action, setAction] = useState<IAction>('create')
    const [visible, setVisible] = useState(false)
    const [deptList, setDeptList] = useState<Dept.deptItem[]>([])
    const [userList, setUserList] = useState<any>([])

    useImperativeHandle(props.ref, () => {
        return { open }
    })
    const open = (type: IAction, data?: Dept.editParams | { parentId: string }) => {
        setVisible(true)
        setAction(type)
        getDeptList()
        if (data) {
            form.setFieldsValue(data)
        }
    }
    useEffect(() => {
        getDeptList()
        getAllUserList()
    }, [])
    const getDeptList = async () => {
        const data = await api.getDeptList()
        setDeptList(data)
    }
    const getAllUserList = async () => {
        const data = await api.getAllUserList()
        const newData = data.map((item) => {
            return { value: item.userName, key: item._id, label: item.userName }
        })
        setUserList(newData)
    }

    const handleSubmit = async () => {
        const valid = await form.validateFields()
        if (valid) {
            if (action === 'create') {
                await api.createDept(form.getFieldsValue())
                message.success("创建成功")
            } else {
                await api.editDept(form.getFieldsValue())
                message.success("修改成功")
            }
            handleCancel()
            props.update()
        }
    }
    const handleCancel = () => {
        setVisible(false)
        form.resetFields()
    }
    return (
        <>
            <Modal
                title={action === 'create' ? '创建部门' : '编辑部门'}
                width={800}
                open={visible}
                okText="确定"
                cancelText="取消"
                onOk={handleSubmit}
                onCancel={handleCancel}
            >
                <Form
                    form={form}
                    labelCol={{ span: 4 }}
                    labelAlign="right"
                >
                    <Form.Item name="_id" hidden><Input /></Form.Item>
                    <Form.Item name="parentId" label="上级部门">
                        <TreeSelect
                            placeholder="请选择上级部门"
                            allowClear
                            treeDefaultExpandAll
                            fieldNames={{ label: 'deptName', value: '_id' }}
                            treeData={deptList}
                        />
                    </Form.Item>
                    <Form.Item name="deptName" label="部门名称" rules={[
                        { required: true, message: "请输入部门名称" }
                    ]}>
                        <Input placeholder="请输入部门名称" />
                    </Form.Item>
                    <Form.Item name="userName" label="负责人" rules={[
                        { required: true, message: "请选择部门负责人" }
                    ]}>
                        <Select options={userList} />
                    </Form.Item>
                </Form>
            </Modal>
        </>
    )
}