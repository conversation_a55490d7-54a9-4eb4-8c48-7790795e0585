import useI18n from '@/hooks/useI8n';
import OperationIconTitle from '../OperationIconTitle';
import { TableColumnsType, Spin, message } from 'antd';
import EnumManagementDrawer from './EnumManagementDrawer';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import OperationGroup from '../OperationGroup';
import {
  queryDomainEnumListService,
  queryDomainEnumDetailService,
  deleteDomainEnumListService,
} from '@/services/enumListService';
import { useModel } from 'umi';
import { CreationSource } from './const';
import ResizableTable from '../ResizableTable';
import { useTableFilter } from '@/hooks/useTableFilter';

interface IEnumManagementList {
  selectedDomainNode: any;
  selectedRootNode: any; // 根节点
  isProduct: boolean;
  onSelectModelObj: any;
  refreshData: () => any;
}

interface IqueryParams {
  pageNo: number;
  pageSize: number;
}

const EnumManagementList: React.FC<IEnumManagementList> = ({
  selectedDomainNode = {},
  selectedRootNode = {},
  isProduct = false,
  onSelectModelObj = () => {},
  refreshData = () => {},
}) => {
  const { formatMessage } = useI18n();

  const isDraft = useMemo(() => {
    return selectedRootNode?.state === 'D';
  }, [selectedRootNode]);

  const { domainObjId = null } = selectedDomainNode;

  const Status: any = useMemo(() => {
    return [
      { name: 'Data Model', key: 'M' },
      { name: 'Constant', key: 'T' },
      { name: 'Dubbo API', key: 'D' },
    ];
  }, []);

  const { enumListQueryObj = { pageNo: 1, pageSize: 5 }, setEnumListQueryObj = () => {} } = useModel(
    'useEnumListTableQueryObjModel',
  );

  // loading...
  const [loading, setLoading] = useState(false);

  // 记录当前查询的数据总数
  const [total, setTotal] = useState(0);

  const [tableDataSource, setTableDataSource] = useState<any>([]);
  const [allTableDataSource, setAllTableDataSource] = useState<any>([]);

  // 记录查询参数
  const [queryParam, setQueryParam] = useState<IqueryParams>({
    pageNo: 1,
    pageSize: 5,
  });

  // 用于 enum的抽屉展示
  const enumManagementDrawerRef = useRef({
    enterAddMode: (id: number | null) => {},
    enterEditMode: (el: any, id: number | null) => {},
  });
  // 使用 useMemo 确保 dataSource 是稳定的引用
  const memoizedDataSource = useMemo(
    () => (Array.isArray(allTableDataSource) ? allTableDataSource : []),
    [allTableDataSource],
  );
  // 自定义hooks，支持表格列筛选并分页展示
  const { filteredDataSource, getColumnSearchProps, getColumnEnumSearchProps, clearAllFilters } =
    useTableFilter(memoizedDataSource);

  const onClickGroup: any = {
    add: (id: number | null) => {
      enumManagementDrawerRef?.current?.enterAddMode(id);
    },
    edit: async (enumId: number) => {
      try {
        await queryDomainEnumDetailService({ enumId }).then((res: any) => {
          const { success = false, data = {} } = res;
          if (success) {
            enumManagementDrawerRef?.current?.enterEditMode(data, domainObjId);
          }
        });
      } catch (error) {}
    },
    delete: async (enumId: number) => {
      try {
        await deleteDomainEnumListService({ enumId, domainObjId }).then((res) => {
          const { success = false } = res;
          if (success) {
            if (allTableDataSource.length === 1 && queryParam.pageNo > 1) {
              setEnumListQueryObj({
                pageNo: queryParam.pageNo - 1,
                pageSize: queryParam.pageSize,
              });
            } else {
              getEnumManagementListDataFn(domainObjId, enumListQueryObj);
            }
            message.success(formatMessage('ENUMMANAGEMENTLIST.DELETE.SUCCESS'));
            refreshData();
          }
        });
      } catch (error) {}
    },
  };

  //  设置 table 列
  const columns: TableColumnsType = [
    {
      dataIndex: 'enumName',
      width: '15%',
      ellipsis: true,
      title: formatMessage('ENUMMANAGEMENTLIST.TABLE.NAME'),
      ...getColumnSearchProps('enumName'),
      render: (text, record, index) => {
        return (
          <span
            style={{ color: '#47e', cursor: 'pointer' }}
            onClick={() => {
              onSelectModelObj(record);
            }}
          >
            {text}
          </span>
        );
      },
    },
    {
      dataIndex: 'enumType',
      width: '15%',
      ellipsis: true,
      title: formatMessage('ENUMMANAGEMENTLIST.TABLE.TYPE'),
      render: (_: string, record: any) => {
        const matchedItem = Status.find((i: { key: string; name: string }) => i.key === record.enumType);
        return matchedItem ? matchedItem.name : '';
      },
      ...getColumnEnumSearchProps('enumType', Status),
    },
    {
      dataIndex: 'creationSrc',
      width: '30%',
      ellipsis: true,
      title: formatMessage('ENUMMANAGEMENTLIST.TABLE.CREATIONSOURCE'),
      render: (_: string, record: any) => {
        const matchedItem = CreationSource.find((i: { key: string; name: string }) => i.key === record.creationSrc);
        return matchedItem ? matchedItem.name : '';
      },
      ...getColumnEnumSearchProps('creationSrc', CreationSource),
    },
    {
      dataIndex: 'comments',
      width: '30%',
      ellipsis: true,
      title: formatMessage('ENUMMANAGEMENTLIST.TABLE.DESC'),
      ...getColumnSearchProps('comments'),
    },
    {
      dataIndex: 'opt',
      width: '10%',
      ellipsis: true,
      title: formatMessage('ENUMMANAGEMENTLIST.TABLE.OPT'),
      render: (text, record, index) => {
        const { enumId } = record;

        if (isDraft) {
          if (isProduct) {
            return (
              <OperationGroup
                btnList={[
                  {
                    value: 'edit',
                    onClick: () => {
                      onClickGroup?.edit(enumId, domainObjId);
                    },
                  },
                  {
                    value: 'delete',
                    title: formatMessage('ENUMMANAGEMENTLIST.TABLE.DELTIP'),
                    onClick: () => {
                      onClickGroup?.delete(enumId);
                    },
                  },
                ]}
              />
            );
          }
          if (record?.creationSrc === 'C') {
            return (
              <OperationGroup
                btnList={[
                  {
                    value: 'edit',
                    onClick: () => {
                      onClickGroup?.edit(enumId, domainObjId);
                    },
                  },
                  {
                    value: 'delete',
                    title: formatMessage('ENUMMANAGEMENTLIST.TABLE.DELTIP'),
                    onClick: () => {
                      onClickGroup?.delete(enumId);
                    },
                  },
                ]}
              />
            );
          }
          return (
            <OperationGroup
              btnList={[
                {
                  value: 'edit',
                  onClick: () => {
                    onClickGroup?.edit(enumId, domainObjId);
                  },
                },
              ]}
            />
          );
        }
        return <></>;
      },
    },
  ];

  // 接口数据查询
  const getEnumManagementListDataFn = async (id: any, params: IqueryParams) => {
    setLoading(true);
    try {
      await queryDomainEnumListService({ domainObjId: id, pageNo: 1, pageSize: 1000 }).then((res) => {
        const { success = false, data = {} } = res;
        if (success) {
          const { totalCount = 0, pageNo = 0, pageSize = 0, list = [] } = data;
          setAllTableDataSource(list);
          setQueryParam({
            pageNo: params?.pageNo,
            pageSize: params?.pageSize,
          });
          setTotal(totalCount);
        }
      });
    } catch (error) {
      //
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    if (domainObjId) {
      setQueryParam({
        pageNo: enumListQueryObj?.pageNo,
        pageSize: enumListQueryObj?.pageSize,
      });
      // getEnumManagementListDataFn(domainObjId, enumListQueryObj);
    }

    return () => {};
  }, [enumListQueryObj]);

  useEffect(() => {
    if (domainObjId && enumListQueryObj) {
      getEnumManagementListDataFn(domainObjId, enumListQueryObj);
    }
  }, [domainObjId]);

  useEffect(() => {
    return () => {
      setEnumListQueryObj({
        pageNo: 1,
        pageSize: 5,
      });
    };
  }, [domainObjId]);

  useEffect(() => {
    if (filteredDataSource) {
      setTableDataSource(filteredDataSource);
      setTotal(filteredDataSource.length);
    }
  }, [filteredDataSource]);

  const afterSubmit = () => {
    if (domainObjId) {
      getEnumManagementListDataFn(domainObjId, enumListQueryObj);
    }
    refreshData();
  };

  useEffect(() => {
    // 重置表格列的筛选条件
    clearAllFilters();
  }, [selectedDomainNode]);

  return (
    <div>
      <div>
        <OperationIconTitle
          title={formatMessage('ENUMMANAGEMENTLIST.TABLE.TITLE')}
          opt={formatMessage('ENUMMANAGEMENTLIST.TABLE.ADD')}
          handleClick={() => {
            onClickGroup?.add(domainObjId);
          }}
          type={isDraft ? 'add' : ''}
        />
        <Spin spinning={loading}>
          <ResizableTable
            size="small"
            dataSource={(tableDataSource || [])?.map((el: any, index: number) => ({
              ...el,
              key: `enumManagementList-${index}-${el?.enumId}`,
            }))}
            columns={columns}
            rowKey="key"
            pagination={{
              hideOnSinglePage: true,
              showSizeChanger: true,
              pageSizeOptions: [5, 10, 15, 20],
              total,
              current: queryParam?.pageNo,
              pageSize: queryParam?.pageSize,
              showTotal: (total) => formatMessage('PROJECT.COMMON.PAGINATION.TOTAL', { total }),
              onChange: (page: any, pageSize: any) => {
                setEnumListQueryObj({
                  ...enumListQueryObj,
                  pageSize,
                  pageNo: page,
                });
              },
            }}
          />
        </Spin>
        <EnumManagementDrawer
          ref={enumManagementDrawerRef}
          domainObjId={domainObjId}
          isProduct={isProduct}
          afterSubmit={afterSubmit}
        />
      </div>
    </div>
  );
};

export default EnumManagementList;
