.activeLineContainer {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  background-color: #ffffff;
  border: 1px solid #e0e0e0;
  padding: 12px 16px;
  box-sizing: border-box;
}

.topContentDraf {
  display: flex;
  align-items: center;
  margin-top: 5px;
  padding-bottom: 7px;
  border-bottom: 1px solid #e0e0e0;
  :global {
    .ant-select-selector {
      border: 1px solid #75B80B !important;
      border-radius: 5px;
      background-color: #C9F982 !important;
      color: #70B602;
    }
  }
}

.topContentRelease {
  display: flex;
  align-items: center;
  margin-top: 5px;
  padding-bottom: 7px;
  border-bottom: 1px solid #e0e0e0;
  :global {
    .ant-select-selector {
      border: 1px solid #4477ee !important;
      border-radius: 5px;
      background-color: #b2e4fc !important;
      color: #4477ee;
    }
  }
}

.hide {
  display: none;
}

.selectContent {
  margin-left: 20px;
}

.contentName{
  font-size: 16px;
  font-family: Nunito Sans-Bold;
  font-weight: bold;
  color: #2d3040;
}

.domainObjContent {
  .title {
    font-size: 16px;
    font-family: Nunito Sans-Bold;
    font-weight: bold;
    color: #2d3040;
    line-height: 24px;
  }
}

.domainObjName {
  color: #47e;
  cursor: pointer;
}

.iconStyle {
  color: #47e;
  cursor: pointer;
}

.versionIcon1 {
  margin-left: 20px;
  color: #21f17f;
  cursor: pointer;
}

.versionIcon2 {
  margin-left: 20px;
  color: #47e;
  cursor: pointer;
}

.versionIcon3 {
  margin-left: 20px;
  color: rgb(235, 138, 100);
  cursor: pointer;
  font-size: 16px;
}

.hotfixVersionColor {
  color: #f12121;
}

.arrowRight {
  padding: 0 10px;
}
