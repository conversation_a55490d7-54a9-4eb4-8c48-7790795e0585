/*
 * @lastTime: 2021-03-05 15:29:11
 * @Description: 同步hooks
 */

// 应用场景: eg. 事件监听的时候,函数里面拿不到最新的state值
// eg.其他场景下涉及到函数闭包之类的

import { useEffect, useState, useCallback } from "react";

const useSyncCallback = (callback: (...parameters: any) => void) => {
  const [proxyState, setProxyState] = useState({ current: false });
  const [params, setParams] = useState<Array<any>>([]);

  const Func = useCallback(
    (...args: any[]) => {
      setProxyState({ current: true });
      setParams(args);
    },
    [proxyState]
  );

  useEffect(() => { 
    if (proxyState.current === true) {
      setProxyState({ current: false });
    }
  }, [proxyState]);

  useEffect(() => {
    proxyState.current && callback(...params);
  });

  return Func;
};

export default useSyncCallback;
