import OperationIconTitle from '@/components/OperationIconTitle';
import ResizableTable from '@/components/ResizableTable';
import useI18n from '@/hooks/useI8n';
import { useTableFilter } from '@/hooks/useTableFilter';
import { TableColumnsType } from 'antd';
import { useMemo } from 'react';

interface IConstantListTable {
  constantListDataSource: any[];
}

const ConstantListTable: React.FC<IConstantListTable> = ({ constantListDataSource = [] }) => {
  const { formatMessage } = useI18n();
  // 使用 useMemo 确保 dataSource 是稳定的引用
  const memoizedDataSource = useMemo(
    () => (Array.isArray(constantListDataSource) ? constantListDataSource : []),
    [constantListDataSource],
  );
  // 自定义hooks，支持表格列筛选并分页展示
  const { filteredDataSource, getColumnSearchProps, getColumnEnumSearchProps } = useTableFilter(memoizedDataSource);

  const columns: TableColumnsType = [
    {
      dataIndex: 'constValue',
      width: '50%',
      ellipsis: true,
      title: formatMessage('ENUMMANAGEMENTLIST.TABLE.VALUE'),
      ...getColumnSearchProps('constValue'),
    },
    {
      dataIndex: 'constName',
      width: '50%',
      ellipsis: true,
      title: formatMessage('ENUMMANAGEMENTLIST.TABLE.NAME'),
      ...getColumnSearchProps('constName'),
    },
  ];

  return (
    <div>
      <OperationIconTitle title={formatMessage('ENUMMANAGEMENTLIST.DRAWER.CONSTANTLIST')} />
      <ResizableTable
        size="small"
        rowKey="key"
        columns={columns}
        pagination={{
          hideOnSinglePage: true,
          pageSize: 5,
          showTotal: (total) => formatMessage('PROJECT.COMMON.PAGINATION.TOTAL', { total }),
        }}
        dataSource={filteredDataSource?.map((el: any, index: number) => ({
          ...el,
          key: `constantListTable-${index}-${el?.constValue}`,
        }))}
      />
    </div>
  );
};

export default ConstantListTable;
