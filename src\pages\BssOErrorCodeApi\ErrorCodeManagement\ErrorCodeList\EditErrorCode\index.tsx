import { Button, Drawer, Form, Input, message, Select, Space, Spin } from 'antd';
import TextArea from 'antd/es/input/TextArea';
import { useForm } from 'antd/es/form/Form';
import { useState, useEffect } from 'react';
import useI18n from '@/hooks/useI8n';

import { updateErrorCode } from '@/services/errorCodeService';
import { useModel } from 'umi';
import styles from '../index.less';
import { debounce } from 'lodash';
import CodePrompt from '@/components/CodePrompt';
import { CREATIONSOURCE } from '../../constant';

interface IEditErrorCode {
  editOpen: boolean;
  odhDomainTreeData: any;
  selectedErrorCode: any;
  isProduct: boolean;
  setEditOpen: (open: boolean) => void;
}

const EditErrorCode: React.FC<IEditErrorCode> = (props) => {
  const { editOpen, odhDomainTreeData, selectedErrorCode, isProduct, setEditOpen } = props;
  const { queryErrorCodeListParams, queryErrorCodeList } = useModel('useErrorCodeModel');
  const { formatMessage } = useI18n();
  const [form] = useForm();
  const [spining, setSpining] = useState<boolean>(false);
  const [defaultMatchConditions, setDefaultMatchConditions] = useState<string>('');
  const [reasonParams, setReasonParams] = useState<Array<{ paramName: string; paramValue: string }>>([]);

  const editRule = isProduct || selectedErrorCode?.creationSrc !== 'P'; // 项目不能修改产品定义的错误码匹配规则

  const onClose = () => {
    setEditOpen(false);
    setDefaultMatchConditions('');
    setReasonParams([]);
  };

  const editErrorCode = async (params: {
    errorCodeId: number;
    errorReason?: string;
    defaultMatchConditions?: string;
    defaultReasonParams?: string[];
  }) => {
    try {
      setSpining(true);
      const { success } = await updateErrorCode(params);
      if (success) {
        message.success(formatMessage('PROJECT.COMMON.EDITSUCCESS'));
        setEditOpen(false);
        queryErrorCodeList({
          ...queryErrorCodeListParams,
        });
      }
    } catch (error) {
      console.error(error);
    } finally {
      setSpining(false);
    }
  };

  const checkErrorCodeData = () => {
    form.validateFields().then(() => {
      const params = {
        errorCodeId: selectedErrorCode?.errorCodeId,
        errorReason: form.getFieldValue('errorReason'),
        ...(!isProduct
          ? {
              defaultMatchConditions: defaultMatchConditions,
              defaultReasonParams: reasonParams.map((param) => param.paramValue),
            }
          : {}),
        defaultMatchConditions: defaultMatchConditions,
        defaultReasonParams: reasonParams.map((param) => param.paramValue),
      };
      editErrorCode(params);
    });
  };

  // 根据reason生成的param的值改变
  const handleCodeChange = (index: number, newValue: string) => {
    setReasonParams((prev) => prev.map((item, i) => (i === index ? { ...item, paramValue: newValue } : item)));
  };

  // 使用正则表达式提取 {} 中的内容
  const extractContent = (str: string) => {
    const matches = str.match(/\{(.*?)\}/g);
    return matches ? matches.map((match) => match.slice(1, -1)) : [];
  };

  const handleErrorReasonChange = debounce((value: string) => {
    const reasonParamsContent = extractContent(value); // 提取内容

    // 创建一个新的 reasonParams 数组
    const newReasonParams = reasonParamsContent.map((item, index) => {
      const existingParam = reasonParams.find((param) => param.paramName === `reasonParam${index}`);

      // 如果存在相同的 paramName，保留原有的 paramValue，否则创建新的对象
      return existingParam ? { ...existingParam } : { paramName: `reasonParam${index}`, paramValue: '' };
    });

    setReasonParams(newReasonParams);
  }, 300); // 300ms 防抖

  useEffect(() => {
    if (editOpen) {
      form.setFieldsValue(selectedErrorCode);
      setDefaultMatchConditions(selectedErrorCode.defaultMatchConditions || '');
      if (selectedErrorCode?.defaultReasonParams?.length) {
        const reasonParamsContent = selectedErrorCode?.defaultReasonParams;
        setReasonParams(
          reasonParamsContent.map((item: string, index: number) => ({
            paramName: `reasonParam${index}`,
            paramValue: item,
          })),
        );
      } else {
        // 错误码可能是导入进来的，只有errorReason，没有defaultReasonParams，所以根据errorReason解析一次
        if (selectedErrorCode?.errorReason) {
          handleErrorReasonChange(selectedErrorCode?.errorReason);
        }
      }
    }
  }, [editOpen, selectedErrorCode]);

  return (
    <Drawer
      title={formatMessage('ERRORCODE.EDIT.TITLE')}
      onClose={onClose}
      maskClosable={false}
      open={editOpen}
      width={900}
      afterOpenChange={() => {
        form.resetFields();
      }}
      footer={
        <Space style={{ float: 'right' }}>
          <Spin spinning={spining}>
            <Button type="primary" onClick={checkErrorCodeData}>
              {formatMessage('PROJECT.COMMON.SUBMIT')}
            </Button>
          </Spin>
          <Button onClick={onClose}>{formatMessage('PROJECT.COMMON.CANCEL')}</Button>
        </Space>
      }
    >
      <Form
        form={form}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
        initialValues={{
          ...selectedErrorCode,
          // ...['1', '2'].map((item: string, index: number) => ({ [`reasonParam${index}`]: item })),
        }}
      >
        <Form.Item
          label={formatMessage('ERRORCODE.COMMON.APPLYDOMAIN')}
          name="domainId"
          rules={[{ required: true, message: '' }]}
        >
          <Select showSearch disabled>
            <Select.Option key="all" value="all">
              All
            </Select.Option>
            {odhDomainTreeData.map((i: any) => (
              <Select.Option key={i.domainId} value={i.domainId}>
                {i.domainName}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item label={formatMessage('ERRORCODE.COMMON.ERRORCODE')} name="errorCode">
          <Input disabled />
        </Form.Item>
        <Form.Item label={formatMessage('ERRORCODE.COMMON.CREATIONSOURCE')} name="creationSrc">
          <Select defaultValue={CREATIONSOURCE[0]} disabled>
            {CREATIONSOURCE.map((i: any) => (
              <Select.Option key={i.key} value={i.key}>
                {i.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          label={formatMessage('ERRORCODE.COMMON.ERRORREASON')}
          name="errorReason"
          rules={[{ required: true, message: '' }]}
          extra={formatMessage('ERRORCODE.COMMON.REASONPARAMETER.NOTE')}
        >
          <TextArea rows={4} onChange={(e) => handleErrorReasonChange(e.target.value)} disabled={!editRule} />
        </Form.Item>
        {!isProduct && (
          <>
            <Form.Item label={`${formatMessage('ERRORCODE.DEFAULT.MATCHCONDITIONS')}`} name="defaultMatchConditions">
              <CodePrompt
                language="java"
                id="defaultMatchConditions"
                className="formItemCodeMirror"
                editable={true}
                minHeight="28px"
                maxHeight="60px"
                basicSetup={{
                  lineNumbers: false,
                  highlightActiveLine: false,
                  highlightActiveLineGutter: false,
                }}
                value={defaultMatchConditions ? defaultMatchConditions : ''}
                allSchema={[]}
                onChange={(codeValue: string) => setDefaultMatchConditions(codeValue)}
              />
            </Form.Item>
            {reasonParams.map((item, index) => {
              return (
                <Form.Item
                  label={`${formatMessage('ERRORCODE.DEFAULT.REASONPARAMETER')} ${index}`}
                  key={item.paramName}
                  name={item.paramName}
                  rules={defaultMatchConditions ? [{ required: true, message: 'Please input.' }] : []}
                  initialValue={item.paramValue} // 这里form.item需要动态生成，故单独设置 initialValue
                >
                  <CodePrompt
                    language="java"
                    id={item.paramName}
                    className="formItemCodeMirror"
                    editable={true}
                    minHeight="28px"
                    maxHeight="60px"
                    basicSetup={{
                      lineNumbers: false,
                      highlightActiveLine: false,
                      highlightActiveLineGutter: false,
                    }}
                    value={item.paramValue}
                    allSchema={[]}
                    onChange={(newValue: string) => handleCodeChange(index, newValue)}
                  />
                </Form.Item>
              );
            })}
          </>
        )}
      </Form>
    </Drawer>
  );
};

export default EditErrorCode;
