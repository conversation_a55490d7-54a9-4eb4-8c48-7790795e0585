import { IGroupList, ICatalog, IAPIGCatalog } from "@/services/typing.d";

// search domain by keys words
const searchDomainByText: (
  searchvalue: string,
  groupData: Array<IGroupList>
) => Array<IGroupList> = (searchvalue, groupData) => {
  const resultsAfterSearch = (groupData || [])
    .map((groupItem) => {
      const { domainList } = groupItem;
      // domain map之后过滤子集为空的场景
      const domianArray = (domainList || [])
        .map((domainItem) => {
          const { entityList } = domainItem;
          // entity 忽略大小写匹配
          const entityArray = (entityList || []).filter((entityItem) =>
            entityItem?.entityName
              ?.toLowerCase()
              ?.includes(searchvalue?.toLowerCase())
          );
          return {
            ...domainItem,
            entityList: entityArray,
            children: entityArray,
          };
        })
        .filter((domainEmptyItem) => domainEmptyItem?.entityList?.length);
      return {
        ...groupItem,
        domainList: domianArray,
        children: domianArray,
      };
    })
    .filter((groupEmptyItem) => groupEmptyItem?.domainList?.length);
  return resultsAfterSearch;
};

// search catalog by keys words
const searchCatalogByText: (
  searchvalue: string,
  catalogData: Array<ICatalog>
) => Array<ICatalog> = (searchvalue, catalogData) => {
  const resultsAfterSearch = (catalogData || [])
    .map((catalogItem) => {
      const { groupList } = catalogItem;
      // group map之后过滤子集为空的场景
      const groupArray = (groupList || [])
        .map((groupItem) => {
          const { subCatalogList } = groupItem;
          // subCatalog 忽略大小写匹配,subCatalog下的服务忽略大小写匹配
          const entityArray = (subCatalogList || []).filter((subCatalogItem) =>
            subCatalogItem?.subCatalog
              ?.toLowerCase()
              ?.includes(searchvalue?.toLowerCase())
              ||
              subCatalogItem?.serviceBaseList?.some((serviceBaseItem) =>
                serviceBaseItem?.serviceName
                ?.toLowerCase()
                ?.includes(searchvalue?.toLowerCase()))
          );
          return {
            ...groupItem,
            subCatalogList: entityArray,
            children: entityArray,
          };
        })
        .filter((groupEmptyItem) => groupEmptyItem?.subCatalogList?.length);
      return {
        ...catalogItem,
        groupList: groupArray,
        children: groupArray,
      };
    })
    .filter((groupEmptyItem) => groupEmptyItem?.groupList?.length);
  return resultsAfterSearch;
};

// search catalog by keys words
const filterServicesBySearchText = (
  catalogList: IAPIGCatalog[],
  searchText: string
): IAPIGCatalog[] => {
  const resultData: IAPIGCatalog[] = JSON.parse(JSON.stringify(catalogList))
  return resultData
    .map(catalog => {
      // 过滤当前catalog的serviceList
      if (catalog.serviceList) {
        catalog.serviceList = catalog.serviceList.filter((service: any) =>
          service.apiMethod.toLowerCase().includes(searchText.toLowerCase()),
        );
      }

      // 递归过滤子节点
      if (catalog.isParent && catalog.catalogChildNodes)  {
        catalog.catalogChildNodes  = filterServicesBySearchText(
          catalog.catalogChildNodes,
          searchText
        );
      }

      // 如果当前catalog的serviceList和catalogChildNodes都为空，则移除该catalog
      if (
        (!catalog.serviceList  || catalog.serviceList.length  === 0) &&
        (!catalog.catalogChildNodes  || catalog.catalogChildNodes.length  === 0)
      ) {
        return null;
      }

      return catalog;
    })
    .filter(catalog => catalog !== null) as IAPIGCatalog[]; // 过滤掉null
};

// search catalog by keys apigServId
const findCatalogByApigServId = (
  catalogList: IAPIGCatalog[],
  targetApigServId: number = 0,
): IAPIGCatalog | null => {
  for (const catalogItem of catalogList) {
    // 检查当前节点的serviceList
    if (catalogItem.serviceList)  {
      const matchedService = catalogItem.serviceList.find(
        (service:any )=> service.apigServId  === targetApigServId
      );
      if (matchedService) {
        return catalogItem;
      }
    }

    // 递归检查子节点
    if (catalogItem.isParent  && catalogItem.catalogChildNodes)  {
      const result = findCatalogByApigServId(
        catalogItem.catalogChildNodes,
        targetApigServId
      );
      if (result) {
        return result;
      }
    }
  }

  // 未找到匹配项
  return null;
};

// 获取所有catalogId的字符串数组
const getAllCatalogIdsAsStrings = (catalogList: IAPIGCatalog[]): string[] => {
  const result: string[] = [];

  const traverse = (catalog: IAPIGCatalog) => {
    // 将当前catalog的catalogId转换为字符串并添加到结果数组
    result.push(catalog.catalogId.toString());

    // 递归遍历子节点
    if (catalog.isParent && catalog.catalogChildNodes)  {
      catalog.catalogChildNodes.forEach(traverse);
    }
  };

  // 遍历顶层catalogList
  catalogList.forEach(traverse);

  return result;
};

// add domain data key
const addDomainKey = (data: Array<IGroupList>) => {
  const dataAfterChildrenSerialize = ((data as IGroupList[]) || []).map(
    (groupItem, groupIndex) => {
      const groupItemC = (groupItem?.domainList || []).map((domainItem, domainIndex) => {
        const domainItemC = (domainItem?.entityList || []).map((entityItem, entityIndex) => ({
          ...entityItem,
          key: `${entityItem.entityKey}-${groupIndex}-${domainIndex}-${entityIndex}`, // 添加索引确保唯一性
        }));
        return {
          ...domainItem,
          key: `${groupItem.group}-${domainItem.domainKey}-${groupIndex}-${domainIndex}`, // 添加索引确保唯一性
          children: domainItemC,
        };
      });
      return {
        ...groupItem,
        key: `${groupItem.group}-${groupIndex}`, // 添加索引确保唯一性
        children: groupItemC,
      };
    }
  );
  return dataAfterChildrenSerialize;
};

// add domain data key,更深一级
const addDomainKeyPlus = (data: Array<IGroupList>) => {
  const dataAfterChildrenSerialize = ((data as IGroupList[]) || []).map(
    (groupItem) => {
      const groupItemC = (groupItem?.domainList || []).map((domainItem) => {
        const domainItemC = (domainItem?.entityList || []).map(
          (entityItem) => {
            // ({ ...entityItem, key: entityItem.entityKey })
            const children = entityItem.entityProperties.map((entityPropertyItem: any) => ({
              ...entityPropertyItem,
              key: `${entityItem.entityKey}-${entityPropertyItem.name}`,
              comments: entityPropertyItem.comments,
            }));
            return {
              ...entityItem,
              key: entityItem.entityKey,
              comments: entityItem.comments,
              children: children.length > 0 ? children : undefined,
            };
          }
        );
        return {
          ...domainItem,
          key: `${groupItem.group}-${domainItem.domainKey}`,
          children: domainItemC,
        };
      });
      return {
        ...groupItem,
        key: groupItem.group,
        children: groupItemC,
      };
    }
  );
  return dataAfterChildrenSerialize;
};

// add catalog data key
const addCatalogKey = (data: Array<ICatalog>): Array<ICatalog> => {
  const dataAfterChildrenSerialize = (data || []).map((catalogItem, catalogIndex) => {
    const catalogItemC = (catalogItem.groupList || []).map((catalogGroupItem, groupIndex) => {
      const subCataC = (catalogGroupItem.subCatalogList || []).map((subItem, subIndex) => ({
        ...subItem,
        key: `${catalogItem.catalog}-${catalogGroupItem.groupName}-${subItem.subCatalog}-${subIndex}`, // 添加索引确保唯一性
        subCatalog: `${catalogItem.catalog}.${catalogGroupItem.groupName}.${subItem.subCatalog}`, // 拼接格式
      }));

      return {
        ...catalogGroupItem,
        key: `${catalogItem.catalog}-${catalogGroupItem.groupName}-${groupIndex}`, // 添加索引确保唯一性
        children: subCataC,
      };
    });

    return {
      ...catalogItem,
      key: `${catalogItem.catalog}-${catalogIndex}`, // 添加索引确保唯一性
      children: catalogItemC,
    };
  });

  return dataAfterChildrenSerialize;
};

export { searchDomainByText, searchCatalogByText, addDomainKey, addCatalogKey,addDomainKeyPlus,filterServicesBySearchText,getAllCatalogIdsAsStrings,findCatalogByApigServId };
