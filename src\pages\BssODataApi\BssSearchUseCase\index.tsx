import styles from './index.less';
import useI18n from '@/hooks/useI8n';
import { useModel } from 'umi';
import SearchUseCaseCatalog from './SearchUseCaseCatalog';
import { Col } from 'antd';
import EmptyDatePage from '@/components/EmptyDataPage';
import DebugService from '@/pages/BssDebugService/DebugService';
import { LeftOutlined } from '@ant-design/icons';
import { useEffect, useMemo } from 'react';

interface BssSearchUseCasePropsType {
  onClose: () => void;
}

const BssSearchUseCase = ({ onClose }: BssSearchUseCasePropsType) => {
  const { formatMessage } = useI18n();

  // 根据 serviceCode 触发接口查询
  const {
    serviceDetailInModel,
    serviceCodeInModel,
    useCaseIdInModel,
    menuItemInModel,
    resetAllData,
    getUseCasePageListFn,
  } = useModel('useSearchUseCaseModel');

  let DebugServiceArea = useMemo(() => {
    return (
      <div style={{ overflowY: 'auto', height: '100%' }}>
        {serviceDetailInModel &&
        useCaseIdInModel &&
        menuItemInModel &&
        Reflect.ownKeys(serviceDetailInModel).length > 0 ? (
          <DebugService
            key={`DebugService-${useCaseIdInModel}`}
            saveUsType="ONLYUSECASE"
            debugMode
            serviceCode={serviceCodeInModel}
            serviceDetail={serviceDetailInModel}
            initSeletedValue={menuItemInModel}
            isEditAPIGService={false}
            hiddenSelectBtn
            isEditUseCase={true}
            otherFnInUseCaseModal={getUseCasePageListFn}
          />
        ) : (
          <EmptyDatePage />
        )}
      </div>
    );
  }, [serviceDetailInModel, serviceCodeInModel, useCaseIdInModel, menuItemInModel]);

  useEffect(() => {
    return () => {
      DebugServiceArea = <EmptyDatePage />;
      resetAllData();
    };
  }, []);

  return (
    <div className={styles.bssSearchUseCase}>
      <div className={styles.header}>
        <LeftOutlined className={styles.returnIcon} onClick={onClose} />
        <span className={styles.returnText} onClick={onClose}>
          {formatMessage('SERVICEUSECASELIST.TITLE.SERVICEUSECASELIST')}
        </span>
      </div>
      <div className={styles.bottom}>
        <Col span={6}>
          <SearchUseCaseCatalog />
        </Col>
        <Col span={18}>{DebugServiceArea}</Col>
      </div>
    </div>
  );
};

export default BssSearchUseCase;
