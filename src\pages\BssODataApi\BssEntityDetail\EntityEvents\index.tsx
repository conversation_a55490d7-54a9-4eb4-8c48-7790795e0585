import React, { useEffect, useMemo } from 'react';
import { IEventList } from '@/services/typing.d';
import styles from './index.less';
import ResizableTable from '@/components/ResizableTable';
import { useTableFilter } from '@/hooks/useTableFilter';
import useI18n from '@/hooks/useI8n';

interface IEvents {
  data: Array<IEventList>;
}

const EntityEvents: React.FC<IEvents> = (props) => {
  const { data } = props;
  const { formatMessage } = useI18n();

  const onOpenRlcEventView = (record: any) => {
    if ((window.parent as any).fish) {
      (window.parent as any).fish.popupView({
        url: 'rlc/modules/rlc-event-mgr/views/RclRuleListView',
        width: '100%',
        height: document.body.clientHeight + 50,
        position: {
          my: 'top',
          at: 'center',
          of: window.parent,
          collision: 'fit',
        },
        viewOption: {
          eventCode: record.eventCode,
          confApp: 'ODH', // 配置的APP Code
          filterRule: 'Y', // 是否按confApp来过滤展示的规则
          extAction: 'Y', // 过滤ACTION_TYPE_ID或TOPIC_ID小于0的记录
        },
      });
    }
  };
  // 使用 useMemo 确保 dataSource 是稳定的引用
  const memoizedDataSource = useMemo(() => (Array.isArray(data) ? data : []), [data]);
  // 自定义hooks，支持表格列筛选并分页展示
  const { filteredDataSource, getColumnSearchProps, clearAllFilters } = useTableFilter(memoizedDataSource);

  useEffect(() => {
    // 重置表格列的筛选条件
    clearAllFilters();
  }, [memoizedDataSource]);

  const clolumns = [
    {
      dataIndex: 'eventName',
      title: formatMessage('ENTITYEVENTS.EVENT_NAME'),
      width: '30%',
      ellipsis: true,
      ...getColumnSearchProps('eventName'),
      render: (_: string, record: any) => (
        <div className={styles.active} onClick={() => onOpenRlcEventView(record)}>
          {record?.eventName}
        </div>
      ),
    },

    {
      dataIndex: 'eventCode',
      title: formatMessage('ENTITYEVENTS.EVENT_CODE'),
      width: '40%',
      ellipsis: true,
      ...getColumnSearchProps('eventCode'),
    },
    {
      dataIndex: 'comments',
      title: formatMessage('ENTITYEVENTS.REMARKS'),
      width: '30%',
      ellipsis: true,
      ...getColumnSearchProps('comments'),
    },
  ];

  return (
    <ResizableTable
      rowKey={(record: any) => record.code}
      style={{ marginTop: '12px' }}
      size="small"
      dataSource={filteredDataSource}
      columns={clolumns}
      pagination={false}
    />
  );
};

export default EntityEvents;
