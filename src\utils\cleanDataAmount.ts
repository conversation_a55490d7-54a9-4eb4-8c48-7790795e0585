import { IAPIGCatalog, ICatalog, IGroupList, IOriginalCatalogList } from "@/services/typing.d";

// 计算 清洗数据 group amount
const cleanGroupData = (listData: Array<IGroupList>) => {
  const listAfterClean = listData || [];
  listAfterClean.forEach((groupData) => {
    const { domainList } = groupData;
    let groupServiceAmount = 0;
    (domainList || []).forEach((domainData) => {
      const { entityList } = domainData;
      const domainServiceAmount = (entityList || []).reduce((pre, cur) => {
        return pre + (cur ? cur?.serviceAmount || 0 : 0);
      }, 0);
      domainData.serviceAmount = domainServiceAmount;
      groupServiceAmount += domainData?.serviceAmount || 0;
    });
    groupData.serviceAmount = groupServiceAmount;
  });
  return listAfterClean;
};

// 因queryCatalogMenu接口改变，返回的字段都改变，所以需要映射一下相应字段
const getReflectedCatalogData = (data:IOriginalCatalogList[]) => {
  return data?.map((firstLevel: IOriginalCatalogList) => ({
    catalog: firstLevel?.catalogName,
    groupList: firstLevel?.domainServiceList?.map((secondLevel) => ({
      groupName: secondLevel?.domainName,
      subCatalogList: secondLevel?.subCatalogServiceList?.map((thirdLevel) => ({
        subCatalog: thirdLevel?.subCatalogName,
        serviceAmount: thirdLevel?.serviceBaseList?.length,
        serviceBaseList: thirdLevel?.serviceBaseList,
      })),
    })),
  }));
}

// 计算 清洗数据 catalog amount
const cleanCatalogData = (catalogListdata: Array<ICatalog>) => {
  const listAfterClean = catalogListdata || [];
  listAfterClean.forEach((catalogData) => {
    const { groupList } = catalogData;
    catalogData.currentPathList = [catalogData?.catalog];
    let groupServiceAmount = 0;
    (groupList || []).forEach((groupData) => {
      const { subCatalogList } = groupData;
      groupData.currentPathList = [...catalogData?.currentPathList || [],groupData?.groupName]
      const domainServiceAmount = (subCatalogList || []).reduce((pre, cur) => {
        return pre + cur?.serviceAmount;
      }, 0);
      groupData.serviceAmount = domainServiceAmount;
      groupServiceAmount += groupData?.serviceAmount || 0;
      (subCatalogList || []).forEach((subCatalogData) => {
        subCatalogData.currentPathList = [...groupData?.currentPathList || [],subCatalogData?.subCatalog]
      })
    });
    catalogData.serviceAmount = groupServiceAmount;
  });
  return listAfterClean;
};

// 递归计算目录项及其子项的服务总数
const getServiceAmount = (catalogChildNodes: IAPIGCatalog[] = []): number => {
  return catalogChildNodes.reduce((total, item) => {
    if (item.isParent) {
      return total + getServiceAmount(item.catalogChildNodes) + (item.serviceList?.length || 0);
    }
    return total + (item.serviceList?.length || 0);
  }, 0);
};

// 递归遍历目录项，为每个项添加serviceAmount属性、menuLevel属性
const addProps = (
  catalog: IAPIGCatalog[] = [],
  parentMenuPath: string[] = [],
  isFirstLevel: boolean = true,
): IAPIGCatalog[] => {
  return catalog.map((catalogItem) => {
    const currentMenuPath = [...parentMenuPath, catalogItem.catalogId.toString()];
    const childNodes = catalogItem.catalogChildNodes || [];

    if (catalogItem.isParent) {
      const childServiceAmount = getServiceAmount(childNodes);
      const ownServiceAmount = catalogItem.serviceList?.length || 0;

      return {
        ...catalogItem,
        menuPath: currentMenuPath,
        isFirstLevel,
        catalogChildNodes: addProps(childNodes, currentMenuPath, false),
        // 如果是第一层，serviceAmount 只计算子节点的总和
        serviceAmount: isFirstLevel ? childServiceAmount : childServiceAmount + ownServiceAmount,
      };
    }

    return {
      ...catalogItem,
      menuPath: currentMenuPath,
      isFirstLevel,
      serviceAmount: catalogItem.serviceList?.length || 0,
    };
  });
};

// 计算清洗数据
const cleanAPIGCatalogData = (listData: Array<IAPIGCatalog> = []) => {
  return addProps(listData, [], true);
};
export { cleanGroupData, cleanCatalogData, cleanAPIGCatalogData, getReflectedCatalogData };
