import React, { useImperativeHandle, forwardRef, useState, useEffect } from 'react';
import { Button, Drawer, Form, Input, Select, Space, message } from 'antd';
import useI18n from '@/hooks/useI8n';
import InputPanel from './InputPanel';
import { OperationIcon } from '@/components/OperationIconTitle';
import { addDomainDataModelListService, modifyDomainDataModelListService } from '@/services/dataModelService';
import { useModel } from 'umi';
import { ISqlProps } from '../types';
import SelectWithAddButton from '@/components/SelectWithAddButton';
import getUUid from '@/utils/getUUid';

const ModelTypeOptions = [
  { value: 'V', label: 'View SQL' },
  { value: 'T', label: 'Table' },
];

const CreationSrcOptions = [
  { value: 'P', label: 'Product-Owned' },
  { value: 'C', label: 'Project-Customized' },
];

// 转换 undefined 为 {inputPanel: {verCode: '', viewSql: ''}}
const switchUndefinedFn = (values: ISqlProps[]) => {
  return values.map((el) => {
    if (!el || typeof el === 'undefined') {
      return {
        inputPanel: {
          verCode: 'default',
          viewSql: '',
        },
      };
    }
    return el;
  });
};

const basicDisabledObj = {
  isModelName: false,
  isModelCode: false,
  isModelType: false,
  isModelClass: false,
  isCreationSrc: false,
  isViewSqlList: false,
  isTableName: false,
  isComments: false,
};

interface IDataModelDrawer {
  domainObjId: number | null;
  domainId: number | null;
  afterSubmit: (el: any) => any;
  isProduct: boolean;
}

const DataModelDrawer = forwardRef(
  ({ domainObjId = null, domainId = null, afterSubmit = () => {}, isProduct = false }: IDataModelDrawer, ref) => {
    // 环境基本信息
    const { classesInfo = [], tableInfo = [], getDomainTableInfoFn } = useModel('useDomainDataModelListModel');

    const { formatMessage } = useI18n();

    const [tableOptions, setTableOptions] = useState<string[]>([]); // table 字段的 Select 内容

    const [curType, setCurType] = useState<string>('');

    const [drawerTitle, setDrawerTitle] = useState('');

    const [form] = Form.useForm();

    const [open, setOpen] = useState(false);

    const [curModelType, setCurModelType] = useState('');
    // 保存当前的记录
    const [curRecord, setCurRecord] = useState<any>({});

    // 控制是否可编辑
    const [isDisabledObj, setIsDisabledObj] = useState(basicDisabledObj);

    const onClose = () => {
      setCurType('');
      setCurModelType('');
      setOpen(false);
      form.resetFields();
    };

    // 处理抽屉的显隐
    const handleDrawerOpenOrClose = (el: boolean) => {
      setOpen(el);
    };

    // 进入 add 模式
    const enterAddMode = () => {
      setDrawerTitle(formatMessage('DATAMODELLIST.DRAWER.ADDTITLE'));
      setCurType('add');
      handleDrawerOpenOrClose(true);
      form.resetFields();
    };

    // 进入 edit模式
    const enterEditMode = (el: any) => {
      setCurRecord(el);
      setDrawerTitle(formatMessage('DATAMODELLIST.DRAWER.EDITTITLE'));
      setCurType('edit');
      handleDrawerOpenOrClose(true);
      const { modelType = '', viewSqlList = [] } = el;
      setCurModelType(modelType);
      switch (modelType) {
        case 'V':
          if (viewSqlList && Array.isArray(viewSqlList)) {
            const newViewSqlList = (viewSqlList || []).map((el: any) => {
              return {
                inputPanel: { ...el },
              };
            });
            form.setFieldsValue({
              ...el,
              viewSqlList: newViewSqlList,
            });
          } else {
            form.setFieldsValue(el);
          }
          break;
        case 'T':
          form.setFieldsValue(el);
          break;
        default:
          break;
      }
    };

    const [loading, setLoading] = useState(false);

    // 数据提交
    const onSubmit = async () => {
      form.validateFields().then(async () => {
        try {
          setLoading(true);
          const values = form.getFieldsValue();
          const { viewSqlList, modelName = '', modelType = '' } = values;
          let newViewSqlList: any[] = [];
          if (viewSqlList && Array.isArray(viewSqlList)) {
            newViewSqlList = viewSqlList.map((el) => {
              const { inputPanel } = el;
              let curVercode = null;
              if (inputPanel.verCode === 'default') {
                curVercode = null;
              } else {
                curVercode = inputPanel.verCode ? inputPanel.verCode : null;
              }
              return {
                ...inputPanel,
                verCode: curVercode,
              };
            });
          }

          switch (curType) {
            case 'add':
              if (domainObjId) {
                const curObj = {
                  ...values,
                  domainObjId,
                };
                if (modelType === 'V') {
                  curObj.viewSqlList = newViewSqlList;
                }
                await addDomainDataModelListService(curObj).then((res) => {
                  const { success = false } = res;
                  if (success) {
                    message.success(formatMessage('DATAMODELLISTDETAIL.ADD.SUCCESS'));
                    onClose();
                    afterSubmit(null);
                  }
                });
              }
              break;
            case 'edit':
              if (domainObjId) {
                const { modelId } = curRecord;
                const curObj = {
                  ...values,
                  domainObjId,
                };
                if (modelType === 'V') {
                  curObj.viewSqlList = newViewSqlList;
                }
                await modifyDomainDataModelListService(modelId, curObj).then((res) => {
                  const { success = false } = res;
                  if (success) {
                    message.success(formatMessage('DATAMODELLISTDETAIL.EDIT.SUCCESS'));
                    onClose();
                    afterSubmit(modelId);
                  }
                });
              }
              break;
            default:
              break;
          }
        } catch (error) {
          //
        } finally {
          setLoading(false);
        }
      });
    };

    useImperativeHandle(ref, () => ({
      enterEditMode,
      enterAddMode,
    }));

    // 定义抽屉页脚
    const DrawerButton = (
      <React.Fragment>
        <Space style={{ float: 'right' }}>
          <Button
            type="primary"
            onClick={() => {
              onSubmit();
            }}
            loading={loading}
          >
            {formatMessage('DATAMODELLIST.DRAWER.SUBMIT')}
          </Button>
          <Button onClick={onClose}>{formatMessage('DATAMODELLIST.DRAWER.CANCEL')}</Button>
        </Space>
      </React.Fragment>
    );

    // 传给子组件SelectWithAddButton的自定义方法，将输入自动转为大写
    const handleCustomInputChange = (value: string) => {
      return value.toUpperCase();
    };

    useEffect(() => {
      if (curType === 'add') {
        if (isProduct) {
          form.setFieldsValue({
            creationSrc: 'P',
          });
        } else {
          form.setFieldsValue({
            creationSrc: 'C',
          });
        }
        setIsDisabledObj({
          ...basicDisabledObj,
          isCreationSrc: true,
        });
      }
      if (curType === 'edit') {
        const { creationSrc = '' } = curRecord;
        if (isProduct) {
          setIsDisabledObj({
            ...basicDisabledObj,
            isCreationSrc: true,
          });
        } else {
          switch (creationSrc) {
            case 'P':
              setIsDisabledObj({
                isModelName: true,
                isModelCode: true,
                isModelType: true,
                isModelClass: true,
                isCreationSrc: true,
                isViewSqlList: true,
                isTableName: true,
                isComments: false,
              });
              break;
            case 'C':
              setIsDisabledObj({
                ...basicDisabledObj,
                isCreationSrc: true,
              });
              break;
            default:
              break;
          }
        }
      }
    }, [isProduct, curType, curRecord]);

    useEffect(() => {
      if (domainId) {
        getDomainTableInfoFn(domainId);
      }
    }, [domainId]);

    useEffect(() => {
      setTableOptions(tableInfo);
    }, [tableInfo]);

    return (
      <Drawer open={open} title={drawerTitle} onClose={onClose} width={720} maskClosable={false} footer={DrawerButton}>
        <Form
          form={form}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
          onValuesChange={(value) => {
            const { modelType = '' } = value;
            if (modelType) {
              setCurModelType(modelType);
              if (modelType === 'V') {
                form.setFieldsValue({
                  viewSqlList: [
                    {
                      inputPanel: {
                        verCode: 'default',
                        viewSql: '',
                      },
                    },
                  ],
                });
              }
            }
          }}
        >
          <Form.Item label={formatMessage('DATAMODELLIST.TABLE.NAME')} name="modelName" rules={[{ required: true }]}>
            <Input disabled={isDisabledObj.isModelName} />
          </Form.Item>
          <Form.Item
            label={formatMessage('DATAMODELLIST.TABLE.CODE')}
            name="modelCode"
            rules={[
              { required: true, message: '' },
              {
                validator: (_, value) => {
                  const regex = /^[A-Z][a-zA-Z0-9]*$/;
                  if (!regex.test(value)) {
                    return Promise.reject(
                      new Error(
                        'The Code format is camel hump format, which can only contain English letters and numbers, starting with uppercase letters',
                      ),
                    );
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <Input allowClear disabled={isDisabledObj.isModelCode} />
          </Form.Item>
          <Form.Item label={formatMessage('DATAMODELLIST.TABLE.TYPE')} name="modelType" rules={[{ required: true }]}>
            <Select disabled={isDisabledObj.isModelType} options={ModelTypeOptions} />
          </Form.Item>
          <Form.Item
            label={formatMessage('DATAMODELLIST.DRAWER.CLASSIFICATION')}
            name="modelClass"
            rules={[{ required: true }]}
          >
            <Select
              options={classesInfo?.map((el: any) => ({
                label: el?.modelClassName,
                value: el?.modelClass,
              }))}
              disabled={isDisabledObj.isModelClass}
            />
          </Form.Item>
          <Form.Item
            label={formatMessage('DATAMODELLIST.TABLE.CREATIONSOURCE')}
            name="creationSrc"
            rules={[{ required: true }]}
          >
            <Select disabled={isDisabledObj.isCreationSrc} options={CreationSrcOptions} />
          </Form.Item>
          {curModelType === 'V' && (
            <Form.Item
              label={formatMessage('DATAMODELLIST.DRAWER.VIEWSQL')}
              rules={[{ required: true }]}
              name="viewSqlList"
            >
              <Form.List name="viewSqlList">
                {(fields, { add, remove }) => (
                  <>
                    {isProduct && (
                      <OperationIcon
                        handleClick={() => {
                          add();
                          const value = form.getFieldValue('viewSqlList');
                          let newValue: any[] = [];
                          if (Array.isArray(value)) {
                            newValue = switchUndefinedFn(value);
                          }
                          form.setFieldsValue({ viewSqlList: newValue });
                        }}
                        opt={formatMessage('DATAMODELLIST.TABLE.ADD')}
                        style={{ marginTop: 4 }}
                        type="add"
                      />
                    )}
                    {fields.map(({ key, name, ...restField }, index) => {
                      return (
                        <Form.Item key={key} {...restField} name={[name, 'inputPanel']}>
                          <InputPanel
                            key={`inputPanel-${index}-${name}`}
                            onDelete={() => remove(name)}
                            isProduct={isProduct}
                            type={curType}
                            disabled={isDisabledObj.isViewSqlList}
                          />
                        </Form.Item>
                      );
                    })}
                  </>
                )}
              </Form.List>
            </Form.Item>
          )}
          {curModelType === 'T' && (
            <Form.Item
              label={formatMessage('DATAMODELLIST.DRAWER.TABLE')}
              rules={[
                { required: true },
                {
                  pattern: /^[A-Z0-9_]+$/,
                  message: 'Only uppercase letters, numbers and underscores (_) are allowed.',
                },
              ]}
              name="tableName"
            >
              <SelectWithAddButton
                originalOptionsList={[
                  ...(curType === 'add' ||
                  !curRecord?.tableName ||
                  tableOptions?.find((item) => item === curRecord?.tableName)
                    ? []
                    : [
                        {
                          label: curRecord?.tableName,
                          value: curRecord?.tableName,
                          key: getUUid(6),
                          state: 'new',
                          // 这里主要处理通过输入“新增”的tableName已经随着该DataModel提交成功了，但实际该tableName并没有入对应的表（非bug，后台逻辑就是不入表）。
                        },
                      ]),
                  ...(tableOptions || []).map((item: string) => ({
                    label: item,
                    value: item,
                    key: getUUid(6),
                    state: 'original',
                  })),
                ]}
                parentForm={form}
                formFieldName="tableName"
                customInputChange={handleCustomInputChange}
              />
            </Form.Item>
          )}
          <Form.Item label={formatMessage('DATAMODELLIST.TABLE.DESC')} name="comments" rules={[{ required: true }]}>
            <Input.TextArea disabled={isDisabledObj.isComments} />
          </Form.Item>
        </Form>
      </Drawer>
    );
  },
);

export default DataModelDrawer;
