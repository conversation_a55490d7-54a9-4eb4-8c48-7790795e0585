import { Button, Drawer, message, Popconfirm, PopconfirmProps, Space, Spin, Tooltip } from 'antd';
import { useForm } from 'antd/es/form/Form';
import { useEffect, useState } from 'react';
import { DomainErrorCodesType, ExpReasonErrorCodeType } from '../../../../types';
import useI18n from '@/hooks/useI8n';
import {
  delErrorCodeRule,
  getDomainErrorCodes,
  EntityServiceDetail,
  getEntityServiceDetail,
  getExpReasonErrorCode,
} from '@/services/errorCodeService';
import ResizableTable from '@/components/ResizableTable';
import { DeleteOutlined, FormOutlined } from '@ant-design/icons';
import classNames from 'classnames';
import styles from '../index.less';
import OperationIconTitle from '@/components/OperationIconTitle';
import AddExpReasonErrorCode from './AddExpReasonErrorCode';
import { CreationSource, MatchStatus } from '../../const';
import EditExpReasonErrorCode from './EditExpReasonErrorCode';
import { transferServiceDetailToSchema } from '@/utils/transferServiceDetailToSchema';

interface IResponseErrorCodeList {
  isProduct: boolean;
  expReasonErrorCodeOpen: boolean;
  respParam: any;
  selectedRootNode: any;
  selectedService: any;
  setExpReasonErrorCodeOpen: (open: boolean) => void;
  updateDataSource: () => void; // 更新响应参数
}
const ResponseErrorCodeList: React.FC<IResponseErrorCodeList> = (props) => {
  const {
    expReasonErrorCodeOpen,
    respParam,
    isProduct,
    selectedRootNode,
    selectedService,
    setExpReasonErrorCodeOpen,
    updateDataSource,
  } = props;
  const { formatMessage } = useI18n();
  const [form] = useForm();
  const [spining, setSpining] = useState<boolean>(false);
  const [expReasonErrorCodeList, setExpReasonErrorCodeList] = useState<ExpReasonErrorCodeType[]>([]);
  const [domainErrorCodes, setDomainErrorCodes] = useState<DomainErrorCodesType[]>([]);
  const [addOpen, setAddOpen] = useState<boolean>(false);
  const [editOpen, setEditOpen] = useState<boolean>(false);
  const [selectedErrorCode, setSelectedErrorCode] = useState<any>();
  const [entityServiceDetail, setEntityServiceDetail] = useState<EntityServiceDetail>();
  // const [allSchema, setAllSchema] = useState<Array<any>>([]); // 当前服务的请求参数和响应参数的schema

  // 查询服务全部内容
  const queryEntityServiceDetail = async () => {
    try {
      setSpining(true);
      const params = {
        serviceId: selectedService?.serviceId,
      };
      const { success, data } = await getEntityServiceDetail(params);
      if (success) {
        setEntityServiceDetail(data);
      }
    } finally {
      setSpining(false);
    }
  };

  const deleteErrorCodeConfirm: PopconfirmProps['onConfirm'] = async (e) => {
    try {
      setSpining(true);
      const params = {
        domainId: selectedRootNode?.domainId,
        respErrorCodeId: selectedErrorCode?.respErrorCodeId,
      };

      const { success } = await delErrorCodeRule(params);

      if (success) {
        message.success(formatMessage('MANAGEEXTENSIONSERVICE.RESPONSE.ERRORCODE.DELETE.SUCCESS'));
        // 删除后重新渲染数据
        queryExpReasonErrorCode({ respParamId: respParam?.respParamId });
      }
    } catch (error) {
      message.error(formatMessage('MANAGEEXTENSIONSERVICE.RESPONSE.ERRORCODE.DELETE.ERROR'));
    } finally {
      setSpining(false);
    }
  };

  const columns = [
    {
      dataIndex: 'errorCode',
      title: formatMessage('MANAGEEXTENSIONSERVICE.RESPONSE.ERRORCODE.CODE'),
      width: '15%',
      ellipsis: true,
    },
    {
      dataIndex: 'errorReason',
      title: formatMessage('MANAGEEXTENSIONSERVICE.RESPONSE.ERRORCODE.REASON'),
      width: '15%',
      ellipsis: true,
    },
    {
      dataIndex: 'matchStatus',
      title: formatMessage('ERRORCODE.EXPREASON.MATCHRESPONSESTATUS'),
      width: '18%',
      ellipsis: true,
      render: (_: string, record: any) => {
        return MatchStatus.filter((i) => i.key === record?.matchStatus).map((i) => i.name);
      },
    },
    {
      dataIndex: 'creationSrc',
      title: formatMessage('PROJECT.COMMON.CREATIONSOURCE'),
      width: '15%',
      ellipsis: true,
      render: (_: string, record: any) => {
        return CreationSource.filter((i) => i.key === record.creationSrc).map((i) => i.name);
      },
    },
    {
      dataIndex: 'matchConditions',
      title: formatMessage('ERRORCODE.EXPREASON.MATCHCONDITIONS'),
      width: '17%',
      ellipsis: true,
    },
    {
      dataIndex: 'matchPriority',
      title: formatMessage('MANAGEEXTENSIONSERVICE.RESPONSE.ERRORCODE.PRIORITY'),
      width: '10%',
      ellipsis: true,
    },
    {
      dataIndex: '',
      title: formatMessage('PROJECT.COMMON.OPERATION'),
      width: '10%',
      render: (_: string, record: any) => {
        return (
          <div>
            {/* 修改按钮 */}
            <Tooltip title={formatMessage('PROJECT.COMMON.EDIT')}>
              <FormOutlined
                className={classNames(styles.iconStyle, {
                  // [styles.hide]: openFromBssODataApi,
                })}
                onClick={() => {
                  setEditOpen(true);
                }}
              />
            </Tooltip>
            {/* 删除按钮及提示 */}
            <Popconfirm
              title={formatMessage('MANAGEEXTENSIONSERVICE.RESPONSE.ERRORCODE.DELETE.TITLE')}
              description={formatMessage('MANAGEEXTENSIONSERVICE.RESPONSE.ERRORCODE.DELETE.DESCRIPTION')}
              onConfirm={() => {
                deleteErrorCodeConfirm();
              }}
              okText={formatMessage('PROJECT.COMMON.YES')}
              cancelText={formatMessage('PROJECT.COMMON.NO')}
            >
              {isProduct ? (
                <Tooltip title={formatMessage('PROJECT.COMMON.DELETE')}>
                  <DeleteOutlined
                    className={classNames(styles.versionIcon2, {
                      [styles.hide]: selectedRootNode?.state !== 'D',
                    })}
                    onClick={() => {}}
                  />
                </Tooltip>
              ) : (
                <Tooltip title={formatMessage('PROJECT.COMMON.DELETE')}>
                  <DeleteOutlined
                    className={classNames(styles.versionIcon2, {
                      [styles.hide]: record?.creationSrc === 'P' || selectedRootNode?.state !== 'D',
                    })}
                    onClick={() => {}}
                  />
                </Tooltip>
              )}
            </Popconfirm>
          </div>
        );
      },
    },
  ];

  const onClose = () => {
    // 更新响应参数
    updateDataSource();
    setExpReasonErrorCodeOpen(false);
  };

  // 查询响应参数异常码规则列表
  const queryExpReasonErrorCode = async (params: { respParamId: number }) => {
    try {
      setSpining(true);
      const { success, data } = await getExpReasonErrorCode(params);
      if (success) {
        setExpReasonErrorCodeList(data);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setSpining(false);
    }
  };

  // 按领域查询领域异常码列表
  const queryDomainErrorCodes = async (params: { domainId: number }) => {
    try {
      setSpining(true);
      const { success, data } = await getDomainErrorCodes(params);
      if (success) {
        setDomainErrorCodes(data);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setSpining(false);
    }
  };

  // 根据是否是TMF服务过滤领域异常码列表
  const filterDomainErrorCodes = (domainErrorCodes: DomainErrorCodesType[] = [], selectedService: any) => {
    const isTMFService = selectedService?.serviceCatg === 'T';

    return domainErrorCodes
      .map((item) => ({
        ...item,
        errorCodes: item.errorCodes.filter(({ errorCode }) =>
          isTMFService ? !errorCode?.startsWith('DBEP-') : errorCode?.startsWith('DBEP-'),
        ),
      }))
      .filter((item) => item.errorCodes.length > 0);
  };

  useEffect(() => {
    if (respParam?.respParamId && expReasonErrorCodeOpen) {
      queryExpReasonErrorCode({
        respParamId: respParam?.respParamId,
      });
    }
  }, [respParam, expReasonErrorCodeOpen]);

  useEffect(() => {
    if (selectedRootNode?.domainId && expReasonErrorCodeOpen) {
      queryDomainErrorCodes({
        domainId: selectedRootNode?.domainId,
      });
    }
  }, [selectedRootNode, expReasonErrorCodeOpen]);

  useEffect(() => {
    if (selectedService?.serviceId && expReasonErrorCodeOpen) {
      queryEntityServiceDetail();
    }
  }, [selectedService, expReasonErrorCodeOpen]);

  // useEffect(() => {
  //   if (entityServiceDetail && expReasonErrorCodeOpen) {
  //     const statusSchema = {
  //       type: 'Number',
  //       title: 'status',
  //       properties: {},
  //     };
  //     const reqSchema = transferServiceDetailToSchema(entityServiceDetail?.parameters || [], 'request');
  //     const respSchema = transferServiceDetailToSchema(entityServiceDetail?.responses || [], 'response');
  //     setAllSchema([reqSchema, respSchema, statusSchema]);
  //   }
  // }, [entityServiceDetail, expReasonErrorCodeOpen]);

  return (
    <>
      <Drawer
        title={formatMessage('MANAGEEXTENSIONSERVICE.RESPONSE.ERRORCODE.SET.TITLE')}
        onClose={onClose}
        maskClosable={false}
        open={expReasonErrorCodeOpen}
        width={1100}
        afterOpenChange={() => {
          form.resetFields();
        }}
        footer={
          <Space style={{ float: 'right' }}>
            <Button type="primary" onClick={onClose}>
              {formatMessage('PROJECT.COMMON.OK')}
            </Button>
          </Space>
        }
      >
        <Spin spinning={spining}>
          <OperationIconTitle
            title={formatMessage('ERRORCODE.ERRORCODELIST.TITLE')}
            opt={formatMessage('ERRORCODE.ADD')}
            handleClick={() => {
              setAddOpen(true);
            }}
            type={selectedRootNode?.state !== 'D' ? '' : 'add'}
          />
          <ResizableTable
            size="small"
            style={{ marginTop: '12px' }}
            columns={columns}
            dataSource={expReasonErrorCodeList || []}
            rowKey={(record: ExpReasonErrorCodeType) => record.respErrorCodeId}
            onRow={(record: any) => ({
              onClick: () => {
                setSelectedErrorCode(record);
              },
            })}
            pagination={false}
          />
        </Spin>
      </Drawer>
      <AddExpReasonErrorCode
        domainErrorCodes={filterDomainErrorCodes(domainErrorCodes, selectedService)}
        selectedRootNode={selectedRootNode}
        addOpen={addOpen}
        respParam={respParam}
        // allSchema={allSchema}
        entityServiceDetail={entityServiceDetail}
        // setAllSchema={setAllSchema}
        selectedService={selectedService}
        setAddOpen={setAddOpen}
        queryDomainErrorCodes={queryDomainErrorCodes}
        queryExpReasonErrorCode={queryExpReasonErrorCode}
      />
      <EditExpReasonErrorCode
        isProduct={isProduct}
        domainErrorCodes={filterDomainErrorCodes(domainErrorCodes, selectedService)}
        selectedRootNode={selectedRootNode}
        editOpen={editOpen}
        respParam={respParam}
        selectedErrorCode={selectedErrorCode}
        entityServiceDetail={entityServiceDetail}
        selectedService={selectedService}
        setEditOpen={setEditOpen}
        queryDomainErrorCodes={queryDomainErrorCodes}
        queryExpReasonErrorCode={queryExpReasonErrorCode}
      />
    </>
  );
};
export default ResponseErrorCodeList;
