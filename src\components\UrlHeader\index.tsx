import { CSSProperties, useEffect, useState } from 'react';
import classNames from 'classnames';
import styles from './index.less';
import { Button, Space, Dropdown, Select } from 'antd';
import { CaretDownOutlined } from '@ant-design/icons';
import useI18n from '@/hooks/useI8n';
import { useModel } from 'umi';

interface UrlHeaderPropsType {
  serviceDetail: any;
  style: CSSProperties;
  shwoBtnGroup: boolean;
  ButtonGroupFnObj?: {
    [k in 'debugServiceFn' | 'onSaveAsFn' | 'onSelectUseCaseFn']?: (e: any) => any;
  };
  hiddenSelectBtn?: boolean;
  isEditAPIGService?: boolean;
  saveUsType?: string;
  refreshAPIGService?: () => void;
}

const UrlHeader = ({
  serviceDetail = {},
  style = {},
  shwoBtnGroup = false,
  ButtonGroupFnObj = {},
  hiddenSelectBtn = false,
  isEditAPIGService = false,
  saveUsType = '',
  refreshAPIGService = () => {},
}: UrlHeaderPropsType) => {
  const { formatMessage } = useI18n();
  const { isConfigEnvironment } = useModel('useSystemConfigDataModel');
  const [selectedAddressType, setSelectedAddressType] = useState<string>('testing');
  const [prefix, setPrefix] = useState<string>('');

  const addressSource = [
    {
      label: 'Testing Address',
      value: 'testing',
    },
    {
      label: 'Production Address',
      value: 'production',
    },
  ];
  const items = [
    {
      key: 'USECASE',
      label: formatMessage('DEBUGSERVICE.URL.USECASE'),
    },
    ...(isConfigEnvironment
      ? [
          {
            key: 'APIGSERVICE',
            label: formatMessage('DEBUGSERVICE.URL.APIGSERVICE'),
          },
        ]
      : []),
  ];

  const onAddressChange = (value: string) => {
    setSelectedAddressType(value);
    if (value === 'testing') {
      // 按照后台需求，写死为9876
      setPrefix(`${window?.location?.protocol}//${window?.location?.hostname}:9876`);
    } else {
      setPrefix(window?.location?.origin);
    }
  };

  useEffect(() => {
    if (serviceDetail?.testPort) {
      // 按照后台需求，写死为9876
      setPrefix(`${window?.location?.protocol}//${window?.location?.hostname}:9876`);
    }
  }, [serviceDetail]);
  return (
    <div className={styles.urlWrapper} style={style}>
      <div>
        <Select
          options={addressSource}
          className={styles.addressSelect}
          onChange={onAddressChange}
          defaultValue={selectedAddressType}
        />
      </div>
      <div className={styles.url}>
        <div
          className={classNames({
            [styles.postStyle]: serviceDetail?.method === 'POST',
            [styles.getStyle]: serviceDetail?.method === 'GET' || serviceDetail?.method === 'PUT',
            [styles.patchStyle]: serviceDetail?.method === 'PATCH',
            [styles.deleteStyle]: serviceDetail?.method === 'DELETE',
          })}
        >
          {serviceDetail?.method}
        </div>
        <div>{`${prefix}${serviceDetail?.servicePath}`}</div>
      </div>
      {shwoBtnGroup && (
        <div className={styles.optionBtnGroup}>
          <Space>
            <Button
              className={classNames(styles.sendBtn, styles.commonHeight)}
              onClick={() => {
                if (ButtonGroupFnObj.debugServiceFn) {
                  ButtonGroupFnObj.debugServiceFn(selectedAddressType);
                }
              }}
            >
              {formatMessage('DEBUGSERVICE.URL.SEND')}
            </Button>
            {isEditAPIGService ? (
              <>
                <Button
                  // disabled={!isConfigEnvironment}
                  className={styles.commonHeight}
                  onClick={() => {
                    refreshAPIGService();
                  }}
                >
                  {formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.REFRESHSERVICE')}
                </Button>
                <Button
                  disabled={!isConfigEnvironment}
                  className={styles.commonHeight}
                  onClick={() => {
                    if (ButtonGroupFnObj.onSaveAsFn) [ButtonGroupFnObj.onSaveAsFn({ key: 'APIGSERVICE' })];
                  }}
                >
                  {formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.EDIT')}
                </Button>
              </>
            ) : saveUsType === 'ONLYUSECASE' ? (
              <Button
                className={styles.commonHeight}
                onClick={() => {
                  if (ButtonGroupFnObj.onSaveAsFn) [ButtonGroupFnObj.onSaveAsFn({ key: 'USECASE' })];
                }}
              >
                {formatMessage('DEBUGSERVICE.USECASEMODAL.EDIT')}
              </Button>
            ) : (
              <Dropdown.Button
                className={styles.commonHeight}
                menu={{ items, onClick: ButtonGroupFnObj.onSaveAsFn }}
                icon={<CaretDownOutlined />}
              >
                {formatMessage('DEBUGSERVICE.URL.SAVE')}
              </Dropdown.Button>
            )}
            {!hiddenSelectBtn && (
              <Button className={styles.commonHeight} onClick={ButtonGroupFnObj.onSelectUseCaseFn}>
                {formatMessage('DEBUGSERVICE.URL.SELECTUSECASE')}
              </Button>
            )}
          </Space>
        </div>
      )}
    </div>
  );
};

export default UrlHeader;
