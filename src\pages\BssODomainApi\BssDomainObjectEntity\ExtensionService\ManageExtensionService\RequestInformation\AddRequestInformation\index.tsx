import React, { useEffect, useRef, useState } from 'react';
import {
  Button,
  Drawer,
  Form,
  Input,
  Select,
  Space,
  message,
  InputNumber,
  Divider,
  InputRef,
  Flex,
  Spin,
  Tooltip,
} from 'antd';
import { operateRequestParam, addSchemaByDomainObjId } from '@/services/entityService';
import { EnumSourceProps } from '../../../../types';
import { useModel } from 'umi';
import flatteningDomainSchemaList from '@/utils/flatteningDomainSchemaList';
import { CommonYesOrNoSource, ParamTypeSourceWithoutObject } from '../../const';
import { getFormatTypeSource } from '../../utils';
import { autoFillDescByPropName } from '@/utils/common';
import useI18n from '@/hooks/useI8n';
import classNames from 'classnames';
import myIcon from '@/static/iconfont/iconfont.css';
import styles from '../index.less';
import CopySchemaDrawer from '../../SchemaManagement/CopySchemaDrawer';

interface IAddRequestInformationDrawer {
  operateType: string;
  selectedService: any;
  enumSource: EnumSourceProps[];
  selectedParamType: string;
  open: boolean;
  onCancel: () => void;
  onOk: () => void;
  updateDomainSchemaList: () => Promise<void>;
  selectedParam?: any;
  selectedReqBodyType?: string;
  selectedRootNode: any;
  isProjectAndProductOwned: boolean;
  domainSchemaList: any;
}

interface optionProps {
  label: string;
  value: string;
}

interface DomainSchemaObj {
  domainObjId: number;
  domainObjName: string;
  schemaList: Array<any>;
}

const AddRequestInformation: React.FC<IAddRequestInformationDrawer> = (props) => {
  const {
    open,
    selectedService,
    selectedParamType,
    selectedReqBodyType,
    enumSource,
    onCancel,
    onOk,
    selectedParam,
    operateType,
    selectedRootNode,
    updateDomainSchemaList,
    isProjectAndProductOwned,
    domainSchemaList = [],
  } = props;
  const { formatMessage } = useI18n();
  const [form] = Form.useForm();
  const [submitSpinning, setSubmitSpinning] = useState<boolean>(false);
  const [formValues, setFormValues] = useState<any>({}); // formValues
  const [avlValueType, setAvlValueType] = useState<string>('');
  const [avlValueTypeSource, setAvlValueTypeSource] = useState<optionProps[]>([]);
  const [avlValueTypeValue, setAvlValueTypeValue] = useState<string>('');
  const [disabled, setDisabled] = useState<boolean>(false);
  const [required, setRequired] = useState<boolean>(false);
  const [defaultValues, setDeFaultValues] = useState<any>({});
  const { currentDomainObjId } = useModel('manageExtensionServiceModel');
  const addSchemaInputRef = useRef<InputRef>(null);
  const [newSchemaName, setNewSchemaName] = useState('');
  const [selectedSchemaObj, setSelectedSchemaObj] = useState<any>({});
  const [isSchemaNameValid, setIsSchemaNameValid] = useState<boolean>(true);
  const [FormatTypeSource, setFormatTypeSource] = useState<any>([]);
  const [copyOpen, setCopyOpen] = useState<boolean>(false); // 拷贝Schema抽屉
  const [copyingSchema, setCopyingSchema] = useState<any>({}); // 待拷贝的Schema

  const currentRequired = Form.useWatch('required', form);
  const currentHidden = Form.useWatch('hidden', form);

  useEffect(() => {
    setFormatTypeSource(getFormatTypeSource(avlValueType));
  }, [avlValueType]);

  // 弹框关闭
  const onClose = async () => {
    setAvlValueTypeValue('');
    form.resetFields();
    onCancel?.();
  };

  const getStringBetween = (str: string, start: string, end: string) => {
    // 正则表达式匹配两个指定字符之间的内容
    const reg = /\[(\S*)\]/;
    if ((str.match(reg) || []).length > 1) {
      return (str.match(reg) || [])[1];
    }
    return '';
  };
  // 调用新增或修改请求参数接口
  const optRequestParam = async (param: any) => {
    setSubmitSpinning(true);
    try {
      const resultData = await operateRequestParam(operateType === 'Add' ? 'new' : 'update', param);

      if (resultData?.success) {
        message.success(
          formatMessage(
            operateType === 'Add'
              ? 'MANAGEEXTENSIONSERVICE.REQUEST.ADD.SUCCESS'
              : 'MANAGEEXTENSIONSERVICE.REQUEST.UPDATE.SUCCESS',
          ),
        );
        form.resetFields();
        onOk?.();
      }
      onCancel?.();
    } catch {
      message.error(
        formatMessage('MANAGEEXTENSIONSERVICE.REQUEST.OPERATION.ERROR', {
          operation: operateType === 'Add' ? formatMessage('PROJECT.COMMON.ADD') : formatMessage('PROJECT.COMMON.EDIT'),
        }),
      );
    } finally {
      setSubmitSpinning(false);
    }
  };

  const onAddSchemaNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setNewSchemaName(value);

    // 校验输入值,开头是大写字母，只能包含英文字母、数字和下划线
    const isValid = /^[A-Z][A-Za-z0-9_]*$/.test(value);
    setIsSchemaNameValid(isValid);
  };

  // 入参校验，构造入参
  const checkServiceReqParamData = () => {
    form.validateFields().then((values) => {
      // 构造入参
      let reqIn;
      switch (selectedParamType) {
        case 'Header':
          reqIn = 'H';
          break;
        case 'Path':
          reqIn = 'P';
          break;
        case 'Query':
          reqIn = 'Q';
          break;
        case 'Cookie':
          reqIn = 'C';
          break;
        case 'Body':
          reqIn = 'B';
          break;
        default:
          reqIn = 'H';
          break;
      }
      let param = {
        ...defaultValues,
        ...values,
        reqIn,
        serviceId: selectedService?.serviceId,
        domainObjId: currentDomainObjId,
      };
      if (selectedParamType === 'Body') {
        param = {
          ...param,
          // isArray: 'N',
          bodyContentType: selectedReqBodyType,
        };
      }
      if (operateType === 'Edit') {
        param = {
          ...param,
          reqParamId: selectedParam?.reqParamId,
        };
      }

      if (!ParamTypeSourceWithoutObject.includes(param.paramType)) {
        param = {
          ...param,
          refSchemaId: selectedSchemaObj?.schemaId,
          paramType: 'Ref',
        };
        // param.enumId = param.paramType;
        // param.paramType = 'Enum';
      } else {
        delete param?.refSchemaId;
        delete param?.refSchemaName;
      }
      if (param.avlValueType) {
        switch (param.avlValueType) {
          case 'Length':
            param.avlValue = `Length[${param.min},${param.max}]`;
            break;

          case 'Enum':
            const eId = enumSource.find(
              (i: any) => `${i.value}` === `${param.enum}` || i.label === `${param.enum}`,
            )?.value;
            param.avlValue = eId;
            param.enumId = eId;
            break;

          case 'Empty':
            param.avlValue = '';
            break;
          case 'Range':
            param.avlValue = `Range[${param.min},${param.max}]`;
            break;
          case 'Format':
            param.avlValue = param.typeFormat;
            param = {
              ...param,
              typeFormat: param.typeFormat,
            };
            break;
          case 'Values':
            param.avlValue = param.values;
            break;

          default:
            param.avlValue = '';
            break;
        }
      }
      if (param?.avlValueType !== 'Enum') {
        delete param?.enumId;
      }
      if (param?.avlValueType !== 'Format') {
        delete param?.typeFormat;
      }
      optRequestParam(param);
    });
  };

  // 通用的重置表单字段函数
  const resetFormFields = (resetAvlValueType = true) => {
    // 重置所有相关字段
    const resetFields: any = {
      typeFormat: undefined,
      min: undefined,
      max: undefined,
      values: undefined,
      enum: undefined,
    };

    if (resetAvlValueType) {
      resetFields.avlValueType = '';
      setAvlValueTypeValue('');
    }

    form.setFieldsValue(resetFields);
  };

  const onPropTypeChange = (value: any) => {
    const flatDomainSchemaList = flatteningDomainSchemaList(domainSchemaList);
    const schemaObj = flatDomainSchemaList?.find((item: any) => item?.schemaId === value);
    setSelectedSchemaObj(schemaObj);
    setAvlValueType(value);

    // 重置所有字段
    resetFormFields();

    setDisabled(false);
    setRequired(true);
    switch (value) {
      case 'String':
        setAvlValueTypeSource([
          { label: 'Empty', value: 'Empty' },
          { label: 'Length', value: 'Length' },
          { label: 'Format', value: 'Format' },
          { label: 'Enum', value: 'Enum' },
          { label: 'Values', value: 'Values' },
        ]);
        form.setFieldsValue({
          avlValueType: 'Empty',
        });
        break;

      case 'Boolean':
        setAvlValueTypeSource([{ label: 'Empty', value: 'Empty' }]);
        form.setFieldsValue({
          avlValueType: 'Empty',
        });
        break;

      case 'Long':
        setAvlValueTypeSource([
          { label: 'Empty', value: 'Empty' },
          { label: 'Length', value: 'Length' },
          { label: 'Range', value: 'Range' },
          { label: 'Enum', value: 'Enum' },
          { label: 'Values', value: 'Values' },
        ]);
        form.setFieldsValue({
          avlValueType: 'Empty',
        });
        break;
      case 'Integer':
        setAvlValueTypeSource([
          { label: 'Empty', value: 'Empty' },
          { label: 'Length', value: 'Length' },
          { label: 'Range', value: 'Range' },
          { label: 'Enum', value: 'Enum' },
          { label: 'Values', value: 'Values' },
        ]);
        form.setFieldsValue({
          avlValueType: 'Empty',
        });
        break;
      case 'Number':
        setAvlValueTypeSource([
          { label: 'Empty', value: 'Empty' },
          { label: 'Length', value: 'Length' },
          { label: 'Range', value: 'Range' },
          { label: 'Format', value: 'Format' },
          { label: 'Values', value: 'Values' },
        ]);
        form.setFieldsValue({
          avlValueType: 'Empty',
        });
        break;
      case 'Datetime':
        setAvlValueTypeSource([
          { label: 'Empty', value: 'Empty' },
          { label: 'Format', value: 'Format' },
          { label: 'Values', value: 'Values' },
        ]);
        form.setFieldsValue({
          avlValueType: 'Empty',
        });
        break;

      default:
        // TODO 可能不需要默认值
        setAvlValueTypeSource([]);
        setDisabled(true);
        setRequired(false);
        break;
    }
  };

  const onAvlValueTypeChange = (value: any) => {
    setAvlValueTypeValue(value);

    // 重置avlValueTypeValue相关字段，但保留avlValueType
    resetFormFields(false);
  };

  const handleEnumFilter = (input: any, option: any) => {
    return option?.label?.toLowerCase()?.indexOf(input?.toLowerCase()) >= 0;
  };

  // 新增Schema
  const addSchema = async (e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>) => {
    e.preventDefault();

    if (isSchemaNameValid) {
      if (currentDomainObjId) {
        const resultData = await addSchemaByDomainObjId({ schemaName: newSchemaName, domainObjId: currentDomainObjId });
        if (resultData?.success) {
          message.success(formatMessage('MANAGEEXTENSIONSERVICE.SCHEMA.ADD.SUCCESS'));
          await updateDomainSchemaList();
          setTimeout(() => {
            addSchemaInputRef.current?.focus();
          }, 0);
        }
      }
    } else {
      message.warning(formatMessage('MANAGEEXTENSIONSERVICE.SCHEMA.NAME.INVALID'));
    }
  };

  useEffect(() => {
    if (newSchemaName) {
      const flatDomainSchemaList = flatteningDomainSchemaList(domainSchemaList);
      const schemaObj = flatDomainSchemaList?.find((item: any) => item?.schemaName === newSchemaName);
      form.setFieldValue('paramType', schemaObj?.schemaId); // 新建Schema后默认选中
      onPropTypeChange(schemaObj?.schemaId);
      setNewSchemaName('');
    }
  }, [domainSchemaList]);

  useEffect(() => {
    if (currentRequired === 'Y' && currentHidden === 'Y') {
      form.setFieldValue('hidden', 'N');
      message.warning(formatMessage('PROJECT.COMMON.REQUIREDANDHIDDENBOTHYESNOTE'));
    }
  }, [currentRequired, currentHidden]);

  useEffect(() => {
    if (open && selectedParam) {
      // 深拷贝行数据后初始化值
      const defaultValues: any = {};

      Object.assign(defaultValues, selectedParam);

      if (!ParamTypeSourceWithoutObject.includes(defaultValues.paramType)) {
        // defaultValues.paramType = defaultValues.enumId;
        // 当前领域下的所有Schema
        const flatDomainSchemaList = flatteningDomainSchemaList(domainSchemaList);
        const schemaObj = flatDomainSchemaList?.find((i: any) => i?.schemaId === defaultValues?.refSchemaId);
        defaultValues.paramType = schemaObj?.schemaId;
        onPropTypeChange(defaultValues.paramType);
      } else {
        // 五种数据基本类型，处理avlValue字段的回显问题
        const { avlValue, typeFormat } = defaultValues;

        if ((avlValue === '' || avlValue === null) && !typeFormat) {
          defaultValues.avlValueType = 'Empty';
        } else if (avlValue === `${defaultValues.enumId}`) {
          defaultValues.avlValueType = 'Enum';
          defaultValues.enum = enumSource.find((i: any) => `${i.value}` === `${avlValue}`)?.label;
        } else if (avlValue?.toLowerCase().startsWith('length')) {
          defaultValues.avlValueType = 'Length';
          const betweenVal = getStringBetween(avlValue, '[', ']')?.split(',');
          defaultValues.min = betweenVal[0];
          defaultValues.max = betweenVal[1];
        } else if (avlValue?.toLowerCase().startsWith('range')) {
          defaultValues.avlValueType = 'Range';
          const betweenVal = getStringBetween(avlValue, '[', ']')?.split(',');
          defaultValues.min = betweenVal[0];
          defaultValues.max = betweenVal[1];
        } else if (typeFormat) {
          defaultValues.avlValueType = 'Format';
          defaultValues.typeFormat = typeFormat;
        } else {
          defaultValues.avlValueType = 'Values';
          defaultValues.values = avlValue;
        }
        onPropTypeChange(defaultValues.paramType);
        onAvlValueTypeChange(defaultValues.avlValueType);
      }
      if (operateType === 'Add') {
        defaultValues.isArray = 'N';
        defaultValues.required = 'N';
        defaultValues.hidden = 'N';
      }
      setDeFaultValues(defaultValues);
      setFormValues(defaultValues);
    }
  }, [selectedParam, open]);

  return (
    <>
      <Drawer
        title={`${
          operateType === 'Add' ? formatMessage('PROJECT.COMMON.ADD') : formatMessage('PROJECT.COMMON.EDIT')
        } ${selectedParamType} ${formatMessage('MANAGEEXTENSIONSERVICE.REQUEST.PARAM')}`}
        onClose={onClose}
        maskClosable={false}
        open={open}
        width={720}
        afterOpenChange={() => {
          form.resetFields();
        }}
        footer={
          <Space style={{ float: 'right' }}>
            <Spin spinning={submitSpinning}>
              <Button type="primary" onClick={checkServiceReqParamData}>
                {formatMessage('PROJECT.COMMON.SUBMIT')}
              </Button>
            </Spin>
            <Button onClick={onClose}>{formatMessage('PROJECT.COMMON.CANCEL')}</Button>
          </Space>
        }
      >
        <Form
          form={form}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
          initialValues={defaultValues}
          onValuesChange={(cValues, alValues) => {
            setFormValues(alValues);
          }}
        >
          <Form.Item
            label={formatMessage('PROJECT.COMMON.NAME')}
            name="reqParamName"
            rules={[
              { required: true, message: '' },
              {
                validator: (_, value) => {
                  if (value?.includes(' ')) {
                    return Promise.reject(
                      new Error(formatMessage('MANAGEEXTENSIONSERVICE.REQUEST.PARAM.NAME.SPACE.ERROR')),
                    );
                  }
                  return Promise.resolve();
                },
              },
            ]}
          >
            <Input
              allowClear
              disabled={isProjectAndProductOwned}
              onBlur={() => autoFillDescByPropName(form, 'reqParamName', 'comments')}
            />
          </Form.Item>
          <Form.Item
            label={formatMessage('MANAGEEXTENSIONSERVICE.REQUEST.PARAM.IS_ARRAY')}
            name="isArray"
            rules={[{ required: true, message: '' }]}
          >
            <Select
              showSearch
              options={CommonYesOrNoSource}
              disabled={isProjectAndProductOwned || selectedParamType === 'Path'}
            />
          </Form.Item>
          <Form.Item
            label={formatMessage('PROJECT.COMMON.REQUIRED')}
            name="required"
            rules={[{ required: true, message: '' }]}
          >
            <Select showSearch options={CommonYesOrNoSource} />
          </Form.Item>
          <Form.Item
            label={formatMessage('PROJECT.COMMON.HIDDEN')}
            name="hidden"
            rules={[{ required: true, message: '' }]}
          >
            <Select showSearch options={CommonYesOrNoSource} />
          </Form.Item>
          <Form.Item
            label={formatMessage('PROJECT.COMMON.TYPE')}
            name="paramType"
            rules={[{ required: true, message: '' }]}
            extra={
              avlValueType &&
              !ParamTypeSourceWithoutObject.includes(avlValueType) &&
              formatMessage('MANAGEEXTENSIONSERVICE.SCHEMA.EDIT.NOTES')
            }
          >
            {selectedParamType === 'Body' && selectedReqBodyType === 'json' ? (
              <Select
                options={[
                  ...ParamTypeSourceWithoutObject.map((item) => ({ label: item, value: item, key: item })),
                  ...domainSchemaList.map((item: DomainSchemaObj) => ({
                    label: <span>{item?.domainObjName}</span>,
                    value: item?.domainObjName,
                    options: item?.schemaList.map((i) => ({
                      label: i?.schemaName,
                      value: i?.schemaId,
                      key: i?.schemaId,
                    })),
                  })),
                ]}
                optionRender={(option) => {
                  // Schema节点
                  if (option.groupOption) {
                    return (
                      <Flex justify="space-between">
                        <div style={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap' }}>
                          {option.label}
                        </div>
                        {/* 拷贝功能 */}
                        <Tooltip title={formatMessage('PROJECT.COMMON.COPY')}>
                          <span
                            className={classNames(`${myIcon.iconfont} ${myIcon['icon-copy']} ${styles.deleteIcon}`, {
                              [styles.hide]: selectedRootNode?.state !== 'D',
                            })}
                            onClick={(e) => {
                              e.stopPropagation();
                              setCopyOpen(true);
                              setCopyingSchema({ schemaName: option.label, schemaId: option.value });
                            }}
                          />
                        </Tooltip>
                      </Flex>
                    );
                  }
                  return option.label;
                }}
                onChange={onPropTypeChange}
                showSearch
                filterOption={(input, option) => {
                  // 基本类型直接过滤
                  if (option?.label && typeof option.label === 'string') {
                    return option.label.toLowerCase().includes(input.toLowerCase());
                  }
                  // 组标题不参与过滤，总是显示（不需要特殊处理，Select 组件会自动处理）
                  return false;
                }}
                disabled={isProjectAndProductOwned}
                dropdownRender={(menu) => (
                  <>
                    {menu}
                    <Divider style={{ margin: '8px 0' }} />
                    <Space style={{ padding: '0 8px 4px' }}>
                      <Input
                        value={newSchemaName}
                        placeholder={formatMessage('MANAGEEXTENSIONSERVICE.SCHEMA.PLACEHOLDER')}
                        ref={addSchemaInputRef}
                        onChange={onAddSchemaNameChange}
                        onKeyDown={(e) => e.stopPropagation()}
                      />
                      <Button type="text" onClick={addSchema}>
                        + {formatMessage('MANAGEEXTENSIONSERVICE.SCHEMA.NEW')}
                      </Button>
                    </Space>
                  </>
                )}
              />
            ) : (
              <Select
                disabled={isProjectAndProductOwned}
                options={[
                  { label: 'String', value: 'String' },
                  { label: 'Boolean', value: 'Boolean' },
                  { label: 'Long', value: 'Long' },
                  { label: 'Number', value: 'Number' },
                  { label: 'Datetime', value: 'Datetime' },
                  { label: 'Integer', value: 'Integer' },
                ]} // 数据源为固定数据源+接口返回数据源
                onChange={onPropTypeChange}
                showSearch
                filterOption={(input, option) => (option?.label as string)?.toLowerCase().includes(input.toLowerCase())}
              />
            )}
          </Form.Item>
          {/* avlValueType 如果为复杂类型则不展示'Example'和'Allowable Values' */}
          {!avlValueType || ParamTypeSourceWithoutObject.includes(avlValueType) ? (
            <>
              <Form.Item
                label={formatMessage('MANAGEEXTENSIONSERVICE.REQUEST.PARAM.ALLOWABLE_VALUES')}
                style={{ marginBottom: 0 }}
              >
                <Form.Item
                  name="avlValueType"
                  style={{ display: 'inline-block', width: 'calc(33% - 5px)' }}
                  rules={[{ required, message: '' }]}
                >
                  <Select options={avlValueTypeSource} onChange={onAvlValueTypeChange} disabled={disabled} />
                </Form.Item>
                {avlValueTypeValue === 'Length' ? (
                  <Form.Item
                    name="min"
                    style={{ display: 'inline-block', width: 'calc(33%)', marginLeft: '5px' }}
                    rules={[
                      { required: true, message: '' },
                      {
                        validator: (_, value) => {
                          const minValue = formValues?.required !== 'Y' ? 0 : 1;
                          if (value < minValue) {
                            return Promise.reject(
                              new Error(formatMessage('MANAGEEXTENSIONSERVICE.MIN.ERROR', { min: minValue })),
                            );
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                  >
                    <InputNumber
                      addonBefore={formatMessage('MANAGEEXTENSIONSERVICE.MIN')}
                      precision={0}
                      min={formValues?.required !== 'Y' ? 0 : 1}
                      max={formValues.max}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                ) : null}
                {avlValueTypeValue === 'Length' ? (
                  <Form.Item
                    name="max"
                    style={{ display: 'inline-block', width: 'calc(33%)', marginLeft: '5px' }}
                    rules={[{ required: true, message: '' }]}
                  >
                    <InputNumber
                      addonBefore={formatMessage('MANAGEEXTENSIONSERVICE.MAX')}
                      precision={0}
                      min={formValues.min}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                ) : null}
                {avlValueTypeValue === 'Enum' ? (
                  <Form.Item
                    name="enum"
                    style={{ display: 'inline-block', width: 'calc(66% + 5px)', marginLeft: '5px' }}
                  >
                    <Select showSearch options={enumSource} filterOption={handleEnumFilter} />
                  </Form.Item>
                ) : null}
                {avlValueTypeValue === 'Format' ? (
                  <Form.Item
                    name="typeFormat"
                    style={{ display: 'inline-block', width: 'calc(66% + 5px)', marginLeft: '5px' }}
                  >
                    <Select options={FormatTypeSource} />
                  </Form.Item>
                ) : null}
                {avlValueTypeValue === 'Values' ? (
                  <Form.Item
                    name="values"
                    style={{ display: 'inline-block', width: 'calc(66% + 5px)', marginLeft: '5px' }}
                    rules={[{ required: true, message: '' }]}
                  >
                    <Input placeholder={formatMessage('MANAGEEXTENSIONSERVICE.VALUES.PLACEHOLDER')} />
                  </Form.Item>
                ) : null}
                {avlValueTypeValue === 'Range' ? (
                  <Form.Item
                    name="min"
                    style={{ display: 'inline-block', width: 'calc(33%)', marginLeft: '5px' }}
                    rules={[{ required: true, message: '' }]}
                  >
                    <InputNumber
                      addonBefore={formatMessage('MANAGEEXTENSIONSERVICE.MIN')}
                      precision={0}
                      max={formValues.max}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                ) : null}
                {avlValueTypeValue === 'Range' ? (
                  <Form.Item
                    name="max"
                    style={{ display: 'inline-block', width: 'calc(33%)', marginLeft: '5px' }}
                    rules={[{ required: true, message: '' }]}
                  >
                    <InputNumber
                      addonBefore={formatMessage('MANAGEEXTENSIONSERVICE.MAX')}
                      precision={0}
                      min={formValues.min}
                      style={{ width: '100%' }}
                    />
                  </Form.Item>
                ) : null}
              </Form.Item>
              <Form.Item label={formatMessage('MANAGEEXTENSIONSERVICE.REQUEST.PARAM.DEFAULT_VALUE')} name="defValue">
                <Input />
              </Form.Item>
              <Form.Item label={formatMessage('PROJECT.COMMON.EXAMPLE')} name="example">
                {selectedParamType === 'Body' && selectedReqBodyType === 'json' ? (
                  <Input.TextArea allowClear />
                ) : (
                  <Input allowClear />
                )}
              </Form.Item>
            </>
          ) : null}
          <Form.Item
            label={formatMessage('PROJECT.COMMON.DESCRIPTION')}
            name="comments"
            extra={formatMessage('PROJECT.COMMON.GETDESCBYPROPNAME')}
          >
            <Input.TextArea allowClear onBlur={() => autoFillDescByPropName(form, 'reqParamName', 'comments')} />
          </Form.Item>
        </Form>
      </Drawer>
      {/* 拷贝Schema */}
      <CopySchemaDrawer
        copyOpen={copyOpen}
        setCopyOpen={setCopyOpen}
        copyingSchema={copyingSchema}
        setNewSchemaName={setNewSchemaName}
        domainSchemaList={domainSchemaList}
        updateDomainSchemaList={updateDomainSchemaList}
      />
    </>
  );
};
export default AddRequestInformation;
