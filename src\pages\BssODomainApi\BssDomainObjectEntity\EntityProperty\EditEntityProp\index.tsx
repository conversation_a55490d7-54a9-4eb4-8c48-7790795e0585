import React, { useState, useEffect } from 'react';
import { Button, Drawer, Form, Input, Select, Space, message, InputNumber, Spin } from 'antd';
import { editDomainEntityProp } from '@/services/entityService';
import { ColumnNameSourceProps, EnumSourceProps, SensitiveLeveType } from '../../types';
import { autoFillDescByPropName } from '@/utils/common';
import useI18n from '@/hooks/useI8n';
import SelectWithAddButton from '@/components/SelectWithAddButton';
import getUUid from '@/utils/getUUid';
import { PublicSensitiveLevelId } from '@/pages/BssODomainApi/const';

interface IEditEntityPropDrawer {
  sensitiveLevelList: SensitiveLeveType[];
  selectedEntity: any;
  isProduct: boolean;
  columnNameSource: ColumnNameSourceProps[];
  enumSource: EnumSourceProps[];
  open: boolean;
  initValue: any;
  ignoreVersionSource: any;
  indexFunctionSource: any;
  onCancel: () => void;
  onOk: () => void;
}

interface IIndexFunctionSource {
  indexFuncId: number;
  indexFuncName: string;
  indexFuncExp: string;
  comments: string;
  applyPropType: string;
}

const EditEntityPropDrawer: React.FC<IEditEntityPropDrawer> = (props) => {
  const {
    open,
    ignoreVersionSource,
    indexFunctionSource,
    selectedEntity,
    isProduct,
    initValue,
    enumSource,
    columnNameSource,
    sensitiveLevelList,
    onCancel,
    onOk,
  } = props;
  const { formatMessage } = useI18n();
  const [form] = Form.useForm();
  const [formValues, setFormValues] = useState<any>({});
  const [defaultValues, setDeFaultValues] = useState<any>({});
  const [avlValueTypeSource, setAvlValueTypeSource] = useState<any>([]);
  const [avlValueTypeValue, setAvlValueTypeValue] = useState<string>('');
  const [disabled, setDisabled] = useState<boolean>(false);
  const [required, setRequired] = useState<boolean>(false);
  const [showIndexFunc, setShowIndexFunc] = useState<boolean>(false);
  const [indexFuncSource, setIndexFuncSource] = useState<any>([]);
  const [submitSpinning, setSubmitSpinning] = useState<boolean>(false);

  const CREATIONSOURCE = [
    { name: 'Product-Owned', key: 'P' },
    { name: 'Project-Customized', key: 'C' },
  ];

  const FormatDateSource = [
    { label: 'TIMESTAMP', value: 'TIMESTAMP' },
    { label: 'DATE', value: 'DATE' },
  ];

  const PropTypeStaticSource = ['String', 'Bytes', 'Long', 'Number', 'Datetime'];

  const SupportFeaturesSource = [
    { name: 'Can Filter', key: 'canFilter' },
    { name: 'Primary Key', key: 'pk' },
    { name: 'Index', key: 'index' },
    { name: 'Mandatory', key: 'mandatory' },
    { name: 'Can Order', key: 'canOrder' },
  ];

  // modal close
  const onClose = async () => {
    form.resetFields();
    onCancel?.();
  };

  const editEntityProp = async (param: any) => {
    setSubmitSpinning(true);
    try {
      // 调用修改实体属性接口
      const resultData = await editDomainEntityProp(param);
      if (resultData) {
        message.success('Succeed in editing entity property!');
        form.resetFields();
        onOk?.();
      }
      onCancel?.();
    } finally {
      setSubmitSpinning(false);
    }
  };

  const checkEditPropData = async () => {
    form.validateFields().then(async (values) => {
      // 构造入参
      const param = {
        ...values,
        ignoreVer: values?.ignoreVer?.length ? values?.ignoreVer?.join(',') : null, // 处理ignoreVer显示空选项的bug
        entityId: selectedEntity.entityId,
        propId: initValue.propId,
      };
      // PublicSensitiveLevelId为前台自定义的不存在于后台表中的sensitiveLevelId，表示该值不会入库。
      if (param?.sensitiveLevelId === PublicSensitiveLevelId) delete param?.sensitiveLevelId;
      if (!showIndexFunc) delete param?.indexFuncId;
      param.keyFlag = param.supportFeatures?.includes('pk') ? 'Y' : 'N';
      param.required = param.supportFeatures?.includes('mandatory') ? 'Y' : 'N';
      param.indexFlag = param.supportFeatures?.includes('index') ? 'Y' : 'N';
      param.orderFlag = param.supportFeatures?.includes('canOrder') ? 'Y' : 'N';
      param.filterFlag = param.supportFeatures?.includes('canFilter') ? 'Y' : 'N';

      if (!PropTypeStaticSource.includes(param.propType)) {
        param.enumId = param.propType;
        param.propType = 'Enum';
      }
      if (param.avlValueType) {
        switch (param.avlValueType) {
          case 'Length':
            param.avlValue = `Length[${param.min},${param.max}]`;
            break;

          case 'Enum':
            const eId = enumSource?.find(
              (i: any) => `${i.value}` === `${param?.enum}` || i.label === `${param?.enum}`,
            )?.value;
            param.avlValue = eId;
            param.enumId = eId;
            break;

          case 'Empty':
            param.avlValue = '';
            break;
          case 'Range':
            param.avlValue = `Range[${param.min},${param.max}]`;
            break;
          case 'Precision&Scale':
            param.avlValue = `Precision=${param.precision},Scale=${param.scale}`;
            break;
          case 'Format':
            param.avlValue = param.format;
            break;

          default:
            param.avlValue = '';
            break;
        }
      }
      editEntityProp(param);
    });
  };

  // 通用的重置表单字段函数
  const resetFormFields = (resetAvlValueType = true) => {
    // 重置所有相关字段
    const resetFields: any = {
      format: undefined,
      min: undefined,
      max: undefined,
      values: undefined,
      enum: undefined,
      precision: undefined,
      scale: undefined,
    };

    if (resetAvlValueType) {
      resetFields.avlValueType = '';
      setAvlValueTypeValue('');
    }

    form.setFieldsValue(resetFields);
  };

  const onPropTypeChange = (value: string) => {
    let source = [];
    form.setFieldsValue({
      avlValueType: '',
      supportFeatures: [],
    });
    setAvlValueTypeValue('');
    setDisabled(false);
    setRequired(true);
    switch (value) {
      case 'String':
        source = [
          { label: 'Length', value: 'Length' },
          { label: 'Enum', value: 'Enum' },
          { label: 'Empty', value: 'Empty' },
        ];
        setAvlValueTypeSource(source);
        form.setFieldsValue({
          avlValueType: 'Empty',
          supportFeatures: ['canFilter'],
        });
        break;

      case 'Bytes':
        source = [{ label: 'Empty', value: 'Empty' }];
        setAvlValueTypeSource(source);
        form.setFieldsValue({
          avlValueType: 'Empty',
          supportFeatures: ['canFilter'],
        });
        break;

      case 'Long':
        source = [
          { label: 'Length', value: 'Length' },
          { label: 'Range', value: 'Range' },
          { label: 'Enum', value: 'Enum' },
          { label: 'Empty', value: 'Empty' },
        ];
        setAvlValueTypeSource(source);
        form.setFieldsValue({
          avlValueType: 'Empty',
          supportFeatures: ['canFilter'],
        });
        break;
      case 'Number':
        source = [{ label: 'Precision&Scale', value: 'Precision&Scale' }];
        setAvlValueTypeSource(source);
        break;
      case 'Datetime':
        source = [{ label: 'Format', value: 'Format' }];
        setAvlValueTypeSource(source);
        break;

      default:
        setAvlValueTypeSource([]);
        form.setFieldsValue({
          supportFeatures: [],
        });
        setDisabled(true);
        setRequired(false);
        break;
    }
  };

  const onAvlValueTypeChange = (value: string) => {
    setAvlValueTypeValue(value);

    // 重置avlValueTypeValue相关字段
    resetFormFields(false);
  };

  const handleEnumFilter = (input: any, option: any) => {
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };
  const handlePropTypeFilter: any = (input: any, option: any) => {
    if (option.options) {
      return option.options.every((i: any) => {
        return i.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
      });
    }
    return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };

  const getStringBetween = (str: string, start: string, end: string) => {
    // 正则表达式匹配两个指定字符之间的内容
    const reg = /\[(\S*)\]/;
    if ((str.match(reg) || []).length > 1) {
      return (str.match(reg) || [])[1];
    }
    return '';
  };

  // 传给子组件SelectWithAddButton的自定义方法，将输入自动转为大写
  const handleCustomInputChange = (value: string) => {
    return value.toUpperCase();
  };

  const handleCustomColumnNameChange = (value: string) => {
    const existItem = columnNameSource?.find((item) => item?.value === value);
    form.setFieldsValue({
      propType: existItem ? existItem.type : 'String',
    });
    onPropTypeChange(existItem ? existItem.type : 'String');
  };

  useEffect(() => {
    // 深拷贝行数据后初始化值
    const defaultValues: any = {};
    const supportValues = [];
    Object.assign(defaultValues, initValue);
    if (defaultValues?.ignoreVer) {
      defaultValues.ignoreVer = defaultValues?.ignoreVer?.split(',');
    }
    // PublicSensitiveLevelId为前台自定义的不存在于后台表中的sensitiveLevelId，表示该值不会入库。
    if (!defaultValues?.sensitiveLevelId) {
      defaultValues.sensitiveLevelId = PublicSensitiveLevelId;
    }
    defaultValues.keyFlag === 'Y' ? supportValues.push('pk') : null;
    defaultValues.required === 'Y' ? supportValues.push('mandatory') : null;
    defaultValues.indexFlag === 'Y' ? supportValues.push('index') : null;
    defaultValues.orderFlag === 'Y' ? supportValues.push('canOrder') : null;
    defaultValues.filterFlag === 'Y' ? supportValues.push('canFilter') : null;
    defaultValues.supportFeatures = supportValues;
    if (!PropTypeStaticSource.includes(defaultValues.propType)) {
      defaultValues.propType = defaultValues.enumId;
      onPropTypeChange(defaultValues.propType);
    } else {
      // 五种数据基本类型，处理avlValue字段的回显问题
      const { avlValue } = defaultValues;
      if (avlValue === '' || avlValue === null) {
        defaultValues.avlValueType = 'Empty';
      } else if (avlValue === `${defaultValues.enumId}`) {
        defaultValues.avlValueType = 'Enum';
        defaultValues.enum = enumSource?.find((i: any) => `${i.value}` === `${avlValue}`)?.label;
      } else if (avlValue?.toLowerCase().startsWith('length')) {
        defaultValues.avlValueType = 'Length';
        const betweenVal = getStringBetween(avlValue, '[', ']')?.split(',');
        defaultValues.min = betweenVal[0];
        defaultValues.max = betweenVal[1];
      } else if (avlValue?.toLowerCase().startsWith('range')) {
        defaultValues.avlValueType = 'Range';
        const betweenVal = getStringBetween(avlValue, '[', ']')?.split(',');
        defaultValues.min = betweenVal[0];
        defaultValues.max = betweenVal[1];
      } else if (avlValue?.toLowerCase().startsWith('precision')) {
        defaultValues.avlValueType = 'Precision&Scale';
        const valueArr = avlValue.split(',');
        defaultValues.precision = valueArr[0]?.split('=')[1];
        defaultValues.scale = valueArr[1]?.split('=')[1];
      } else if (FormatDateSource.filter((item) => item.value === avlValue).length > 0) {
        defaultValues.avlValueType = 'Format';
        defaultValues.format = avlValue;
      }
      if (defaultValues?.propType && defaultValues?.supportFeatures?.find((item: any) => item === 'index')) {
        const indexFuncOption = indexFunctionSource
          ?.filter((item: IIndexFunctionSource) => item?.applyPropType === defaultValues?.propType)
          .map((item: IIndexFunctionSource) => ({
            name: item?.indexFuncName,
            key: item?.indexFuncId,
          }));

        if (indexFuncOption.length > 0) {
          setIndexFuncSource(indexFuncOption);
          setShowIndexFunc(true);
        }
      } else {
        setShowIndexFunc(false);
      }

      onPropTypeChange(defaultValues.propType);
      onAvlValueTypeChange(defaultValues.avlValueType);
    }
    setDeFaultValues(defaultValues);
  }, [initValue, open]);

  useEffect(() => {
    const formData = form.getFieldsValue();
    // 不能使用例如formValues?.propType来进行判断，否则视图和数据更新不及时
    if (formData?.propType && formData?.supportFeatures?.find((item: any) => item === 'index')) {
      const indexFuncOption = indexFunctionSource
        ?.filter((item: IIndexFunctionSource) => item?.applyPropType === formData?.propType)
        .map((item: IIndexFunctionSource) => ({
          name: item?.indexFuncName,
          key: item?.indexFuncId,
        }));

      if (indexFuncOption.length > 0) {
        setIndexFuncSource(indexFuncOption);
        setShowIndexFunc(true);
      }
    } else {
      setShowIndexFunc(false);
    }
  }, [formValues]); // 不能依赖form.getFieldsValue()，否则死循环

  return (
    <Drawer
      title={formatMessage('ENTITYPROP.EDIT.TITLE')}
      onClose={onClose}
      maskClosable={false}
      open={open}
      width={720}
      afterOpenChange={() => {
        form.resetFields();
      }}
      footer={
        <Space style={{ float: 'right' }}>
          <Spin spinning={submitSpinning}>
            <Button type="primary" onClick={checkEditPropData}>
              {formatMessage('PROJECT.COMMON.SUBMIT')}
            </Button>
          </Spin>
          <Button onClick={onClose}>{formatMessage('PROJECT.COMMON.CANCEL')}</Button>
        </Space>
      }
    >
      <Form
        form={form}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
        initialValues={defaultValues}
        onValuesChange={(cValues, alValues) => {
          setFormValues(alValues);
        }}
      >
        <Form.Item
          label={formatMessage('PROJECT.COMMON.NAME')}
          name="propName"
          rules={[
            { required: true, message: '' },
            {
              validator: (_, value) => {
                if (value?.includes(' ')) {
                  return Promise.reject(new Error(formatMessage('ENTITYPROP.NAME.NOSPACES')));
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <Input
            allowClear
            disabled={!isProduct && initValue?.creationSrc === 'P'}
            onBlur={() => autoFillDescByPropName(form, 'propName', 'comments')}
          />
        </Form.Item>
        <Form.Item
          label={formatMessage('ENTITYPROP.COLUMN_NAME')}
          name="columnName"
          rules={[
            { required: true, message: '' },
            {
              pattern: /^[A-Z0-9_]+$/,
              message: formatMessage('ENTITYPROP.COLUMN_NAME.FORMAT'),
            },
          ]}
        >
          <SelectWithAddButton
            disabled={!isProduct && initValue?.creationSrc === 'P'}
            originalOptionsList={[
              ...(!initValue?.columnName || columnNameSource?.find((item) => item.value === initValue?.columnName)
                ? []
                : [
                    {
                      label: initValue?.columnName,
                      value: initValue?.columnName,
                      key: getUUid(6),
                      type: 'String',
                      state: 'new',
                      // 这里主要处理通过输入"新增"的columnName已经随着该prop提交成功了，但实际该columnName并没有入对应的表（非bug，后台逻辑就是不入表）。
                    },
                  ]),
              ...columnNameSource,
            ]}
            parentForm={form}
            formFieldName="columnName"
            customInputChange={handleCustomInputChange}
            customSelectChange={handleCustomColumnNameChange}
          />
        </Form.Item>
        <Form.Item
          label={formatMessage('PROJECT.COMMON.TYPE')}
          name="propType"
          rules={[{ required: true, message: '' }]}
        >
          <Select
            options={[
              { label: 'String', value: 'String' },
              { label: 'Bytes', value: 'Bytes' },
              { label: 'Long', value: 'Long' },
              { label: 'Number', value: 'Number' },
              { label: 'Datetime', value: 'Datetime' },
              {
                label: <span>Enum</span>,
                title: 'Enum',
                options: enumSource,
              },
            ]} // 数据源为固定数据源+接口返回数据源
            onChange={onPropTypeChange}
            showSearch
            filterOption={handlePropTypeFilter}
          />
        </Form.Item>
        <Form.Item label={formatMessage('ENTITYPROP.ALLOWABLE_VALUES')} style={{ marginBottom: 0 }}>
          <Form.Item
            name="avlValueType"
            style={{ display: 'inline-block', width: 'calc(33% - 5px)' }}
            rules={[{ required, message: '' }]}
          >
            <Select options={avlValueTypeSource} onChange={onAvlValueTypeChange} disabled={disabled} />
          </Form.Item>
          {avlValueTypeValue === 'Length' ? (
            <Form.Item
              name="min"
              style={{ display: 'inline-block', width: 'calc(33% - 5px)', marginLeft: '5px' }}
              rules={[{ required: true, message: '' }]}
            >
              <InputNumber
                addonBefore={formatMessage('ENTITYPROP.MIN')}
                precision={0}
                min={1}
                max={formValues.max}
                style={{ width: '100%' }}
              />
            </Form.Item>
          ) : null}
          {avlValueTypeValue === 'Length' ? (
            <Form.Item
              name="max"
              style={{ display: 'inline-block', width: 'calc(33% - 8px)', marginLeft: '5px' }}
              rules={[{ required: true, message: '' }]}
            >
              <InputNumber
                addonBefore={formatMessage('ENTITYPROP.MAX')}
                precision={0}
                min={formValues.min}
                style={{ width: '100%' }}
              />
            </Form.Item>
          ) : null}
          {avlValueTypeValue === 'Enum' ? (
            <Form.Item
              name="enum"
              style={{ display: 'inline-block', width: 'calc(66% - 8px)', marginLeft: '5px' }}
              rules={[{ required: true, message: '' }]}
            >
              <Select options={enumSource} showSearch filterOption={handleEnumFilter} />
            </Form.Item>
          ) : null}
          {avlValueTypeValue === 'Format' ? (
            <Form.Item
              name="format"
              style={{ display: 'inline-block', width: 'calc(66% - 8px)', marginLeft: '5px' }}
              rules={[{ required: true, message: '' }]}
            >
              <Select options={FormatDateSource} />
            </Form.Item>
          ) : null}
          {avlValueTypeValue === 'Precision&Scale' ? (
            <Form.Item
              name="precision"
              style={{ display: 'inline-block', width: 'calc(33% - 8px)', marginLeft: '5px' }}
              rules={[{ required: true, message: '' }]}
            >
              <InputNumber
                addonBefore={formatMessage('ENTITYPROP.PRECISION')}
                precision={0}
                style={{ width: '100%' }}
              />
            </Form.Item>
          ) : null}
          {avlValueTypeValue === 'Precision&Scale' ? (
            <Form.Item
              name="scale"
              style={{ display: 'inline-block', width: 'calc(33% - 8px)', marginLeft: '5px' }}
              rules={[{ required: true, message: '' }]}
            >
              <InputNumber addonBefore={formatMessage('ENTITYPROP.SCALE')} precision={0} style={{ width: '100%' }} />
            </Form.Item>
          ) : null}
          {avlValueTypeValue === 'Range' ? (
            <Form.Item
              name="min"
              style={{ display: 'inline-block', width: 'calc(33% - 5px)', marginLeft: '5px' }}
              rules={[{ required: true, message: '' }]}
            >
              <InputNumber
                addonBefore={formatMessage('ENTITYPROP.MIN')}
                precision={0}
                max={formValues.max}
                style={{ width: '100%' }}
              />
            </Form.Item>
          ) : null}
          {avlValueTypeValue === 'Range' ? (
            <Form.Item
              name="max"
              style={{ display: 'inline-block', width: 'calc(33% - 8px)', marginLeft: '5px' }}
              rules={[{ required: true, message: '' }]}
            >
              <InputNumber
                addonBefore={formatMessage('ENTITYPROP.MAX')}
                precision={0}
                min={formValues.min}
                style={{ width: '100%' }}
              />
            </Form.Item>
          ) : null}
        </Form.Item>
        <Form.Item label={formatMessage('ENTITYPROP.DEFAULT_VALUE')} name="defValue">
          <Input />
        </Form.Item>
        <Form.Item label={formatMessage('DOMAINDETAIL.PROPERTIES.FEATURES')} name="supportFeatures">
          <Select mode="multiple" disabled={disabled}>
            {SupportFeaturesSource.map((i) => (
              <Select.Option key={i.key} value={i.key}>
                {i.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        {showIndexFunc && (
          <Form.Item label={formatMessage('ENTITYPROP.INDEX_FUNCTION')} name="indexFuncId">
            <Select allowClear>
              {indexFuncSource.map((i: any) => (
                <Select.Option key={i.key} value={i.key}>
                  {i.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        )}
        <Form.Item label={formatMessage('ENTITYPROP.SENSITIVE_DATA_LEVEL')} name="sensitiveLevelId">
          <Select>
            {sensitiveLevelList.map((i) => (
              <Select.Option key={i.sensitiveLevelId} value={i.sensitiveLevelId}>
                {i.sensitiveLevelName}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item label={formatMessage('PROJECT.COMMON.CREATIONSOURCE')} name="creationSrc">
          <Select disabled>
            {CREATIONSOURCE.map((i) => (
              <Select.Option key={i.key} value={i.key}>
                {i.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        {isProduct ? (
          <Form.Item label={formatMessage('ENTITYPROP.IGNORE_VERSION')} name="ignoreVer">
            <Select mode="multiple">
              {ignoreVersionSource?.map((i: any) => (
                <Select.Option key={i.key} value={i.key}>
                  {i.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        ) : null}
        <Form.Item
          label={formatMessage('PROJECT.COMMON.DESCRIPTION')}
          name="comments"
          rules={[{ required: true, message: '' }]}
          extra={formatMessage('PROJECT.COMMON.GETDESCBYPROPNAME')}
        >
          <Input.TextArea allowClear onBlur={() => autoFillDescByPropName(form, 'propName', 'comments')} />
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default EditEntityPropDrawer;
