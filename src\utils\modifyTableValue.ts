import { ITableDataInterface } from "@/typing";
import { IParameter } from "@/services/typing";
import { debugServiceProps, saveAsUseCaseServicePropsType } from "@/services";
import cloneDeep from "lodash/cloneDeep";

interface dataSourceType extends ITableDataInterface {
  children: dataSourceType[];
}

const renderRequestType = (item: IParameter) => {
  let type = item?.dataModel?.modelName || item.dataType || "";
  if (item?.isArray === "Y") {
    return item?.dataModel?.modelName ? `List<${type}>` : `${type}[]`;
  }
  return type;
};

// 递归处理parameters
export const processTreeData: (
  data: Array<IParameter>,
  pName?: string,
  pIndex?: number,
) => Array<ITableDataInterface> = (data, pName, pIndex) => {
  return data?.map((item, index) => {
    const { name, reqIn, avlValue, example, required, hidden, comments, isArray, typeFormat, dataModel, contentType } =
      item;

    let curValue = '';
    if (avlValue) {
      if (regExp.test(avlValue)) {
        curValue = example;
      }
    } else {
      curValue = example;
    }

    return {
      rowKey: `${pName || ''}-${pIndex || ''}-${name}-${index}`,
      in: reqIn || '',
      type: renderRequestType(item),
      name,
      required,
      hidden,
      example,
      avlValue,
      comments,
      isArray,
      typeFormat,
      dataModel,
      contentType,
      value: curValue,
      children:
        item?.dataModel && item?.dataModel?.properties?.length > 0
          ? processTreeData(item.dataModel.properties, item.name, index)
          : undefined,
    };
  });
};

// 对象中添加新属性
export const modifyTableValueFn = ({
  dataSource = [] as dataSourceType[],
  rowKey = '',
  colName = '',
  value = '' as string | any[],
}): dataSourceType[] => {
  dataSource.forEach((item: dataSourceType | any, index) => {
    if (item.rowKey === rowKey) {
      item[colName] = value;
    } else {
      if (item?.children?.length > 0) {
        modifyTableValueFn({
          dataSource: item?.children,
          rowKey,
          colName,
          value,
        });
      }
    }
  });
  return dataSource;
};

export const insertObjFn = ({ array = [] as any, newObj = {}, idx = 0 }) => {
  const temp = array.filter((el: any) => el.dataIndex !== 'example');
  let newArray = [];

  // 将原数组的前三个元素复制到新数组中
  newArray = temp.slice(0, idx);

  // 在新数组的第四个位置插入新对象
  newArray.push(newObj);

  return newArray.concat(temp.slice(idx));
};

export const regExp = /^(range|length|Range|Length)[\(\[]/;

export const getFormatterDataFn = (value: string) => {
  // range(、range[、length(、length[ 字符
  const firstSplit = (value || '').split(',');
  const optionsTempData = (firstSplit || [])
    .filter((i) => !!i)
    .map((i) => {
      const secondSplit = i.split(':');
      return {
        label: secondSplit?.[0]?.trim(),
        value: secondSplit?.[0]?.trim(),
        name: secondSplit?.[0]?.trim(),
        desc: secondSplit?.[1]?.trim() || '',
      };
    });
  return optionsTempData;
};

// 查询参数数据格式化
export const getRequestFormattingData = (values: any) => {
  const processData = processTreeData(values?.parameters);

  const headerArr = processData?.filter((i) => i.in === 'H');
  const pathArr = processData?.filter((i) => i.in === 'P');
  const queryArr = processData?.filter((i) => i.in === 'Q');
  const bodyArr = processData?.filter((i) => i.in === 'B');
  const cookieArr = processData?.filter((i) => i.in === 'C');

  return {
    newArrObj: {
      headerArr,
      pathArr,
      queryArr,
      bodyArr,
      cookieArr,
    },
    newObj: {
      H: cloneDeep(headerArr),
      P: cloneDeep(pathArr),
      Q: cloneDeep(queryArr),
      B: cloneDeep(bodyArr),
      C: cloneDeep(cookieArr),
    },
  };
};

/**
 * 1.将{in：[]}转化为 接口调用的数据 和 {in, name, value}[] 这种用于 use case 的数据
 */
export const changeDataToBeSentToUsedFn = ({ dataToBeSent, servPath, formatValuefn, servMethod }: any) => {
  let debugparamsData: debugServiceProps = {
    url: servPath,
    method: servMethod,
  };
  let serviceReqParamList = [];

  let recordRequiedParams: string[] = [];

  // 处理header参数
  if (dataToBeSent.H?.length > 0) {
    let headersObj: { [k: string]: any } = {};
    dataToBeSent.H.forEach((el: ITableDataInterface) => {
      const { name = '', value } = el;

      if (value && value.length > 0) {
        let curValue = formatValuefn(el);
        headersObj[name] = curValue;
        serviceReqParamList.push({
          reqIn: 'H',
          paramName: name,
          paramValue: curValue,
        });
      }
    });
    debugparamsData.headers = headersObj;
  }

  if (dataToBeSent?.B?.length > 0) {
    // 获取 body 的 name
    const { example, name, value } = dataToBeSent.B?.[0];

    try {
      debugparamsData.body = JSON.parse(dataToBeSent.BM);
    } catch (error) {
      debugparamsData.body = dataToBeSent.BM;
    }
    serviceReqParamList.push({
      reqIn: 'B',
      paramName: name,
      paramValue: dataToBeSent.BM,
    });

    if (dataToBeSent.BM) {
      for (let key in dataToBeSent) {
        if (dataToBeSent?.[key]?.length > 0 && key !== 'B' && key !== 'BM') {
          if (key === 'P') {
            let url = servPath;
            dataToBeSent[key].forEach((el: ITableDataInterface) => {
              const { name = '', value, required } = el;
              if (required === 'Y' && (!value || value?.length === 0)) {
                recordRequiedParams.push(name);
              }
              let curValue = formatValuefn(el);
              // 使用字符串拼接将变量插入正则表达式
              const pattern = new RegExp(`{${name}}`);
              url = url?.replace(pattern, curValue);
              serviceReqParamList.push({
                reqIn: 'P',
                paramName: name,
                paramValue: curValue,
              });
            });
            debugparamsData.url = url;
          }
          if (key === 'Q') {
            let paramsObj: { [k in string]: any } = {};
            dataToBeSent[key].forEach((el: ITableDataInterface) => {
              const { name = '', value, required } = el;
              if (required === 'Y' && (!value || value?.length === 0)) {
                recordRequiedParams.push(name);
              }
              // value有值才传，否则不传
              if (value && value?.length > 0) {
                let curValue = formatValuefn(el);
                paramsObj[name] = curValue;
                serviceReqParamList.push({
                  reqIn: 'Q',
                  paramName: name,
                  paramValue: curValue,
                });
              }
            });
            debugparamsData.query = paramsObj;
          }
        }
      }
    }
  } else {
    for (let key in dataToBeSent) {
      if (dataToBeSent?.[key]?.length > 0 && key !== 'B' && key !== 'BM') {
        if (key === 'P') {
          let url = servPath;
          dataToBeSent[key].forEach((el: ITableDataInterface) => {
            const { name = '', value, required } = el;
            if (required === 'Y' && (!value || value?.length === 0)) {
              recordRequiedParams.push(name);
            }
            let curValue = formatValuefn(el);
            // 使用字符串拼接将变量插入正则表达式
            const pattern = new RegExp(`{${name}}`);
            url = url?.replace(pattern, curValue);
            serviceReqParamList.push({
              reqIn: 'P',
              paramName: name,
              paramValue: curValue,
            });
          });
          debugparamsData.url = url;
        }
        if (key === 'Q') {
          let paramsObj: { [k in string]: any } = {};
          dataToBeSent[key].forEach((el: ITableDataInterface) => {
            const { name = '', value, required } = el;
            // if(required === 'Y' && (!value || value?.length === 0)){
            //   recordRequiedParams.push(name);
            // }
            // value有值才传，否则不传
            if (value && value?.length > 0) {
              let curValue = formatValuefn(el);
              paramsObj[name] = curValue;
              serviceReqParamList.push({
                reqIn: 'Q',
                paramName: name,
                paramValue: curValue,
              });
            }
          });
          debugparamsData.query = paramsObj;
        }
      }
    }
  }
  return {
    debugparamsData,
    serviceReqParamList,
    recordRequiedParams,
  };
};

/**
 * 2. 将 {in, name, value}[] 转化为 {in: []} 这种用于 dataToBeSent 的数据
 */
export const changeParamsListToDataToBeSent = (paramsList = []) => {
  let tempDataToBeSent: { [k: string]: any[] } = {};
  paramsList?.forEach((el: { reqIn: string; paramName?: string; paramValue?: string | object }) => {
    const { reqIn, paramName, paramValue } = el;
    const obj = {
      ...el,
      in: reqIn,
      name: paramName,
      value: paramValue,
    };
    if (tempDataToBeSent[reqIn]) {
      tempDataToBeSent[reqIn].push(obj);
    } else {
      const temp = [obj];
      tempDataToBeSent[reqIn] = temp;
    }
  });
  return tempDataToBeSent;
};

// 处理查询参数，将嵌套结构转换为扁平的键值对
export const processParams = (params: any) => {
  if (!params || !Array.isArray(params)) return {};

  const result: Record<string, any> = {};

  params.forEach((item) => {
    // 如果有value且不为空，则添加到结果中
    if (item?.value !== undefined && item?.value !== null && item?.value !== '') {
      if (typeof item.value === 'object' && !Array.isArray(item.value)) {
        // 如果value是对象，处理子属性
        const flattenedObj: Record<string, any> = {};

        // 处理直接子属性
        Object.entries(item.value).forEach(([key, val]) => {
          if (val !== undefined && val !== null && val !== '') {
            flattenedObj[key] = val;
          }
        });

        // 如果有children数组，检查它们的value
        if (Array.isArray(item.children)) {
          item.children.forEach((child: any) => {
            if (child?.value !== undefined && child?.value !== null && child?.value !== '') {
              flattenedObj[child.name] = child.value;
            }
          });
        }

        // 如果扁平化后的对象有内容，添加到结果中
        if (Object.keys(flattenedObj).length > 0) {
          result[item.name] = flattenedObj;
        }
      } else {
        // 如果value不是对象，直接添加
        result[item.name] = item.value;
      }
    }
  });

  return result;
};