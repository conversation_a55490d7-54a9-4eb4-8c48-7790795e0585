import React, { useEffect, useState } from 'react';
import { Popconfirm, TreeSelect, Flex, Button } from 'antd';
import { CaretDownOutlined, PoweroffOutlined, RedoOutlined } from '@ant-design/icons';
import { refreshCache } from '@/services';
import useI18n from '@/hooks/useI8n';
import { Spin, Tree } from 'antd';
import styles from './index.less';
import myIcon from '@/static/iconfont/iconfont.css';
import { useModel } from 'umi';
import RestartOdhServiceModal from '@/components/RestartOdhServiceModal';
import { isHotfix } from '@/global';

interface IBssDomainLeft {
  currentSelectKey?: string;
  odhDomainTreeData?: Array<any>;
  onSelectNode: (entityData: object) => void; // domain 选择
  onSelectRootNode: (entityData: object) => void; // 选择的根节点
  onChangeSelectKey: (key: any) => void; // 刷新缓存 通知上级刷新缓存
  refreshTreeNodeData: (treeNodeData: object) => void; // 刷新缓存 通知上级刷新缓存
  onSelectScrollNode: (node: object) => void;
  onRefresh: () => void;
}

const BssDomainLeft: React.FC<IBssDomainLeft> = (props) => {
  const {
    odhDomainTreeData,
    currentSelectKey,
    onSelectNode,
    onSelectRootNode,
    onChangeSelectKey,
    refreshTreeNodeData,
    onSelectScrollNode,
    onRefresh,
  } = props;

  const { setIsManageExtensionService } = useModel('manageExtensionServiceModel');
  const { formatMessage } = useI18n();

  const [spining, setSpining] = useState<boolean>(false); // menu loading
  const [odhDomainListData, setOdhDomainListData] = useState<Array<any>>([]); // 搜索之后的值
  const [selectedKeys, setSelectedKeys] = React.useState<React.Key[]>([]); // select keys
  const [filteredTreeData, setFilteredTreeData] = useState([]);
  const [expandedKeys, setExpandedKeys] = useState([]); //展开节点的keyList
  const [searchValue, setSearchValue] = useState(''); // 搜索值
  const [openRestartOdhServiceModal, setOpenRestartOdhServiceModal] = useState<boolean>(false);

  const filterTreeNode = (inputValue, treeNode) => {
    // 如果节点文本包含搜索值，则返回true，保留节点
    return treeNode.title.toLowerCase().includes(inputValue.toLowerCase());
  };

  const getParentNode = (currentKey: string) => {
    let topParent = null;
    const findParent = (nodes, parent) => {
      nodes.forEach((node) => {
        if (currentKey === node.key) {
          topParent = parent;
          if (topParent === null) {
            topParent = node;
          }
        } else if (node.children) {
          findParent(node.children, node);
        }
      });
    };
    findParent(odhDomainListData, null);
    return topParent;
  };

  const getCurrentNode = (selectedKey: string, odhDomainList: any) => {
    let currentNode = null;
    const findNode = (nodes) => {
      nodes.forEach((node) => {
        if (selectedKey === node.key) {
          currentNode = node;
        } else if (node.children) {
          findNode(node.children);
        }
      });
    };
    if (odhDomainList) {
      findNode(odhDomainList, null);
    } else {
      findNode(odhDomainListData, null);
    }
    return currentNode;
  };

  const onSelectTreeNode = (selectedKeys: React.Key[], info: any) => {
    if (selectedKeys?.length > 0) {
      onChangeSelectKey?.(selectedKeys[0]);
    }
  };

  const onSelectTree = (selectedKeys: React.Key[], info: any) => {
    if (selectedKeys?.length > 0) {
      const path = info.node?.path || [];
      const expandKeyList = path.map((item) => {
        return item.key;
      });
      const expandKeys = [...new Set([...expandedKeys, ...expandKeyList])];
      setExpandedKeys(expandKeys);
      setSelectedKeys(selectedKeys);
      if (info.node?.level === 3) {
        //选中当前的领域对象
        let currentKey = selectedKeys[0];
        let domainObjNode = getParentNode(currentKey);
        onSelectNode?.(domainObjNode);
        onSelectScrollNode?.(info.node);
      } else {
        onSelectNode?.(info.node);
      }
      //获取当前选中数据的最顶级节点
      let currentKey = selectedKeys[0];
      if (currentKey !== '') {
        let rootNode = getParentNode(currentKey);
        while (rootNode !== null && rootNode?.level !== 1) {
          rootNode = getParentNode(currentKey);
          currentKey = rootNode?.key;
        }
        onSelectRootNode?.(rootNode);
      }
    }
  };
  const onSelectSearch = (value: string, node: any) => {
    if (value !== '') {
      if (node?.level === 3) {
        return;
      }
      setSelectedKeys([value]);
      const currentNode = getCurrentNode(value);
      onSelectTree([value], { node: currentNode });
    }
  };

  const handleOdhDamainData = (odhDomainList: []) => {
    if (odhDomainList?.length) {
      odhDomainList.map((odhDomain) => {
        //一级菜单
        odhDomain.title = odhDomain.domainName;
        odhDomain.key = 'DOMAIN_' + odhDomain.domainId;
        odhDomain.value = 'DOMAIN_' + odhDomain.domainId;
        odhDomain.level = 1;
        odhDomain.module = 'DOMAIN';
        odhDomain.icon = <span className={`${myIcon.iconfont} ${myIcon['icon-domain']}`}></span>;

        let domainPath = [
          {
            name: odhDomain.title,
            key: odhDomain.key,
            level: odhDomain.level,
            domainState: odhDomain.state,
            isDisable: false,
            data: odhDomain,
          },
        ];
        odhDomain.path = domainPath;

        if (odhDomain.odhDomainObjList?.length) {
          odhDomain.children = odhDomain.odhDomainObjList;
          delete odhDomain.odhDomainObjList;

          odhDomain.children.map((odhDomainObj) => {
            odhDomainObj.title = odhDomainObj.domainObjName;
            odhDomainObj.key = 'DOMAIN_OBJ_' + odhDomainObj.domainObjId;
            odhDomainObj.value = 'DOMAIN_OBJ_' + odhDomainObj.domainObjId;
            odhDomainObj.level = 2;
            odhDomainObj.module = 'DOMAIN_OB';
            odhDomainObj.icon = <span className={`${myIcon.iconfont} ${myIcon['icon-domain-object']}`}></span>;

            let domainObjPath = [
              {
                name: odhDomain.title,
                key: odhDomain.key,
                level: odhDomain.level,
                domainState: odhDomain.state,
                isDisable: true,
                data: odhDomain,
              },
            ];
            let pathObj = {
              name: odhDomainObj.title,
              key: odhDomainObj.key,
              level: odhDomainObj.level,
              data: odhDomainObj,
            };
            domainObjPath.push(pathObj);
            odhDomainObj.path = domainObjPath;

            if (odhDomainObj.objDetailList?.length) {
              odhDomainObj.children = odhDomainObj.objDetailList;
              delete odhDomainObj.objDetailList;

              odhDomainObj.children.map((objDetail) => {
                objDetail.level = 3;
                let domainObjDetailPath = [];
                Object.assign(domainObjDetailPath, odhDomainObj.path);

                if (objDetail.detailName === 'ODH_ENTITY') {
                  objDetail.key = odhDomainObj.key + '_ENTITY';
                  objDetail.value = odhDomainObj.key + '_ENTITY';
                  objDetail.title = 'Entities';
                  objDetail.children = odhDomainObj.entityList;
                  objDetail.icon = <span className={`${myIcon.iconfont} ${myIcon['icon-entity']}`}></span>;
                  delete odhDomainObj.entityList;

                  let pathObj = {
                    name: objDetail.title,
                    key: objDetail.key,
                    level: objDetail.level,
                    data: objDetail,
                  };
                  domainObjDetailPath.push(pathObj);
                  objDetail.path = domainObjDetailPath;

                  objDetail.children.map((detailValue) => {
                    detailValue.title = detailValue.entityName;
                    detailValue.level = 4;
                    detailValue.key = objDetail.key + '_' + detailValue.entityId;
                    detailValue.value = objDetail.key + '_' + detailValue.entityId;
                    detailValue.icon = <span className={`${myIcon.iconfont} ${myIcon['icon-entity']}`}></span>;
                    detailValue.module = 'Entity';

                    let detailValuePath = [];
                    Object.assign(detailValuePath, objDetail.path);
                    let pathObj = {
                      name: detailValue.title,
                      key: detailValue.key,
                      level: detailValue.level,
                      data: detailValue,
                    };
                    detailValuePath.push(pathObj);
                    detailValue.path = detailValuePath;
                  });
                } else if (objDetail.detailName === 'ODH_DATA_MODEL') {
                  objDetail.key = odhDomainObj.key + '_MODEL';
                  objDetail.value = odhDomainObj.key + '_MODEL';
                  objDetail.children = odhDomainObj.dataModelList;
                  objDetail.title = 'Data Models';
                  objDetail.icon = <span className={`${myIcon.iconfont} ${myIcon['icon-data-model']}`}></span>;
                  delete odhDomainObj.dataModelList;

                  let pathObj = {
                    name: objDetail.title,
                    key: objDetail.key,
                    level: objDetail.level,
                    data: objDetail,
                  };
                  domainObjDetailPath.push(pathObj);
                  objDetail.path = domainObjDetailPath;

                  objDetail.children.map((detailValue) => {
                    detailValue.title = detailValue.modelName;
                    detailValue.level = 4;
                    detailValue.key = objDetail.key + '_' + detailValue.modelId;
                    detailValue.value = objDetail.key + '_' + detailValue.modelId;
                    detailValue.icon = <span className={`${myIcon.iconfont} ${myIcon['icon-data-model']}`}></span>;
                    detailValue.module = 'MODEL';

                    let detailValuePath = [];
                    Object.assign(detailValuePath, objDetail.path);
                    let pathObj = {
                      name: detailValue.title,
                      key: detailValue.key,
                      level: detailValue.level,
                      data: detailValue,
                    };
                    detailValuePath.push(pathObj);
                    detailValue.path = detailValuePath;
                  });
                } else if (objDetail.detailName === 'ODH_ENUM') {
                  objDetail.key = odhDomainObj.key + '_ENUM';
                  objDetail.value = odhDomainObj.key + '_ENUM';
                  objDetail.children = odhDomainObj.enumList;
                  objDetail.title = 'Enums';
                  objDetail.icon = <span className={`${myIcon.iconfont} ${myIcon['icon-enum']}`}></span>;
                  delete odhDomainObj.enumList;

                  let pathObj = {
                    name: objDetail.title,
                    key: objDetail.key,
                    level: objDetail.level,
                    data: objDetail,
                  };
                  domainObjDetailPath.push(pathObj);
                  objDetail.path = domainObjDetailPath;

                  objDetail.children.map((detailValue) => {
                    detailValue.title = detailValue.enumName;
                    detailValue.level = 4;
                    detailValue.key = objDetail.key + '_' + detailValue.enumId;
                    detailValue.value = objDetail.key + '_' + detailValue.enumId;
                    detailValue.module = 'ENUM';
                    detailValue.icon = <span className={`${myIcon.iconfont} ${myIcon['icon-enum']}`}></span>;

                    let detailValuePath = [];
                    Object.assign(detailValuePath, objDetail.path);
                    let pathObj = {
                      name: detailValue.title,
                      key: detailValue.key,
                      level: detailValue.level,
                      data: detailValue,
                    };
                    detailValuePath.push(pathObj);
                    detailValue.path = detailValuePath;
                  });
                }
              });
            }
          });
        }
      });

      setOdhDomainListData(odhDomainList);
      refreshTreeNodeData(odhDomainList);

      if (currentSelectKey === '') {
        setSelectedKeys([odhDomainList[0].key]);
        onSelectTree([odhDomainList[0].key], { node: odhDomainList[0] });
      } else {
        setSelectedKeys([currentSelectKey]);
        const currentNode = getCurrentNode(currentSelectKey, odhDomainList);
        onSelectTree([currentSelectKey], { node: currentNode });
      }
      setFilteredTreeData(odhDomainList);
    }
  };

  const onExpand = (keys) => {
    if (keys) {
      setExpandedKeys((prevKeys) => (keys.includes(prevKeys[0]) ? keys : [keys[0]]));
    }
  };

  // 处理数据
  useEffect(() => {
    if (odhDomainTreeData?.length > 0) {
      handleOdhDamainData(odhDomainTreeData);
    }
  }, [odhDomainTreeData]);

  useEffect(() => {
    setIsManageExtensionService(false);
    setSelectedKeys([currentSelectKey]);
    //获取当前key的node信息
    const currentNode = getCurrentNode(currentSelectKey);
    onSelectTree([currentSelectKey], { node: currentNode });
  }, [currentSelectKey]);

  return (
    <>
      <Spin wrapperClassName={styles.spinContainer} spinning={spining}>
        <div className={styles.bssApiLeftContent}>
          {/* hotfix界面增加背景颜色，简单的遮罩层方案即可 */}
          {isHotfix && <div className={styles.mask}></div>}
          {/* 搜索切换区域 */}
          <div className={styles.bssApiLeftContentSearch}>
            <Flex justify="space-between" align="center">
              <div style={{ fontWeight: 'bold' }}>{formatMessage('PROJECT.COMMON.DOMAIN')}</div>
              <Flex align="center" gap="0.3125rem">
                <Button style={{ width: 0 }} onClick={() => setOpenRestartOdhServiceModal(true)}>
                  <PoweroffOutlined />
                </Button>
              </Flex>
            </Flex>
            <TreeSelect
              showSearch={true}
              style={{ width: '100%', marginTop: '5px' }}
              allowClear
              value={searchValue}
              treeData={odhDomainListData}
              placeholder="Please select"
              treeDefaultExpandAll
              filterTreeNode={filterTreeNode}
              onChange={(value) => {
                setSearchValue(value);
              }}
              onSelect={onSelectSearch}
            />
          </div>
          {/* list展示区域 */}
          {odhDomainListData.length > 0 ? (
            <div className={styles.bssApiLeftContentList}>
              {/* domain */}
              <Tree
                blockNode={true}
                showIcon
                defaultExpandAll={true}
                switcherIcon={<CaretDownOutlined />}
                treeData={odhDomainListData}
                onSelect={onSelectTreeNode}
                selectedKeys={selectedKeys}
                expandedKeys={expandedKeys}
                onExpand={onExpand}
              />
            </div>
          ) : (
            <div className={styles.noData}>No Domain Data</div>
          )}
        </div>
      </Spin>
      <RestartOdhServiceModal open={openRestartOdhServiceModal} setVisible={setOpenRestartOdhServiceModal} />
    </>
  );
};

export default BssDomainLeft;
