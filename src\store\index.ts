import { create } from 'zustand'

import type { User } from "../types/api";
import storage from '../utils/storage';

export const useStore = create<{
    token: string
    userInfo: {
        userImg: string
        _id: string
        userId: number
        userName: string
        userEmail: string
        mobile: string
        deptId: string
        deptName: string
        job: string
        state: number
        role: number
        createId: number
        roleList: string
    }
    collapsed: boolean
    isDark: boolean
    cityList: [{ id: number, name: string }]
    getToken: (token: string) => void
    getUserInfo: (userInfo: User.userItem) => void
    updateCollapsed: () => void
    updateTheme: (isDark: boolean) => void
}>(set => (
    {
        token: '',
        userInfo: {
            userImg: '',
            _id: '',
            userId: 0,
            userName: '',
            userEmail: '',
            mobile: '',
            deptId: '',
            deptName: '',
            job: '',
            state: 0,
            role: 0,
            createId: 0,
            roleList: ''
        },
        collapsed: false,
        isDark: storage.get('isDark'),
        cityList: [
            { id: 1001, name: '北京' }
        ],
        getToken: token => set({ token }),
        getUserInfo: (userInfo: User.userItem) => set({ userInfo }),
        updateCollapsed: () => set(state => ({ collapsed: !state.collapsed })),
        updateTheme: (isDark: boolean) => set({ isDark })
    }
))



