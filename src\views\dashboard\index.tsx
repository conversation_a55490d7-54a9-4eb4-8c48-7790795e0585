import { But<PERSON>, Card, Descriptions, type DescriptionsProps } from 'antd'
import styles from './index.module.less'
import { useEffect, useState } from 'react'
import { useCharts } from '../../hook/useCharts'
import { useStore } from '../../store'
import { formatMoney, formatNum, formatState } from '../../utils'
import api from '../../api'
import type { DashBoardType } from '../../types/api'

const DashBoard = () => {
    const userInfo = useStore(state => state.userInfo)
    const [report, setReport] = useState<DashBoardType.ReportData>()
    const [lineRef, lineChart] = useCharts()
    const [pieCityRef, pieCityChart] = useCharts()
    const [pieAgeRef, pieAgeChart] = useCharts()
    const [radarRef, radarChart] = useCharts()

    const renderLineChart = async () => {
        try {
            if (!lineChart) return
            const data = await api.getLineData()
            lineChart?.setOption(
                {
                    title: {
                        text: '订单和流水走势图'
                    },
                    legend: {
                        data: ['订单', '流水']
                    },
                    tooltip: {
                        trigger: 'axis'
                    },
                    grid: {
                        left: 50,
                        right: 50,
                        bottom: '10%'
                    },
                    xAxis: {
                        data: data.label,
                    },
                    yAxis: {
                        type: 'value'
                    },
                    series: [{
                        name: '订单',
                        type: 'line',
                        data: data.order
                    },
                    {
                        name: '流水',
                        type: 'line',
                        data: data.money
                    }
                    ]
                }
            )
        } catch (error) {
            console.log(error);
        }
    }
    const renderPieCityChart = async () => {
        try {
            if (!pieCityChart) return
            const data = await api.getPieCityData()
            pieCityChart?.setOption(
                {
                    title: {
                        text: '司机城市分布',
                        left: 'center',

                    },
                    tooltip: {
                        trigger: 'item'
                    },
                    legend: {
                        orient: 'vertical',
                        left: 'left'
                    },
                    series: [
                        {
                            name: '城市分布',
                            type: 'pie',
                            radius: '50%',
                            data: data
                        }
                    ]
                }
            )
        } catch (error) {
            console.log(error);
        }
    }
    const renderPieAgeChart = async () => {
        try {
            if (!pieAgeChart) return
            const data = await api.getPieAgeData()
            pieAgeChart?.setOption(
                {
                    title: {
                        text: '司机年龄分布',
                        left: 'center',

                    },
                    tooltip: {
                        trigger: 'item'
                    },
                    legend: {
                        orient: 'vertical',
                        left: 'left'
                    },
                    series: [
                        {
                            name: '年龄分布',
                            type: 'pie',
                            radius: [50, 100],
                            roseType: 'area',
                            data: data.sort(function (a, b) {
                                return a.value - b.value;
                            }),
                        },
                    ],
                }
            )
        } catch (error) {
            console.log(error);
        }
    }
    const renderRadarChart = async () => {
        try {
            if (!radarChart) return
            const data = await api.getRadarData()
            radarChart?.setOption(
                {
                    legend: {
                        data: ['模型诊断'],
                    },
                    radar: {
                        indicator: data.indicator
                    },
                    series: [
                        {
                            name: '模型诊断',
                            type: 'radar',
                            data: data.data
                        }
                    ]
                }
            )
        } catch (error) {
            console.log(error);
        }
    }
    useEffect(() => {
        renderLineChart()
        renderPieCityChart()
        renderPieAgeChart()
        renderRadarChart()
    }, [lineChart, pieCityChart, pieAgeChart, radarChart])
    const items: DescriptionsProps['items'] = [
        {
            key: '1',
            label: '用户ID',
            children: userInfo.userId,
        },
        {
            key: '2',
            label: '邮箱',
            children: userInfo.userEmail,
        },
        {
            key: '3',
            label: '状态',
            children: formatState(userInfo.state),
        },
        {
            key: '4',
            label: '手机号',
            children: userInfo.mobile,
        },
        {
            key: '5',
            label: '岗位',
            children: userInfo.job,
        },
        {
            key: '6',
            label: '部门',
            children: userInfo.deptName,
        }
    ];
    useEffect(() => {
        getReportData()
    }, [])
    const getReportData = async () => {
        try {
            const data = await api.getReportData()
            setReport(data)
        } catch (error) {
            console.log(error);
        }
    }
    const handleRefresh = () => {
        renderPieCityChart()
        renderPieAgeChart()
    }
    return (
        <div className={styles.container}>
            <div className={styles.userInfo}>
                <img className={styles.userImg} src={userInfo.userImg || '/src/assets/react.svg'} />
                <Descriptions title="User Info" items={items} />
            </div>
            <div className={styles.report}>
                <div className={styles.card}>
                    <div className={styles.title}>司机数量</div>
                    <div className={styles.data}>{formatNum(report?.driverCount)}个</div>
                </div>
                <div className={styles.card}>
                    <div className={styles.title}>总流水</div>
                    <div className={styles.data}>{formatMoney(report?.totalMoney)}元</div>
                </div>
                <div className={styles.card}>
                    <div className={styles.title}>总订单</div>
                    <div className={styles.data}>{formatNum(report?.orderCount)}单</div>
                </div>
                <div className={styles.card}>
                    <div className={styles.title}>开通城市</div>
                    <div className={styles.data}>{formatNum(report?.cityNum)}座</div>
                </div>
            </div>
            <div className={styles.chart}>
                <Card title="订单流水走势图" extra={<Button type='primary' onClick={renderLineChart}>刷新</Button>}>
                    <div ref={lineRef} className={styles.itemLine}></div>
                </Card>
            </div>
            <div className={styles.chart} >
                <Card title="司机分布" extra={<Button type='primary' onClick={handleRefresh}>刷新</Button>}>
                    <div className={styles.pieChart}>
                        <div ref={pieCityRef} className={styles.itemPie}></div>
                        <div ref={pieAgeRef} className={styles.itemPie}></div>
                    </div>
                </Card>
            </div>
            <div className={styles.chart}>
                <Card title="模型诊断" extra={<Button type='primary' onClick={renderRadarChart}>刷新</Button>}>
                    <div ref={radarRef} className={styles.itemLine}></div>
                </Card>
            </div>
        </div>
    )
}

export default DashBoard