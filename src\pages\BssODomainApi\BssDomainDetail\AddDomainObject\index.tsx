import React, { useState, useRef, useEffect } from 'react';
import useI18n from '@/hooks/useI8n';
import { Button, Drawer, Form, Input, Select, Space, Spin, message } from 'antd';
import { addDomainObject } from '@/services/domainService';

interface IAddDomainObjectDrawer {
  isProduct: boolean;
  domainId: number;
  open: boolean;
  ignoreVersionSource: any;
  onCancel: () => void;
  onOk: () => void;
}

const AddDomainObjectDrawer: React.FC<IAddDomainObjectDrawer> = (props) => {
  const { open, ignoreVersionSource, domainId, isProduct, onCancel, onOk } = props;
  const [submitSpinning, setSubmitSpinning] = useState<boolean>(false);

  const { formatMessage } = useI18n();

  const [form] = Form.useForm();
  const [formValues, setFormValues] = useState<any>({}); // formValues

  const CREATIONSOURCE = [
    {
      name: 'Product-Owned',
      key: 'P',
    },
    {
      name: 'Project-Customized',
      key: 'C',
    },
  ];

  const TYPESOURCE = [
    {
      name: 'Aggregate',
      key: 'A',
    },
    {
      name: 'Collection',
      key: 'C',
    },
  ];

  const initValues = {
    creationSrc: isProduct ? 'P' : 'C',
  };

  // modal close
  const onClose = async () => {
    onCancel?.();
    form.resetFields();
  };

  const addDomainObj = async () => {
    setSubmitSpinning(true);
    const curPostData = {
      ...formValues,
      ignoreVer: formValues?.ignoreVer?.length ? formValues?.ignoreVer?.join(',') : null, // 处理ignoreVer显示空选项的bug
      domainId,
    };
    try {
      const resultData = await addDomainObject(curPostData);

      if (resultData?.success) {
        message.success(formatMessage('DOMAIN.OBJECT.ADD.SUCCESS'));
        onOk?.();
        form.resetFields();
      }
    } catch (error) {
    } finally {
      setSubmitSpinning(false);
    }
  };

  const checkDomainObjData = () => {
    form.validateFields().then(() => {
      addDomainObj();
    });
  };

  return (
    <Drawer
      title={formatMessage('DOMAIN.OBJECT.ADD.TITLE')}
      onClose={onClose}
      maskClosable={false}
      open={open}
      width={720}
      afterOpenChange={() => {
        form.resetFields();
      }}
      footer={
        <Space style={{ float: 'right' }}>
          <Spin spinning={submitSpinning}>
            <Button type="primary" onClick={checkDomainObjData}>
              {formatMessage('PROJECT.COMMON.SUBMIT')}
            </Button>
          </Spin>
          <Button onClick={onClose}>{formatMessage('PROJECT.COMMON.CANCEL')}</Button>
        </Space>
      }
    >
      <Form
        form={form}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
        initialValues={initValues}
        onValuesChange={(cValues, alValues) => {
          setFormValues(alValues);
        }}
      >
        <Form.Item
          label={formatMessage('DOMAIN.OBJECT.FORM.NAME')}
          name="domainObjName"
          rules={[{ required: true, message: formatMessage('DOMAIN.OBJECT.FORM.NAME.REQUIRED') }]}
        >
          <Input allowClear />
        </Form.Item>
        <Form.Item
          label={formatMessage('DOMAIN.OBJECT.FORM.CODE')}
          name="domainObjCode"
          rules={[
            { required: true, message: formatMessage('DOMAIN.OBJECT.FORM.CODE.REQUIRED') },
            {
              validator: (_, value) => {
                const regex = /^[a-z][a-zA-Z0-9]*$/;
                if (!regex.test(value)) {
                  return Promise.reject(new Error(formatMessage('DOMAIN.OBJECT.FORM.CODE.VALIDATOR_LOWERCASE')));
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <Input allowClear />
        </Form.Item>
        <Form.Item label={formatMessage('DOMAIN.OBJECT.FORM.TYPE')} name="domainObjType">
          <Select>
            {TYPESOURCE.map((i) => (
              <Select.Option key={i.key} value={i.key}>
                {i.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item label={formatMessage('DOMAIN.ENTITY.FORM.CREATION_SOURCE')} name="creationSrc">
          <Select disabled={true}>
            {CREATIONSOURCE.map((i) => (
              <Select.Option key={i.key} value={i.key}>
                {i.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        {isProduct ? (
          <Form.Item label={formatMessage('DOMAIN.ENTITY.FORM.IGNORE_VERSION')} name="ignoreVer">
            <Select mode="multiple">
              {ignoreVersionSource.map((i: any) => (
                <Select.Option key={i.key} value={i.key}>
                  {i.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        ) : null}
        <Form.Item
          label={formatMessage('DOMAIN.ENTITY.FORM.DESCRIPTION')}
          name="comments"
          rules={[{ required: true, message: '' }]}
        >
          <Input.TextArea allowClear />
        </Form.Item>
      </Form>
    </Drawer>
  );
};
export default AddDomainObjectDrawer;
