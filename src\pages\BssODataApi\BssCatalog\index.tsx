import React, { useEffect, useState, useRef } from 'react';
import { Input, Select, message } from 'antd';
import {
  SearchOutlined,
  DownloadOutlined,
  FileSearchOutlined,
  OpenAIOutlined,
  HeatMapOutlined,
} from '@ant-design/icons';
import { IGroupList, IEntityList, ICatalog, IOriginalCatalogList } from '@/services/typing.d';
import { getAICCPrompt, queryCatalogMenu, queryDomainMenu } from '@/services';
import CustomerCollpaseForDomain from './CustomerCollpaseForDomain';
import CustomerCollpaseForCatalog from './CustomerCollpaseForCatalog';
import { ICurrentActiveKeyCatalog } from '@/typing.d';
import classNames from 'classnames';
import { BYCATALOG, BYDOMAIN } from '@/constants';
import useI18n from '@/hooks/useI8n';
import { Spin } from 'antd';
import { judgeExistInEntity, judgeExistInCatalog } from '@/utils/judgeCurrentKeyInData';
import DownloadFileModal from './DownloadFileModal';
import { cleanCatalogData, cleanGroupData, getReflectedCatalogData } from '@/utils/cleanDataAmount';
import styles from './index.less';
import { useStore } from '@/store';
import AICCPromptModal from './AICCPromptModal';
import myIcon from '@/static/iconfont/iconfont.css';

interface IBssCatalogLeft {
  otherEntitykey?: string;
  onSelectMenu: (entityData: IEntityList) => void; // domain 选择
  onSelectCatalogMenu: (menu: ICurrentActiveKeyCatalog) => void; // catalog 选择
  onSelectType: (selectType: string) => void; // 类型选择
  onRefresh: () => void; // 刷新缓存 通知上级刷新缓存
  onOpenSelectUseCase: () => void;
  onOpenSelectAPIGService: () => void;
  onOpenSelectErrorCode: () => void;
}

const BssCatalogLeft: React.FC<IBssCatalogLeft> = (props) => {
  const { setIsAICCTypeShow } = useStore();

  const {
    otherEntitykey,
    onSelectMenu,
    onSelectCatalogMenu,
    onSelectType,
    onRefresh,
    onOpenSelectUseCase,
    onOpenSelectAPIGService,
    onOpenSelectErrorCode,
  } = props;

  const searchref = useRef<any>();
  const { formatMessage } = useI18n();
  const ODataApiType = [
    {
      name: formatMessage('MENU.SELECT.DOMAIN'),
      key: BYDOMAIN,
    },
    {
      name: formatMessage('MENU.SELECT.CATALOG'),
      key: BYCATALOG,
    },
  ];

  const [searchvalue, setSearchValue] = useState(''); // 搜索值
  const [selectType, setSelectType] = useState<string>(ODataApiType[0].key); // 目录类型
  const [listData, setListData] = useState<Array<IGroupList>>([]); // domain 原始数据
  const [listDataAfterClean, setListDataAfterClean] = useState<Array<IGroupList>>([]); // domain转换数量之后的数据
  const [catalogListdata, setCatalogListData] = useState<Array<ICatalog>>([]); // catalog原始数据
  const [catalogListdataAfterClean, setCatalogListDataAfterClean] = useState<Array<ICatalog>>([]); // catalog转换数量之后的数据
  const [spining, setSpining] = useState<boolean>(false); // menu loading
  const [menuRefreshFlag, setMenuRefreshFlag] = useState({
    flag: false,
    data: [],
  }); // control menu refresh
  const [openDownload, setOpenDownload] = useState<boolean>(false); // download modal
  const [openAICCPrompt, setOpenAICCPrompt] = useState<boolean>(false); // AICCPrompt modal
  const [AICCPromptInformation, setAICCPromptInformation] = useState<any>({}); // AICCPrompt 信息
  const [selectedEntity, setSelectedEntity] = useState<any>({}); // 选中的实体（左侧菜单）

  // 目录请求
  const requestCatalogData = async () => {
    if (selectType === BYCATALOG) {
      const { data } = await queryCatalogMenu();
      // 因queryCatalogMenu接口改变，返回的字段都改变，所以需要映射一下相应字段
      const reflectedData = getReflectedCatalogData(data || []);
      setCatalogListData(reflectedData || []);
      return reflectedData || [];
    } else {
      const { data } = await queryDomainMenu();
      setListData(data || []);
      return data || [];
    }
  };

  // AICC Prompt 请求
  const getAICCPromptInformation = async () => {
    try {
      const { success, data } = await getAICCPrompt();
      if (success) {
        setAICCPromptInformation(data?.data);
      }
    } catch {
      message.error('Failed to get AI prompt information');
    }
  };

  // 搜索
  const handleSearch = (value: string) => {
    setSearchValue(value);
  };

  // init data
  const initMenuData = async () => {
    setSpining(true);
    try {
      await requestCatalogData();
      setSpining(false);
    } catch (error) {
      setSpining(false);
    }
  };

  // 查询数据
  useEffect(() => {
    if (selectType) {
      initMenuData();
    }
  }, [selectType]);

  // 清洗domainList 并设置group和domain的serviceAmount
  useEffect(() => {
    const listAfterClean = cleanGroupData(listData || []);
    setListDataAfterClean(listAfterClean);
  }, [listData]);

  // 清洗catalogList 并设置catalog和subcatalog的serviceAmount
  useEffect(() => {
    const listAfterClean = cleanCatalogData(catalogListdata || []);
    setCatalogListDataAfterClean(listAfterClean);
  }, [catalogListdata]);

  // useEffect(() => {
  //   getAICCPromptInformation();
  // }, []);
  return (
    <Spin wrapperClassName={styles.spinContainer} spinning={spining}>
      <div className={styles.bssApiLeftContent}>
        <div className={styles.titleContainer}>
          <span className={styles.title}>{formatMessage('MENU.TITLE.TITLE')}</span>
          <div>
            {/* AICC联调界面入口*/}
            {/* {selectType === BYDOMAIN && (
              <div
                className={styles.downLoad}
                title={formatMessage('PROJECT.COMMON.AICCPROMPT')}
                onClick={() => {
                  setOpenAICCPrompt(true);
                }}
              >
                <OpenAIOutlined />
              </div>
            )} */}
            <div
              className={styles.downLoad}
              title={formatMessage('PROJECT.COMMON.ERRORCODE')}
              onClick={onOpenSelectErrorCode}
            >
              <span className={`${myIcon.iconfont} ${myIcon['icon-error-code']} ${styles.versionIcon1}`} />
            </div>
            <div
              className={styles.downLoad}
              title={formatMessage('PROJECT.COMMON.APIGSERVICE')}
              onClick={onOpenSelectAPIGService}
            >
              <span className={`${myIcon.iconfont} ${myIcon['icon-apig-service']} ${styles.versionIcon1}`} />
            </div>
            <div
              className={styles.downLoad}
              title={formatMessage('PROJECT.COMMON.SEARCHUSECASE')}
              onClick={onOpenSelectUseCase}
            >
              <FileSearchOutlined />
            </div>
            <div
              className={styles.downLoad}
              title={formatMessage('PROJECT.COMMON.DOWNLOAD')}
              onClick={() => {
                if (selectType === BYDOMAIN) {
                  setIsAICCTypeShow(true);
                } else {
                  setIsAICCTypeShow(false);
                }
                setOpenDownload(true);
              }}
            >
              <DownloadOutlined />
            </div>
          </div>
        </div>
        {/* 搜索切换区域 */}
        <div className={styles.bssApiLeftContentSearch}>
          <Select
            style={{ width: '115px' }}
            defaultValue={selectType}
            value={selectType}
            onChange={(value) => {
              setSearchValue('');
              setSelectType(value);
              onSelectType(value);
              // 切换清空搜索的值
              // searchref.current.input!.value = ''; 这里清空有问题, react不响应,已提issue:  官方未提供clear,建议受控该组件
              // 不受控的临时方案
              (
                document?.querySelector('.bssCatalogInput')?.querySelector('.anticon.anticon-close-circle') as any
              )?.click();
              searchref?.current?.input?.blur();
            }}
          >
            {ODataApiType.map((i) => (
              <Select.Option key={i.key} value={i.key}>
                {`${i.name}`}
              </Select.Option>
            ))}
          </Select>
          <Input
            className={classNames(styles.search, 'bssCatalogInput')}
            placeholder={formatMessage('MENU.SEARCH.PLACEHOLDER')}
            onPressEnter={() => handleSearch(searchref?.current?.input?.value)}
            allowClear
            autoComplete="off"
            onChange={(e) => {
              if (!e?.target?.value) {
                handleSearch('');
              }
            }}
            suffix={
              <SearchOutlined
                style={{ cursor: 'pointer' }}
                onClick={() => handleSearch(searchref?.current?.input?.value)}
              />
            }
            ref={searchref}
          />
        </div>
        {/* list展示区域 */}
        <div className={styles.bssApiLeftContentList}>
          {/* domain */}
          {selectType === BYDOMAIN ? (
            <CustomerCollpaseForDomain
              menuRefreshFlag={menuRefreshFlag}
              completeRefreash={() =>
                setMenuRefreshFlag({
                  flag: false,
                  data: [],
                })
              }
              otherEntitykey={otherEntitykey}
              searchvalue={searchvalue}
              groupData={listDataAfterClean}
              onItemChange={(entityItem) => {
                setSelectedEntity(entityItem);
                onSelectMenu?.(entityItem);
              }}
              onRefresh={(currentActiveKey) => {
                // 选中项是不是在新的数据里面
                const isExist = judgeExistInEntity(currentActiveKey, listData);
                if (isExist) {
                  // 刷新详情
                  onRefresh?.();
                }
              }}
            />
          ) : null}
          {/* catalog */}
          {selectType === BYCATALOG ? (
            <CustomerCollpaseForCatalog
              searchvalue={searchvalue}
              catalogData={catalogListdataAfterClean}
              menuRefreshFlag={menuRefreshFlag}
              completeRefreash={() =>
                setMenuRefreshFlag({
                  flag: false,
                  data: [],
                })
              }
              onItemChange={(menu) => {
                onSelectCatalogMenu(menu);
              }}
              onRefresh={(currentActiveKey) => {
                // 选中项是不是在新的数据里面
                const isExist = judgeExistInCatalog(currentActiveKey, catalogListdata);
                if (isExist) {
                  // 刷新详情
                  onRefresh?.();
                }
              }}
            />
          ) : null}
        </div>
      </div>
      <DownloadFileModal selectType={selectType} open={openDownload} onCancel={() => setOpenDownload(false)} />
      <AICCPromptModal
        selectedEntity={selectedEntity}
        AICCPromptInformation={AICCPromptInformation}
        open={openAICCPrompt}
        onCancel={() => setOpenAICCPrompt(false)}
      />
    </Spin>
  );
};

export default BssCatalogLeft;
