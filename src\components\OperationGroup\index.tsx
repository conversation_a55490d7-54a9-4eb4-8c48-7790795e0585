/**
 * 用于表单的操作项，支持 edit 和 delete
 */
import { Space, Popconfirm, Tooltip } from 'antd';
import { FormOutlined, DeleteOutlined, CheckOutlined, CloseOutlined } from '@ant-design/icons';
import styles from './index.less';
import useI18n from '@/hooks/useI8n';

interface IOperationGroup {
  btnList: Array<{
    value: string;
    onClick: () => void;
    title?: string;
    [k: string]: any;
  }>;
}

const OperationGroup = ({ btnList = [] }: IOperationGroup) => {
  const { formatMessage } = useI18n();
  return (
    <div className={styles.operationGroup}>
      <Space size={20}>
        {btnList.map((item, index) => {
          const { value, ...rest } = item;
          switch (value) {
            case 'edit':
              return (
                <Tooltip title={formatMessage('PROJECT.COMMON.EDIT')}>
                  <FormOutlined key={`edit-${index}`} className={styles.operationItem} {...rest} />
                </Tooltip>
              );
            case 'delete':
              const { onClick, title = '', ...other } = rest;
              return (
                <Popconfirm
                  key={`delete-${index}`}
                  title={title}
                  onConfirm={() => {
                    item?.onClick();
                  }}
                  okText={formatMessage('PROJECT.COMMON.CONFIRM')}
                  cancelText={formatMessage('PROJECT.COMMON.CANCEL')}
                >
                  <Tooltip title={formatMessage('PROJECT.COMMON.DELETE')}>
                    <DeleteOutlined className={styles.operationItem} {...other} />
                  </Tooltip>
                </Popconfirm>
              );
            case 'save':
              return (
                <Tooltip title={formatMessage('PROJECT.COMMON.SAVE')}>
                  <CheckOutlined key={`save-${index}`} className={styles.operationItem} {...rest} />
                </Tooltip>
              );
            case 'cancel':
              return (
                <Tooltip title={formatMessage('PROJECT.COMMON.CANCEL')}>
                  <CloseOutlined key={`cancel-${index}`} className={styles.operationItem} {...rest} />
                </Tooltip>
              );
            default:
              return null;
          }
        })}
      </Space>
    </div>
  );
};

export default OperationGroup;
