import React, { useEffect, useState, useRef } from 'react';
import BssDomainLeft from './BssDomain';
import BssDomainDetail from './BssDomainDetail';
import BssDomainObjectDetail from './BssDomainObjectDetail';
import BssDomainObjectEntity from './BssDomainObjectEntity';
import DataModelListDetail from '@/components/DataModelList/DataModelListDetail';
import EnumManagementDetail from '@/components/EnumManagementList/EnumManagementDetail';
import { Row, Col, Spin } from 'antd';
import styles from './index.less';
import classNames from 'classnames';
import { queryODomainTreeData, getIndexFunc } from '@/services/domainService';
import { getIgnoreVersion } from '@/services/dataModelService';
import { useModel } from 'umi';
import UnusablePage from '@/components/UnusablePage';
import { isHotfix } from '@/global';

const BssODomainApi = () => {
  const { systemConfigData, isConfigEnvironment, initSystemConfigData } = useModel('useSystemConfigDataModel');
  const [spining, setSpining] = useState<boolean>(false); // menu loading
  const [odhDomainTreeData, setOdhDomainTreeData] = useState<Array<any>>([]); // 搜索之后的值

  const [selectedDomainNode, setSelectedDomainNode] = React.useState<object>(); //

  const [selectedNodeLeft, setSelectedNodeLeft] = useState<string>(''); // download modal

  const [isProduct, setIsProduct] = useState<boolean>(false); // 系统配置项（产品环境or项目定制环境）

  const [selectedRootNode, setSelectedRootNode] = React.useState<object>({}); //
  const [scrollNode, setScrollNode] = React.useState<object>({}); //
  const [treeNodeData, setTreeNodeData] = React.useState<object>([]); //

  const [currentSelectKey, setCurrentSelectKey] = useState<string>('');
  const [ignoreVerList, setIgnoreVerList] = useState<any>([]);
  const [ignoreVersionSource, setIgnoreVersionSource] = useState<any>([]);
  const [indexFunctionSource, setIndexFunctionSource] = useState<any>([]);
  const [configLoading, setConfigLoading] = useState(true);

  // 安全的获取通过iframe传递的domainCode
  const getSafeDomainCode = () => {
    try {
      // 处理普通查询参数
      const urlParams = new URLSearchParams(window.location.search);
      let domainCode = urlParams.get('domainCode');

      // 如果没有，尝试从hash中获取
      if (!domainCode) {
        const hash = window.location.hash;
        const queryIndex = hash.indexOf('?');
        if (queryIndex !== -1) {
          const hashParams = new URLSearchParams(hash.substring(queryIndex + 1));
          domainCode = hashParams.get('domainCode');
        }
      }

      // 验证domainCode
      if (domainCode && typeof domainCode === 'string' && domainCode.trim() !== '') {
        return domainCode.trim();
      }

      return null; // 或返回默认值
    } catch (error) {
      console.error('Error parsing domainCode:', error);
      return null;
    }
  };
  // init data
  const initMenuData = async () => {
    setSpining(true);
    try {
      const { success, data } = await queryODomainTreeData();
      if (success) {
        const iframeDomainCode = getSafeDomainCode();
        if (iframeDomainCode) {
          const filteredData = (data.odhDomainList || []).filter((item: any) => item?.domainCode === iframeDomainCode);
          setOdhDomainTreeData(filteredData);
        } else {
          setOdhDomainTreeData(data.odhDomainList || []);
        }
        setSpining(false);
      }
    } catch (error) {
      setSpining(false);
    }
  };

  // 查询索引函数
  const queryIndexFunc = async () => {
    const { success, data } = await getIndexFunc();
    if (success) {
      setIndexFunctionSource(data);
    }
  };

  const getParentNode = (currentKey: string) => {
    let topParent = null;
    const findParent = (nodes, parent) => {
      nodes.forEach((node) => {
        if (currentKey === node.key) {
          topParent = parent;
          if (topParent === null) {
            topParent = node;
          }
        } else if (node.children) {
          findParent(node.children, node);
        }
      });
    };
    findParent(treeNodeData, null);
    return topParent;
  };

  const qryIgnoreVersion = async () => {
    const { success, data } = await getIgnoreVersion();
    if (success) {
      setIgnoreVerList(data);
    }
  };

  // 查询数据
  useEffect(() => {
    const loadConfig = async () => {
      //查询系统配置项，当前为产品定义环境or项目定制环境
      await initSystemConfigData();
      setConfigLoading(false);
    };
    loadConfig();
  }, []);

  // 查询数据
  useEffect(() => {
    if (!(!isConfigEnvironment && isHotfix)) {
      initMenuData();
      setSelectedNodeLeft('');
      // setSelectedDomainNode();
      //查询忽略版本
      qryIgnoreVersion();
      // 查询索引函数
      queryIndexFunc();
    }
  }, [isHotfix, isConfigEnvironment]);

  useEffect(() => {
    setIsProduct(systemConfigData?.product || false);
  }, [systemConfigData]);

  useEffect(() => {
    if (odhDomainTreeData?.length > 0) {
      const currentDomainObjData = odhDomainTreeData?.find((item: any) => {
        if (selectedDomainNode?.domainId) {
          // 如果 selectedDomainNode 有 domainId，直接比较 item 的 domainId
          return item?.domainId === selectedDomainNode.domainId;
        } else {
          // 如果没有 domainId，检查 item 的 children 中是否有匹配的 domainObjId
          return item?.children?.some((domainObj: any) => domainObj?.domainObjId === selectedDomainNode?.domainObjId);
        }
      });
      const ignoreVersionObj = ignoreVerList.filter(
        (item: any) => item?.productLine === currentDomainObjData?.prodLine,
      )[0];
      const ignoreVerSource = ignoreVersionObj?.verCodes?.map((item: any) => {
        return {
          name: item,
          key: item,
        };
      });
      setIgnoreVersionSource(ignoreVerSource || []);
    }
  }, [odhDomainTreeData, selectedDomainNode]);

  return (
    <Spin wrapperClassName={styles.spinContainer} spinning={spining}>
      {configLoading ? null : (
        <>
          {/* 在使用环境不能使用Hotfix的功能 */}
          {!isConfigEnvironment && isHotfix ? (
            <UnusablePage />
          ) : (
            <div id="bssApiLeftContainer" style={{ height: '100%' }}>
              <Row gutter={8} style={{ height: '100%' }}>
                <Col span={6} style={{ height: '100%', overflowY: 'auto' }}>
                  {/* 目录 */}
                  <BssDomainLeft
                    currentSelectKey={currentSelectKey}
                    odhDomainTreeData={odhDomainTreeData}
                    onSelectRootNode={(rootNode) => {
                      setSelectedRootNode(rootNode);
                    }}
                    refreshTreeNodeData={(treeNodeData) => {
                      setTreeNodeData(treeNodeData);
                    }}
                    onRefresh={() => {
                      initMenuData();
                    }}
                    onSelectNode={(data) => {
                      switch (data?.level) {
                        case 1:
                          setSelectedNodeLeft('DOMAIN');
                          setSelectedDomainNode(data);
                          break;

                        case 2:
                          setSelectedNodeLeft('DOMAIN_OBJ');
                          setScrollNode(data);
                          setSelectedDomainNode(data);
                          break;

                        case 4:
                          if (data.module === 'Entity') {
                            setSelectedNodeLeft('DOMAIN_ENTITY');
                            setSelectedDomainNode(data);
                            break;
                          } else if (data.module === 'MODEL') {
                            setSelectedNodeLeft('DOMAIN_MODAL');
                            setSelectedDomainNode(data);
                            break;
                          } else if (data.module === 'ENUM') {
                            setSelectedNodeLeft('DOMAIN_ENUM');
                            setSelectedDomainNode(data);
                            break;
                          }

                        default:
                          break;
                      }
                    }}
                    onChangeSelectKey={(key) => {
                      setCurrentSelectKey(key);
                    }}
                    onSelectScrollNode={(node) => {
                      setScrollNode(node);
                    }}
                  />
                </Col>
                <Col span={18} style={{ height: '100%', overflowY: 'auto' }}>
                  {selectedNodeLeft === 'DOMAIN' ? (
                    <BssDomainDetail
                      ignoreVersionSource={ignoreVersionSource}
                      selectedDomainNode={selectedDomainNode}
                      isProduct={isProduct}
                      setPageLoading={setSpining}
                      onRefresh={(currentSelectKey) => {
                        initMenuData();
                        setCurrentSelectKey(currentSelectKey);
                      }}
                      onSelectDomainObj={(data) => {
                        let key = null;
                        if (data.entityId) {
                          setSelectedNodeLeft('DOMAIN_ENTITY');
                          const currentKey = 'DOMAIN_OBJ_' + data.domainObjId + '_ENTITY_' + data.entityId;
                          setCurrentSelectKey(currentKey);
                          setSelectedDomainNode(data);
                          key = currentKey;
                        } else {
                          setSelectedNodeLeft('DOMAIN_OBJ');
                          const currentKey = 'DOMAIN_OBJ_' + data.domainObjId;
                          setCurrentSelectKey(currentKey);
                          setSelectedDomainNode(data);
                          key = currentKey;
                        }
                        let rootNode = getParentNode(key);
                        while (rootNode !== null && rootNode?.level !== 1) {
                          rootNode = getParentNode(key);
                          key = rootNode?.key;
                        }
                        setSelectedRootNode(rootNode);
                      }}
                    />
                  ) : null}

                  {selectedNodeLeft === 'DOMAIN_OBJ' ? (
                    <BssDomainObjectDetail
                      ignoreVersionSource={ignoreVersionSource}
                      selectedDomainNode={selectedDomainNode}
                      isProduct={isProduct}
                      selectedRootNode={selectedRootNode}
                      scrollNode={scrollNode}
                      onRefresh={(currentSelectKey) => {
                        initMenuData();
                        setCurrentSelectKey(currentSelectKey);
                      }}
                      onSelectMenuObj={(data) => {
                        let currentKey = '';
                        switch (data?.module) {
                          case 'Entity':
                            setSelectedNodeLeft('DOMAIN_ENTITY');
                            currentKey = 'DOMAIN_OBJ_' + data.domainObjId + '_ENTITY_' + data.entityId;
                            setCurrentSelectKey(currentKey);
                            setSelectedDomainNode(data);
                            break;

                          case 'MODEL':
                            setSelectedNodeLeft('DOMAIN_MODAL');
                            currentKey = 'DOMAIN_OBJ_' + data.domainObjId + '_MODEL_' + data.modelId;
                            setCurrentSelectKey(currentKey);
                            setSelectedDomainNode(data);
                            break;

                          case 'ENUM':
                            setSelectedNodeLeft('DOMAIN_ENUM');
                            currentKey = 'DOMAIN_OBJ_' + data.domainObjId + '_ENUM_' + data.enumId;
                            setCurrentSelectKey(currentKey);
                            setSelectedDomainNode(data);
                            break;
                          case 'DOMAIN':
                            setSelectedNodeLeft('DOMAIN');
                            currentKey = 'DOMAIN_' + data.domainId;
                            setCurrentSelectKey(currentKey);
                            setSelectedDomainNode(data);
                            break;
                          case 'DOMAIN_OBJ':
                            setSelectedNodeLeft('DOMAIN_OBJ');
                            currentKey = 'DOMAIN_OBJ_' + data.domainObjId;
                            setCurrentSelectKey(currentKey);
                            setSelectedDomainNode(data);
                            break;
                          default:
                            break;
                        }
                      }}
                    />
                  ) : null}

                  {selectedNodeLeft === 'DOMAIN_ENTITY' ? (
                    <BssDomainObjectEntity
                      indexFunctionSource={indexFunctionSource}
                      ignoreVersionSource={ignoreVersionSource}
                      selectedDomainNode={selectedDomainNode}
                      isProduct={isProduct}
                      selectedRootNode={selectedRootNode}
                      onSelectExtensionService={(data: any) => {
                        // console.log(data);
                      }}
                      onRefresh={(currentSelectKey) => {
                        initMenuData();
                        setCurrentSelectKey(currentSelectKey);
                      }}
                    />
                  ) : null}
                  {selectedNodeLeft === 'DOMAIN_MODAL' ? (
                    <DataModelListDetail
                      selectedDomainNode={selectedDomainNode}
                      isProduct={isProduct}
                      selectedRootNode={selectedRootNode}
                      onRefresh={(currentSelectKey) => {
                        initMenuData();
                        setCurrentSelectKey(currentSelectKey);
                      }}
                    />
                  ) : null}
                  {selectedNodeLeft === 'DOMAIN_ENUM' ? (
                    <EnumManagementDetail
                      selectedDomainNode={selectedDomainNode}
                      isProduct={isProduct}
                      selectedRootNode={selectedRootNode}
                      onRefresh={(currentSelectKey) => {
                        initMenuData();
                        setCurrentSelectKey(currentSelectKey);
                      }}
                    />
                  ) : null}

                  {selectedNodeLeft === '' ? <div className={styles.rightContainer}>No Domain Data</div> : null}
                </Col>
              </Row>
            </div>
          )}
        </>
      )}
    </Spin>
  );
};

export default BssODomainApi;
