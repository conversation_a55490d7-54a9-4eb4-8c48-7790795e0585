import { Menu, Input, DatePicker, Form } from "antd";
import dayjs, { Dayjs } from "dayjs";
import {
  getUseCasePageListService,
  getUseCasePageListServicePropsType,
} from "@/services";
import { useEffect, useState } from "react";
import { SearchOutlined } from "@ant-design/icons";
import useI18n from "@/hooks/useI8n";
import styles from "./index.less";
import { useModel } from "umi";

interface formValuesPropsType {
  dateRange: Dayjs[];
  useCaseName: string;
}

// dayjs 转换为 字符串
const dayFormat = (dayRange: Dayjs[]) => {
  return dayRange.map((el: Dayjs) => el?.format("YYYY-MM-DD") || "");
};

const SearchUseCaseForm = () => {
  const [form] = Form.useForm();
  const { formatMessage } = useI18n();

  const { setQueryParams } = useModel("useSearchUseCaseModel");

  // 保存搜索条件
  const [saveSearchInfo, setSaveSearchInfo] =
    useState<getUseCasePageListServicePropsType>({
      useCaseName: "",
      startDate: "",
      endDate: "",
    });

  // 监听 form 值变化，变化时更新搜索条件
  const onValuesChange = (changedValues: formValuesPropsType) => {
    if (changedValues.dateRange) {
      const [startDate, endDate] = dayFormat(changedValues.dateRange);
      setSaveSearchInfo({
        ...saveSearchInfo,
        startDate,
        endDate,
      });
    } else {
      setSaveSearchInfo({
        ...saveSearchInfo,
        useCaseName: changedValues.useCaseName,
      });
    }
  };

  // 初始化，设置 form 初始值
  useEffect(() => {
    if (form) {
      const curDate = [dayjs().subtract(9, "day"), dayjs()];
      form.setFieldsValue({
        useCaseName: "",
        dateRange: [dayjs().subtract(9, "day"), dayjs()],
      });
      const [startDate, endDate] = dayFormat(curDate);
      setSaveSearchInfo({ startDate, endDate, useCaseName: "" });
    }
  }, []);

  // 当搜索条件改变时（时间为必填项），调用 Use Case List 获取函数
  useEffect(() => {
    if (saveSearchInfo?.startDate && saveSearchInfo?.endDate) {
      setQueryParams(saveSearchInfo);
    }
  }, [saveSearchInfo]);

  return (
    <Form
      form={form}
      onValuesChange={onValuesChange}
      className={styles.useCaseListForm}
    >
      <Form.Item name="useCaseName">
        <Input
          suffix={<SearchOutlined />}
          placeholder={formatMessage("SERVICEUSECASELIST.FORM.PLACEHOLDER")}
          allowClear
        />
      </Form.Item>
      <Form.Item
        name="dateRange"
        label={formatMessage("SERVICEUSECASELIST.FORM.CREATEDATE")}
      >
        <DatePicker.RangePicker
          allowClear={false}
          style={{ border: "none", boxShadow: "none" }}
        />
      </Form.Item>
    </Form>
  );
};

export default SearchUseCaseForm;
