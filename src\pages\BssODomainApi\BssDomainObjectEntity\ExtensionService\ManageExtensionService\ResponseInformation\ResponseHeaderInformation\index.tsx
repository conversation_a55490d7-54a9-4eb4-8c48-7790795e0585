import classNames from 'classnames';
import styles from '../index.less';
import { useState } from 'react';
import useI18n from '@/hooks/useI8n';
import AddResponseHeaderInformation from './AddResponseHeaderInformation';
import ResizableTable from '@/components/ResizableTable';
import { useTableFilter } from '@/hooks/useTableFilter';
import { RequiredSource, RespHeaderOptions } from '../../const';
import { message, Popconfirm, PopconfirmProps, Spin, Tooltip } from 'antd';
import { DeleteOutlined, FormOutlined } from '@ant-design/icons';
import { operateResponseParam } from '@/services/entityService';
import { useModel } from 'umi';
import EditResponseHeaderInformation from './EditResponseHeaderInformation';

interface IResponseHeaderInformation {
  selectedStatus: string;
  responseHeaderDataSource: any;
  selectedService: any;
  isProjectAndProductOwned: boolean;
  enumSource: any[];
  selectedRootNode: any;
  queryResponseInfo: () => Promise<void>;
}

const ResponseHeaderInformation: React.FC<IResponseHeaderInformation> = (props) => {
  const {
    selectedStatus = '',
    responseHeaderDataSource,
    selectedService,
    isProjectAndProductOwned,
    enumSource,
    selectedRootNode,
    queryResponseInfo,
  } = props;
  const { formatMessage } = useI18n();
  const { currentDomainObjId } = useModel('manageExtensionServiceModel');

  const [spining, setSpining] = useState<boolean>(false);
  const [selectedResponseHeader, setSelectedResponseHeader] = useState<any>({});
  const [openAddResponseHeaderInformation, setOpenAddResponseHeaderInformation] = useState<boolean>(false);
  const [openEditResponseHeaderInformation, setOpenEditResponseHeaderInformation] = useState<boolean>(false);
  const [selectedParam, setSelectedParam] = useState<any>({});

  // 自定义hooks，支持表格列筛选并分页展示
  const { filteredDataSource, getColumnSearchProps, getColumnEnumSearchProps, clearAllFilters } = useTableFilter(
    Array.isArray(responseHeaderDataSource) ? responseHeaderDataSource : [],
  );

  const customAvaValueFilter = (item: any, obj: any) => {
    if (enumSource) {
      let showData = '';
      if (item?.avlValue && item?.enumId) {
        showData = enumSource.find(
          (i: any) => `${i.value}` === `${item.enumId}` || i.label === `${item.enumId}`,
        )?.label;
      } else if (item?.typeFormat) {
        showData = `Format : ${item?.typeFormat}`;
      } else {
        showData = item.avlValue;
      }
      return (showData || '')
        .toString()
        .toLowerCase()
        .includes((Object.values(obj)[1] as string).toLowerCase());
    }
    return true; // 默认返回 true
  };

  const deleteConfirm: PopconfirmProps['onConfirm'] = async (e) => {
    setSpining(true);
    const param = {
      respParamId: selectedParam?.respParamId,
      domainObjId: currentDomainObjId,
    };
    try {
      const resultData = await operateResponseParam('delete', param);

      if (resultData?.success) {
        message.success(formatMessage('MANAGEEXTENSIONSERVICE.RESPONSE.DELETE.SUCCESS', RespHeaderOptions.option3));
        // 删除后重新渲染数据
        queryResponseInfo();
      }
    } catch (error) {
      console.error(error);
    } finally {
      setSpining(false);
    }
  };

  // param list表格
  const columns = [
    {
      dataIndex: 'respParamName',
      title: formatMessage('PROJECT.COMMON.NAME'),
      width: '15%',
      ellipsis: true,
      ...getColumnSearchProps('respParamName'),
      render: (_: string, record: any) => {
        if (record?.hidden === 'Y') {
          return <span className={styles.hiddenParam}>{record?.respParamName}</span>;
        }
        return record?.respParamName;
      },
    },
    {
      dataIndex: 'paramType',
      title: formatMessage('PROJECT.COMMON.TYPE'),
      width: '20%',
      ellipsis: true,
      ...getColumnSearchProps('paramType'),
      render: (_: string, record: any) => {
        return record?.isArray === 'Y' ? `${record.paramType}[]` : record.paramType;
      },
    },
    {
      dataIndex: 'avlValue',
      title: formatMessage('MANAGEEXTENSIONSERVICE.REQUEST.PARAM.ALLOWABLE_VALUES'),
      width: '20%',
      ellipsis: true,
      ...getColumnSearchProps('avlValue', customAvaValueFilter),
      render: (_: string, record: any) => {
        if (record.avlValue && record.enumId) {
          return enumSource.find((i: any) => `${i.value}` === `${record.enumId}` || i.label === `${record.enumId}`)
            ?.label;
        }
        if (record?.typeFormat) {
          return `Format : ${record?.typeFormat}`;
        }
        return record.avlValue;
      },
    },
    {
      dataIndex: 'example',
      title: formatMessage('PROJECT.COMMON.EXAMPLE'),
      width: '10%',
      ellipsis: true,
      ...getColumnSearchProps('example'),
      render: (value: any) => {
        if (typeof value === 'string') {
          return value;
        }
        return '';
      },
    },
    {
      dataIndex: 'comments',
      title: formatMessage('PROJECT.COMMON.DESCRIPTION'),
      width: '25%',
      ellipsis: true,
      ...getColumnSearchProps('comments'),
    },
    {
      dataIndex: '',
      title: formatMessage('PROJECT.COMMON.OPERATION'),
      width: '9%',
      render: (_: string, record: any) => {
        // 如果是Schema类型，Schema里面的参数是递归添加的，没有reqParamId，则此处不允许修改和删除
        if (record?.respParamId) {
          return (
            <div>
              <Tooltip title={formatMessage('PROJECT.COMMON.EDIT')}>
                <FormOutlined
                  className={classNames(styles.iconStyle, {
                    [styles.hide]: selectedRootNode?.state !== 'D',
                  })}
                  onClick={() => {
                    setOpenEditResponseHeaderInformation(true);
                  }}
                />
              </Tooltip>
              <Popconfirm
                title={formatMessage('MANAGEEXTENSIONSERVICE.RESPONSE.DELETE.TITLE', RespHeaderOptions.option2)}
                description={formatMessage(
                  'MANAGEEXTENSIONSERVICE.RESPONSE.DELETE.DESCRIPTION',
                  RespHeaderOptions.option3,
                )}
                onConfirm={deleteConfirm}
                okText={formatMessage('PROJECT.COMMON.YES')}
                cancelText={formatMessage('PROJECT.COMMON.NO')}
              >
                {isProjectAndProductOwned ? (
                  <></>
                ) : (
                  <Tooltip title={formatMessage('PROJECT.COMMON.DELETE')}>
                    <DeleteOutlined
                      className={classNames(styles.versionIcon2, {
                        [styles.hide]: selectedRootNode?.state !== 'D',
                      })}
                      onClick={() => {}}
                    />
                  </Tooltip>
                )}
              </Popconfirm>
            </div>
          );
        }
      },
    },
  ];

  return (
    <div style={{ marginTop: '12px' }}>
      <Spin spinning={spining}>
        <span className={styles.strongText}>Header </span>
        <span
          className={classNames(styles.iconStyle, {
            [styles.hide]: selectedRootNode?.state !== 'D' || isProjectAndProductOwned,
          })}
          onClick={() => {
            setSelectedParam({});
            setOpenAddResponseHeaderInformation(true);
          }}
        >
          + {formatMessage('PROJECT.COMMON.ADD')}
        </span>

        <ResizableTable
          size="small"
          style={{ marginTop: '12px' }}
          columns={columns}
          dataSource={filteredDataSource}
          rowKey={(record: any) => record?.propId}
          pagination={false}
          onRow={(record: any) => ({
            onClick: () => {
              setSelectedParam(record);
            },
          })}
        />
      </Spin>

      {openAddResponseHeaderInformation && (
        <AddResponseHeaderInformation
          selectedStatus={selectedStatus}
          isProjectAndProductOwned={isProjectAndProductOwned}
          open={openAddResponseHeaderInformation}
          enumSource={enumSource}
          selectedService={selectedService}
          onCancel={() => setOpenAddResponseHeaderInformation(false)}
          onOk={() => {
            // 新增实体属性成功后，刷新当前表格
            setOpenAddResponseHeaderInformation(false);
            queryResponseInfo();
          }}
        />
      )}
      {openEditResponseHeaderInformation && (
        <EditResponseHeaderInformation
          selectedStatus={selectedStatus}
          isProjectAndProductOwned={isProjectAndProductOwned}
          open={openEditResponseHeaderInformation}
          enumSource={enumSource}
          selectedParam={selectedParam}
          selectedService={selectedService}
          onCancel={() => setOpenEditResponseHeaderInformation(false)}
          onOk={() => {
            // 新增实体属性成功后，刷新当前表格
            setOpenEditResponseHeaderInformation(false);
            queryResponseInfo();
          }}
        />
      )}
    </div>
  );
};

export default ResponseHeaderInformation;
