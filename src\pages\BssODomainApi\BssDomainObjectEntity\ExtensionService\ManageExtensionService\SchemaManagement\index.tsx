import { useState, useEffect, useRef } from 'react';
import {
  <PERSON><PERSON>,
  Col,
  Drawer,
  Flex,
  Form,
  Input,
  message,
  Modal,
  Popconfirm,
  Row,
  Select,
  Space,
  Spin,
  Tooltip,
  Tree,
} from 'antd';
import { CodeSandboxOutlined, FormOutlined, DeleteOutlined } from '@ant-design/icons';
import styles from './index.less';
import OperationIconTitle from '@/components/OperationIconTitle';
import { useModel } from 'umi';
import { qrySchemaDetail, modifySchemaInfo, deleteSchemaById } from '@/services/entityService';
import classNames from 'classnames';
import AddSchemaProperty from './AddSchemaProperty';
import AddCompositionSchema from './AddCompositionSchema';
import AddByCode from './AddByCode';
import flatteningDomainSchemaList from '@/utils/flatteningDomainSchemaList';
import lodash from 'lodash';
import ResizableTable from '@/components/ResizableTable';
import { useTableFilter } from '@/hooks/useTableFilter';
import { CreationSource, RequiredSource } from '../const';
import { debounce } from 'lodash';
import myIcon from '@/static/iconfont/iconfont.css';
import CopySchemaDrawer from './CopySchemaDrawer';
import useI18n from '@/hooks/useI8n';

interface treeDataItemType {
  key: string;
  title: string;
  children: any;
}

const { Search } = Input;

const SchemaManagement = (props: any) => {
  const {
    open,
    onCancel,
    domainSchemaList = [],
    selectedRootNode,
    isProduct,
    currentSchema,
    updateDomainSchemaList,
    enumSource,
    sensitiveLevelList = [],
  } = props;
  const { formatMessage } = useI18n();
  const [schemaDetailSpinning, setSchemaDetailSpinning] = useState<boolean>(false);
  const [submitSpinning, setSubmitSpinning] = useState<boolean>(false);
  const [expandedKeys, setExpandedKeys] = useState<string[]>([]);
  const [searchValue, setSearchValue] = useState('');
  const [autoExpandParent, setAutoExpandParent] = useState(true);
  const [selectedSchema, setSelectedSchema] = useState<any>();
  const [operateType, setOperateType] = useState<string>('Add');
  const [selectedParam, setSelectedParam] = useState<any>({});
  const [schemaPropDataSource, setSchemaPropDataSource] = useState<any>([]);
  const [staticSchemaPropDataSource, setStaticSchemaPropDataSource] = useState<any>([]); // 选择Schema后最初始的参数数据，用于判断是否修改了Schema参数
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]); // [`${selectedSchema?.domainObjId}-${selectedSchema?.schemaId}`]
  const [tempSelectedKeys, setTempSelectedKeys] = useState<string[]>([]); // 临时存储左侧树形列表的key
  const [openAddSchemaProperty, setOpenAddSchemaProperty] = useState<boolean>(false);
  const [openAddCompositionSchema, setOpenAddCompositionSchema] = useState<boolean>(false);
  const [openAddByCode, setOpenAddByCode] = useState<boolean>(false);
  const [isComposition, setIsComposition] = useState<boolean>(false);
  const [isModalOpen, setIsModalOpen] = useState<boolean>(false);
  const [treeData, setTreeData] = useState<treeDataItemType[]>([]);
  const [copyOpen, setCopyOpen] = useState<boolean>(false); // 拷贝Schema抽屉
  const [copyingSchema, setCopyingSchema] = useState<any>({}); // 待拷贝的Schema
  const [schemaHistory, setSchemaHistory] = useState<any[]>([]); // Array to track schema navigation history
  const [currentHistoryIndex, setCurrentHistoryIndex] = useState<number>(-1); // Current position in history
  const [navigatingHistory, setNavigatingHistory] = useState<boolean>(false); // Flag to prevent recording history during navigation
  const treeContainerRef = useRef<HTMLDivElement>(null);

  const paramTypeSource = ['String', 'Boolean', 'Long', 'Number', 'Datetime', 'Integer', 'Object'];
  const { currentDomainObjId } = useModel('manageExtensionServiceModel');

  const [form] = Form.useForm();
  // 项目定制环境(systemConfig为false)且修改的记录"定义来源"（CREATION_SRC）为P（Product-Owned）
  const isProjectAndProductOwned = !isProduct && selectedSchema?.creationSrc === 'P';

  // 自定义hooks，支持表格列筛选并分页展示
  const { filteredDataSource, getColumnSearchProps, getColumnEnumSearchProps, clearAllFilters } = useTableFilter(
    Array.isArray(schemaPropDataSource) ? schemaPropDataSource : [],
  );

  // 针对特殊表格列，自定义过滤逻辑
  const customTypeFilter = (item: any, obj: any) => {
    if (item.type && !paramTypeSource.includes(item.type)) {
      // 因为在SchemaManagement通过Add弹框添加的属性，必须在SchemaManagement弹框中点击'Submit'后才会调接口，未点'Submit'之前
      // 也要展示添加的属性，同时也要展示从接口获取的已经存在的属性，所以此处渲染type的逻辑如下：
      if (domainSchemaList.length > 0) {
        const allSchemaList = flatteningDomainSchemaList(domainSchemaList);
        const schemaObj = allSchemaList?.filter((i: any) => i?.schemaId === item?.refSchemaId)[0];

        const propertyType =
          item?.isArray === 'Y'
            ? `List<${item?.refSchemaName || item?.schemaName || schemaObj?.schemaName}>`
            : `${item?.refSchemaName || item?.schemaName || schemaObj?.schemaName}`;

        return (propertyType || '')
          .toString()
          .toLowerCase()
          .includes((Object.values(obj)[1] as string).toLowerCase());
      }
    } else {
      const propertyType = item?.isArray === 'Y' ? `${item.type}[]` : item.type;
      return (propertyType || '')
        .toString()
        .toLowerCase()
        .includes((Object.values(obj)[1] as string).toLowerCase());
    }
    return true; // 默认返回 true
  };

  const customAvaValueFilter = (item: any, obj: any) => {
    if (enumSource) {
      let showData = '';
      if (item?.avlValue && item?.enumId) {
        showData = enumSource.find(
          (i: any) => `${i.value}` === `${item.enumId}` || i.label === `${item.enumId}`,
        )?.label;
      } else if (item?.typeFormat) {
        showData = `Format : ${item?.typeFormat}`;
      } else {
        showData = item.avlValue;
      }
      return (showData || '')
        .toString()
        .toLowerCase()
        .includes((Object.values(obj)[1] as string).toLowerCase());
    }
    return true; // 默认返回 true
  };

  const SchemaTypeStaticSource = [
    {
      label: 'Object',
      value: 'Object',
    },
    {
      label: 'And (allOf, must be valid against all of the subschemas)',
      value: 'AllOf',
    },
    {
      label: 'Or (anyOf, must be valid against any of the subschemas)',
      value: 'AnyOf',
    },
    {
      label: 'Xor (oneOf, must be valid against exactly one of the subschemas)',
      value: 'OneOf',
    },
  ];

  // 弹框关闭
  const onClose = () => {
    onCancel?.();
  };

  const convertDataToTreeData = (data: any[], searchValue: string) => {
    const lowerSearchValue = searchValue.toLowerCase(); // 将搜索值转换为小写
    const originTreeData = data
      .filter((domainObj) => domainObj.schemaList && domainObj.schemaList.length > 0) // 过滤掉 schemaList 为空的项
      .map((domainObj) => {
        const children = domainObj.schemaList
          .filter(
            (schema: { schemaName: string }) => schema.schemaName.toLowerCase().includes(lowerSearchValue), // 只保留 schemaName 包含 searchValue 的项(忽略大小写)
          )
          .map((schema: { schemaName: string; schemaId: any; creationSrc: string }) => ({
            title: schema.schemaName,
            key: `${domainObj.domainObjId}-${schema.schemaId}`,
            schemaId: schema.schemaId,
            creationSrc: schema.creationSrc,
            domainObjId: domainObj.domainObjId,
            isLeaf: true, // 标记为叶子节点
          }));

        return {
          title: domainObj.domainObjName,
          key: domainObj.domainObjId.toString(),
          children: children.length > 0 ? children : null,
        };
      });

    // 返回有children的节点
    const finalTreeData = originTreeData.filter((item: { title: string; key: string; children: any }) => item.children);
    return finalTreeData;
  };

  // 删除SchemaProp确认
  const deleteSchemaPropConfirm = (data: any) => {
    if (data?.propId) {
      const newSource = schemaPropDataSource.filter((item: any) => item?.propId !== data?.propId);
      setSchemaPropDataSource(
        [...newSource].map((item: any, index: number) => ({ ...item, rowDraggableKey: `${index}` })),
      );
    } else {
      const newSource = schemaPropDataSource.filter((item: any) => item?.id !== data?.id);

      setSchemaPropDataSource(
        [...newSource].map((item: any, index: number) => ({ ...item, rowDraggableKey: `${index}` })),
      );
    }
  };

  // 删除Schema
  const deleteSchema = async (param: { domainObjId: number; schemaId: number }) => {
    setSchemaDetailSpinning(true);
    try {
      const { success } = await deleteSchemaById(param);
      if (success) {
        // 如果删除的当前展示的Schema，将右侧数据置空
        if (param?.schemaId === selectedSchema?.schemaId) {
          setSchemaPropDataSource([]);
          setSelectedSchema(null);
          form.resetFields();
        }
        await updateDomainSchemaList();
      }
    } catch (e) {
      message.error(formatMessage('SCHEMAMANAGEMENT.DELETE_ERROR'));
    } finally {
      setSchemaDetailSpinning(false);
    }
  };

  // 对象模型的表格列
  const schemaPropertyColumns = [
    {
      dataIndex: 'name',
      title: formatMessage('SCHEMAPROPERTY.NAME'),
      width: '15%',
      ellipsis: true,
      ...getColumnSearchProps('name'),
      render: (_: string, record: any) => {
        if (record?.hidden === 'Y') {
          return <span className={styles.hiddenProperty}>{record?.name}</span>;
        }
        return record?.name;
      },
    },
    {
      dataIndex: 'type',
      title: formatMessage('SCHEMAPROPERTY.TYPE'),
      width: '15%',
      ellipsis: true,
      ...getColumnSearchProps('type', customTypeFilter),
      render: (_: string, record: any) => {
        if (record.type && !paramTypeSource.includes(record.type)) {
          // 因为在SchemaManagement通过Add弹框添加的属性，必须在SchemaManagement弹框中点击'OK'后才会调接口，未点'OK'之前
          // 也要展示添加的属性，同时也要展示从接口获取的已经存在的属性，所以此处渲染type的逻辑如下：
          if (record?.propId) {
            const allSchemaList = flatteningDomainSchemaList(domainSchemaList);
            const schemaObj = allSchemaList?.find((i: any) => i?.schemaId === record?.refSchemaId);
            return (
              <span
                className={styles.textStyle}
                onClick={() => {
                  onSelect([`${currentDomainObjId}-${schemaObj?.schemaId}`]);
                }}
              >
                {record?.isArray === 'Y'
                  ? `List<${record?.refSchemaName || record?.schemaName || schemaObj?.schemaName}>`
                  : `${record?.refSchemaName || record?.schemaName || schemaObj?.schemaName}`}
              </span>
            );
          } else {
            // 新添加的非基本类型的参数，数据未入库时不允许点击跳转
            return (
              <span className={styles.textDisableStyle}>
                {record?.isArray === 'Y'
                  ? `List<${record?.refSchemaName || record?.schemaName}>`
                  : `${record?.refSchemaName || record?.schemaName}`}
              </span>
            );
          }
        } else {
          return record?.isArray === 'Y' ? `${record.type}[]` : record.type;
        }
      },
    },
    {
      dataIndex: 'required',
      title: formatMessage('SCHEMAPROPERTY.REQUIRED'),
      width: '10%',
      ellipsis: true,
      render: (_: string, record: any) => {
        if (record?.required) {
          const matchedItem = RequiredSource.find((i: { key: string; name: string }) => i.key === record.required);
          return matchedItem ? matchedItem.name : '';
        }
      },
      ...getColumnEnumSearchProps('required', RequiredSource),
    },
    {
      dataIndex: 'avlValue',
      title: formatMessage('SCHEMAPROPERTY.ALLOWABLE_VALUES'),
      width: '15%',
      ellipsis: true,
      ...getColumnSearchProps('type', customAvaValueFilter),
      render: (_: string, record: any) => {
        if (record?.avlValue && record?.enumId) {
          return enumSource.find((i: any) => `${i.value}` === `${record.enumId}` || i.label === `${record.enumId}`)
            ?.label;
        }
        if (record?.typeFormat) {
          return `Format : ${record?.typeFormat}`;
        }
        return record.avlValue;
      },
    },
    {
      dataIndex: 'example',
      title: formatMessage('SCHEMAPROPERTY.EXAMPLE'),
      width: '10%',
      ellipsis: true,
      ...getColumnSearchProps('example'),
      render: (value: any) => {
        if (typeof value === 'string') {
          return value;
        }
        return '';
      },
    },
    {
      dataIndex: 'description',
      title: formatMessage('SCHEMAPROPERTY.DESCRIPTION'),
      width: '20%',
      ellipsis: true,
      ...getColumnSearchProps('description'),
    },
    {
      dataIndex: '',
      title: formatMessage('SCHEMAPROPERTY.OPERATION'),
      width: '10%',
      render: (_: string, record: any) => {
        return (
          <div>
            <Tooltip title={formatMessage('PROJECT.COMMON.EDIT')}>
              <FormOutlined
                className={classNames(styles.iconStyle, {
                  [styles.hide]: selectedRootNode?.state !== 'D',
                })}
                onClick={() => {
                  setOperateType('Edit');
                  setOpenAddSchemaProperty(true);
                }}
              />
            </Tooltip>
            <Popconfirm
              title={formatMessage('SCHEMAMANAGEMENT.DELETE_SCHEMA_PROPERTY_TITLE')}
              description={
                <div style={{ width: '500px' }}>
                  <div>{formatMessage('SCHEMAMANAGEMENT.DELETE_SCHEMA_PROPERTY_CONFIRM')}</div>
                  <div>{formatMessage('SCHEMAMANAGEMENT.DELETE_SCHEMA_PROPERTY_NOTE')}</div>
                </div>
              }
              onConfirm={() => deleteSchemaPropConfirm(record)}
              okText={formatMessage('PROJECT.COMMON.OK')}
              cancelText={formatMessage('SCHEMAMANAGEMENT.CANCEL')}
            >
              {/* 在项目环境，允许给产品定义的Schema增加属性，新增的属性CREATION_SRC存为"C,支持修改属性的信息，也支持删除 */}
              {(!isProjectAndProductOwned || record?.creationSrc === 'C') && (
                <Tooltip title={formatMessage('PROJECT.COMMON.DELETE')}>
                  <DeleteOutlined
                    className={classNames(styles.iconStyle, {
                      [styles.hide]: selectedRootNode?.state !== 'D',
                    })}
                    onClick={() => {}}
                  />
                </Tooltip>
              )}
            </Popconfirm>
          </div>
        );
      },
    },
  ];

  // 复杂模型的表格列
  const compositionSchemaColumns = [
    {
      dataIndex: 'order',
      title: formatMessage('SCHEMAPROPERTY.ORDER'),
      width: '15%',
      ellipsis: true,
      render: (_: any, __: any, index: number) => index + 1,
    },
    {
      dataIndex: 'type',
      title: formatMessage('SCHEMAPROPERTY.TYPE'),
      width: '20%',
      ellipsis: true,
      render: (_: string, record: any) => {
        if (record.type && !paramTypeSource.includes(record.type)) {
          // 因为在SchemaManagement通过Add弹框添加的属性，必须在SchemaManagement弹框中点击'OK'后才会调接口，未点'OK'之前
          // 也要展示添加的属性，同时也要展示从接口获取的已经存在的属性，所以此处渲染type的逻辑如下：
          if (record?.propId) {
            const allSchemaList = flatteningDomainSchemaList(domainSchemaList);
            const schemaObj = allSchemaList?.find((i: any) => i?.schemaId === record?.refSchemaId);
            return (
              <span
                className={styles.textStyle}
                onClick={() => {
                  onSelect([`${currentDomainObjId}-${schemaObj?.schemaId}`]);
                }}
              >
                {record?.isArray === 'Y'
                  ? `List<${record?.refSchemaName || record?.schemaName || schemaObj?.schemaName}>`
                  : `${record?.refSchemaName || record?.schemaName || schemaObj?.schemaName}`}
              </span>
            );
          } else {
            // 新添加的非基本类型的参数，数据未入库时不允许点击跳转
            return (
              <span className={styles.textDisableStyle}>
                {record?.isArray === 'Y'
                  ? `List<${record?.refSchemaName || record?.schemaName}>`
                  : `${record?.refSchemaName || record?.schemaName}`}
              </span>
            );
          }
        } else {
          return record?.isArray === 'Y' ? `${record.type}[]` : record.type;
        }
      },
    },
    {
      dataIndex: 'avlValue',
      title: formatMessage('SCHEMAPROPERTY.ALLOWABLE_VALUES'),
      width: '15%',
      ellipsis: true,
      render: (_: string, record: any) => {
        if (record.avlValue && record.enumId) {
          return enumSource.filter((i: any) => i.value === record.enumId).map((i: any) => i.label);
        }
        return record.avlValue;
      },
    },
    {
      dataIndex: 'example',
      title: formatMessage('SCHEMAPROPERTY.EXAMPLE'),
      width: '15%',
      ellipsis: true,
      render: (value: any) => {
        if (typeof value === 'string') {
          return value;
        }
        return '';
      },
    },
    {
      dataIndex: 'description',
      title: formatMessage('SCHEMAPROPERTY.DESCRIPTION'),
      width: '25%',
      ellipsis: true,
    },
    {
      dataIndex: '',
      title: formatMessage('SCHEMAPROPERTY.OPERATION'),
      width: '10%',
      render: (_: string, record: any) => {
        return (
          <div>
            <Tooltip title={formatMessage('PROJECT.COMMON.EDIT')}>
              <FormOutlined
                className={classNames(styles.iconStyle, {
                  [styles.hide]: selectedRootNode?.state !== 'D',
                })}
                onClick={() => {
                  setOperateType('Edit');
                  setOpenAddCompositionSchema(true);
                }}
              />
            </Tooltip>
            <Popconfirm
              title={formatMessage('SCHEMAMANAGEMENT.DELETE_SCHEMA_PROPERTY_CONFIRM')}
              description={
                <div style={{ width: '500px' }}>
                  <div>{formatMessage('SCHEMAMANAGEMENT.DELETE_SCHEMA_PROPERTY_CONFIRM')}</div>
                  <div>{formatMessage('SCHEMAMANAGEMENT.DELETE_SCHEMA_PROPERTY_NOTE')}</div>
                </div>
              }
              onConfirm={() => deleteSchemaPropConfirm(record)}
              okText={formatMessage('PROJECT.COMMON.OK')}
              cancelText={formatMessage('SCHEMAMANAGEMENT.CANCEL')}
            >
              {!isProjectAndProductOwned && (
                <Tooltip title={formatMessage('PROJECT.COMMON.DELETE')}>
                  <DeleteOutlined
                    className={classNames(styles.iconStyle, {
                      [styles.hide]: selectedRootNode?.state !== 'D',
                    })}
                    onClick={() => {}}
                  />
                </Tooltip>
              )}
            </Popconfirm>
          </div>
        );
      },
    },
  ];

  // 展开树形列表
  const onExpand = (expandedKey: any) => {
    setExpandedKeys(expandedKey);
    setAutoExpandParent(false);
  };

  // 搜索框
  const onSearchChange = (e: { target: { value: any } }) => {
    setAutoExpandParent(true);
    const { value } = e.target;
    setSearchValue(value);
  };

  // 渲染树结构
  const renderTreeNodes = (data: any[]) =>
    data.map((item) => {
      const lowerSearchValue = searchValue.toLowerCase(); // 将搜索值转换为小写

      // 使用正则表达式找到匹配的部分
      const regex = new RegExp(`(${lowerSearchValue})`, 'i');
      const parts = item.title.split(regex); // 根据匹配项分割标题
      const title =
        parts.length > 1 && searchValue ? (
          <span>
            {parts.map((part: string, index: number) =>
              part.toLowerCase() === lowerSearchValue ? (
                // 高亮匹配的字符
                <span key={index} className={styles.siteTreeSearchValue}>
                  {part}
                </span>
              ) : (
                part
              ),
            )}
          </span>
        ) : (
          <span>{item.title}</span>
        );
      // 如果有子节点，递归渲染子节点
      if (item?.children?.length > 0) {
        return (
          <Tree.TreeNode title={title} key={item.key}>
            {renderTreeNodes(item.children)}
          </Tree.TreeNode>
        );
      }
      // 叶子节点
      return (
        <Tree.TreeNode
          key={item.key}
          title={
            <div className={styles.treeNode}>
              <Flex gap="5px">
                <CodeSandboxOutlined className={classNames({ [styles.textStyle]: item?.creationSrc === 'C' })} />
                <span style={{ whiteSpace: 'nowrap' }}>{title}</span>
                {/* 拷贝按钮 */}
                <Tooltip title={formatMessage('PROJECT.COMMON.COPY')}>
                  <span
                    className={classNames(`${myIcon.iconfont} ${myIcon['icon-copy']} ${styles.deleteIcon}`, {
                      [styles.hide]: selectedRootNode?.state !== 'D',
                    })}
                    onClick={(e) => {
                      e.stopPropagation();
                      setCopyOpen(true);
                      setCopyingSchema(item);
                    }}
                  />
                </Tooltip>
                {/* 删除按钮及确认框 */}
                <Popconfirm
                  className={styles.deleteIcon}
                  title={formatMessage('SCHEMAMANAGEMENT.DELETE_SCHEMA_CONFIRM')}
                  description={
                    <div style={{ width: '500px' }}>
                      <div>{formatMessage('SCHEMAMANAGEMENT.DELETE_SCHEMA_CONFIRM')}</div>
                      <div>{formatMessage('SCHEMAMANAGEMENT.DELETE_SCHEMA_NOTE')}</div>
                    </div>
                  }
                  onConfirm={(e) => {
                    e!.stopPropagation(); // !断言e一定不为undefined,阻止冒泡避免调用onSelect方法，下同
                    const param = { domainObjId: item?.domainObjId, schemaId: item?.schemaId };
                    deleteSchema(param);
                  }}
                  onCancel={(e) => {
                    e!.stopPropagation();
                  }}
                  okText={formatMessage('PROJECT.COMMON.OK')}
                  cancelText={formatMessage('SCHEMAMANAGEMENT.CANCEL')}
                >
                  {isProduct ? (
                    <Tooltip title={formatMessage('PROJECT.COMMON.DELETE')}>
                      <DeleteOutlined
                        className={classNames(styles.deleteIcon, {
                          [styles.hide]: selectedRootNode?.state !== 'D',
                        })}
                        onClick={(e) => {
                          e.stopPropagation();
                        }}
                      />
                    </Tooltip>
                  ) : (
                    <Tooltip title={formatMessage('PROJECT.COMMON.DELETE')}>
                      <DeleteOutlined
                        className={classNames(styles.iconStyle, {
                          [styles.hide]: item?.creationSrc === 'P' || selectedRootNode?.state !== 'D',
                        })}
                        onClick={(e) => {
                          e.stopPropagation();
                        }}
                      />
                    </Tooltip>
                  )}
                </Popconfirm>
              </Flex>
            </div>
          }
        />
      );
    });

  // 检查是否有未保存的Schema属性修改
  const checkUnsavedChanges = (targetSchemaId: number | null) => {
    // 如果selectedSchema存在，且两个数据不一致，即代表修改过schema property
    if (targetSchemaId && selectedSchema && !lodash.isEqual(staticSchemaPropDataSource, schemaPropDataSource)) {
      setTempSelectedKeys([`${selectedSchema?.domainObjId}-${targetSchemaId}`]);
      setIsModalOpen(true);
      return true;
    }
    return false;
  };

  // Navigate to previous schema in history
  const goToPreviousSchema = () => {
    if (currentHistoryIndex > 0) {
      const previousIndex = currentHistoryIndex - 1;
      const previousSchema = schemaHistory[previousIndex];

      // 检查是否有未保存的修改
      if (checkUnsavedChanges(previousSchema.schemaId)) {
        return; // 如果有未保存的修改，等待用户确认
      }

      setNavigatingHistory(true);
      setCurrentHistoryIndex(previousIndex);

      // Select in tree and query details without adding to history
      setSelectedKeys([`${previousSchema.domainObjId}-${previousSchema.schemaId}`]);
      querySchemaDetailWithoutHistory(previousSchema.schemaId);
    }
  };

  // Navigate to next schema in history
  const goToNextSchema = () => {
    if (currentHistoryIndex < schemaHistory.length - 1) {
      const nextIndex = currentHistoryIndex + 1;
      const nextSchema = schemaHistory[nextIndex];

      // 检查是否有未保存的修改
      if (checkUnsavedChanges(nextSchema.schemaId)) {
        return; // 如果有未保存的修改，等待用户确认
      }

      setNavigatingHistory(true);
      setCurrentHistoryIndex(nextIndex);

      // Select in tree and query details without adding to history
      setSelectedKeys([`${nextSchema.domainObjId}-${nextSchema.schemaId}`]);
      querySchemaDetailWithoutHistory(nextSchema.schemaId);
    }
  };

  // Add current schema to history
  const addToHistory = (schema: any) => {
    // If navigating through history and then clicking on a schema,
    // we should truncate the forward history and add this as a new entry
    if (navigatingHistory) {
      setNavigatingHistory(false); // Reset navigation flag
    }

    // Only add if it's different from the last entry in history
    if (schemaHistory.length === 0 || schemaHistory[currentHistoryIndex]?.schemaId !== schema.schemaId) {
      // Remove forward history when a new navigation occurs
      const newHistory = schemaHistory.slice(0, currentHistoryIndex + 1);
      const updatedHistory = [...newHistory, schema];
      setSchemaHistory(updatedHistory);
      setCurrentHistoryIndex(updatedHistory.length - 1);
    }
  };

  // 确保选中节点在可视区域内
  const scrollToSelectedNode = () => {
    if (treeContainerRef.current && selectedKeys.length > 0) {
      // 确保父节点展开
      const parts = selectedKeys[0].split('-');
      if (parts.length > 1) {
        const domainObjId = parts[0];
        if (!expandedKeys.includes(domainObjId)) {
          setExpandedKeys((prevKeys) => [...prevKeys, domainObjId]);
          // 展开父节点后需要等待渲染完成再滚动
          setTimeout(() => {
            scrollToNodeElement();
          }, 300);
          return;
        }
      }
      // 直接滚动
      scrollToNodeElement();
    }
  };

  // 使用DOM方法滚动到选中节点
  const scrollToNodeElement = () => {
    if (!treeContainerRef.current || selectedKeys.length === 0) return;

    setTimeout(() => {
      try {
        // 使用更可靠的选择器查找选中的节点元素
        // 1. 首先尝试通过data-key属性查找
        let selectedElement = treeContainerRef.current!.querySelector(`[data-key="${selectedKeys[0]}"]`);

        // 2. 如果没找到，尝试通过aria-selected属性查找
        if (!selectedElement) {
          selectedElement = treeContainerRef.current!.querySelector('.ant-tree-node-selected');
        }

        // 3. 如果还没找到，尝试通过标题内容查找
        if (!selectedElement && selectedSchema?.schemaName) {
          // 查找所有包含span的元素
          const allSpans = treeContainerRef.current!.querySelectorAll('.ant-tree-title span');
          // 遍历查找包含schema名称的span
          for (let i = 0; i < allSpans.length; i++) {
            if (allSpans[i].textContent === selectedSchema.schemaName) {
              // 找到包含schema名称的span，获取其父节点的父节点（树节点）
              selectedElement = allSpans[i].closest('.ant-tree-treenode');
              break;
            }
          }
        }

        if (selectedElement) {
          // console.log('找到选中节点:', selectedElement);
          // 计算滚动位置，使元素在视图中间
          const container = treeContainerRef.current!;
          const containerRect = container.getBoundingClientRect();
          const elementRect = selectedElement.getBoundingClientRect();

          // 计算元素相对于容器的位置
          const relativeTop = elementRect.top - containerRect.top;

          // 计算滚动位置，使元素在视图中间
          const scrollPosition = container.scrollTop + relativeTop - containerRect.height / 2 + elementRect.height / 2;

          // 平滑滚动到计算的位置
          container.scrollTo({
            top: scrollPosition,
            behavior: 'smooth',
          });
        } else {
          // console.log('未找到选中节点:', selectedSchema?.schemaName, selectedKeys[0]);
        }
      } catch (error) {
        console.error('滚动到节点时出错:', error);
      }
    }, 300);
  };

  // 树形列表选中事件
  const onSelect = (selectedKey: any) => {
    if (selectedKey.length > 0) {
      const schemaKey = selectedKey[0].split('-');
      // [`${selectedSchema?.domainObjId}-${selectedSchema?.schemaId}`]，schemaKey.length = 1，是根节点，>1 时才有叶子节点
      const schemaId = schemaKey.length > 1 ? Number(schemaKey[schemaKey.length - 1]) : null;

      // 使用提取的函数检查未保存的修改
      if (checkUnsavedChanges(schemaId)) {
        return; // 如果有未保存的修改，等待用户确认
      }

      if (schemaId) {
        setSelectedKeys(selectedKey); // 只有点击叶子节点才重新设置key
        querySchemaDetail(schemaId);
      }
    }
  };

  // 查询Schema详情
  const querySchemaDetail = async (id: number) => {
    setSchemaDetailSpinning(true);
    try {
      const { success, data } = await qrySchemaDetail({ schemaId: id });
      if (success) {
        setSelectedSchema(data);
        // Add to history when schema is loaded successfully and not navigating through history
        if (data && !navigatingHistory) {
          addToHistory(data);
        }
        // 滚动到选中节点
        setTimeout(() => scrollToSelectedNode(), 200);
      }
    } catch (e) {
      message.error(formatMessage('SCHEMAMANAGEMENT.QUERY_ERROR'));
    } finally {
      setSchemaDetailSpinning(false);
    }
  };

  // Query schema detail without adding to history (for navigation buttons)
  const querySchemaDetailWithoutHistory = async (id: number) => {
    setSchemaDetailSpinning(true);
    try {
      const { success, data } = await qrySchemaDetail({ schemaId: id });
      if (success) {
        setSelectedSchema(data);
        // 滚动到选中节点
        setTimeout(() => scrollToSelectedNode(), 200);
      }
    } catch (e) {
      message.error(formatMessage('SCHEMAMANAGEMENT.QUERY_ERROR'));
    } finally {
      setSchemaDetailSpinning(false);
      setNavigatingHistory(false); // Reset navigation flag after completion
    }
  };

  // 提示框确认按钮
  const handleModalOk = () => {
    if (tempSelectedKeys.length > 0) {
      const schemaKey = tempSelectedKeys[0].split('-');
      // [`${selectedSchema?.domainObjId}-${selectedSchema?.schemaId}`]，schemaKey.length = 1，是根节点，>1 时才有叶子节点
      const schemaId = schemaKey.length > 1 ? Number(schemaKey[schemaKey.length - 1]) : null;
      if (schemaId) {
        setSelectedKeys(tempSelectedKeys); // 只有点击叶子节点才重新设置key
        querySchemaDetail(schemaId);
      }
    }
    setIsModalOpen(false);
  };

  // 获取新增或者修改的参数
  const getPropertyData = (data: any) => {
    // 如果存在propId，则一定是修改
    if (data?.propId) {
      const newSource = schemaPropDataSource.map((item: any) => {
        if (item?.propId === data?.propId) {
          return data;
        }
        return item;
      });
      setSchemaPropDataSource(
        [...newSource].map((item: any, index: number) => ({ ...item, rowDraggableKey: `${index}` })),
      );
    } else {
      // 根据id判断是修改还是新增
      const isEdit = schemaPropDataSource.filter((item: any) => item?.id === data?.id).length > 0;
      if (isEdit) {
        const newSource = schemaPropDataSource.map((item: any) => {
          if (item?.id === data?.id) {
            return data;
          }
          return item;
        });
        setSchemaPropDataSource(
          [...newSource].map((item: any, index: number) => ({ ...item, rowDraggableKey: `${index}` })),
        );
      } else {
        setSchemaPropDataSource(
          [...schemaPropDataSource, data].map((item: any, index: number) => ({ ...item, rowDraggableKey: `${index}` })),
        );
      }
    }

    setOpenAddCompositionSchema(false);
    setOpenAddSchemaProperty(false);
    setOpenAddByCode(false);
  };

  // 获取通过代码新增或者修改的参数
  const getPropertyDataByCode = (data: any) => {
    // 创建一个新的数组来存储更新后的schemaPropDataSource
    const updatedDataSource = schemaPropDataSource.map((item: any) => {
      // 在data中查找与当前item.name相同的项
      let matchingItem = data.find((newItem: any) => newItem.name === item.name);
      // 如果找到匹配项，用新的项替换旧的项，否则保留原来的项
      if (matchingItem && item?.propId) {
        matchingItem = {
          ...matchingItem,
          propId: item?.propId,
        };
      }
      return matchingItem ? matchingItem : item;
    });

    // 找出data中没有在schemaPropDataSource中出现的项
    const newItems = data.filter(
      (newItem: any) => !schemaPropDataSource.some((item: any) => item.name === newItem.name),
    );
    setSchemaPropDataSource(
      [...updatedDataSource, ...newItems].map((item: any, index: number) => ({ ...item, rowDraggableKey: `${index}` })),
    );
    setOpenAddByCode(false);
    setOpenAddCompositionSchema(false);
    setOpenAddSchemaProperty(false);
  };

  const addOrEditSchemaInfo = async (param: any) => {
    setSubmitSpinning(true);
    try {
      const { success } = await modifySchemaInfo(param);
      if (success) {
        message.success(formatMessage('SCHEMAMANAGEMENT.MODIFY_SUCCESS'));
        querySchemaDetail(selectedSchema?.schemaId);
      } else {
        message.error(formatMessage('SCHEMAMANAGEMENT.MODIFY_FAILED'));
      }
    } catch (error) {
      console.error('An error occurred:', error);
      message.error(formatMessage('SCHEMAMANAGEMENT.ERROR_OCCURRED'));
    } finally {
      await updateDomainSchemaList();
      setSubmitSpinning(false);
    }
  };

  const checkSchemaData = async () => {
    form.validateFields().then(() => {
      const formValue = form.getFieldsValue();
      // 构造入参
      let param = {
        schemaName: formValue?.schemaName, // 可修改且未受控，从formValue获取
        schemaType: selectedSchema?.schemaType,
        schemaId: selectedSchema?.schemaId,
        domainObjId: selectedSchema?.domainObjId,
        description: formValue?.description, // 可修改且未受控，从formValue获取
        propList: schemaPropDataSource,
      };
      addOrEditSchemaInfo(param);
    });
  };

  // 改变SchemaType
  const onSchemaTypeChange = (value: string) => {
    if (value === 'Object') {
      setIsComposition(false);
    } else {
      setIsComposition(true);
      setSchemaPropDataSource([]);
    }
  };

  const getExpandedKeys = (data: treeDataItemType[], searchValue: string) => {
    const lowerSearchValue = searchValue.toLowerCase(); // 将搜索值转换为小写
    return data
      .map((item) => {
        if (item.children) {
          const childMatch = item.children.some(
            (child: any) => child.title.toLowerCase().indexOf(lowerSearchValue) > -1,
          );
          if (childMatch) {
            return item.key;
          }
        }
        return '';
      })
      .filter((item, i, self) => item && self.indexOf(item) === i);
  };

  useEffect(() => {
    setSchemaPropDataSource([]);
    form.setFieldsValue(selectedSchema);
    if (selectedSchema?.schemaType === 'Object') {
      setIsComposition(false);
    } else {
      setIsComposition(true);
    }
    setSelectedKeys([`${selectedSchema?.domainObjId}-${selectedSchema?.schemaId}`]);
    const initSource =
      selectedSchema?.propList?.map((item: any, index: number) => ({ ...item, rowDraggableKey: `${index}` })) || [];
    setSchemaPropDataSource(initSource);
    setStaticSchemaPropDataSource(initSource);
    // 重置表格列的筛选条件
    clearAllFilters();
  }, [selectedSchema]);

  useEffect(() => {
    setSearchValue('');
    if (currentSchema?.schemaId) {
      querySchemaDetail(currentSchema?.schemaId);
      setExpandedKeys([`${currentSchema?.domainObjId}`]);
      setSelectedKeys([`${currentSchema?.domainObjId}-${currentSchema?.schemaId}`]);
    }
  }, [currentSchema]);

  useEffect(() => {
    const debouncedConvert = debounce(() => {
      const data = convertDataToTreeData(domainSchemaList, searchValue);
      setTreeData(data);
      if (searchValue) {
        const expandedKey = getExpandedKeys(data, searchValue);
        setExpandedKeys(expandedKey);
      }
    }, 500);

    debouncedConvert(); // 调用防抖函数

    // 清理函数，在组件卸载或依赖项变化时取消防抖
    return () => {
      debouncedConvert.cancel();
    };
  }, [domainSchemaList, searchValue]);

  useEffect(() => {
    // Reset history when drawer opens
    setSchemaHistory([]);
    setCurrentHistoryIndex(-1);
    setNavigatingHistory(false);
  }, [open]);

  return (
    <>
      <div>
        <Drawer
          title={formatMessage('SCHEMAMANAGEMENT.TITLE')}
          onClose={onClose}
          maskClosable={false}
          open={open}
          width={1300}
          styles={{
            body: {
              padding: '10px',
            },
          }}
          footer={
            <Space style={{ float: 'right' }}>
              <Space style={{ marginRight: '3rem' }}>
                <Button onClick={goToPreviousSchema} disabled={currentHistoryIndex <= 0}>
                  {formatMessage('PROJECT.COMMON.PREVIOUS')}
                </Button>
                <Button onClick={goToNextSchema} disabled={currentHistoryIndex >= schemaHistory.length - 1}>
                  {formatMessage('PROJECT.COMMON.NEXT')}
                </Button>
              </Space>
              <Spin spinning={submitSpinning}>
                <Button
                  disabled={!selectedSchema}
                  type="primary"
                  onClick={checkSchemaData}
                  className={classNames({
                    [styles.hide]: selectedRootNode?.state !== 'D',
                  })}
                >
                  {formatMessage('SCHEMAMANAGEMENT.SUBMIT')}
                </Button>
              </Spin>
              <Button onClick={onClose}>{formatMessage('SCHEMAMANAGEMENT.CANCEL')}</Button>
            </Space>
          }
        >
          <div className={styles.container}>
            <div className={styles.contLeft} ref={treeContainerRef}>
              <Search
                value={searchValue}
                allowClear
                style={{ marginBottom: 8 }}
                placeholder={formatMessage('SCHEMAMANAGEMENT.SEARCH')}
                onChange={onSearchChange}
              />
              <Tree
                onExpand={onExpand}
                expandedKeys={expandedKeys}
                autoExpandParent={autoExpandParent}
                showIcon // 显示图标
                onSelect={onSelect}
                selectedKeys={selectedKeys}
              >
                {renderTreeNodes(treeData)}
              </Tree>
            </div>
            <div className={styles.contDivider}></div>
            <div className={styles.contRight}>
              <Spin spinning={schemaDetailSpinning}>
                <div>
                  <OperationIconTitle title={formatMessage('SCHEMAMANAGEMENT.BASIC_INFO')} />
                  <Form
                    form={form}
                    labelCol={{ span: 11 }}
                    wrapperCol={{ span: 13 }}
                    // initialValues={defaultValues}
                  >
                    <Row>
                      <Col span={11}>
                        <Form.Item
                          label={formatMessage('PROJECT.COMMON.NAME')}
                          name="schemaName"
                          rules={[
                            {
                              required: true,
                              message: 'Please input the schema name!',
                            },
                          ]}
                        >
                          <Input allowClear disabled={isProjectAndProductOwned || selectedRootNode?.state !== 'D'} />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item label={formatMessage('PROJECT.COMMON.CREATIONSOURCE')} name="creationSrc">
                          <Select disabled>
                            {CreationSource.map((i) => (
                              <Select.Option key={i.key} value={i.key}>
                                {i.name}
                              </Select.Option>
                            ))}
                          </Select>
                        </Form.Item>
                      </Col>
                    </Row>
                    <Row>
                      <Col span={11}>
                        <Form.Item label={formatMessage('PROJECT.COMMON.BELONGDOMAINOBJECT')} name="domainObjName">
                          <Input allowClear disabled />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item label={formatMessage('PROJECT.COMMON.TYPE')} name="schemaType">
                          <Select options={SchemaTypeStaticSource} onChange={onSchemaTypeChange} disabled />
                        </Form.Item>
                      </Col>
                    </Row>
                    <Row>
                      <Col span={23} style={{ marginLeft: '0.5625rem' }}>
                        <Form.Item
                          label={formatMessage('PROJECT.COMMON.DESCRIPTION')}
                          name="description"
                          labelCol={{ span: 5 }}
                          wrapperCol={{ span: 19 }}
                        >
                          <Input.TextArea
                            style={{ width: '99%' }}
                            rows={1}
                            allowClear
                            disabled={selectedRootNode?.state !== 'D'}
                          />
                        </Form.Item>
                      </Col>
                    </Row>
                  </Form>
                </div>
                {!isComposition ? (
                  <div>
                    <Flex align="center">
                      <OperationIconTitle title={formatMessage('SCHEMAMANAGEMENT.SCHEMA_PROPERTIES')} />
                      <span
                        className={classNames(styles.iconStyle, {
                          [styles.hide]: selectedRootNode?.state !== 'D',
                        })}
                        onClick={() => {
                          setOperateType('Add');
                          setSelectedParam({});
                          setOpenAddSchemaProperty(true);
                        }}
                      >
                        + {formatMessage('SCHEMAMANAGEMENT.ADD')}
                      </span>
                      <span
                        className={classNames(styles.iconStyle, {
                          [styles.hide]: isProjectAndProductOwned || selectedRootNode?.state !== 'D',
                        })}
                        onClick={() => {
                          setOpenAddByCode(true);
                        }}
                      >
                        + {formatMessage('SCHEMAMANAGEMENT.ADD_BY_CODE')}
                      </span>
                    </Flex>
                    <ResizableTable
                      rowDraggable={true}
                      setDataSource={setSchemaPropDataSource}
                      size="small"
                      style={{ marginTop: '12px' }}
                      columns={schemaPropertyColumns}
                      dataSource={filteredDataSource}
                      rowKey={(record: any) => record?.name}
                      pagination={false}
                      onRow={(record: any) => ({
                        onClick: () => {
                          setSelectedParam(record);
                        },
                      })}
                    />
                  </div>
                ) : (
                  <></>
                  // <div>
                  //   <Flex align="center">
                  //     <OperationIconTitle title="Composition Schemas" />
                  //     <span
                  //       className={classNames(styles.iconStyle, {
                  //         [styles.hide]: isProjectAndProductOwned || selectedRootNode?.state !== 'D',
                  //       })}
                  //       onClick={() => {
                  //         setOperateType('Add');
                  //         setSelectedParam({});
                  //         setOpenAddCompositionSchema(true);
                  //       }}
                  //     >
                  //       + Add
                  //     </span>
                  //   </Flex>
                  //   <ResizableTable
                  //     size="small"
                  //     style={{ marginTop: '12px' }}
                  //     columns={compositionSchemaColumns}
                  //     dataSource={schemaPropDataSource}
                  //     rowKey={(record:any) => record?.propId}
                  //     pagination={false}
                  //     onRow={(record:any) => ({
                  //       onClick: () => {
                  //         setSelectedParam(record);
                  //       },
                  //     })}
                  //   />
                  // </div>
                )}
              </Spin>
            </div>
          </div>
          <Modal
            title={formatMessage('SCHEMAMANAGEMENT.UNSAVED_CHANGES')}
            open={isModalOpen}
            onOk={handleModalOk}
            onCancel={() => {
              setIsModalOpen(false);
            }}
          >
            <p>{formatMessage('SCHEMAMANAGEMENT.UNSAVED_CHANGES_CLEAR')}</p>
          </Modal>
        </Drawer>
      </div>
      {/* 新增或修改参数信息-Object数据模型 */}
      <AddSchemaProperty
        sensitiveLevelList={sensitiveLevelList}
        isProjectAndProductOwned={isProjectAndProductOwned}
        schemaPropDataSource={schemaPropDataSource}
        selectedRootNode={selectedRootNode}
        operateType={operateType}
        selectedParam={selectedParam}
        open={openAddSchemaProperty}
        domainSchemaList={domainSchemaList}
        enumSource={enumSource}
        onCancel={() => setOpenAddSchemaProperty(false)}
        onOk={getPropertyData}
        updateDomainSchemaList={updateDomainSchemaList}
      />
      {/* 新增或修改参数信息-通过Java代码生成 */}
      <AddByCode
        selectedSchema={selectedSchema ?? currentSchema}
        operateType={operateType}
        open={openAddByCode}
        onCancel={() => setOpenAddByCode(false)}
        onAddByCodeOk={getPropertyDataByCode}
      />
      {/* 新增或修改参数信息-组合数据模型 */}
      <AddCompositionSchema
        selectedRootNode={selectedRootNode}
        operateType={operateType}
        selectedParam={selectedParam}
        open={openAddCompositionSchema}
        enumSource={enumSource}
        onCancel={() => setOpenAddCompositionSchema(false)}
        onOk={getPropertyData}
        updateDomainSchemaList={updateDomainSchemaList}
      />
      {/* 拷贝Schema */}
      <CopySchemaDrawer
        copyOpen={copyOpen}
        setCopyOpen={setCopyOpen}
        copyingSchema={copyingSchema}
        domainSchemaList={domainSchemaList}
        updateDomainSchemaList={updateDomainSchemaList}
      />
    </>
  );
};

export default SchemaManagement;
