import OperationIconTitle from '@/components/OperationIconTitle';
import { DeleteOutlined, FormOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { Flex, Input, message, Popconfirm, PopconfirmProps, Space, Spin, Tabs, Tooltip, Typography } from 'antd';
import { useEffect, useState } from 'react';
import styles from './index.less';
import classNames from 'classnames';
import {
  qryExtensionServiceResponseInfoByServiceId,
  operateResponseParam,
  qryBodyExample,
  qrySchemaDetail,
} from '@/services/entityService';
import AddResponseInformation from './AddResponseInformation';
import { CodeMirrorRender } from '@/components';
import { useModel } from 'umi';
import SchemaManagement from '../SchemaManagement';
import flatteningDomainSchemaList from '@/utils/flatteningDomainSchemaList';
import ResizableTable from '@/components/ResizableTable';
import { ParamTypeSource, RespHeaderOptions } from '../const';
import { getErrorBodyExample, getErrorSchemaDetail } from '@/services/errorCodeService';
import EditResponseInformation from './EditResponseInformation';
import ResponseErrorCodeList from './ResponseErrorCodeList';
import ScriptManagement from '../ScriptManagement';
import myIcon from '@/static/iconfont/iconfont.css';
import useI18n from '@/hooks/useI8n';
import { ErrorSchemaType, SensitiveLeveType } from '../../../types';
import AddResponseHeaderInformation from './ResponseHeaderInformation/AddResponseHeaderInformation';
import ResponseHeaderInformation from './ResponseHeaderInformation';
type TargetKey = React.MouseEvent | React.KeyboardEvent | string;
type ChildType = {
  respParamName: any;
  paramType: any;
  required: any;
  avlValue: any;
  comments: any;
  refSchemaId: number;
  schemaName: any;
  children?: ChildType[]; // 定义 children 为可选属性
  respKey: any;
};
const { Title } = Typography;

interface ResponseInformationProps {
  allHttpStatusList: any[];
  enumSource: any[];
  selectedService: any;
  domainSchemaList: any[];
  isProjectAndProductOwned: boolean;
  selectedRootNode: any; // 根节点
  isProduct: boolean;
  sensitiveLevelList: SensitiveLeveType[];
  errorSchemaList: ErrorSchemaType[];
  updateDomainSchemaList: () => Promise<void>;
}

const ResponseInformation = (props: ResponseInformationProps) => {
  const {
    allHttpStatusList = [],
    enumSource = [],
    selectedRootNode,
    isProduct = false,
    selectedService,
    domainSchemaList = [],
    errorSchemaList = [],
    updateDomainSchemaList,
    isProjectAndProductOwned = false,
    sensitiveLevelList = [],
  } = props;
  const { formatMessage } = useI18n();
  const { currentDomainObjId, isImportBySwaggerSuccess, setIsImportBySwaggerSuccess } =
    useModel('manageExtensionServiceModel');
  const [spining, setSpining] = useState<boolean>(false); // menu loading
  const [isExampleShow, setIsExampleShow] = useState<boolean>(false);
  const [openAddResponseInformation, setOpenAddResponseInformation] = useState<boolean>(false);
  const [openEditResponseInformation, setOpenEditResponseInformation] = useState<boolean>(false);
  const [responseDataSource, setResponseDataSource] = useState<any>([]);
  const [responseHeaderDataSource, setResponseHeaderDataSource] = useState<any>([]);
  const [responseInformation, setResponseInformation] = useState<any>([]);
  const [responseHeaderInformation, setResponseHeaderInformation] = useState<any>([]);
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [selectedParam, setSelectedParam] = useState<any>({});
  const [httpStatusList, setHttpStatusList] = useState<any>([]);
  const [respRootSchema, setRespRootSchema] = useState<{
    schemaId: number;
    schemaName: string;
    domainObjId: number;
  } | null>();
  const [respBodyExample, setRespBodyExample] = useState<any>({});
  const [selectedSchema, setSelectedSchema] = useState<{ schemaId: number; schemaName: string }>();
  const { openSchemaManagementFromResp, setOpenSchemaManagementFromResp, openSchemaManagementFromReq } =
    useModel('manageExtensionServiceModel');
  const [hasExecuted, setHasExecuted] = useState(false); // 新增状态来跟踪是否已经执行
  const [expReasonErrorCodeOpen, setExpReasonErrorCodeOpen] = useState<boolean>(false);
  const [openScriptManagement, setOpenScriptManagement] = useState<boolean>(false);

  const ParamNameListWhichCanShowMoreBtn = ['errorCode', 'resultCode', 'res_code', 'code'];

  const onTabsChange = (key: string) => {
    setRespRootSchema(null); // 重置respRootSchema
    setSelectedStatus(key);
    if (key === 'AfterServiceResponseScript') {
      setIsExampleShow(false);
      setOpenScriptManagement(true);
    }
  };

  const deleteEntityPropConfirm: PopconfirmProps['onConfirm'] = async (e) => {
    setSpining(true);
    const param = {
      respParamId: selectedParam?.respParamId,
      domainObjId: currentDomainObjId,
    };
    try {
      const resultData = await operateResponseParam('delete', param);

      if (resultData?.success) {
        message.success(formatMessage('MANAGEEXTENSIONSERVICE.RESPONSE.DELETE.SUCCESS', RespHeaderOptions.option1));
        // 删除后重新渲染数据
        setHasExecuted(false);
        queryResponseInfo();
        setRespBodyExample({});
      }
    } catch (error) {
      console.error(error);
    } finally {
      setSpining(false);
    }
  };

  // param list表格
  const columns = [
    {
      dataIndex: 'respParamName',
      title: formatMessage('PROJECT.COMMON.NAME'),
      width: '15%',
      ellipsis: true,
    },
    {
      dataIndex: 'paramType',
      title: formatMessage('PROJECT.COMMON.TYPE'),
      width: '20%',
      ellipsis: true,
      render: (_: string, record: any) => {
        if (record.paramType && !ParamTypeSource.includes(record.paramType)) {
          if (record.paramType === 'Void') {
            return '';
          }
          if (record?.refSchemaId < 0) {
            const schemaObj = errorSchemaList.find((i: ErrorSchemaType) => i?.schemaId === record?.refSchemaId);
            return schemaObj?.schemaName;
          } else {
            const allSchemaList = flatteningDomainSchemaList(domainSchemaList);
            const schemaObj = allSchemaList?.find((i: any) => i?.schemaId === record?.refSchemaId);
            return (
              <span
                className={styles.iconStyle}
                onClick={async () => {
                  await updateDomainSchemaList();
                  setSelectedSchema(schemaObj);
                  setOpenSchemaManagementFromResp(true);
                }}
              >
                {record?.isArray === 'Y'
                  ? `List<${record?.refSchemaName || record?.schemaName || schemaObj?.schemaName}>`
                  : `${record?.refSchemaName || record?.schemaName || schemaObj?.schemaName}`}
              </span>
            );
          }
        }
        return record?.isArray === 'Y' ? `${record.paramType}[]` : record.paramType;
      },
    },
    {
      dataIndex: 'avlValue',
      title: formatMessage('MANAGEEXTENSIONSERVICE.REQUEST.PARAM.ALLOWABLE_VALUES'),
      width: '30%',
      ellipsis: true,
      render: (_: string, record: any) => {
        if (respRootSchema && respRootSchema?.schemaId < 0) {
          if (ParamNameListWhichCanShowMoreBtn.find((item) => item === record?.respParamName)) {
            return (
              <div className={styles.moreErrorCode}>
                <span className={styles.avlValueText}>{record.avlValue}</span>
                <span
                  className={styles.moreText}
                  onClick={() => {
                    setExpReasonErrorCodeOpen(true);
                  }}
                >
                  {formatMessage('MANAGEEXTENSIONSERVICE.RESPONSE.MORE')}
                </span>
              </div>
            );
          }
        } else {
          if (record.paramType === 'Void') {
            return formatMessage('PROJECT.COMMON.NODATA');
          }
          if (record.avlValue && record.enumId) {
            return enumSource.find((i: any) => `${i.value}` === `${record.enumId}` || i.label === `${record.enumId}`)
              ?.label;
          }
          if (record?.typeFormat) {
            return `Format : ${record?.typeFormat}`;
          }
        }
        return record.avlValue;
      },
    },
    {
      dataIndex: 'comments',
      title: formatMessage('PROJECT.COMMON.DESCRIPTION'),
      width: '25%',
      ellipsis: true,
    },
    {
      dataIndex: '',
      title: formatMessage('PROJECT.COMMON.OPERATION'),
      width: '9%',
      render: (_: string, record: any) => {
        // 如果是Schema类型，Schema里面的参数是递归添加的，没有respParamId，则此处不允许修改和删除
        if (record?.respParamId) {
          return (
            <div>
              <Tooltip title={formatMessage('PROJECT.COMMON.EDIT')}>
                <FormOutlined
                  className={classNames(styles.iconStyle, {
                    [styles.hide]: selectedRootNode?.state !== 'D',
                  })}
                  onClick={() => {
                    setOpenEditResponseInformation(true);
                  }}
                />
              </Tooltip>

              <Popconfirm
                title={formatMessage('MANAGEEXTENSIONSERVICE.RESPONSE.DELETE.TITLE', RespHeaderOptions.option1)}
                description={formatMessage(
                  'MANAGEEXTENSIONSERVICE.RESPONSE.DELETE.DESCRIPTION',
                  RespHeaderOptions.option1,
                )}
                onConfirm={deleteEntityPropConfirm}
                okText={formatMessage('PROJECT.COMMON.YES')}
                cancelText={formatMessage('PROJECT.COMMON.NO')}
              >
                {isProjectAndProductOwned ? (
                  <></>
                ) : (
                  <Tooltip title={formatMessage('PROJECT.COMMON.DELETE')}>
                    <DeleteOutlined
                      className={classNames(styles.versionIcon2, {
                        [styles.hide]: selectedRootNode?.state !== 'D',
                      })}
                      onClick={() => {}}
                    />
                  </Tooltip>
                )}
              </Popconfirm>
            </div>
          );
        }
      },
    },
  ];

  const renderResponseInformation = () => {
    return (
      <div>
        {/* body参数 */}
        <Space>
          <span className={styles.strongText}>Body: </span>
          <span>Content Type</span>
          <Input disabled style={{ width: '120px' }} value={responseDataSource[0]?.respContentType} />
        </Space>
        <ResizableTable
          size="small"
          style={{ marginTop: '12px' }}
          columns={columns}
          dataSource={responseDataSource}
          rowKey={(record: any) => record?.respKey}
          pagination={false}
          onRow={(record: any) => ({
            onClick: () => {
              setSelectedParam(record);
            },
          })}
        />
        {isExampleShow && (
          <div style={{ margin: '10px 0' }}>
            <Title level={5}>{formatMessage('PROJECT.COMMON.EXAMPLE')}</Title>
            <CodeMirrorRender value={respBodyExample} />
          </div>
        )}
        {/* header参数 */}
        <ResponseHeaderInformation
          selectedStatus={selectedStatus}
          responseHeaderDataSource={responseHeaderDataSource}
          selectedService={selectedService}
          isProjectAndProductOwned={isProjectAndProductOwned}
          enumSource={enumSource}
          selectedRootNode={selectedRootNode}
          queryResponseInfo={queryResponseInfo}
        />
      </div>
    );
  };

  // 查询扩展服务响应信息
  const queryResponseInfo = async () => {
    setSpining(true);
    try {
      const param: any = {
        serviceId: selectedService?.serviceId,
      };
      const { success, data } = await qryExtensionServiceResponseInfoByServiceId(param);
      if (success) {
        const respList = data?.odhServiceRespVOList || [];
        // 过滤出响应Body、Header数据
        setResponseInformation(respList?.filter((item: any) => item?.respIn === 'B'));
        setResponseHeaderInformation(respList?.filter((item: any) => item?.respIn === 'H'));
      }
    } catch (error) {
      console.error(error);
    } finally {
      setSpining(false);
    }
  };

  // 查询入参/出参样例
  const queryRespBodyExample = async (schemaId: string) => {
    setSpining(true);
    try {
      let response;
      if (Number(schemaId) < 0) {
        const respParam = responseInformation.find((i: any) => i?.respCode === selectedStatus);
        response = await getErrorBodyExample({ respParamId: respParam?.respParamId });
      } else {
        response = await qryBodyExample({ schemaId });
      }

      if (response.success) {
        setRespBodyExample(response.data);
      } else {
        console.error('Failed to fetch response body example');
      }
    } catch (error) {
      console.error('An error occurred while fetching response body example:', error);
    } finally {
      setSpining(false);
    }
  };

  useEffect(() => {
    const newHttpStatus = allHttpStatusList?.filter((code: number) => {
      // 过滤响应参数中已经存在的状态码
      return !responseInformation?.some((item: any) => item?.respCode === `${code}`);
    });
    setHttpStatusList(newHttpStatus);

    if (responseInformation?.length > 0 && !hasExecuted) {
      setSelectedStatus(responseInformation[0]?.respCode);
      setHasExecuted(true);
    }
  }, [responseInformation, allHttpStatusList]);

  // useEffect(() => {
  //   if (!responseInformation?.length) {
  //     setSelectedStatus('AfterServiceResponseScript');
  //   }
  // }, [responseInformation]);

  useEffect(() => {
    if (!openSchemaManagementFromResp && !openSchemaManagementFromReq) {
      if (respRootSchema?.schemaId) {
        queryRespBodyExample(respRootSchema?.schemaId.toString());
      }
    }
  }, [respRootSchema, openSchemaManagementFromResp, openSchemaManagementFromReq]);

  useEffect(() => {
    // 进入扩展服务详情管理界面或关闭Schema管理界面时，查询响应信息
    if (!openSchemaManagementFromResp && !openSchemaManagementFromReq && selectedService?.serviceId) {
      queryResponseInfo();
    }
  }, [openSchemaManagementFromResp, openSchemaManagementFromReq, selectedService]);

  useEffect(() => {
    if (isImportBySwaggerSuccess) {
      setHasExecuted(false);
      queryResponseInfo();
      setIsImportBySwaggerSuccess(false);
    }
  }, [isImportBySwaggerSuccess]);

  // 递归设置children
  const mapPropListToChildren = async (propList: any[], rootKey: any): Promise<ChildType[]> => {
    const results = await Promise.all(
      propList.map(async (i: any) => {
        if (i?.hidden !== 'Y') {
          const child: ChildType = {
            ...i,
            respParamName: i?.name,
            refSchemaId: i?.refSchemaId,
            paramType: i?.type,
            required: i?.required,
            avlValue: i?.avlValue,
            comments: i?.description,
            schemaName: i?.schemaName,
            respKey: `${rootKey}-${i?.name}`,
          };
          if (i.refSchemaInfo) {
            child.paramType = i.refSchemaInfo?.schemaId;
            child.schemaName = i.refSchemaInfo?.schemaName;
            if (i.refSchemaInfo.propList?.length > 0) {
              child.children = await mapPropListToChildren(i.refSchemaInfo.propList, child.respKey);
            }
          }
          return child;
        }
        return undefined; // 显式返回 undefined
      }),
    );

    // 过滤掉 undefined 值
    return results.filter((child): child is ChildType => child !== undefined);
  };

  // 更新响应参数
  const updateDataSource = async () => {
    if (responseInformation) {
      // 使用 Promise.all 并行处理所有符合条件的响应信息
      const updateStatusList = await Promise.all(
        responseInformation
          // 过滤出状态码与选定状态相同的body参数
          .filter((i: any) => i?.respCode === selectedStatus && i?.respIn === 'B')
          .map(async (item: any) => {
            // 检查是否存在引用SchemaId
            if (item?.refSchemaId) {
              setIsExampleShow(true);
              let resultData;
              // 根据 refSchemaId 的值决定调用哪个 API
              if (item?.refSchemaId < 0) {
                // 如果 refSchemaId 小于 0，调用获取错误模式详情的 API
                resultData = await getErrorSchemaDetail({ respParamId: item?.respParamId });
              } else {
                // 否则，调用查询模式详情的 API
                resultData = await qrySchemaDetail({ schemaId: item?.refSchemaId });
              }
              // 如果 API 调用成功
              if (resultData?.success) {
                // 更新响应根模式的状态
                setRespRootSchema({
                  schemaId: item?.refSchemaId,
                  schemaName: item?.paramType || item?.schemaName,
                  domainObjId: resultData.data?.domainObjId,
                });
                if (resultData.data?.propList?.length > 0) {
                  item.children = await mapPropListToChildren(resultData.data?.propList, item?.respParamName);
                }
              }
            } else {
              // 如果没有引用模式 ID，隐藏示例
              setIsExampleShow(false);
            }
            // 返回更新后的项，添加 respKey 属性
            return { ...item, respKey: item?.respParamName };
          }),
      );
      setResponseDataSource(updateStatusList);
    }
  };

  useEffect(() => {
    if (selectedStatus !== 'AfterServiceResponseScript') {
      setOpenScriptManagement(false);
      updateDataSource();

      const filteredRespHeaderData = responseHeaderInformation
        // 过滤出状态码与选定状态相同的header参数
        .filter((i: any) => i?.respCode === selectedStatus);
      setResponseHeaderDataSource(filteredRespHeaderData);
    }
  }, [selectedStatus, responseInformation, responseHeaderInformation]);

  // useEffect(() => {
  //   if (selectedStatus === 'AfterServiceResponseScript') {
  //     setIsExampleShow(false);
  //     setOpenScriptManagement(true);
  //   }
  // }, [selectedStatus]);

  return (
    <Spin spinning={spining}>
      <div>
        <OperationIconTitle
          title={formatMessage('MANAGEEXTENSIONSERVICE.RESPONSE.INFORMATION')}
          type={!isProjectAndProductOwned && selectedRootNode?.state === 'D' ? 'add' : ''}
          opt={formatMessage('PROJECT.COMMON.ADD')}
          handleClick={() => {
            setSelectedParam({});
            setOpenAddResponseInformation(true);
          }}
        />
        <div>
          <Tabs
            items={[
              ...responseInformation?.map((item: any) => ({
                label: (
                  <>
                    <Tooltip title={item?.respCodeDesc}>
                      <InfoCircleOutlined />
                    </Tooltip>
                    <span>{item?.respCode}</span>
                  </>
                ),
                key: item?.respCode,
                children: renderResponseInformation(),
              })),
              {
                label: (
                  <Flex align="center" gap={5}>
                    <span className={`${myIcon.iconfont} ${myIcon['icon-script']}`} />
                    <span>{formatMessage('MANAGEEXTENSIONSERVICE.RESPONSE.AFTER_SERVICE_SCRIPT')}</span>
                  </Flex>
                ),
                key: 'AfterServiceResponseScript',
                children: (
                  <ScriptManagement
                    selectedRootNode={selectedRootNode}
                    type="resp"
                    selectedService={selectedService}
                    open={openScriptManagement}
                  />
                ),
              },
            ]}
            onChange={onTabsChange}
            activeKey={selectedStatus}
            hideAdd
          />
        </div>
      </div>
      {/* 新增或修改参数信息 */}
      {openAddResponseInformation && (
        <AddResponseInformation
          domainSchemaList={domainSchemaList}
          errorSchemaList={errorSchemaList}
          isProjectAndProductOwned={isProjectAndProductOwned}
          updateDomainSchemaList={updateDomainSchemaList}
          httpStatusList={httpStatusList}
          selectedRootNode={selectedRootNode}
          open={openAddResponseInformation}
          enumSource={enumSource}
          selectedService={selectedService}
          responseInformation={responseInformation}
          onCancel={() => setOpenAddResponseInformation(false)}
          onOk={() => {
            // 新增实体属性成功后，刷新当前表格
            setOpenAddResponseInformation(false);
            queryResponseInfo();
          }}
          onTabsChange={onTabsChange}
        />
      )}
      {openEditResponseInformation && (
        <EditResponseInformation
          domainSchemaList={domainSchemaList}
          errorSchemaList={errorSchemaList}
          isProjectAndProductOwned={isProjectAndProductOwned}
          updateDomainSchemaList={updateDomainSchemaList}
          httpStatusList={httpStatusList}
          selectedRootNode={selectedRootNode}
          selectedParam={selectedParam}
          open={openEditResponseInformation}
          enumSource={enumSource}
          selectedService={selectedService}
          responseInformation={responseInformation}
          onCancel={() => setOpenEditResponseInformation(false)}
          onOk={() => {
            // 修改实体属性成功后，刷新当前表格
            setOpenEditResponseInformation(false);
            queryResponseInfo();
          }}
          onTabsChange={onTabsChange}
        />
      )}
      <SchemaManagement
        sensitiveLevelList={sensitiveLevelList}
        enumSource={enumSource}
        currentSchema={selectedSchema}
        isProduct={isProduct}
        selectedRootNode={selectedRootNode}
        domainSchemaList={domainSchemaList}
        open={openSchemaManagementFromResp}
        onCancel={() => setOpenSchemaManagementFromResp(false)}
        // onOk={() => {
        //   queryResponseInfo();
        //   setOpenSchemaManagementFromResp(false);
        // }}
        updateDomainSchemaList={updateDomainSchemaList}
      />
      <ResponseErrorCodeList
        isProduct={isProduct}
        expReasonErrorCodeOpen={expReasonErrorCodeOpen}
        respParam={responseInformation?.find((i: any) => i?.respCode === selectedStatus)}
        selectedRootNode={selectedRootNode}
        selectedService={selectedService}
        setExpReasonErrorCodeOpen={setExpReasonErrorCodeOpen}
        updateDataSource={updateDataSource}
      />
    </Spin>
  );
};

export default ResponseInformation;
