/*
 *headerHtml: 自定义codemirror header
 *language: 语言， 初始化阶段支持json，java，python，ruby，sql，yaml，php
 ** 如果需要其他语言，添加在constants中或者通过code mirror的extensions添加
 *extensions: 扩展，支持自定义扩展, 原code mirror的属性
 */

import CodeMirror, { Extension, ReactCodeMirrorProps } from '@uiw/react-codemirror';
import { forwardRef, Ref } from 'react';
import { languages } from './constants';
import classNames from 'classnames';
import { espresso } from 'thememirror';
import styles from './index.less';

interface ICodeMirrorCmp extends ReactCodeMirrorProps {
  headerHtml?: React.ReactNode;
  language?: string;
  disabled?: boolean;
  readOnly?: boolean;
}

const CodeMirrorCmp = forwardRef((props: ICodeMirrorCmp, ref: Ref<any>) => {
  const { headerHtml, language, extensions = [], disabled = false, readOnly = false, ...restProps } = props;
  const launguage: Extension[] = language ? (languages.get(language) as Extension[]) : [];
  const { basicSetup } = restProps;

  return (
    <div
      className={classNames(styles.codeMirrorEditor, {
        [styles.codeMirrorExistHeader]: !!headerHtml,
        [styles.codeMirrorReadOnly]: readOnly,
        [styles.codeMirrorShowLineNumbers]: typeof basicSetup === 'object' ? basicSetup?.lineNumbers : true, // codemirror默认展示行号
      })}
    >
      {headerHtml ? <div className={styles.codeMirrorPanel}>{headerHtml}</div> : null}
      <CodeMirror
        readOnly={disabled || readOnly}
        // style={{
        //   pointerEvents: disabled ? 'none' : 'auto', // 禁用指针事件
        // }}
        ref={ref}
        theme={espresso}
        onMouseDown={(e) => e.stopPropagation()}
        onClick={(e) => e.stopPropagation()}
        extensions={[launguage, ...extensions]}
        {...restProps}
      />
      {/* 遮罩层，传入disabled属性时覆盖 CodeMirror */}
      {disabled && (
        <div
          style={{
            position: 'absolute',
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundColor: 'rgba(240, 240, 240, 0.5)',
            cursor: 'not-allowed',
          }}
        />
      )}
    </div>
  );
});

export default CodeMirrorCmp;
