.modalBottom {
    display: flex;
    flex-direction: row!important;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    font-size: 14px;
    font-family: NunitoSans-Regular, NunitoSans;
    font-weight: 400;
    color: #575966;
    line-height: 24px;
  
    & > div {
      display: flex;
      align-items: center;
    }
  }
  
  .modalContent {
    font-family: NunitoSans-Regular, NunitoSans;
    font-weight: 400;
    color: #575966;
    line-height: 22px;
    max-height: calc(100vh * 0.6 - 124px);
    min-height: calc(200px - 124px);
    overflow-y: auto;
    padding-right: 8px;
  }
  
  .errorModalStyle {
    :global {
      .ant-modal-header {
        background-color: #fff;
        padding: 24px 24px 12px 26px;
        height: auto;
        border-bottom:0;
        .ant-modal-title {
          & > div {
            display: flex;
            align-items: center;
          }
        }
      }
      .ant-modal-body {
        padding: 0 12px 0 60px;
      }
      .ant-modal-footer {
        padding: 28px 12px 24px 60px;
        border-top: none;
        height: auto;
        button{
          margin-right: 12px;
        }
      }
    }
  }
  