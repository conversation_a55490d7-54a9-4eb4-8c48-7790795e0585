import React, { useEffect, useState } from 'react';
import ErrorCodeManagement from './ErrorCodeManagement';
import OtherErrorDetail from './OtherErrorDetail';
import { useModel } from 'umi';
import { isHotfix } from '@/global';
import UnusablePage from '@/components/UnusablePage';

interface IBssOErrorCodeApiProps {
  openFromBssODataApi?: boolean; // 是否从BssODataApi页面打开
}

const BssOErrorCodeApi: React.FC<IBssOErrorCodeApiProps> = (props) => {
  const { openFromBssODataApi = false } = props;
  const { isShowOtherErrorDetail } = useModel('useErrorCodeModel');
  const { isConfigEnvironment, initSystemConfigData } = useModel('useSystemConfigDataModel');
  const [configLoading, setConfigLoading] = useState(true);

  useEffect(() => {
    const loadConfig = async () => {
      //查询系统配置项，当前为产品定义环境or项目定制环境
      await initSystemConfigData();
      setConfigLoading(false);
    };
    loadConfig();
  }, []);

  return (
    !configLoading &&
    (!isConfigEnvironment && isHotfix && !openFromBssODataApi ? (
      <UnusablePage /> //{/* 在使用环境不能使用Hotfix的功能,且从Open Data API Reference（openFromBssODataApi为true）进入的错误码界面，为非hotfix错误码界面 */}
    ) : (
      <>
        {!isShowOtherErrorDetail && <ErrorCodeManagement openFromBssODataApi={openFromBssODataApi} />}
        {isShowOtherErrorDetail && <OtherErrorDetail />}
      </>
    ))
  );
};
export default BssOErrorCodeApi;
