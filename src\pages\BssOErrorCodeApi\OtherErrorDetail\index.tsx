import React from 'react';
import styles from './index.less';
import { LeftOutlined } from '@ant-design/icons';
import useI18n from '@/hooks/useI8n';
import OtherErrorFilterConditions from './OtherErrorFilterConditions';
import { useForm } from 'antd/es/form/Form';
import { Spin } from 'antd';
import { useModel } from 'umi';
import OtherErrorList from './OtherErrorList';

interface IOtherErrorDetailProps {}

const OtherErrorDetail: React.FC<IOtherErrorDetailProps> = (props) => {
  const {} = props;
  const { setIsShowOtherErrorDetail } = useModel('useErrorCodeModel');
  const { formatMessage } = useI18n();
  const [otherErrorFilterConditionsForm] = useForm();
  const { otherErrorSpinning } = useModel('useErrorCodeModel');

  return (
    <div className={styles.bssSearchUseCase}>
      <div className={styles.header}>
        <LeftOutlined
          className={styles.returnIcon}
          onClick={() => {
            setIsShowOtherErrorDetail(false);
          }}
        />
        <span
          className={styles.returnText}
          onClick={() => {
            setIsShowOtherErrorDetail(false);
          }}
        >
          {formatMessage('ERRORCODE.OTHERERRORDETAIL.TITLE')}
        </span>
      </div>
      <Spin wrapperClassName={styles.spinContainer} spinning={otherErrorSpinning}>
        <div className={styles.activeLineContainer}>
          <OtherErrorFilterConditions otherErrorFilterConditionsForm={otherErrorFilterConditionsForm} />
          <OtherErrorList />
        </div>
      </Spin>
    </div>
  );
};
export default OtherErrorDetail;
