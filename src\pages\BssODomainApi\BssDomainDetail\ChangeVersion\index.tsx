import React, { useState, useEffect, useMemo, ReactNode } from 'react';
import { Modal, Steps } from 'antd';
import { getOperateStage } from '@/services/domainService';
import { LoadingOutlined } from '@ant-design/icons';

interface IChangeVersionModal {
  open: boolean;
  onCancel: () => void;
  onOk: () => void;
  startStep: number;
  selectedDomainNode?: any;
}

interface resType {
  step: number;
  state: string;
  stateDesc: string;
}

// 状态描述
const DescGroups = {
  desc1: 'Import domain definition file.',
  desc2: 'Release domain version.',
  desc3: 'Generate domain released version compile file.',
  desc4: 'Restart odh service.',
  desc5: 'Refresh open data domain information.',
};

// 状态
const StatusGroups = {
  inProcess: 'In Progress',
  waiting: 'Waiting',
  finished: 'Finished',
  progress: 'progress',
  failed: 'Failed',
};

const ChangeVersionModal: React.FC<IChangeVersionModal> = ({
  open = false,
  onCancel = () => {},
  onOk = () => {},
  startStep,
  selectedDomainNode,
}) => {
  const [currentStep, setCurrentStep] = useState<number>(0); // 切换版本
  const [currentStatus, setCurrentStatus] = useState<any>(StatusGroups.progress); // 当前状态
  const [step1Title, setStep1Title] = useState<ReactNode>(
    startStep === 0 ? StatusGroups.inProcess : StatusGroups.waiting,
  );
  const [step2Title, setStep2Title] = useState<ReactNode>(
    startStep === 1 ? StatusGroups.inProcess : StatusGroups.waiting,
  );
  const [step3Title, setStep3Title] = useState<ReactNode>(
    startStep === 2 ? StatusGroups.inProcess : StatusGroups.waiting,
  );
  const [step4Title, setStep4Title] = useState<ReactNode>(
    startStep === 3 ? StatusGroups.inProcess : StatusGroups.waiting,
  );
  const [step5Title, setStep5Title] = useState<ReactNode>(StatusGroups.waiting);
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [cancelButtonDisplay, setCancelButtonDisplay] = useState<string>('none');
  const [okButtonDisplay, setOkButtonDisplay] = useState<string>('');

  // 局部数据重置
  const resetLocal = () => {
    setCancelButtonDisplay('');
    setCurrentStatus('error');
    setOkButtonDisplay('none');
  };

  // 数据重置
  const resetAll = () => {
    resetLocal();
    setCurrentStep(0);
    setStep1Title(startStep === 0 ? StatusGroups.inProcess : StatusGroups.waiting);
    setStep2Title(startStep === 1 ? StatusGroups.inProcess : StatusGroups.waiting);
    setStep3Title(startStep === 2 ? StatusGroups.inProcess : StatusGroups.waiting);
    setStep4Title(startStep === 3 ? StatusGroups.inProcess : StatusGroups.waiting);
    setStep5Title(StatusGroups.waiting);
    setConfirmLoading(false);
  };

  const getState = (data: resType) => {
    switch (data?.state) {
      case 'P':
        return StatusGroups.inProcess;
      case 'S':
        return StatusGroups.finished;
      case 'F':
        return data?.stateDesc ? (
          <div style={{ width: '25rem' }} title={data?.stateDesc}>
            {StatusGroups.failed}: {data?.stateDesc}
          </div>
        ) : (
          StatusGroups.failed
        );
      default:
        return StatusGroups.waiting;
    }
  };

  const setLastestStepTitle = (data: resType) => {
    const { step } = data;
    switch (step) {
      case 2:
        setStep2Title(getState(data));
        break;
      case 3:
        setStep3Title(getState(data));
        break;
      case 4:
        setStep4Title(getState(data));
        break;
      case 5:
        setStep5Title(getState(data));
        break;
    }
  };

  // 定时调用接口校验流程是否完成
  const changeVersionStep = async () => {
    setCancelButtonDisplay('none');
    setCurrentStatus(StatusGroups.progress);
    setOkButtonDisplay(''); // 显示 Ok 按钮，但是控制按钮为加载状态
    let count = 0;
    let totalCount = 150;
    let lastestStepBeforeExcepiton = 2; // getOperateStage轮询时，可能上一次还是正常的，下一次step直接返回-1，所以记录上一次正常返回的step
    switch (startStep) {
      case 0:
        totalCount = 225; // 2s * 225 = 450s = 7min30s 导入
        break;
      case 1:
        totalCount = 165; // 2s * 165 = 330s = 5min30s 发布
        break;
      case 2:
        totalCount = 240; // 2s * 240 = 480s = 8min 切换
        break;
      case 3:
        totalCount = 240; // 2s * 240 = 480s = 8min 重启
        break;
    }
    // 用于处理操作阶段的逻辑
    const handleOperateStage = async () => {
      try {
        const res = await getOperateStage({ domainId: selectedDomainNode?.domainId });
        count += 1;
        const { success, data } = res;
        if (success && data?.step !== -1 && data?.state) {
          if (data?.step === 1) {
            setStep1Title(getState(data));
            if (data?.state === 'S') {
              setStep1Title(StatusGroups.finished);
              setStep2Title(StatusGroups.inProcess);
            }
          } else if (data?.step === 2) {
            setStep1Title(StatusGroups.finished);
            setStep2Title(getState(data));
            if (data?.state === 'S') {
              setStep3Title(StatusGroups.inProcess);
            }
          } else if (data?.step === 3) {
            setStep1Title(StatusGroups.finished);
            setStep2Title(StatusGroups.finished);
            setStep3Title(getState(data));
            if (data?.state === 'S') {
              setStep4Title(StatusGroups.inProcess);
            }
          } else if (data?.step === 4) {
            setStep1Title(StatusGroups.finished);
            setStep2Title(StatusGroups.finished);
            setStep3Title(StatusGroups.finished);
            setStep4Title(getState(data));
            if (data?.state === 'S') {
              setStep5Title(StatusGroups.inProcess);
            }
          } else if (data?.step === 5) {
            setStep1Title(StatusGroups.finished);
            setStep2Title(StatusGroups.finished);
            setStep3Title(StatusGroups.finished);
            setStep4Title(StatusGroups.finished);
            setStep5Title(getState(data));
            if (data?.state === 'S') {
              clearInterval(reloadFun);
              setConfirmLoading(false);
            }
          }
          // startStep不同，初始化的步骤条总数不同，故setCurrentStep时要进行data?.step - startStep或data?.step - 1 - startStep处理
          if (data?.state === 'S') {
            setCurrentStep(data?.step - startStep);
            lastestStepBeforeExcepiton = data?.step + 1;
          } else {
            setCurrentStep(data?.step - 1 - startStep);
            lastestStepBeforeExcepiton = data?.step;
            if (data?.state === 'F') {
              clearInterval(reloadFun);
              resetLocal();
            }
          }
        } else {
          setLastestStepTitle({ step: lastestStepBeforeExcepiton, state: 'F', stateDesc: data?.stateDesc || '' });
          clearInterval(reloadFun);
          resetLocal();
        }
        if (count >= totalCount) {
          // 每2秒发起一次请求,请求totalCount次流程还没结束，默认失败，可以取消切换操作
          setLastestStepTitle({ step: lastestStepBeforeExcepiton, state: 'F', stateDesc: data?.stateDesc || '' });
          clearInterval(reloadFun);
          resetLocal();
        }
      } catch (error) {
        setLastestStepTitle({ step: lastestStepBeforeExcepiton, state: 'F', stateDesc: '' });
        clearInterval(reloadFun);
        resetLocal();
      }
    };

    // 每隔2秒执行一次
    const reloadFun = setInterval(handleOperateStage, 2000);
  };

  useEffect(() => {
    if (open) {
      setConfirmLoading(true);
      changeVersionStep();
    } else {
      resetAll();
    }
  }, [open]);

  const loadingStatus: any = (currentStatus: string) => {
    if (currentStatus === 'error') {
      return undefined;
    }
    return <LoadingOutlined />;
  };

  // 步骤条 成员
  const items: any = useMemo(() => {
    const itemList = [
      {
        title: step1Title,
        description: DescGroups.desc1,
      },
      {
        title: step2Title,
        description: DescGroups.desc2,
      },
      {
        title: step3Title,
        description: DescGroups.desc3,
      },
      {
        title: step4Title,
        description: DescGroups.desc4,
      },
      {
        title: step5Title,
        description: DescGroups.desc5,
      },
    ];
    const newItemList = itemList.slice(startStep).map((item, index) => {
      return {
        ...item,
        icon: currentStep === index ? loadingStatus(currentStatus) : undefined,
      };
    });
    return newItemList;
  }, [step1Title, step2Title, step3Title, step4Title, step5Title, currentStep, currentStatus, startStep]);

  return (
    <Modal
      width={500}
      destroyOnClose
      open={open}
      forceRender={open}
      title="Switch Domain Test Version"
      keyboard={false}
      maskClosable={false}
      closable={false}
      cancelButtonProps={{
        onClick: () => {
          onCancel();
        },
        style: { display: cancelButtonDisplay },
      }}
      okButtonProps={{
        style: { display: okButtonDisplay },
      }}
      onOk={() => onOk()}
      confirmLoading={confirmLoading}
    >
      {/* content */}
      <div>
        <Steps direction="vertical" current={currentStep} status={currentStatus} items={items} />
      </div>
    </Modal>
  );
};

export default ChangeVersionModal;
