import { request } from "umi";
import { stringify } from "qs";


// 查询领域对象数据模型列表
export async function queryDomainDataModelListService(
  domainObjId: number | null,
  params: { pageNo: number; pageSize: number }
) {
  return request(`/odh-web/odh/web/domain/datamodels/${domainObjId}`, {
    method: "GET",
    params,
  });
}

//领域实体
interface IaddDomainEntity {
  entityCode: string;
  entityName: string;
  domainObjId: number;
  modelId: number;
  entityType: string;
  defaultPage: number;
  ignoreVer?: string;
  creationSrc?: string;
  comments?: string;
  entityId?: number; //修改
}

//领域实体服务
interface IaddDomainEntityService {
  entityId: number;
  serviceName: string;
  creationSrc: string;
  ignoreVer?: string;
  serviceCatg: string;
  serviceSubCatg?: string;
  serviceSubCatgName?: string;
  httpMethod: string;
  servicePath?: string;
  implType: string;
  odhServiceProjDubboVo?: {
    dubboClass?: string,
    dubboMethod?: string
  };
  comments?: string;
  serviceId?: number; //修改
}

//领域实体属性
interface IaddDomainEntityProp {
  entityId: number;
  columnName: string;
  propName: string;
  propType: string;
  enumId?: number;
  avlValue?: string;
  keyFlag: string;
  required: string;
  indexFlag: string;
  orderFlag: string;
  filterFlag: string;
  ignoreVer?: string;
  comments?: string;
  propId?: number; //修改
}

//领域实体属性
type odhEntityRelProp = Partial<{
  propId?: number;
  expandPropId?: number;
  expandId?:number
}>;
interface IaddDomainEntityExpand {
  entityId: number;
  propName: string;
  relType: string;
  expandEntityId: number;
  ignoreVer?: string;
  comments?: string;
  odhEntityRelPropList: Array<odhEntityRelProp>;
}

//领域实体事件
interface IaddDomainEntityEvent {
  entityId: number;
  eventName: string;
  eventCode: string;
  ignoreVer?: string;
  comments?: string;
  eventId?: number; //修改
}

// 新增领域实体
export async function addDomainEntity(
  data: IaddDomainEntity
) {
  return request(`/odh-web/odh/web/domain/entity`, {
    method: "POST",
    data,
  });
}

// 修改领域实体

export async function modifyDomainEntity(
  entityId: number,
  data: IaddDomainEntity
) {
  return request(`/odh-web/odh/web/domain/entity/${entityId}`, {
    method: "PATCH",
    data,
  });
}

// 删除领域实体
export async function deleteDomainEntity(
  entityId: number
) {
  return request(`/odh-web/odh/web/domain/entity/${entityId}`, {
    method: "DELETE",
  });
}

//查询服务分类
export async function queryServiceCatgList() {
  return request(`/odh-web/odh/web/extendService/catgList`, {
    method: "GET",
  });
}

//查询服务子分类
export async function queryServiceSubCatgList() {
  return request(`/odh-web/odh/web/extendService/subCatgList`, {
    method: "GET",
  });
}

//查询实体服务根路径
export async function qryServiceRootPath(params: { entityId: number }) {
  return request(`/odh-web/odh/web/extendService/qryServiceRootPath?${stringify(params)}`, {
    method: "GET",
  });
}

//查询实体服务
export async function qryEntityServiceList(entityId: number) {
  return request(`/odh-web/odh/web/domain/entity/extendServices/${entityId}`, {
    method: "GET",
  });
}

//查询所有tfm服务
export async function qryAllTfmServices(params:{domainId:number}) {
  return request(`/odh-web/odh/web/common/getAllTfmServices?${stringify(params)}`, {
    method: "GET",
  });
}

//新增实体服务
export async function addDomainEntityService(
  params: { entityId: number },
  data: IaddDomainEntityService
) {
  return request(`/odh-web/odh/web/extendService/addExtendService?${stringify(params)}`, {
    method: "POST",
    data,
  });
}
//修改实体服务
export async function editDomainEntityService(
  params: { entityId: number },
  data: IaddDomainEntityService
) {
  return request(`/odh-web/odh/web/extendService/updateExtendService?${stringify(params)}`, {
    method: "POST",
    data,
  });
}

//删除实体服务
export async function deleteDomainEntityService(params: {
  entityId: number,
  serviceId: number,
}) {
  return request(`/odh-web/odh/web/extendService/deleteExtendService?${stringify(params)}`, {
    method: "DELETE",
  });
}

//用 modelId 查询表和视图的列名称
export async function qryColumnNameList(params: { modelId: number }) {
  return request(`/odh-web/odh/web/domain/mgmt/column?${stringify(params)}`, {
    method: "GET",
  });
}

//查询领域对象下的enum列表
export async function qryEnumInfoList(data: {
  pageNo: number,
  pageSize: number,
  domainId: number,

}) {
  return request(`/odh-web/odh/web/domain/enum/qryEnumListInfo`, {
    method: "POST",
    data,
  });
}


//查询实体属性
export async function qryEntityPropList(entityId: number) {
  return request(`/odh-web/odh/web/domain/entity/properties/${entityId}`, {
    method: "GET",
  });
}

//新增实体属性
export async function addDomainEntityProp(data: IaddDomainEntityProp) {
  return request(`/odh-web/odh/web/entity/prop/addEntityProp`, {
    method: "POST",
    data,
  });
}

//修改实体属性
export async function editDomainEntityProp(data: IaddDomainEntityProp) {
  return request(`/odh-web/odh/web/entity/prop/modifyEntityProp`, {
    method: "POST",
    data,
  });
}

//删除实体属性
export async function deleteEntityProp(params: {
  entityId: number,
  propId: number,
}) {
  return request(`/odh-web/odh/web/entity/prop/deleteEntityProp?${stringify(params)}`, {
    method: "POST",
    headers: {
      cookie: window?.top?.portal?.appGlobal?.attributes || {}
    }
  });
}

//查询关联类型
export async function qryRelaTypeList() {
  return request(`/odh-web/odh/web/domain/expandRelEntity/relTypes`, {
    method: "GET",
  });
}

//查询领域实体扩展对象列表
export async function qryExpandEntityList(entityId: number) {
  return request(`/odh-web/odh/web/domain/entity/expandEntities/${entityId}`, {
    method: "GET",
  });
}

//新增领域实体扩展对象
export async function addDomainEntityExpand(
  params: { entityId: number },
  data: IaddDomainEntityExpand
  ) {
  return request(`/odh-web/odh/web/domain/expandRelEntity?${stringify(params)}`, {
    method: "POST",
    data,
  });
}
//修改领域实体扩展对象
export async function modifyDomainEntityExpand(
  expandId: number,
  data: IaddDomainEntityExpand
) {
  return request(`/odh-web/odh/web/domain/expandRelEntity/${expandId}`, {
    method: "PATCH",
    data,
    headers: {
      cookie: window?.top?.portal?.appGlobal?.attributes || {}
    }
  });
}

//删除领域实体扩展对象
export async function deleteEntityExpand(expandId: number) {
  return request(`/odh-web/odh/web/domain/expandRelEntity/${expandId}`,{
    method: "DELETE",
  });
}

//查询rlc接口
export async function qryAllEvents() {
  return request(`/odh-web/odh/web/entity/event/qryAllEvents`, {
    method: "GET",
  });
}

//查询领域实体事件列表
export async function qryEntityEventList(entityId: number) {
  return request(`/odh-web/odh/web/domain/entity/events/${entityId}`, {
    method: "GET",
  });
}

//新增实体事件
export async function addDomainEntityEvent(
  data: IaddDomainEntityEvent
  ) {
  return request(`/odh-web/odh/web/entity/event/addEntityEvent`, {
    method: "POST",
    data,
  });
}
//修改实体事件
export async function modDomainEntityEvent(
  data: IaddDomainEntityEvent
  ) {
  return request(`/odh-web/odh/web/entity/event/modifyEntityEvent`, {
    method: "POST",
    data,
  });
}

//删除实体事件
export async function deleteEntityEventById(params: {
  entityId: number,
  eventId: number,
}) {
  return request(`/odh-web/odh/web/entity/event/deleteEntityEvent?${stringify(params)}`, {
    method: "POST",
    headers: {
      cookie: window?.top?.portal?.appGlobal?.attributes || {}
    }
  });
}

//查询dubbo class和method
export async function queryDubboInterfaceDetails(data: {
  fuzzyKey: string,
}) {
  return request(`/odh-web/odh/web/common/filterDubboInterfaceDetailsByClassPath`, {
    method: "POST",
    data,
    headers: {
      cookie: window?.top?.portal?.appGlobal?.attributes || {}
    }
  });
}

//查询领域实体事件列表
export async function qryEntityRelByDomainObjId(params: { domainObjId: number }) {
  return request(`/odh-web/odh/web/domain/mgmt/obj/qryEntityRelByDomainObjId?${stringify(params)}`, {
    method: "GET",
  });
}

//查询扩展服务请求参数
export async function qryExtensionServiceRequestInfoByServiceId(params: { serviceId: string }) {
  return request(`/odh-web/odh/web/extendService/req/info?${stringify(params)}`, {
    method: "GET",
  });
}

//操作请求参数 新增：new 修改：update 删除：delete
export async function operateRequestParam(operateType:string,data: any) {
  return request(`/odh-web/odh/web/extendService/req/${operateType}`, {
    method: "POST",
    data,
  });
}

//新增Schema
export async function addSchemaByDomainObjId(data: {schemaName:string, domainObjId: number}) {
  return request('/odh-web/odh/web/extendService/schema/new', {
    method: "POST",
    data,
  });
}

//查询领域下schema列表
export async function qrySchemaListByDomainId(params: { domainId: number }) {
  return request(`/odh-web/odh/web/extendService/schema/list?${stringify(params)}`, {
    method: "GET",
  });
}

//查询响应参数
export async function qryExtensionServiceResponseInfoByServiceId(params: { serviceId: string }) {
  return request(`/odh-web/odh/web/extendService/resp/info?${stringify(params)}`, {
    method: "GET",
  });
}

//查询所有http status状态码
export async function qryAllHttpStatus() {
  return request('/odh-web/odh/web/common/qryAllHttpStatus', {
    method: "GET",
  });
}

//操作响应参数 新增：new 修改：update 删除：delete
export async function operateResponseParam(operateType:string,data: any) {
  return request(`/odh-web/odh/web/extendService/resp/${operateType}`, {
    method: "POST",
    data,
  });
}

//查询Schema详情
export async function qrySchemaDetail(params: { schemaId: number }) {
  return request(`/odh-web/odh/web/extendService/schema/info?${stringify(params)}`, {
    method: "GET",
  });
}

//新增或修改Schema信息,每次修改都需要全量传入接口文档中的字段，后台先删除旧的再新增
export async function modifySchemaInfo(data: any) {
  return request('/odh-web/odh/web/extendService/schema/modify', {
    method: "POST",
    data,
  });
}

//查询入参/出参样例
export async function qryBodyExample(params: { schemaId: string }) {
  return request(`/odh-web/odh/web/extendService/bodyExample?${stringify(params)}`, {
    method: "GET",
  });
}

//添加prop根据javaCode
export async function addPropByCode(data: {schemaId:number, javaCode: string, domainObjId: number}) {
  return request('/odh-web/odh/web/extendService/schema/addPropByCode', {
    method: "POST",
    data,
  });
}

// 通过Swagger文件添加服务
export async function addFromFile(data: any) {
  return request('/odh-web/odh/web/extendService/schema/addServiceFromSwagger', {
    method: 'POST',
    data,
  });
}

// 删除schema
export async function deleteSchemaById(params:{domainObjId:number,schemaId:number}) {
  return request(`/odh-web/odh/web/extendService/schema/deleteSchema?${stringify(params)}`, {
    method: 'DELETE',
  });
}

// 批量删除服务参数
export async function batchDelete(params: {reqIds:string,domainObjId:number}) {
  return request(`/odh-web/odh/web/extendService/req/batchDelete?${stringify(params)}`, {
    method: 'POST',
  });
}

//查询脚本代码片段
export async function qryScriptSnippetList(params: { exePhase: string }) {
  return request(`/odh-web/odh/web/extendService/service/scriptSnippetList?${stringify(params)}`, {
    method: "GET",
  });
}

//查询执行脚本
export async function qryScriptListById(params: { serviceId: number }) {
  return request(`/odh-web/odh/web/extendService/service/scriptListById?${stringify(params)}`, {
    method: "GET",
  });
}

// 保存执行脚本
export async function operateServiceScript(data:{
  serviceId: number,
  exePhase: string;
  exeScript:string;
  exeScriptId?:number;
}) {
  return request(`/odh-web/odh/web/extendService/service/operateServiceScript`, {
    method: 'POST',
    data
  });
}

// 根据属性名称获取描述信息
export async function getDescByProp(params: { prop: string }) {
  return request(`/odh-web/odh/web/common/getDescByProp?${stringify(params)}`, {
    method: "GET",
  });
}

// 查询敏感数据级别列表
export async function qrySensitiveLevelList() {
  return request(`/odh-web/odh/web/domain/mgmt/prop/querySensitiveLevelList`, {
    method: "GET",
  });
}

// 迁移扩展服务
export async function transferEntityServices(data: Array<{
  serviceId: number,
  bfDomainObjCode: string,
  bfEntityCode: string,
  afDomainObjCode: string,
  afEntityCode: string,
  afEntityId: number
}>) {
  return request(`/odh-web/odh/web/extendService/service/saveTransferService`, {
    method: 'POST',
    data
  });
}
