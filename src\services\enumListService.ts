import { stringify } from "qs";
import { request } from "umi";

interface IaddDomainEnumListService {
  domainObjId: number;
  comments: string;
  enumName: string;
  enumType: string;
  creationSrc: string;
}

// 新增领域枚举接口
export async function addDomainEnumListService(
  data: IaddDomainEnumListService
) {
  return request(`/odh-web/odh/web/domain/enum/addEnum`, {
    method: "POST",
    data,
  });
}

interface IdeleteDomainEnumListService {
  domainObjId: number | null;
  enumId: number;
}

// 删除领域枚举接口
export async function deleteDomainEnumListService(
  params: IdeleteDomainEnumListService
) {
  return request(`/odh-web/odh/web/domain/enum/deleteEnum?${stringify(params)}`, {
    method: "POST",
  });
}

interface ImodifyDomainEnumListService {}

// 修改领域枚举接口
export async function modifyDomainEnumListService(
  data: ImodifyDomainEnumListService
) {
  return request(`/odh-web/odh/web/domain/enum/modifyEnum`, {
    method: "POST",
    data,
  });
}

interface IqueryDomainEnumListService {
  domainObjId?: number;
  pageNo?: number;
  pageSize?: number;
}

// 查询数据枚举列表
export async function queryDomainEnumListService(
  data: IqueryDomainEnumListService
) {
  return request(`/odh-web/odh/web/domain/enum/qryEnumListInfo`, {
    method: "POST",
    data,
  });
}

// 查询领域枚举详情
export async function queryDomainEnumDetailService(params: { enumId: number }) {
  return request(`/odh-web/odh/web/domain/enum/qryEnumDetail`, {
    method: "GET",
    params,
  });
}

// 查询领域对象数据模型列表
export async function queryDomainDataModelListService(
  domainObjId: number | null,
  params: { pageNo: number; pageSize: number }
) {
  return request(`/odh-web/odh/web/domain/datamodels/${domainObjId}`, {
    method: "GET",
    params,
  });
}

// 用 moduleId 查询表和视图的列名称
export async function queryColumnsService(params: {
  modelId: string | number;
}) {
  return request(`/odh-web/odh/web/domain/mgmt/column`, {
    method: "GET",
    params,
  });
}
