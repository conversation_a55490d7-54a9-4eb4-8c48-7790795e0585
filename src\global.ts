export const openDomainApiHotfixPath = '/hotfixManagement/openDomainApiHotfix';

export const openErrorCodeApiHotfixPath = '/hotfixManagement/openErrorCodeApiHotfix';

// 安全获取当前菜单URL
function getCurrentMenuUrl(): string | undefined {
  try {
    // @ts-ignore - 忽略类型检查
    return window?.top?.portal?.appGlobal?.get('currentMenu')?.menuUrl;
  } catch (e) {
    console.error('获取当前菜单URL失败', e);
    return undefined;
  }
}

export const isHotfix =
  getCurrentMenuUrl() === `/odh-web/#${openDomainApiHotfixPath}` ||
  getCurrentMenuUrl() === `/odh-web/#${openErrorCodeApiHotfixPath}`;

// 安全获取portal语言
export const portalLanguage = (() => {
  try {
    // @ts-ignore - 忽略类型检查
    return window?.top?.portal?.appGlobal?.get('language');
  } catch (e) {
    console.error('获取语言失败', e);
    return 'en'; // 默认英文
  }
})();

