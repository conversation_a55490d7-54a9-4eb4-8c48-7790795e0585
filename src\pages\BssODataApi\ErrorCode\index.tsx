import styles from './index.less';
import useI18n from '@/hooks/useI8n';
import BssOErrorCodeApi from '@/pages/BssOErrorCodeApi';

import { LeftOutlined } from '@ant-design/icons';
import { Spin } from 'antd';
import { useModel } from 'umi';

interface BssErrorCodeType {
  onClose: () => void;
}

const ErrorCode = ({ onClose }: BssErrorCodeType) => {
  const { formatMessage } = useI18n();
  const { isShowOtherErrorDetail } = useModel('useErrorCodeModel');

  return (
    <div className={styles.bssSearchUseCase}>
      {!isShowOtherErrorDetail && (
        <div className={styles.header}>
          <LeftOutlined className={styles.returnIcon} onClick={onClose} />
          <span className={styles.returnText} onClick={onClose}>
            {formatMessage('ERRORCODE.ERRORCODELIST.TITLE')}
          </span>
        </div>
      )}

      <div className={styles.bottom}>
        <BssOErrorCodeApi openFromBssODataApi={true} />
      </div>
    </div>
  );
};

export default ErrorCode;
