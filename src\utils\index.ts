import { useStore } from '../store'
import type { Menu } from "../types/api"

//金额的格式化
export const formatMoney = (num?: number | string) => {
    if (!num) return 0.00
    const a = parseFloat(num.toString())
    return a.toLocaleString('zh-CN', { style: 'currency', currency: 'CNY' })
}

//格式化数字
export const formatNum = (num?: number | string) => {
    if (!num) return 0
    const a = num.toString()
    if (a.indexOf('.') > -1) return a.replace(/(\d)(?=(\d{3})+\.)/g, '$1,')
    return a.replace(/(\d)(?=(\d{3})+$)/g, '$1,')
}

//日期的格式化
export const formatDate = (date?: Date | string, rule?: string) => {
    let nowDate = new Date()
    if (date instanceof Date) nowDate = date
    else if (date) nowDate = new Date(date)
    if (rule === 'yyy-MM-dd') return nowDate.toLocaleDateString().replaceAll('/', '-')
    if (rule === 'HH-mm-ss') return nowDate.toLocaleTimeString().replaceAll('/', '-')
    return nowDate.toLocaleString().replaceAll('/', '-')
}

//用户状态转换
export const formatState = (state: number) => {
    if (state === 1) return '在职'
    if (state === 2) return '试用期'
    if (state === 3) return '离职'
}

//数组转树
export const getTree = (list: any, result: any, pid: number) => {
    for (let i = 0; i < list.length; i++) {
        if (list[i].pid === pid) {
            let item = { ...list[i], children: [] }
            result.push(item)
            getTree(list, item.children, item.id)
        }
    }
}

//获取页面路径
export const getPath = (list: Menu.menuItem[]): string[] => {
    return list.reduce((result: string[], item: Menu.menuItem) => {
        return result.concat(Array.isArray(item.children) && !(item.buttons) ? getPath(item.children) : item.path + '')
    }, [])
}

//手机号加密
export const secretMobile = (mobile: string | number) => {
    if (!mobile) return '-'
    const newMobile = mobile.toString()
    return newMobile.replace(/(\d{3})\d*(\d{4})/, '$1****$2')
}

//实现面包屑树
export const getBreadTree = (tree: Menu.menuItem[], pathName: string, path: string[]): string[] => {
    if (!tree) return []
    for (const data of tree) {
        path.push(data.menuName)
        if (data.path === pathName) return path
        if (data.children?.length) {
            const list = getBreadTree(data.children, pathName, path)
            if (list?.length) return list
        }
        path.pop()
    }
    return []
}

export const findParentKey = (
    menuItems: any,
    targetKey: string | number,
    parentKey: string | number | null = null
): string | number | null => {
    // 遍历当前层级的菜单项
    for (const item of menuItems) {
        // 将 item.key 转换为字符串进行比较，以处理数字 key 的情况
        if (String(item.key) === String(targetKey)) {
            // 如果当前项的 key 就是目标 key，返回其父级 key
            return parentKey;
        }
        // 如果当前项有子菜单，则在子菜单中递归查找
        if (item.children && item.children.length > 0) {
            const foundInChildren = findParentKey(item.children, targetKey, item.key);
            if (foundInChildren !== null) {
                return foundInChildren;
            }
        }
    }
    // 没有找到匹配的项
    return null;
};