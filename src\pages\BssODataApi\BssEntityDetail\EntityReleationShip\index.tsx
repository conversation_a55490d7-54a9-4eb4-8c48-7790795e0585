import React, { useEffect, useState } from 'react';
import { IDomainEntityRel, IEntityDto, IServiceList, IEventList } from '@/services/typing.d';
import useI18n from '@/hooks/useI8n';
import { SERVICETYPE, ENTITYSERVICETYPE, LEGENTOTHERTYPE, ENTITYLEGENTOTHERTYPE } from '@/constants';
import RenderSvgByInnerHTML from '@/components/RenderSvgByInnerHTML';
import styles from './index.less';
import DraggableSVGByInnerHTML from '@/components/DraggableSVGByInnerHTML';

interface IEntityReleationShip {
  entityDtoData?: IEntityDto;
  domainEntityRelData?: IDomainEntityRel;
  serviceListData?: Array<IServiceList>;
  eventListData?: Array<IEventList>;
  entityFlag?: boolean;
}

const YumlDiagram = require('yuml-diagram');

const EntityReleationShip: React.FC<IEntityReleationShip> = (props) => {
  const { domainEntityRelData, entityDtoData, serviceListData, eventListData, entityFlag } = props;
  const [yumlSvgCode, setYumlSvgCode] = useState<any>('');

  const { formatMessage } = useI18n();

  // 针对entityName进行转换, yuml-diagram的节点名称需要将单空格转换成双空格 =》 应对yum-diagram插件的bug
  const transferEntityName = (entityName: string) => (entityName || '').replaceAll(' ', '  ');

  // 获取实体的领域对象
  const getDomainObj = (text: any) => {
    const lastIndex = text.lastIndexOf('.');
    const domainObj = lastIndex !== -1 ? text.substring(0, lastIndex) : text;
    return domainObj;
  };

  // 拼接节点字符串
  const getNodeText = (data: Array<IDomainEntityRel>) => {
    // 计算yuml的字符串 节点部分
    const nodeText = data.reduce((pre, next) => {
      let result = '';

      // 显示的key
      let keyProperties = (next?.keyProperties || []).reduce(
        (preKey, nextKey) => (preKey ? `${preKey}🔑 ${nextKey};` : `🔑 ${nextKey};`),
        '',
      );
      // 区分当前节点 当前节点绿色显示
      const rootObj = getDomainObj(entityDtoData?.entityKey);
      const currentObj = getDomainObj(next?.entityKey);
      if (next?.entityKey === entityDtoData?.entityKey) {
        keyProperties = `${keyProperties}...{bg:green}`;
      } else if (rootObj === currentObj) {
        keyProperties = `${keyProperties}...{bg:#fff5e6}`;
      } else {
        keyProperties = `${keyProperties}...{bg:#ffffff}`;
      }
      // 父节点
      const parentNodeText = `${pre}
[${transferEntityName(next?.entityName || '')}|${keyProperties}]`;
      result = parentNodeText;
      // 子节点
      if (next?.subEntityList?.length) {
        const chidlrenNodeText = getNodeText(next?.subEntityList);
        result = `${result} ${chidlrenNodeText}`;
      }
      return result;
    }, '');
    return nodeText;
  };

  // 计算yuml的字符串 连接线部分
  const getLineText = (data: Array<IDomainEntityRel>, parent?: string) => {
    const lineText = data.reduce((pre, next) => {
      // 解析后台的对应关系
      let result = '';
      const replaceColon = (next?.relShip || '').replace(':', '-');
      // yuml-diagram的节点名称需要将单空格转换成双空格 =》 应对yum-diagram插件的bug
      const transferEntityNameString = transferEntityName(next?.entityName || '');
      if (parent) {
        // 当前项拼接
        if (next?.relType) {
          result =
            next?.relType === 'ONE'
              ? `${pre}
[${parent}]${replaceColon}<>[${transferEntityNameString}]`
              : `${pre}
[${parent}]${replaceColon}++[${transferEntityNameString}]`;
        } else {
          result = `${pre}`;
        }
        // 有child集 找child
        if (next?.subEntityList?.length) {
          const childLineText = getLineText(next?.subEntityList, transferEntityNameString);
          result = `${result} ${childLineText}`;
        }
      } else if (next?.subEntityList?.length) {
        // 无父集 直接找子集
        const childLineText = getLineText(next?.subEntityList, transferEntityNameString);
        result = `${pre} ${result} ${childLineText}`;
      }
      return result;
    }, '');
    return lineText;
  };

  // 计算yuml的字符串 service部分
  const getServiceLine = (data: Array<IServiceList>) => {
    let serviceText = '';
    const filterService = data.filter((i) => i.catalog !== 'Open Data Service');
    if (entityFlag) {
      ENTITYSERVICETYPE.forEach((serviceTypeItem) => {
        const singleTypeService = filterService.filter((i) => i.catalog === serviceTypeItem.type);
        let tempServiceT = '';
        (singleTypeService || []).forEach((serviceItem) => {
          tempServiceT = `${tempServiceT} ${serviceItem?.serviceName}();`;
        });
        // 该类型下有接口才展示
        if (tempServiceT) {
          serviceText = `${serviceText}
  [${serviceTypeItem.type}|${tempServiceT}{bg:${serviceTypeItem.color}}]-.->[${transferEntityName(
            entityDtoData?.entityName || '',
          )}]`;
        }
      });
    } else {
      SERVICETYPE.forEach((serviceTypeItem) => {
        const singleTypeService = filterService.filter((i) => i.catalog === serviceTypeItem.type);
        let tempServiceT = '';
        (singleTypeService || []).forEach((serviceItem) => {
          tempServiceT = `${tempServiceT} ${serviceItem?.serviceName}();`;
        });
        // 该类型下有接口才展示
        if (tempServiceT) {
          serviceText = `${serviceText}
  [${serviceTypeItem.type}|${tempServiceT}{bg:${serviceTypeItem.color}}]-.->[${transferEntityName(
            entityDtoData?.entityName || '',
          )}]`;
        }
      });
    }
    return serviceText;
  };
  // 计算yuml的字符串 event部分
  const getEventLine = (data: Array<IEventList>) => {
    let eventText = '';
    SERVICETYPE.forEach((eventTypeItem) => {
      if (eventTypeItem.type === 'Event') {
        let tempEventT = '';
        (data || []).forEach((eventItem) => {
          tempEventT = `${tempEventT} ${'🔖'} ${eventItem?.eventName};`;
        });
        // 该类型下有接口才展示
        if (tempEventT) {
          eventText = `${eventText}
[${eventTypeItem.type}|${tempEventT}{bg:${eventTypeItem.color}}]-.->[${transferEntityName(
            entityDtoData?.entityName || '',
          )}]`;
        }
      }
    });
    return eventText;
  };

  // 渲染yuml
  const drawYumlDiagram = (data: Array<IDomainEntityRel>) => {
    const textStart = '// {type:class}';
    // 计算yuml的字符串 节点部分
    const nodeText = getNodeText(data);
    // 计算yuml的字符串 连接线部分
    const lineText = getLineText(data);
    // 计算yuml的字符串 service部分
    const serviceText = getServiceLine(serviceListData || []);
    // 计算yuml的字符串 event部分
    const eventText = getEventLine(eventListData || []);
    // 完整的yuml字符串
    const completeText = `${textStart}${nodeText}

    ${lineText}

    ${serviceText}

    ${eventText}`;

    // 渲染yuml
    const yuml = new YumlDiagram();
    const yumlSvg = yuml.processYumlDocument(completeText, false);
    (document as any).getElementById('svgDiv').innerHTML = yumlSvg;
    // 无滚动条方案 计算宽、高
    const clientWidth = (document.querySelector('#svgDiv') as any)?.clientWidth || 0;
    const svgWidth = (document.querySelector('#svgDiv svg') as any).clientWidth || 0;
    const svgHeight = (document.querySelector('#svgDiv svg') as any).clientHeight || 0;
    const rate = clientWidth / svgWidth > 1 ? 0.7 : clientWidth / svgWidth; // 比例过大默认0.7
    (document.querySelector('#svgDiv svg') as any).style.width = `${svgWidth * rate - 1}px`;
    (document.querySelector('#svgDiv svg') as any).style.height = `${svgHeight * rate}px`;
    // 渲染真实的svg
    const yumlSvgNode = document.querySelector('#svgDiv svg')?.outerHTML;
    setYumlSvgCode(yumlSvgNode);
    (document as any).getElementById('svgDiv').innerHTML = '';

    // draw legend
    drawLegend(yuml, rate);
  };

  // 渲染legend
  const drawLegend = (yuml: any, rate: number) => {
    if (entityFlag) {
      const legendText = ENTITYLEGENTOTHERTYPE.concat(ENTITYSERVICETYPE).reduce(
        (pre, next) =>
          `${pre}
      [${next.type}{bg: ${next.color}}]
      `,
        '// {type:class}',
      );
      const legendSvg = yuml.processYumlDocument(legendText, false);
      // 按比例设置图例的宽度
      (document as any).getElementById('legendDiv').innerHTML = legendSvg;
      const svgWidth = (document.querySelector('#legendDiv svg') as any).clientWidth || 0;
      const rateWidth = svgWidth * rate;
      const svgheight = (document.querySelector('#legendDiv svg') as any).clientHeight || 0;
      const rateHeight = svgheight * rate;
      (document as any).getElementById('legendDiv').innerHTML = legendSvg;
      (document.querySelector('#legendDiv svg') as any).style.width = `${rateWidth}px`;
      (document.querySelector('#legendDiv svg') as any).style.height = `${rateHeight}px`;
    } else {
      const legendText = LEGENTOTHERTYPE.concat(SERVICETYPE).reduce(
        (pre, next) =>
          `${pre}
      [${next.type}{bg: ${next.color}}]
      `,
        '// {type:class}',
      );
      const legendSvg = yuml.processYumlDocument(legendText, false);
      // 按比例设置图例的宽度
      (document as any).getElementById('legendDiv').innerHTML = legendSvg;
      const svgWidth = (document.querySelector('#legendDiv svg') as any).clientWidth || 0;
      const rateWidth = svgWidth * rate;
      const svgheight = (document.querySelector('#legendDiv svg') as any).clientHeight || 0;
      const rateHeight = svgheight * rate;
      (document as any).getElementById('legendDiv').innerHTML = legendSvg;
      (document.querySelector('#legendDiv svg') as any).style.width = `${rateWidth}px`;
      (document.querySelector('#legendDiv svg') as any).style.height = `${rateHeight}px`;
    }
  };

  // 图形化yuml
  useEffect(() => {
    if (entityDtoData?.entityKey && domainEntityRelData?.entityKey) {
      drawYumlDiagram(domainEntityRelData?.entityKey ? [domainEntityRelData] : []);
    } else {
      (document as any).getElementById('svgDiv').innerHTML = '';
      (document as any).getElementById('legendDiv').innerHTML = '';
      setYumlSvgCode('');
    }
  }, [domainEntityRelData, entityDtoData, serviceListData, eventListData]);

  return (
    <div>
      <div id="svgDiv" />
      <div className={styles.yumlContent} id="svgDiv2">
        {/* {yumlSvgCode ? <RenderSvgByInnerHTML key="renderKey" innerHTML={yumlSvgCode} /> : null} */}
        {yumlSvgCode ? <DraggableSVGByInnerHTML key="renderKey" innerHTML={yumlSvgCode} /> : null}
      </div>
      <div className={styles.legendContent}>
        <div className={styles.legendTitle}>{formatMessage('DOMAINDETAIL.RELA.LEGEND')}</div>
        <div className={styles.legendContainer} id="legendDiv" />
      </div>
    </div>
  );
};

export default EntityReleationShip;
