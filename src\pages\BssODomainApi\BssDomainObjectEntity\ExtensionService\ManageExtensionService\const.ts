// 适用于ManageExtensionService目录下的所有常量

export const RequiredSource: any = [
  { name: 'Yes', key: 'Y' },
  { name: 'No', key: 'N' },
];

export const CommonYesOrNoSource: any = [
  { label: 'Yes', value: 'Y' },
  { label: 'No', value: 'N' },
];

export const requestBodyFormat = [
  {
    label: 'json',
    value: 'json',
  },
  {
    label: 'form-data',
    value: 'form-data',
  },
];

export const ParamTypeSource = ['String', 'Boolean', 'Long', 'Number', 'Datetime', 'Integer', 'Object'];

export const ParamTypeSourceWithoutObject = ['String', 'Boolean', 'Long', 'Number', 'Datetime', 'Integer'];

export const CreationSource = [
  {
    name: 'Product-Owned',
    key: 'P',
  },
  {
    name: 'Project-Customized',
    key: 'C',
  },
];

export const ResponseDataSource = [
  {
    label: 'Single Data',
    value: 'singleData',
  },
  {
    label: 'Array Data',
    value: 'arrayData',
  },
  {
    label: 'No Data',
    value: 'noData',
  },
];

export const ContentTypeSource = [
  {
    label: 'json',
    value: 'json',
  },
  {
    label: 'binary',
    value: 'binary',
  },
];

export const MatchStatus = [
  {
    name: 'Success',
    key: 'S',
  },
  {
    name: 'Exception',
    key: 'E',
  },
];

// response header国际化相关
export const RespHeaderOptions = {
  option1:{Header:''},
  option2:{Header:'Header'},
  option3:{Header:'header'},
}