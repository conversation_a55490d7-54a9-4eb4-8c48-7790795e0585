declare module 'react-resizable' {
  import * as React from 'react';

  export interface ResizableBoxProps {
    width: number;
    height: number;
    handle?: React.ReactNode;
    minConstraints?: [number, number];
    maxConstraints?: [number, number];
    onResizeStop?: (e: React.SyntheticEvent, data: { size: { width: number, height: number } }) => void;
    onResizeStart?: (e: React.SyntheticEvent, data: { size: { width: number, height: number } }) => void;
    onResize?: (e: React.SyntheticEvent, data: { size: { width: number, height: number } }) => void;
    draggableOpts?: object;
    resizeHandles?: Array<'s' | 'w' | 'e' | 'n' | 'sw' | 'nw' | 'se' | 'ne'>;
    transformScale?: number;
    children?: React.ReactNode;
  }

  export class ResizableBox extends React.Component<ResizableBoxProps, {}> {}

  export interface ResizableProps {
    width: number;
    height: number;
    handle?: React.ReactNode;
    minConstraints?: [number, number];
    maxConstraints?: [number, number];
    onResizeStop?: (e: React.SyntheticEvent, data: { size: { width: number, height: number } }) => void;
    onResizeStart?: (e: React.SyntheticEvent, data: { size: { width: number, height: number } }) => void;
    onResize?: (e: React.SyntheticEvent, data: { size: { width: number, height: number } }) => void;
    draggableOpts?: object;
    resizeHandles?: Array<'s' | 'w' | 'e' | 'n' | 'sw' | 'nw' | 'se' | 'ne'>;
    transformScale?: number;
    children?: React.ReactNode;
  }

  export class Resizable extends React.Component<ResizableProps, {}> {}
}
declare module 'react-highlight-words' {
  import * as React from 'react';

  interface HighlightWordsProps {
    highlightClassName?: string;
    searchWords: string[];
    textToHighlight: string;
    autoEscape?: boolean;
    caseSensitive?: boolean;
    highlightStyle?: React.CSSProperties; // 添加这个属性
  }

  export default class HighlightWords extends React.Component<HighlightWordsProps> {}
}
