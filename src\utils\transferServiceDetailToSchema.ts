// 定义 SchemaType 接口，表示生成的 schema 的结构
interface SchemaType {
  type: string; // 数据类型
  title: string; // 标题
  properties: Record<string, SchemaType>; // 属性，键为属性名，值为 SchemaType
}

// 根据 reqIn 的值返回请求参数类型
const getReqParamType = (reqIn: string): string => {
  switch (reqIn) {
    case 'Q':
      return 'query'; // 查询参数
    case 'B':
      return 'body'; // 请求体
    case 'H':
      return 'header'; // 请求头
    case 'C':
      return 'cookie'; // Cookie
    default:
      return '';
  }
};

// 生成 schema 树的函数
const generateSchemaTree = (paramItem: any): SchemaType => {
  // 初始化 schema 对象
  const schema: SchemaType = {
    type: paramItem?.dataType || '', // 设置数据类型，如果没有则为空
    title: '',
    properties: {},
  };

  // 获取请求参数类型
  const reqParamType = paramItem?.reqIn ? getReqParamType(paramItem.reqIn) : '';
  schema.title = reqParamType || paramItem?.name; // 设置标题，优先使用请求参数类型

  const properties = paramItem?.dataModel?.properties || [];

  // 遍历属性，递归生成子 schema
  properties?.forEach((item: any) => {
    schema.properties[item.name] = generateSchemaTree(item); // 递归调用以生成子属性的 schema
  });

  return schema;
};

// 将服务详细信息转换为 schema 的函数
const transferServiceDetailToSchema = (paramList: any[] = [], returnType: string): SchemaType => {
  // 初始化 schema 对象
  const schema: SchemaType = {
    type: 'Object', // 设置类型为对象
    title: returnType === 'request' ? 'request' : 'response', // 根据返回类型设置标题
    properties: {},
  };

  // 遍历参数列表，生成对应的 schema
  paramList?.forEach((item: any) => {
    const reqParamType = item?.reqIn ? getReqParamType(item.reqIn) : ''; // 获取请求参数类型
    const propertyKey = reqParamType || item.name;
    if(returnType === 'request' ) {
      // 如果属性不存在，则初始化为一个对象
      if (!schema.properties[propertyKey]) {
        schema.properties[propertyKey] = {
          type: 'Object', // 设置数据类型
          title: reqParamType,
          properties: {},
        };
      }
      if(item?.reqIn && item?.reqIn !== 'B') {
      // 将 item.name 属性添加到 properties 中
      schema.properties[propertyKey].properties[item.name] = {
        type: item?.dataType || '', // 设置数据类型
        title: item.name,
        properties: {},
      };
      }else {
        schema.properties[propertyKey].properties = generateSchemaTree(item)?.properties;
      }
    }else {
      schema.properties= generateSchemaTree(item)?.properties;
    }
  });

  return schema;
};

// 生成状态码Schema
const generateStatusSchema = (service:any, matchStatus:string) => {
  if (!service?.implType) return null;

  // 根据 matchStatus 的值来决定是否生成 statusSchema
  if (matchStatus === 'E') {
    switch (service.implType) {
      case 'R':
        return {
          type: 'Number',
          title: 'status',
          properties: {},
        };
      case 'D':
        return {
          type: 'String',
          title: 'exceptionClass',
          properties: {},
        };
      default:
        return null;
    }
  } else {
    // 如果 matchStatus 不是 'E'，可以根据需要返回其他的 schema
    if (service.implType === 'R') {
      return {
        type: 'Number',
        title: 'status',
        properties: {},
      };
    }
    return null;
  }
};

// 生成响应Body Schema
const generateRespSchema = (matchStatus: string, serviceDetail: any, respParam: any) => {
  if (matchStatus === 'E') {
    const propObj = respParam?.children?.reduce((acc: any, item: any) => {
      acc[item?.name] = {
        type: item?.paramType,
        title: item?.name,
        properties: {},
      };
      return acc;
    }, {});
    return {
      type: 'Object',
      title: 'response',
      properties: propObj,
    };
  } else {
    const successRespSchema = serviceDetail?.responses?.find(
      (item: any) => Number(item?.respCode) >= 200 && Number(item?.respCode) < 300,
    );
    return transferServiceDetailToSchema(successRespSchema ? [successRespSchema] : [], 'response');
  }
};

// 生成响应Header Schema
const generateRespHeaderSchema = (serviceDetail: any, selectStatus?: string) => {
  // 初始化 schema 对象
  const schema: SchemaType = {
    type: 'Object', // 设置类型为对象
    title: 'responseHeader',
    properties: {},
  };
  // 若传了selectStatus，则过滤出对应状态码的响应Header;不传selectStatus，则展示所有响应Header，若有重名Header，后面的覆盖前面的
  const paramList = serviceDetail?.responses?.filter(
    (item: any) => item?.respIn === 'H' && (selectStatus ? item?.respCode === selectStatus : true),
  );
  // 遍历参数列表，生成对应的 schema
  paramList?.forEach((item: any) => {
    // 将 item.name 属性添加到 properties 中
    schema.properties[item.name] = {
      type: item?.dataType || '', // 设置数据类型
      title: item.name,
      properties: {},
    };
  });

  return schema;
};

export { transferServiceDetailToSchema, generateStatusSchema, generateRespSchema, generateRespHeaderSchema };
