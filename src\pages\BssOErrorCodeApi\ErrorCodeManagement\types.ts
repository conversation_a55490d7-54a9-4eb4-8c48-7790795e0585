export interface FormValuesType {
  creationSrc?: string;
  domainId?: number;
  errorCode?: string;
  errorReason?: string;
}

export interface ErrorCodeListItemType {
  creationSrc: string;
  domainId: number;
  errorCode: string;
  errorReason: string;
  domainName: string;
  createdDate: string;
  updateDate: string;
  errorCodeId: number;
  useErrorCodeDef: string;
  defaultMatchConditions: string;
  defaultReasonParams: string[];
}

export interface ErrorCodeListDataType {
  list: ErrorCodeListItemType[];
  pageNo: number;
  pageSize: number;
  totalCount: number;
  totalPage: number;
}

export interface newErrorCodeType {
  domainId?: number;
  errorCode: string;
  errorReason: string;
  defaultMatchConditions?: string;
  defaultReasonParams?: string[];
}

export interface GetErrorCodeListType {
  creationSrc?: string;
  domainId?: number | string;
  errorCode?: string;
  errorReason?: string;
  pageNo: number;
  pageSize: number;
  isRelated?: string;
}

export interface ErrorCodeServiceListItemType {
  domainId: number;
  domainName: string;
  domainObjId: number;
  domainObjName: string;
  entityId: number;
  entityName: string;
  respCode: string;
  serviceId: number;
  serviceName: string;
}

export interface ErrorCodeServiceListDataType {
  list: ErrorCodeServiceListItemType[];
  pageNo: number;
  pageSize: number;
  totalCount: number;
  totalPage: number;
}
