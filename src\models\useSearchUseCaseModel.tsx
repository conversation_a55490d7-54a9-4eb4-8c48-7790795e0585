import { useState, useEffect } from 'react';
import {
  getUseCasePageListService,
  getUseCasePageListServicePropsType,
  queryServiceDetailByCode,
  deleteUseCaseService,
} from '@/services';
import { UseCaseBeQueriedListPropsType } from '@/services/typing';

export default function useSearchUseCaseModel() {
  const [queryParams, setQueryParams] = useState<getUseCasePageListServicePropsType>({
    startDate: '',
    endDate: '',
    useCaseName: '',
  });

  // 当前的menuList
  const [useCaseMenuListInModel, setUseCaseMenuListInModel] = useState<UseCaseBeQueriedListPropsType[] | []>([]);
  const [menuItemInModel, setMenuItemInModel] = useState<any>({});

  // Use Case List 获取函数
  const getUseCasePageListFn = async (extensionData?: { isEditUseCase: boolean; currentUseCaseId: number }) => {
    await getUseCasePageListService(queryParams).then((res) => {
      if (res?.success) {
        const { data = [] } = res;
        setUseCaseMenuListInModel(data);
        // 如果是编辑UseCase,编辑成功更新数据
        if (extensionData?.isEditUseCase && extensionData?.currentUseCaseId) {
          const matchedValue = data?.find((item: any) => item?.useCaseId === extensionData?.currentUseCaseId);
          setServiceCodeInModel(matchedValue?.serviceCode);
          setUseCaseIdInModel(matchedValue?.useCaseId);
          setMenuItemInModel(matchedValue);
        }
      } else {
        setUseCaseMenuListInModel([]);
      }
    });
  };

  useEffect(() => {
    if (queryParams?.startDate && queryParams?.endDate) {
      getUseCasePageListFn();
    }
    return () => {};
  }, [queryParams]);

  // 获取 serviceDetail，用于展示debug界面
  const [serviceCodeInModel, setServiceCodeInModel] = useState<string>('');
  const [serviceDetailInModel, setServiceDetailInModel] = useState<any>();
  const [useCaseIdInModel, setUseCaseIdInModel] = useState<any>('');

  // 查询服务详情
  const queryServiceDetail = async (serviceCode: string) => {
    if (!serviceCode) return;
    try {
      const { data } = await queryServiceDetailByCode({
        serviceCode,
      });
      if (data?.serviceCode) {
        setServiceDetailInModel(data);
      }
    } catch (error) {}
  };

  useEffect(() => {
    if (serviceCodeInModel && useCaseIdInModel) {
      queryServiceDetail(serviceCodeInModel);
    } else {
      setServiceDetailInModel([]);
    }
  }, [serviceCodeInModel, useCaseIdInModel]);

  // 保存当前 展开的菜单项 和 被选择的菜单项
  const [openKeysInModel, setOpenKeysInModel] = useState<string[]>([]);
  const [selectedKeysInModel, setSelectedKeysInModel] = useState<string[]>([]);

  // Use Case List 删除函数
  const deleteUseCasePageList = async (useCaseId: string) => {
    try {
      const { success } = await deleteUseCaseService(useCaseId);
      if (success) {
        const filterData = useCaseMenuListInModel?.filter((el) => String(el.useCaseId) !== String(useCaseId));
        setUseCaseMenuListInModel(filterData);
      } else {
        setUseCaseMenuListInModel([]);
      }
    } catch (error) {}
  };

  // 重置所有数据
  const resetAllData = () => {
    setServiceCodeInModel('');
    setUseCaseIdInModel('');
    setServiceDetailInModel({});
    setMenuItemInModel({});
    setOpenKeysInModel([]);
    setSelectedKeysInModel([]);
  };

  return {
    useCaseMenuListInModel,
    serviceDetailInModel,
    serviceCodeInModel,
    useCaseIdInModel,
    menuItemInModel,
    setQueryParams,
    setServiceCodeInModel,
    setUseCaseIdInModel,
    setMenuItemInModel,
    resetAllData,
    deleteUseCasePageList,
    getUseCasePageListFn,

    openKeysInModel,
    setOpenKeysInModel,
    selectedKeysInModel,
    setSelectedKeysInModel,
  };
}
