import React, { useEffect, useState } from 'react';
import { IEntityDto, IDomainEntityRel } from '@/services/typing.d';
import classNames from 'classnames';
import useI18n from '@/hooks/useI8n';
import styles from './index.less';

interface IEntityDescription {
  entityDtoData?: IEntityDto;
  domainEntityRelData?: IDomainEntityRel;
  onOtherEntityClick?: (entityKey: string) => void;
}

const EntityDescription: React.FC<IEntityDescription> = (props) => {
  const { entityDtoData, domainEntityRelData, onOtherEntityClick } = props;
  const { formatMessage } = useI18n();

  const [parent, setParent] = useState<IDomainEntityRel | undefined>(); // 父级
  const [childrenList, setChildrenList] = useState<Array<IDomainEntityRel>>([]); // 一级children的集合

  // 查找当前的parent
  const findCurrentEntityParent: (
    data: Array<IDomainEntityRel>,
    curEntityKey: string,
    parent?: IDomainEntityRel,
  ) => IDomainEntityRel | undefined = (data, curEntityKey, parent) => {
    for (let i = 0; i < data?.length; i++) {
      if (data[i]?.entityKey === curEntityKey) {
        return parent ? parent : data[i];
      } else {
        const findParent = findCurrentEntityParent(data[i]?.subEntityList || [], curEntityKey, data[i]);
        if (findParent?.entityKey) {
          return findParent;
        }
      }
    }
  };

  // 查找当前entity的一级的child
  const findCurrentEntityChildrenList: (
    data: Array<IDomainEntityRel>,
    curEntityKey: string,
  ) => Array<IDomainEntityRel> = (data, curEntityKey) => {
    for (let i = 0; i < data?.length; i++) {
      if (data[i]?.entityKey === curEntityKey) {
        return data[i].subEntityList || [];
      } else {
        const findChild = findCurrentEntityChildrenList(data[i]?.subEntityList || [], curEntityKey);
        if (findChild?.length) {
          return findChild;
        }
      }
    }
    return [];
  };

  useEffect(() => {
    if (entityDtoData?.entityKey) {
      // 查找当前的parent
      const findParent = findCurrentEntityParent(
        domainEntityRelData ? [domainEntityRelData] : [],
        entityDtoData?.entityKey || '',
      );
      setParent(findParent);
      // 查找当前entity的一级的child
      const findChildren = findCurrentEntityChildrenList(
        domainEntityRelData ? [domainEntityRelData] : [],
        entityDtoData?.entityKey || '',
      );
      setChildrenList(findChildren);
    } else {
      setParent(undefined);
      setChildrenList([]);
    }
  }, [entityDtoData]);

  return (
    <div className={styles.entityDesContent}>
      <div className={styles.entityDesList}>
        {/* entity name */}
        <div className={styles.entityDesItem}>
          <div className={styles.title}>{formatMessage('DOMAINDETAIL.DESC.ENTITYNAME')}</div>
          <div className={styles.content}>{entityDtoData?.entityName}</div>
        </div>
        {entityDtoData?.domainObjectType === 'A' && (
          <>
            {/* parent entity */}
            <div className={styles.entityDesItem}>
              <div className={styles.title}>{formatMessage('DOMAINDETAIL.DESC.PARENTENTITY')}</div>
              <div className={styles.content}>
                {parent?.entityKey === entityDtoData?.entityKey ? (
                  'N/A'
                ) : (
                  <span
                    className={styles.singleItemEntity}
                    onClick={() => onOtherEntityClick?.(parent?.entityKey || '')}
                  >
                    {parent?.entityName}
                  </span>
                )}
              </div>
            </div>
            {/* Subordinate Entity */}
            <div className={styles.entityDesItem}>
              <div className={styles.title}>{formatMessage('DOMAINDETAIL.DESC.CHILDENTITY')}</div>
              <div className={classNames(styles.content, styles.linkContent)}>
                {(childrenList || []).map((i) => (
                  <span
                    key={i?.entityKey}
                    className={styles.singleItemEntity}
                    onClick={() => onOtherEntityClick?.(i?.entityKey || '')}
                  >
                    {i?.entityName},
                  </span>
                ))}
              </div>
            </div>
          </>
        )}
        {/* Description */}
        <div className={styles.entityDesItem}>
          <div className={styles.title}>{formatMessage('PROJECT.COMMON.DESC')}</div>
          <div className={styles.content} title={entityDtoData?.comments || ''}>
            {entityDtoData?.comments}
          </div>
        </div>
      </div>
    </div>
  );
};

export default EntityDescription;
