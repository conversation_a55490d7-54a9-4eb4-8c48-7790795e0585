import React, { useEffect, useMemo, useState } from 'react';
import { Popconfirm, Spin, Tooltip, message } from 'antd';
import type { PopconfirmProps } from 'antd';
import { FormOutlined, DeleteOutlined } from '@ant-design/icons';
import { qryAllEvents, qryEntityEventList, deleteEntityEventById } from '@/services/entityService';
import classNames from 'classnames';
import styles from '../index.less';
import AddEntityEventDrawer from './AddEntityEvent';
import EditEntityEventDrawer from './EditEntityEvent';
import OperationIconTitle from '@/components/OperationIconTitle';
import ResizableTable from '@/components/ResizableTable';
import { useTableFilter } from '@/hooks/useTableFilter';
import useI18n from '@/hooks/useI8n';

interface IEntityEvent {
  entityId?: string; // 当前节点
  selectedRootNode?: any; // 根节点
  isProduct?: boolean;
  ignoreVersionSource: any;
}

const EntityEvent: React.FC<IEntityEvent> = (props) => {
  const { formatMessage } = useI18n();
  const { ignoreVersionSource, entityId, selectedRootNode, isProduct } = props;

  const [spining, setSpining] = useState<boolean>(false); // menu loading

  const [entityEventsSource, setEentityEventsSource] = React.useState<any>([]); // 领域实体事件数据源
  const [selectedEvent, setSelectedEvent] = React.useState<any>({}); // 当前选中的实体事件行数据
  const [rlcEventCatgSource, setRlcEventCatgSource] = React.useState<any>([]);
  const [openAddEntityEventDrawer, setOpenAddEntityEventDrawer] = useState<boolean>(false); // 新增实体事件
  const [openEditEntityEventDrawer, setOpenEditEntityEventDrawer] = useState<boolean>(false); // 修改实体事件

  const CreationSource = [
    { name: 'Product-Owned', key: 'P' },
    { name: 'Project-Customized', key: 'C' },
  ];
  // 使用 useMemo 确保 dataSource 是稳定的引用
  const memoizedDataSource = useMemo(() => {
    if (!Array.isArray(entityEventsSource) || entityEventsSource.length === 0) {
      return [];
    }
    // 暂时隐藏 eventCode 没有映射关系的 entity event 数据，解决印尼环境问题
    return entityEventsSource.filter((eventItem: any) => {
      return rlcEventCatgSource.some((item: any) => {
        return item.events?.some((i: any) => {
          return i.eventCode === eventItem.eventCode;
        });
      });
    });
  }, [entityEventsSource, rlcEventCatgSource]);

  // 自定义hooks，支持表格列筛选并分页展示
  const { filteredDataSource, getColumnSearchProps, getColumnEnumSearchProps, clearAllFilters } =
    useTableFilter(memoizedDataSource);

  // 自定义过滤表格列
  const eventCodeFilter = (item: any, obj: any) => {
    if (rlcEventCatgSource?.length > 0) {
      let eventCode = '';
      rlcEventCatgSource.forEach((rlcEventItem: any) => {
        rlcEventItem.events
          ?.filter((i: any) => i.eventCode === item.eventCode)
          .forEach((j: any) => {
            eventCode = j.eventName;
          });
      });
      return (eventCode || '')
        .toString()
        .toLowerCase()
        .includes((Object.values(obj)[1] as string).toLowerCase());
    }
    return true; // 默认返回 true
  };

  const deleteEntityEvent: PopconfirmProps['onConfirm'] = async (e) => {
    const param: any = {
      eventId: selectedEvent?.eventId,
      entityId,
    };

    const resultData = await deleteEntityEventById(param);
    if (resultData?.success) {
      message.success(formatMessage('ENTITYEVENT.DELETE.SUCCESS'));
      // 删除领域对象后重新渲染数据
      queryEntityEvents();
    }
  };

  const onOpenRlcEventView = (record: any) => {
    if ((window?.parent as any)?.fish) {
      (window.parent as any).fish?.popupView({
        url: 'rlc/modules/rlc-event-mgr/views/RclRuleListView',
        width: '100%',
        height: document.body.clientHeight + 50,
        position: {
          my: 'top',
          at: 'center',
          of: window.parent,
          collision: 'fit',
        },
        viewOption: {
          eventCode: record.eventCode,
          confApp: 'ODH', // 配置的APP Code
          filterRule: 'Y', // 是否按confApp来过滤展示的规则
          extAction: 'Y', // 过滤ACTION_TYPE_ID或TOPIC_ID小于0的记录
        },
      });
    }
  };
  // entity event list表格
  const columns = [
    {
      dataIndex: 'eventName',
      title: formatMessage('ENTITYEVENT.EVENT_NAME'),
      width: '20%',
      ellipsis: true,
      ...getColumnSearchProps('eventName'),
      render: (_: string, record: any) => (
        <div className={styles.domainObjName} onClick={() => onOpenRlcEventView(record)}>
          {record?.eventName}
        </div>
      ),
    },
    {
      dataIndex: 'eventCode',
      title: formatMessage('ENTITYEVENT.EVENT_CODE'),
      width: '20%',
      ellipsis: true,
      ...getColumnSearchProps('eventCode', eventCodeFilter),
      render: (_: string, record: any) => {
        let eventCode = '';
        rlcEventCatgSource.forEach((item: any) => {
          item.events
            ?.filter((i: any) => i.eventCode === record.eventCode)
            .forEach((i: any) => {
              eventCode = i.eventName;
            });
        });
        return eventCode;
      },
    },
    {
      dataIndex: 'creationSrc',
      title: formatMessage('ENTITYEVENT.CREATION_SOURCE'),
      width: '20%',
      ellipsis: true,
      render: (_: string, record: any) => {
        return CreationSource.filter((i) => i.key === record.creationSrc).map((i) => i.name);
      },
      ...getColumnEnumSearchProps('creationSrc', CreationSource),
    },
    {
      dataIndex: 'comments',
      title: formatMessage('ENTITYEVENT.DESCRIPTION'),
      width: '30%',
      ellipsis: true,
      ...getColumnSearchProps('comments'),
    },
    {
      dataIndex: '',
      title: formatMessage('ENTITYEVENT.OPERATION'),
      width: '10%',
      render: (_: string, record: any) => {
        return (
          <div>
            <Tooltip title={formatMessage('PROJECT.COMMON.EDIT')}>
              <FormOutlined
                className={classNames(styles.iconStyle, {
                  [styles.hide]: selectedRootNode?.state !== 'D',
                })}
                onClick={() => {
                  setOpenEditEntityEventDrawer(true);
                }}
              />
            </Tooltip>

            <Popconfirm
              title={formatMessage('ENTITYEVENT.DELETE.TITLE')}
              description={formatMessage('ENTITYEVENT.DELETE.DESCRIPTION')}
              onConfirm={deleteEntityEvent}
              okText={formatMessage('ENTITYEVENT.DELETE.YES')}
              cancelText={formatMessage('ENTITYEVENT.DELETE.NO')}
            >
              {isProduct ? (
                <Tooltip title={formatMessage('PROJECT.COMMON.DELETE')}>
                  <DeleteOutlined
                    className={classNames(styles.versionIcon2, {
                      [styles.hide]: selectedRootNode?.state !== 'D',
                    })}
                    onClick={() => {}}
                  />
                </Tooltip>
              ) : (
                <Tooltip title={formatMessage('PROJECT.COMMON.DELETE')}>
                  <DeleteOutlined
                    className={classNames(styles.versionIcon2, {
                      [styles.hide]: record?.creationSrc === 'P' || selectedRootNode?.state !== 'D',
                    })}
                    onClick={() => {}}
                  />
                </Tooltip>
              )}
            </Popconfirm>
          </div>
        );
      },
    },
  ];

  const queryEntityEvents = async () => {
    setSpining(true);
    try {
      const { success, data } = await qryEntityEventList(Number(entityId));
      if (success) {
        setEentityEventsSource(data);
      } else {
        setEentityEventsSource([]);
      }
      setSpining(false);
    } catch (error) {
      setSpining(false);
    }
  };

  const queryEventCatgList = async () => {
    const { success, data } = await qryAllEvents();
    if (success) {
      setRlcEventCatgSource(data);
    } else {
      setRlcEventCatgSource([]);
    }
  };

  useEffect(() => {
    // 查询RLC Event Catalog和RLC Event Name下拉列表信息
    queryEventCatgList();
  }, []);

  useEffect(() => {
    // 查询实体事件详情
    queryEntityEvents();
    // 重置表格列的筛选条件
    clearAllFilters();
  }, [entityId]);

  return (
    <Spin spinning={spining}>
      {/* 领域实体属性列表 */}
      <div>
        <div className={styles.title}>
          <OperationIconTitle
            title={formatMessage('ENTITYEVENT.TITLE')}
            type={selectedRootNode?.state === 'D' ? 'add' : ''}
            opt={formatMessage('PROJECT.COMMON.ADD')}
            handleClick={() => {
              setOpenAddEntityEventDrawer(true);
            }}
          />
        </div>
        <div className={styles.content}>
          <ResizableTable
            size="small"
            style={{ marginTop: '12px' }}
            columns={columns}
            dataSource={filteredDataSource || []}
            rowKey={(record: any) => record?.eventId}
            pagination={{
              hideOnSinglePage: true,
              pageSize: 10,
              showTotal: (total) => formatMessage('PROJECT.COMMON.PAGINATION.TOTAL', { total }),
            }}
            onRow={(record: any) => ({
              onClick: () => {
                setSelectedEvent(record);
              },
            })}
          />
        </div>
      </div>

      {/* 新增实体事件 */}
      <AddEntityEventDrawer
        ignoreVersionSource={ignoreVersionSource}
        open={openAddEntityEventDrawer}
        entityId={Number(entityId)}
        onCancel={() => setOpenAddEntityEventDrawer(false)}
        rlcEventCatgSource={rlcEventCatgSource}
        isProduct={isProduct as boolean}
        onOk={() => {
          // 新增实体事件成功后，刷新当前表格
          setOpenAddEntityEventDrawer(false);
          queryEntityEvents();
        }}
      />

      {/* 修改实体事件 */}
      <EditEntityEventDrawer
        ignoreVersionSource={ignoreVersionSource}
        initValue={selectedEvent}
        open={openEditEntityEventDrawer}
        onCancel={() => setOpenEditEntityEventDrawer(false)}
        rlcEventCatgSource={rlcEventCatgSource}
        entityId={Number(entityId)}
        isProduct={isProduct as boolean}
        onOk={() => {
          // 修改实体事件成功后，刷新当前表格
          setOpenEditEntityEventDrawer(false);
          queryEntityEvents();
        }}
      />
    </Spin>
  );
};

export default EntityEvent;
