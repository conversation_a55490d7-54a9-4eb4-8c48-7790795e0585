import React, { useEffect, useState } from 'react';
import { Modal, Upload, message, Button, Spin, Flex } from 'antd';
import { useIntl, useModel } from 'umi';
import type { RcFile, UploadFile } from 'antd/es/upload/interface';
import { InboxOutlined } from '@ant-design/icons';
import { addFromFile } from '@/services/entityService';

interface AddBySwaggerModalParams {
  open: boolean;
  setVisible: any;
  selectedService: any;
  updateDomainSchemaList: () => void;
}

const { Dragger } = Upload;
const AddBySwaggerModal: React.FC<AddBySwaggerModalParams> = (props) => {
  const { open, setVisible, selectedService, updateDomainSchemaList } = props;
  const { setIsImportBySwaggerSuccess } = useModel('manageExtensionServiceModel');
  const [jsonFileList, setJsonFileList] = useState<UploadFile[]>([]);
  const [spining, setSpining] = useState<boolean>(false);

  const uploadChange = (newFileList: UploadFile[]) => {
    setJsonFileList(newFileList);
  };

  const onSubmit = async () => {
    setSpining(true);
    try {
      const formData = new FormData();
      if (jsonFileList?.length > 0) {
        formData.append('swaggerFile', jsonFileList[0] as RcFile);
        formData.append('serviceId', selectedService?.serviceId);
      } else {
        message.warning('Please select a file to upload.');
        return;
      }

      const { success } = await addFromFile(formData);

      if (success) {
        message.success('Import Success!');
        updateDomainSchemaList();
        setIsImportBySwaggerSuccess(true);
        setVisible(false);
      } else {
        message.error('Import failed.');
      }
    } catch (error) {
      console.error('An error occurred:', error);
    } finally {
      setSpining(false);
    }
  };

  useEffect(() => {
    uploadChange([]);
  }, [open]);
  return (
    <Modal
      title="Add By Swagger"
      open={open}
      // width={600}
      maskClosable={false}
      onCancel={() => setVisible(false)}
      footer={
        <Flex gap="1rem" justify="end">
          <Button onClick={() => setVisible(false)}>Cancel</Button>
          <Spin spinning={spining}>
            <Button onClick={onSubmit} type="primary">
              Confirm
            </Button>
          </Spin>
        </Flex>
      }
    >
      <Dragger
        accept=".json"
        className="dragger"
        maxCount={1}
        onRemove={(file) => {
          const index = jsonFileList.indexOf(file);
          const newFileList = jsonFileList.slice();
          newFileList.splice(index, 1);
          uploadChange(newFileList);
        }}
        beforeUpload={(file) => {
          uploadChange([file]);
          return false;
        }}
        fileList={jsonFileList}
      >
        <p className="ant-upload-drag-icon">
          <InboxOutlined />
        </p>
        <p className="ant-upload-text">Click or drag Swagger file to this area to upload</p>
      </Dragger>
    </Modal>
  );
};

export default AddBySwaggerModal;
