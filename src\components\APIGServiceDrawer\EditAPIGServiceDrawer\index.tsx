{
  /**该部分需求说明：
  新增APIG服务时：
  1.debug页面如果参数（目前仅需支持query参数）有值，打开该弹框，会自动生成OData参数。
  2.无法直接增、删APIG参数，只能通过解析Access Relative URL和OData参数得到APIG参数。
  3.解析规则：Access Relative URL中${}里的内容，OData参数value中$()里的内容。
  修改APIG服务时：
  1.OData参数和APIG参数首先从接口中获取，如果更改Access Relative URL或者OData参数，则重新解析。
*/
}
import useI18n from '@/hooks/useI8n';
import { Form, Input, Drawer, Space, Button, Spin, Flex, Select, TreeSelect } from 'antd';
import styles from './index.less';
import React, { useCallback, useEffect, useImperativeHandle, useState } from 'react';
import { qryApiBaseInfoByApiId, saveAsAPIGPropsType } from '@/services';
import ODataParamsTable from '../ODataParamsTable';
import APIGParamsTable from '../APIGParamsTable';
import getUUid from '@/utils/getUUid';
import debounce from 'lodash/debounce';
import { APIGServiceCatalogItemType } from '@/services/typing';
import { ODH_SUFFIX } from '@/pages/BssODataApi/APIGService/CustomerCollpaseForAPIGService/const';

interface caseDrawerPropsType {
  title: string;
  open: boolean;
  loading: boolean;
  dataToBeSent: { [k: string]: any };
  serviceDetail: any;
  APIGServiceCatalogList: APIGServiceCatalogItemType[];
  APIGServiceDetail: any;
  setOpen: (e: any) => void;
  onAPIGServiceDrawerSend: (e: saveAsAPIGPropsType) => void;
}

interface IDataItem {
  key: string;
  name: string;
  value: string;
  isEdit: boolean;
  reqIn: string;
}

interface INameListItem {
  name: string;
  hasDefaultValue: boolean;
}

interface OptionType {
  title: string;
  value: number;
  key: number;
  titlePath: string;
  children?: OptionType[]; // 子选项
  selectable?: boolean; // 是否可选择
}

const EditAPIGServiceDrawer = React.forwardRef(
  (
    {
      title = '',
      open = false,
      loading = false,
      dataToBeSent = {},
      serviceDetail = {},
      APIGServiceCatalogList = [],
      APIGServiceDetail = {},
      setOpen,
      onAPIGServiceDrawerSend = () => {},
    }: caseDrawerPropsType,
    ref,
  ) => {
    const { formatMessage } = useI18n();
    const [form] = Form.useForm();
    const [APIGParamOriginData, setAPIGParamOriginData] = useState<any>([]);
    const [APIGParamDataFromOData, setAPIGParamDataFromOData] = useState<any>([]);
    const [APIGParamDataFromUrl, setAPIGParamDataFromUrl] = useState<any>([]);
    const [paramList, setParamList] = useState<any>([]);
    const [APIGParamList, setAPIGParamList] = useState<any>([]);
    const [accessRelativeUrl, setAccessRelativeUrl] = useState<string>('');
    const [isGenerateFromUrl, setIsGenerateFromUrl] = useState<boolean>(false);
    const [defaultValues, setDefaultValues] = useState<any>();

    // 从子组件ODataParamsTable获取数据
    const handleAPIGParamOriginDataChange = (data: any) => {
      setAPIGParamOriginData(data);
    };

    // 从子组件APIGParamsTable获取数据
    const handleAPIGParamListChange = (data: any) => {
      setAPIGParamList(data);
    };

    // 搜索catalog
    const handleCatalogFilter = (inputValue: string, treeNode: any) => {
      // 检查当前节点是否为叶子节点
      const isLeafNode = !treeNode.isParent;

      // 检查节点标题是否包含输入值
      const isMatch = treeNode.title.toLowerCase().includes(inputValue.toLowerCase());

      // 只有叶子节点才会被显示
      return isLeafNode && isMatch;
    };

    // 递归生成树形结构的选项
    const generateTreeOptions = (
      catalogList: APIGServiceCatalogItemType[],
      parentPath: string[] = [],
    ): OptionType[] => {
      return catalogList.map((parent) => {
        const currentPath = [...parentPath, parent.catalogName];

        const option: OptionType = {
          title: parent.catalogName,
          titlePath: currentPath.join('->'), // 添加完整路径
          value: parent.catalogId,
          key: parent.catalogId,
          selectable: !parent.isParent, // 叶子节点可选择
        };

        // 如果有子目录，递归生成子选项
        if (parent.catalogChildNodes && parent.catalogChildNodes.length > 0) {
          option.children = generateTreeOptions(parent.catalogChildNodes, currentPath);
        }

        return option;
      });
    };

    const onCancel = () => {
      setOpen(false);
      setAPIGParamOriginData([]);
      setAPIGParamDataFromOData([]);
      setAPIGParamDataFromUrl([]);
      setParamList([]);
      setAPIGParamList([]);
      setIsGenerateFromUrl(false);
      form.resetFields();
      setAccessRelativeUrl('');
    };

    // 根据apiId查询apig服务基本信息，设置Api Name
    const setApiName = async (apiId: number) => {
      try {
        if (apiId) {
          const { success, data } = await qryApiBaseInfoByApiId({ apiId });
          if (success) {
            form.setFieldValue('apiName', data?.result?.apiName || '');
          }
        }
      } catch (error) {
        console.error(error);
      }
    };

    // 提交
    const onOk = () => {
      form.validateFields().then((values) => {
        try {
          const oDataParam = APIGParamOriginData.map((item: IDataItem) => `${item?.name}=${item?.value}`).join('&');
          onAPIGServiceDrawerSend({
            ...values,
            apigServId: APIGServiceDetail?.apigServId,
            domainName: APIGServiceDetail?.domainName,
            odataParam: oDataParam || '',
            paramList: APIGParamList || [],
            apiId: APIGServiceDetail?.apiId,
            overwriteFlag: 'N',
            newVersionFlag: 'N',
          });
        } finally {
        }
      });
    };

    // 使用正则表达式提取 $() 中的内容
    const extractValues = (list: IDataItem[]) => {
      // 对value中存在$()的项，将$()内的内容提取出来
      const results = list.map((item) => {
        const matches = { reqIn: item.reqIn, nameList: [] as INameListItem[] };
        const regex = /\$\((.+?)\)/g;
        let match;
        // 若name为$a,value为$(a),则将a解析出来,并且hasDefaultValue为true
        if (item.name.replace(/^\$(.*)/, '$($1)') === item.value && (match = regex.exec(item.value)) !== null) {
          matches.nameList.push({ name: match[1], hasDefaultValue: true });
        } else {
          // 否则将每个$()内的内容提取出来,并且hasDefaultValue为false
          while ((match = regex.exec(item.value)) !== null) {
            matches.nameList.push({ name: match[1], hasDefaultValue: false });
          }
        }
        return matches;
      });
      return results;
    };

    // 使用正则表达式提取 {} 中的内容
    const extractContent = (str: string) => {
      const matches = str.match(/\{(.*?)\}/g);
      return matches ? matches.map((match) => match.slice(1, -1)) : [];
    };

    // 根据OData的数据源，生成APIG服务参数
    const generateFromOData = (APIGOriginData: IDataItem[]) => {
      const itemsWithDollarParentheses = APIGOriginData.filter((item: IDataItem) => /\$\(.+?\)/.test(item.value));
      // 提取 $() 中的内容并处理
      const extractedValues = extractValues(itemsWithDollarParentheses) || [];
      let newDataSource: any[] = [];
      extractedValues.forEach((item: { reqIn: string; nameList: INameListItem[] }) => {
        item?.nameList.forEach((i: INameListItem) => {
          if (i.hasDefaultValue) {
            const matchedItem = paramList.find((i: any) => i?.name === `$${item?.nameList[0]?.name}`);
            if (matchedItem) {
              const newItem = {
                key: getUUid(12),
                reqIn: matchedItem.reqIn,
                paramName: matchedItem.name.replace('$', ''),
                isArray: matchedItem.isArray,
                avlValue: matchedItem.avlValue,
                required: matchedItem.required,
                paramType: matchedItem.dataType,
                example: matchedItem.example,
                comments: matchedItem.comments,
              };
              newDataSource.push(newItem);
            }
          } else {
            const newItem = {
              key: getUUid(12),
              reqIn: item?.reqIn,
              paramName: i.name,
              isArray: 'N',
              avlValue: null,
              required: 'Y',
              paramType: 'String',
              example: null,
              comments: null,
            };
            newDataSource.push(newItem);
          }
        });
      });
      setAPIGParamDataFromOData(newDataSource);
    };

    // 根据Access Relative URL生成APIG服务的path参数
    const generateFromURL = () => {
      // 修改APIG Service， 打开弹框后，APIG服务path参数从接口中获取，修改Service Path时才生成新的APIG服务path参数
      if (isGenerateFromUrl) {
        const extractedContent = extractContent(accessRelativeUrl) || [];
        let newDataSource: any[] = [];
        extractedContent.forEach((item: string) => {
          const matchedItem = paramList.find((i: any) => i?.name === item);
          if (matchedItem) {
            const newItem = {
              key: getUUid(12),
              reqIn: 'P',
              paramName: matchedItem.name.replace('$', ''),
              isArray: matchedItem.isArray,
              avlValue: matchedItem.avlValue,
              required: matchedItem.required,
              paramType: matchedItem.dataType,
              example: matchedItem.example,
              comments: matchedItem.comments,
            };
            newDataSource.push(newItem);
          } else {
            const newItem = {
              key: getUUid(12),
              reqIn: 'P',
              paramName: item,
              isArray: 'N',
              avlValue: null,
              required: 'Y',
              paramType: 'String',
              example: null,
              comments: null,
            };
            newDataSource.push(newItem);
          }
        });
        setAPIGParamDataFromUrl(newDataSource);
      }
    };

    useImperativeHandle(ref, () => ({
      onCancel,
    }));

    // 使用 useCallback 和 debounce 创建防抖函数
    const debouncedGenerateFromURL = useCallback(debounce(generateFromURL, 1000), [accessRelativeUrl, paramList]);

    useEffect(() => {
      debouncedGenerateFromURL();
      // 清理函数，取消未执行的防抖调用
      return () => {
        debouncedGenerateFromURL.cancel();
      };
    }, [debouncedGenerateFromURL]);

    useEffect(() => {
      if (open) {
        if (serviceDetail) {
          const list = serviceDetail?.parameters
            ? serviceDetail?.parameters?.filter((item: any) => item?.reqIn !== 'B')
            : [];
          setParamList(list);
        }
      }
    }, [open, serviceDetail]);

    useEffect(() => {
      const defaultValue = {
        apiCatalogId: APIGServiceDetail.apiCatalogId,
        apiMethod: APIGServiceDetail.apiMethod,
        accessRelativeUrl: APIGServiceDetail.accessRelativeUrl,
        comments: APIGServiceDetail.comments,
        odataParam: APIGServiceDetail?.odataParam || [],
        apigParams: APIGServiceDetail?.apigParams?.map((item: any) => ({ ...item, key: getUUid(12) })) || [],
        apiVersion: APIGServiceDetail?.apiVersion,
        aiAutoTestPrompt: APIGServiceDetail?.aiAutoTestPrompt,
      };
      setDefaultValues(defaultValue);
      if (open && APIGServiceDetail?.apiId) {
        setApiName(APIGServiceDetail?.apiId);
      }
    }, [open, APIGServiceDetail]);

    return (
      <Drawer
        title={title}
        open={open}
        onClose={loading ? () => {} : onCancel}
        // width={window.innerWidth * 0.6}
        width={1000}
        footer={
          <Space style={{ float: 'right' }}>
            <Spin spinning={loading}>
              <Button type="primary" onClick={() => onOk()}>
                {formatMessage('PROJECT.COMMON.SUBMIT')}
              </Button>
            </Spin>
            <Button disabled={loading} onClick={onCancel}>
              {formatMessage('PROJECT.COMMON.CANCEL')}
            </Button>
          </Space>
        }
      >
        <Form
          form={form}
          initialValues={defaultValues}
          labelCol={{ span: 8, className: styles.formItemLabel }}
          wrapperCol={{ span: 14 }}
        >
          <Form.Item
            label={formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.APICATALOG')}
            name="apiCatalogId"
            rules={[
              {
                required: true,
              },
            ]}
          >
            <TreeSelect
              disabled // 暂时不允许修改
              showSearch
              allowClear
              treeDefaultExpandAll
              treeNodeLabelProp="titlePath" // 输入框中显示 titlePath 而不是 title
              filterTreeNode={handleCatalogFilter} // 过滤函数
              treeData={generateTreeOptions(APIGServiceCatalogList)}
            />
          </Form.Item>
          <Form.Item
            label={formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.APIMETHOD')}
            name="apiMethod"
            rules={[
              {
                required: true,
              },
              {
                pattern: /^[a-zA-Z0-9_.-]+$/,
                message: formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.ONLYLETTERS'),
              },
            ]}
          >
            {/* 因为要给apiMethod添加.odh后缀，保留原始的Form.Item和Input，但设置为隐藏（display: none）。这样表单的值和验证逻辑不变 */}
            <Input disabled style={{ display: 'none' }} />
          </Form.Item>
          {/* 单独设置一个Form.Item，不绑定到任何表单字段，只用于显示添加后缀的新值 */}
          <Form.Item label=" " colon={false} style={{ marginTop: '-3.25rem' }}>
            <div style={{ display: 'flex', alignItems: 'center' }}>
              <Input
                disabled
                value={form.getFieldValue('apiMethod') ? `${form.getFieldValue('apiMethod')}${ODH_SUFFIX}` : ''}
              />
            </div>
          </Form.Item>
          <Form.Item
            label={formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.APINAME')}
            name="apiName"
            rules={[
              {
                required: true,
                message: formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.PLEASEINPUTAPINAME'),
              },
              {
                pattern: /^[a-zA-Z0-9_.-]+$/,
                message: formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.ONLYLETTERS'),
              },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label={formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.APIVERSION')}
            name="apiVersion"
            rules={[
              {
                required: true,
              },
            ]}
          >
            <Input disabled />
          </Form.Item>
          <Form.Item label={formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.ACCESSRELATIVEURL')} style={{ flex: 1 }}>
            <Flex>
              <Form.Item>
                <Input disabled value={'/api/transferRest'} />
              </Form.Item>
              <Form.Item
                name="accessRelativeUrl"
                style={{ flex: 3 }}
                rules={[
                  {
                    required: true,
                  },
                ]}
              >
                <Input
                  value={accessRelativeUrl}
                  onChange={(e) => {
                    setIsGenerateFromUrl(true);
                    setAccessRelativeUrl(e.target.value);
                  }}
                />
              </Form.Item>
            </Flex>
          </Form.Item>
          {serviceDetail?.odataFlag === 'Y' && (
            <>
              <Form.Item label={formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.ODATAPARAM')} name="odataParam">
                <div>
                  <ODataParamsTable
                    isEditAPIGService={true}
                    paramList={paramList.filter((item: any) => item?.reqIn === 'Q' || item?.reqIn === 'H')}
                    dataToBeSent={dataToBeSent}
                    getAPIGParamsOriginData={handleAPIGParamOriginDataChange}
                    generateFromOData={generateFromOData}
                    dataFromApi={defaultValues?.odataParam}
                  />
                </div>
              </Form.Item>
              <Form.Item label={formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.APIGPARAM')} name="paramList">
                <APIGParamsTable
                  APIGParamDataFromOData={APIGParamDataFromOData}
                  setAPIGParamDataFromOData={setAPIGParamDataFromOData}
                  APIGParamDataFromUrl={APIGParamDataFromUrl}
                  getAPIGParamList={handleAPIGParamListChange}
                  dataFromApi={defaultValues?.apigParams}
                  isEditAPIGService={true}
                  isGenerateFromUrl={isGenerateFromUrl}
                />
              </Form.Item>
            </>
          )}
          <Form.Item label={formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.COMMENTS')} name="comments">
            <Input.TextArea />
          </Form.Item>
          <Form.Item label={formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.AIAUTOTESTPROMPT')} name="aiAutoTestPrompt">
            <Input.TextArea rows={6} />
          </Form.Item>
        </Form>
      </Drawer>
    );
  },
);

export default EditAPIGServiceDrawer;
