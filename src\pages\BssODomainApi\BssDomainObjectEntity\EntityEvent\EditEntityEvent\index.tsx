import React, { useState, useEffect } from 'react';
import { Button, Drawer, Form, Input, Select, Space, Spin, message } from 'antd';
import { modDomainEntityEvent } from '@/services/entityService';
import { RlcEventCatgSourceEventProps, RlcEventCatgSourceProps } from '../../types';
import useI18n from '@/hooks/useI8n';

interface IEditEntityEventDrawer {
  entityId: number;
  isProduct: boolean;
  open: boolean;
  initValue: any;
  rlcEventCatgSource: RlcEventCatgSourceProps[];
  ignoreVersionSource: any;
  onCancel: () => void;
  onOk: () => void;
}

const EditEntityEventDrawer: React.FC<IEditEntityEventDrawer> = (props) => {
  const { open, ignoreVersionSource, entityId, isProduct, initValue, rlcEventCatgSource, onCancel, onOk } = props;
  const { formatMessage } = useI18n();
  const [form] = Form.useForm();
  const [formValues, setFormValues] = useState<any>({}); // formValues
  const [defaultValues, setDeFaultValues] = useState<any>({});

  const [eventCodeSource, setEventCodeSource] = useState<any>([]);
  const [submitSpinning, setSubmitSpinning] = useState<boolean>(false);

  const CREATIONSOURCE = [
    { name: 'Product-Owned', key: 'P' },
    { name: 'Project-Customized', key: 'C' },
  ];

  // modal close
  const onClose = async () => {
    form.resetFields();
    onCancel?.();
  };

  const editEntityEvent = async () => {
    setSubmitSpinning(true);
    try {
      // 调用修改实体事件接口
      const curPostData = {
        ...formValues,
        ignoreVer: formValues?.ignoreVer?.length ? formValues?.ignoreVer?.join(',') : null, // 处理ignoreVer显示空选项的bug
        entityId,
        eventId: initValue.eventId,
      };
      const resultData = await modDomainEntityEvent(curPostData);
      if (resultData) {
        message.success(formatMessage('ENTITYEVENT.EDIT.SUCCESS'));
        form.resetFields();
        onOk?.();
      }
    } finally {
      setSubmitSpinning(false);
    }
  };

  const checkEntityEventData = async () => {
    form.validateFields().then(async () => {
      editEntityEvent();
    });
  };

  const onEventCatgChange = (value: string) => {
    form.setFieldsValue({
      eventCode: '',
    });
    const eventCodeData = rlcEventCatgSource.filter((i: RlcEventCatgSourceProps) => i.catalogId === Number(value));
    if (eventCodeData?.length > 0) {
      setEventCodeSource(eventCodeData[0].events);
    }
  };

  useEffect(() => {
    // 深拷贝行数据后初始化值
    const defaultValues: any = {};
    Object.assign(defaultValues, initValue);
    if (defaultValues?.ignoreVer) {
      defaultValues.ignoreVer = defaultValues?.ignoreVer?.split(',');
    }
    // 回显rlc event Name
    rlcEventCatgSource.forEach((item: RlcEventCatgSourceProps) => {
      if (
        item.events?.filter((i: RlcEventCatgSourceEventProps) => i.eventCode === defaultValues.eventCode)?.length > 0
      ) {
        setEventCodeSource(item.events);
      }
      item.events
        ?.filter((i: any) => i.eventCode === defaultValues.eventCode)
        .forEach((i: any) => {
          defaultValues.rlcEventCatg = i.catalogId;
        });
    });
    setDeFaultValues(defaultValues);
    setFormValues(defaultValues);
  }, [initValue, rlcEventCatgSource]);

  return (
    <Drawer
      title={formatMessage('ENTITYEVENT.EDIT.TITLE')}
      onClose={onClose}
      maskClosable={false}
      open={open}
      width={720}
      afterOpenChange={() => {
        form.resetFields();
      }}
      footer={
        <Space style={{ float: 'right' }}>
          <Spin spinning={submitSpinning}>
            <Button type="primary" onClick={checkEntityEventData}>
              {formatMessage('ENTITYEVENT.ADD.SUBMIT')}
            </Button>
          </Spin>
          <Button onClick={onClose}>{formatMessage('ENTITYEVENT.ADD.CANCEL')}</Button>
        </Space>
      }
    >
      <Form
        form={form}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
        initialValues={defaultValues}
        onValuesChange={(cValues, alValues) => {
          setFormValues(alValues);
        }}
      >
        <Form.Item
          label={formatMessage('ENTITYEVENT.ADD.EVENT_NAME')}
          name="eventName"
          rules={[{ required: true, message: '' }]}
        >
          <Input allowClear disabled={!isProduct && initValue?.creationSrc === 'P'} />
        </Form.Item>
        <Form.Item
          label={formatMessage('ENTITYEVENT.ADD.RLC_EVENT_CATALOG')}
          name="rlcEventCatg"
          rules={[{ required: true, message: '' }]}
        >
          <Select disabled={!isProduct && initValue?.creationSrc === 'P'} onChange={onEventCatgChange}>
            {rlcEventCatgSource.map((i: any) => (
              <Select.Option key={i.catalogId} value={i.catalogId}>
                {i.catalogName}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          label={formatMessage('ENTITYEVENT.ADD.RLC_EVENT_NAME')}
          name="eventCode"
          rules={[{ required: true, message: '' }]}
        >
          <Select disabled={!isProduct && initValue?.creationSrc === 'P'}>
            {eventCodeSource?.map((i: any) => (
              <Select.Option key={i.eventCode} value={i.eventCode}>
                {i.eventName}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item label={formatMessage('ENTITYEVENT.ADD.CREATION_SOURCE')} name="creationSrc">
          <Select disabled>
            {CREATIONSOURCE.map((i) => (
              <Select.Option key={i.key} value={i.key}>
                {i.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        {isProduct ? (
          <Form.Item label={formatMessage('ENTITYEVENT.ADD.IGNORE_VERSION')} name="ignoreVer">
            <Select mode="multiple">
              {ignoreVersionSource?.map((i: any) => (
                <Select.Option key={i.key} value={i.key}>
                  {i.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        ) : null}

        <Form.Item
          label={formatMessage('ENTITYEVENT.ADD.DESCRIPTIONS')}
          name="comments"
          rules={[{ required: true, message: '' }]}
        >
          <Input.TextArea allowClear />
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default EditEntityEventDrawer;
