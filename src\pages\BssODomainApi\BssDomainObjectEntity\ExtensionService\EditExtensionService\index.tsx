import React, { useState, useEffect } from 'react';
import { Button, Drawer, Form, Input, Select, Space, message, AutoComplete, Spin, Divider } from 'antd';
import { editDomainEntityService } from '@/services/entityService';
import { SelectedEntityProps, SelectedRootNode } from '../../types';
import getUUid from '@/utils/getUUid';
import SelectWithAddButton from '@/components/SelectWithAddButton';
import { CommonOptionItemType } from '@/components/SelectWithAddButton/type';
import { useModel } from 'umi';
import useI18n from '@/hooks/useI8n';

interface IEditExtensionServiceDrawer {
  selectedEntity: SelectedEntityProps;
  isProduct: boolean;
  serviceCatgSource: any;
  serviceSubCatgSource: any;
  dubboClassSource: any;
  serviceRootPath: string;
  open: boolean;
  initValues: any;
  ignoreVersionSource: any;
  selectedRootNode: SelectedRootNode; // 根节点
  tfmServices: CommonOptionItemType[];
  onCancel: () => void;
  onOk: () => void;
}
type VersionItem = {
  comments: string;
  serviceCatg: string;
  verCode: string;
};

const EditExtensionServiceDrawer: React.FC<IEditExtensionServiceDrawer> = (props) => {
  const {
    open,
    ignoreVersionSource,
    selectedEntity,
    isProduct,
    initValues,
    serviceCatgSource = [],
    serviceSubCatgSource,
    serviceRootPath,
    dubboClassSource,
    selectedRootNode,
    tfmServices = [],
    onCancel,
    onOk,
  } = props;
  const { formatMessage } = useI18n();
  const [form] = Form.useForm();
  const [formValues, setFormValues] = useState<any>({}); // formValues
  const [defaultValues, setDeFaultValues] = useState<any>({});
  const [dubboMethodSource, setDubboMethodSource] = useState<any>([]);
  const [catgVerVOList, setCatgVerVOList] = useState<any>([]);
  const [selectedImplType, setSelectedImplType] = useState<string>('');
  const [isSimpleParameter, setIsSimpleParameter] = useState<string>(''); //Dubbo Method是否为简单类型
  const [spining, setSpining] = useState<boolean>(false);
  const [isDubboClassOrMethodChange, setIsDubboClassOrMethodChange] = useState<boolean>(false); // 与原始数据比较，Dubbo Class或 Dubbo Method 是否改变

  // 项目定制环境(systemConfig为false)且修改的记录"定义来源"（CREATION_SRC）为P（Product-Owned）
  const isProjectAndProductOwned = !isProduct && initValues?.creationSrc === 'P';
  // 项目定制环境(systemConfig为false)且修改的记录"定义来源"（CREATION_SRC）为C（Project-Customized）
  const isProjectAndProjectCustomized = !isProduct && initValues?.creationSrc === 'C';
  const { has81E } = useModel('useSystemConfigDataModel');

  const isShowProjectionCallService =
    selectedRootNode?.appCode === 'custc' || // Customer
    selectedRootNode?.appCode === 'coc' || // Order
    (selectedRootNode?.appCode === 'cpc' && has81E) || // Offer
    (selectedRootNode?.appCode === 'sic' && has81E) || // Resource
    selectedRootNode?.appCode === 'test';

  const CommonSource = [
    {
      label: 'No',
      value: 'N',
    },
    {
      label: 'Yes',
      value: 'Y',
    },
  ];

  const CREATIONSOURCE = [
    { name: 'Product-Owned', key: 'P' },
    { name: 'Project-Customized', key: 'C' },
  ];

  const HttpMethodSource = [
    // GET、PUT、POST、PATCH、DELETE
    { label: 'GET', value: 'GET' },
    { label: 'PUT', value: 'PUT' },
    { label: 'POST', value: 'POST' },
    { label: 'PATCH', value: 'PATCH' },
    { label: 'DELETE', value: 'DELETE' },
  ];

  const ImplementTypeSource = [
    { label: 'Orchestration', value: 'N' },
    { label: 'Projection Dubbo API', value: 'D' },
    { label: 'Projection Restful API', value: 'R' },
    ...(isShowProjectionCallService ? [{ label: 'Projection Call Service', value: 'S' }] : []),
  ];

  // modal close
  const onClose = async () => {
    form.resetFields();
    onCancel?.();
  };

  const editExtensionService = async (param: any) => {
    setSpining(true);
    try {
      // 调用修改实体扩展服务接口
      const resultData = await editDomainEntityService({ entityId: selectedEntity.entityId }, param);

      if (resultData?.success) {
        message.success(formatMessage('EXTENSIONSERVICE.EDIT.SUCCESS'));
        form.resetFields();
        onOk?.();
      } else {
        message.error(formatMessage('EXTENSIONSERVICE.EDIT.FAIL'));
      }
    } catch (error) {
      console.error('An error occurred:', error);
      message.error(formatMessage('EXTENSIONSERVICE.EDIT.ERROR'));
    } finally {
      setSpining(false);
    }
  };

  const checkExtServiceData = async () => {
    form.validateFields().then(async (values) => {
      // 构造入参
      const param = {
        ...defaultValues,
        ...values,
        matchVer: values.matchVer?.join(','),
        entityId: selectedEntity.entityId,
        serviceId: initValues.serviceId,
      };
      const filterResult = serviceSubCatgSource.filter((item: any) => item.serviceSubCatgName === param.serviceSubCatg);
      if (filterResult.length > 0) {
        param.serviceSubCatg = filterResult[0].serviceSubCatg;
      } else {
        param.serviceSubCatgName = param.serviceSubCatg;
        delete param.serviceSubCatg;
      }
      if (param.implType === 'D') {
        const defaultSelectDubboMethod = dubboMethodSource?.find((item: any) => {
          return param.dubboMethod === `${item.methodName}${item?.paramString}`;
        });

        if (defaultSelectDubboMethod) {
          const formattedDubboMethod = `${defaultSelectDubboMethod.methodName}-${
            defaultSelectDubboMethod.paramString
          }-${defaultSelectDubboMethod.simpleParameter}-${defaultSelectDubboMethod.parameterTypes?.join(',')}`;
          param.dubboMethod = formattedDubboMethod;
        }
        const dubboMethodList = param.dubboMethod.split('-');
        const simpleParam = dubboMethodList[2] === 'false' ? 'N' : 'Y';
        param.odhServiceProjDubboVo = {
          dubboClass: param.dubboClass,
          dubboMethod: dubboMethodList[0],
          simpleParam,
          methodParamClass: dubboMethodList[3],
        };
      } else {
        delete param.dubboClass;
        delete param.dubboMethod;
        delete param.odhServiceProjDubboVo;
      }
      if (param.implType === 'R') {
        param.odhServiceProjRestVo = {
          servicePath: param.restfulApiPath,
          serviceMethod: param.restfulApiHttpMethod,
        };
      } else {
        delete param.odhServiceProjRestVo;
      }
      if (param.implType === 'S') {
        param.odhServiceProjCallservVo = {
          serviceId: initValues.serviceId,
          serviceName: param.tfmServiceName,
        };
        delete param.tfmServiceName;
      } else {
        delete param.odhServiceProjCallservVo;
      }

      if (isProjectAndProductOwned || param.implType !== 'D') {
        param.reFresh = 'N'; // 扩展服务的类型不为Dubbo或者项目修改产品的扩展服务，则不需要刷新
      }

      editExtensionService(param);
    });
  };

  const onImplTypeChange = (value: string) => {
    setSelectedImplType(value);
    setIsSimpleParameter('');
    form.setFieldsValue({
      dubboClass: '',
      dubboMethod: '',
      restfulApiHttpMethod: '',
      restfulApiPath: '',
      tfmServiceName: '',
    });
  };

  const onDubboClassChange = (value: any) => {
    form.setFieldsValue({
      dubboMethod: '',
    });
    setIsSimpleParameter('');
    const methodData = dubboClassSource.filter((i: any) => i.interfaceClass === value);
    if (methodData?.length > 0) {
      const newSource = methodData[0]?.methods?.map((item: any) => {
        const paramTypes = item?.parameterTypes?.map((param: any) => {
          const parts = param?.split('.');
          const lastPart = parts[parts.length - 1];
          return lastPart;
        });
        return {
          ...item,
          paramString: `(${paramTypes?.join(',')})`,
        };
      });
      setDubboMethodSource(newSource || []);
    }
  };

  const onDubboMethodChange = (value: any) => {
    const isSimpleParam = value?.split('-')[2];
    setIsSimpleParameter(isSimpleParam);
  };

  useEffect(() => {
    // 深拷贝行数据后初始化值
    if (Object.keys(initValues).length !== 0) {
      const defaultValues: any = {};
      Object.assign(defaultValues, initValues);
      defaultValues.reFresh = 'N'; // reFresh默认为N(Refresh Request API Reference是类似一次性按钮的功能，每次编辑扩展服务时，都默认为No)
      if (defaultValues?.matchVer) {
        defaultValues.matchVer = defaultValues?.matchVer?.split(',');
      }
      const suffix =
        serviceCatgSource.filter((i: any) => i.serviceCatg === defaultValues.serviceCatg)[0]?.nameSpace || '';
      const defaultVersionList = createVersionList(defaultValues?.serviceCatg, serviceCatgSource);
      setCatgVerVOList(defaultVersionList);
      defaultValues.entityName = selectedEntity?.entityName;
      defaultValues.rootPath = suffix ? `${serviceRootPath}/${suffix}` : serviceRootPath;
      defaultValues.dubboClass = defaultValues.odhServiceProjDubboVo?.dubboClass;
      onDubboClassChange(defaultValues.dubboClass);
      const dubboMethodParams = defaultValues.odhServiceProjDubboVo?.methodParamClass
        ?.split(',')
        ?.map((item: string) => item?.split('.')?.pop()); // 取最后一个部分.join(',');
      defaultValues.dubboMethod = `${defaultValues.odhServiceProjDubboVo?.dubboMethod}(${dubboMethodParams || ''})`;
      defaultValues.restfulApiPath = defaultValues.odhServiceProjRestVo?.servicePath;
      defaultValues.restfulApiHttpMethod = defaultValues.odhServiceProjRestVo?.serviceMethod;
      defaultValues.tfmServiceName = defaultValues.odhServiceProjCallservVo?.serviceName;

      // 回显serviceSubCatg
      const filterResult = serviceSubCatgSource.filter(
        (item: any) => item.serviceSubCatg === defaultValues.serviceSubCatg,
      );
      if (filterResult.length > 0) {
        defaultValues.serviceSubCatg = filterResult[0].serviceSubCatgName;
      }

      if (defaultValues?.servicePath) {
        const newPath = defaultValues.servicePath.substring(
          serviceRootPath.length + 1,
          defaultValues.servicePath.length,
        );
        defaultValues.servicePath = newPath;
      }

      if (defaultValues?.odhServiceProjDubboVo) {
        if (defaultValues?.odhServiceProjDubboVo?.simpleParam === 'Y') {
          setIsSimpleParameter('true');
        } else {
          setIsSimpleParameter('false');
        }
      }

      if (defaultValues.implType === 'D') {
        setSelectedImplType('D');
      } else if (defaultValues.implType === 'R') {
        setSelectedImplType('R');
      } else if (defaultValues.implType === 'S') {
        setSelectedImplType('S');
      } else {
        setSelectedImplType('N');
      }
      setDeFaultValues(defaultValues);
      setFormValues(defaultValues);
    }
  }, [initValues, open]);

  // 获取Service Catalog对应的版本列表
  const createVersionList = (serviceCatalog: any, serviceCatalogSource: any) => {
    const list = serviceCatalogSource
      .filter((i: any) => i.serviceCatg === serviceCatalog)[0]
      ?.catgVerVOList.map((item: VersionItem) => item?.verCode) // 提取版本号
      .filter(Boolean) // 过滤掉 undefined
      .map((verCode: string) => ({
        label: verCode,
        value: verCode,
      }))
      .sort((a: any, b: any) => {
        const versionA = a.label.replace('v', '');
        const versionB = b.label.replace('v', '');
        return versionA - versionB; // 按照数字顺序排序
      });

    return list;
  };

  const handleSelectServiceCatg = (value: any) => {
    // 拼接后缀
    const prePath = serviceRootPath;
    const suffix = serviceCatgSource.filter((i: any) => i.serviceCatg === value)[0]?.nameSpace || '';
    if (suffix) {
      form.setFieldValue('rootPath', `${prePath}/${suffix}`);
    } else {
      form.setFieldValue('rootPath', prePath);
    }

    const versionList = createVersionList(value, serviceCatgSource);

    if (!versionList?.length) {
      form.setFieldValue('serviceVer', '');
    } else {
      form.setFieldValue('serviceVer', versionList[0].value);
    }
    setCatgVerVOList(versionList);
  };

  const onServicePathChange = (e: any) => {
    const { value } = e.target;
    // 去除所有空格
    const newValue = value.replace(/\s/g, '');
    // 直接修改目标的值
    form.setFieldValue('servicePath', newValue);
  };

  const onRestfulApiPathathChange = (e: any) => {
    const { value } = e.target;
    // 去除所有空格
    const newValue = value.replace(/\s/g, '');
    // 直接修改目标的值
    form.setFieldValue('restfulApiPath', newValue);
  };

  useEffect(() => {
    // Dubbo 方法如果为复杂类型，则不能选择GET方法
    if (isSimpleParameter === 'false' && formValues?.httpMethod === 'GET' && selectedImplType === 'D') {
      form.setFieldValue('httpMethod', '');
      message.warning(formatMessage('EXTENSIONSERVICE.ADD.DUBBO_METHOD_ERROR'));
    }

    // 配置rest服务时，odh服务的httpMethod只能配置成和实际对端的httpMethod一样或者是POST
    if (formValues?.restfulApiHttpMethod && formValues?.httpMethod) {
      const isHttpMethodValidWithRestful = [formValues?.restfulApiHttpMethod, 'POST'].includes(formValues?.httpMethod);
      if (!isHttpMethodValidWithRestful) {
        form.setFieldValue('httpMethod', '');
        message.warning(formatMessage('EXTENSIONSERVICE.ADD.RESTFUL_METHOD_ERROR'));
      }
    }

    // Implement Type从其他类型变为Dubbo类型，或者原本是Dubbo类型，但是dubboClass或dubboMethod改变时，reFresh只能设置为Y
    if (!isProjectAndProductOwned && formValues.implType === 'D') {
      let dubboMethod = JSON.parse(JSON.stringify(formValues.dubboMethod || ''));
      const defaultSelectDubboMethod = dubboMethodSource?.find((item: any) => {
        return formValues.dubboMethod === `${item.methodName}${item?.paramString}`;
      });

      if (defaultSelectDubboMethod) {
        const formattedDubboMethod = `${defaultSelectDubboMethod.methodName}-${defaultSelectDubboMethod.paramString}-${
          defaultSelectDubboMethod.simpleParameter
        }-${defaultSelectDubboMethod.parameterTypes?.join(',')}`;
        dubboMethod = formattedDubboMethod;
      }
      const dubboMethodList = dubboMethod.split('-');
      const simpleParam = dubboMethodList[2] === 'false' ? 'N' : 'Y';

      const odhServiceProjDubboVo = {
        dubboClass: formValues.dubboClass,
        dubboMethod: dubboMethodList[0],
        simpleParam,
        methodParamClass: dubboMethodList[3],
      };

      const defaultOdhServiceProjDubboVo = {
        dubboClass: defaultValues?.odhServiceProjDubboVo?.dubboClass,
        dubboMethod: defaultValues?.odhServiceProjDubboVo?.dubboMethod,
        simpleParam: defaultValues?.odhServiceProjDubboVo?.simpleParam,
        methodParamClass: defaultValues?.odhServiceProjDubboVo?.methodParamClass,
      };
      if (JSON.stringify(odhServiceProjDubboVo) === JSON.stringify(defaultOdhServiceProjDubboVo)) {
        setIsDubboClassOrMethodChange(false);
      } else {
        form.setFieldValue('reFresh', 'Y');
        setIsDubboClassOrMethodChange(true);
      }
    }
  }, [isSimpleParameter, formValues, dubboMethodSource]);

  return (
    <Drawer
      title={formatMessage('EXTENSIONSERVICE.EDIT.TITLE')}
      onClose={onClose}
      maskClosable={false}
      open={open}
      width={960}
      afterOpenChange={() => {
        form.resetFields();
      }}
      footer={
        <Space style={{ float: 'right' }}>
          <Spin spinning={spining}>
            <Button type="primary" onClick={checkExtServiceData}>
              {formatMessage('EXTENSIONSERVICE.ADD.SUBMIT')}
            </Button>
          </Spin>
          <Button onClick={onClose}>{formatMessage('EXTENSIONSERVICE.ADD.CANCEL')}</Button>
        </Space>
      }
    >
      <Form
        form={form}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
        initialValues={defaultValues}
        onValuesChange={(cValues, alValues) => {
          setFormValues(alValues);
        }}
      >
        <Form.Item
          label={formatMessage('EXTENSIONSERVICE.ADD.SERVICE_NAME')}
          name="serviceName"
          rules={[{ required: true, message: '' }]}
        >
          <Input allowClear />
        </Form.Item>
        <Form.Item label={formatMessage('EXTENSIONSERVICE.ADD.ENTITY')} name="entityName">
          <Input disabled />
        </Form.Item>
        <Form.Item label={formatMessage('EXTENSIONSERVICE.ADD.CREATION_SOURCE')} name="creationSrc">
          <Select disabled>
            {CREATIONSOURCE.map((i) => (
              <Select.Option key={i.key} value={i.key}>
                {i.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          label={formatMessage('EXTENSIONSERVICE.ADD.DEPRECATED')}
          name="deprecated"
          rules={[{ required: true, message: '' }]}
        >
          <Select options={CommonSource} />
        </Form.Item>
        {isProduct ? (
          <Form.Item
            label={formatMessage('EXTENSIONSERVICE.ADD.MATCH_VERSION')}
            name="matchVer"
            rules={[{ required: true, message: '' }]}
          >
            <Select mode="multiple" disabled={isProjectAndProductOwned}>
              {/* 需求#11408424，仅将扩展服务的ignoreVer字段改为matchVer字段，source还是取ignoreVersionSource */}
              {ignoreVersionSource?.map((i: any) => (
                <Select.Option key={i.key} value={i.key}>
                  {i.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        ) : null}

        <Form.Item
          label={formatMessage('EXTENSIONSERVICE.ADD.SERVICE_CATALOG')}
          style={{ marginBottom: 0 }}
          rules={[{ required: true, message: '' }]}
        >
          <Form.Item
            name="serviceCatg"
            style={{ display: 'inline-block', width: 'calc(33%)' }}
            rules={[{ required: true, message: '' }]}
          >
            <Select disabled={true} onSelect={handleSelectServiceCatg}>
              {serviceCatgSource.map((i: any) => (
                <Select.Option key={i.serviceCatg} value={i.serviceCatg}>
                  {i.serviceCatgName}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="serviceSubCatg"
            style={{ display: 'inline-block', width: 'calc(66% - 5px)', marginLeft: '5px' }}
            rules={[{ required: true, message: '' }]}
          >
            <SelectWithAddButton
              originalOptionsList={serviceSubCatgSource}
              parentForm={form}
              formFieldName="serviceSubCatg"
            />
          </Form.Item>
        </Form.Item>

        <Form.Item
          label={formatMessage('EXTENSIONSERVICE.ADD.SERVICE_PATH')}
          style={{ marginBottom: 0 }}
          rules={[{ required: true, message: '' }]}
        >
          <div style={{ position: 'relative' }}>
            <div style={{ display: 'flex' }}>
              <Form.Item
                name="httpMethod"
                style={{ display: 'inline-block', width: 'calc(20%)' }}
                rules={[{ required: true, message: '' }]}
              >
                <Select options={HttpMethodSource} disabled={isProjectAndProductOwned} />
              </Form.Item>
              <Form.Item name="rootPath" style={{ display: 'inline-block', width: 'calc(35%)' }}>
                <Input disabled />
              </Form.Item>
              <Form.Item name="serviceVer" style={{ display: 'inline-block', width: 'calc(13%)' }}>
                <Select
                  options={catgVerVOList}
                  showSearch
                  disabled={isProjectAndProductOwned || !catgVerVOList?.length}
                />
              </Form.Item>
              <Form.Item name="servicePath" style={{ display: 'inline-block', width: 'calc(31%)' }}>
                <Input allowClear disabled={isProjectAndProductOwned} onChange={onServicePathChange} />
              </Form.Item>
            </div>
            {isProduct && (
              <div style={{ margin: '-20px 0 20px 0', color: '#8c8c8c' }}>
                {formatMessage('EXTENSIONSERVICE.EDIT.SERVICE_PATH_NOTE')}
              </div>
            )}
          </div>
        </Form.Item>
        <Form.Item
          label={formatMessage('EXTENSIONSERVICE.ADD.IMPLEMENT_TYPE')}
          name="implType"
          rules={[{ required: true, message: '' }]}
        >
          <Select options={ImplementTypeSource} disabled={isProjectAndProductOwned} onChange={onImplTypeChange} />
        </Form.Item>
        {selectedImplType === 'D' && (
          <>
            <Form.Item
              label={formatMessage('EXTENSIONSERVICE.ADD.DUBBO_CLASS_PATH')}
              name="dubboClass"
              rules={[{ required: true, message: '' }]}
            >
              <Select onChange={onDubboClassChange} showSearch disabled={isProjectAndProductOwned}>
                {dubboClassSource.map((i: any) => (
                  <Select.Option key={i.interfaceClass} value={i.interfaceClass}>
                    {i.interfaceClass}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item
              label={formatMessage('EXTENSIONSERVICE.ADD.DUBBO_METHOD')}
              name="dubboMethod"
              rules={[{ required: true, message: '' }]}
            >
              <Select showSearch onChange={onDubboMethodChange} disabled={isProjectAndProductOwned}>
                {dubboMethodSource.map((i: any) => (
                  <Select.Option
                    key={`${i.methodName}-${i?.paramString}-${i?.simpleParameter}`}
                    value={`${i.methodName}-${i?.paramString}-${i?.simpleParameter}-${i?.parameterTypes?.join(',')}`}
                  >
                    {`${i.methodName}${i?.paramString}`}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
            {!isProjectAndProductOwned && (
              <Form.Item
                label={formatMessage('EXTENSIONSERVICE.EDIT.REFRESH')}
                name="reFresh"
                rules={[{ required: true, message: '' }]}
                extra={formatMessage('EXTENSIONSERVICE.EDIT.REFRESH_NOTE')}
              >
                <Select disabled={isDubboClassOrMethodChange} options={CommonSource} />
              </Form.Item>
            )}
          </>
        )}
        {selectedImplType === 'R' && (
          <Form.Item label={formatMessage('EXTENSIONSERVICE.ADD.RESTFUL_API_PATH')} style={{ marginBottom: 0 }}>
            <Form.Item
              name="restfulApiHttpMethod"
              style={{ display: 'inline-block', width: 'calc(20%)' }}
              rules={[{ required: true, message: '' }]}
            >
              <Select options={HttpMethodSource} disabled={isProjectAndProductOwned} />
            </Form.Item>
            <Form.Item
              name="restfulApiPath"
              style={{ display: 'inline-block', width: 'calc(80%)' }}
              rules={[{ required: true, message: '' }]}
            >
              <Input allowClear disabled={isProjectAndProductOwned} onChange={onRestfulApiPathathChange} />
            </Form.Item>
          </Form.Item>
        )}
        {selectedImplType === 'S' && (
          <Form.Item
            label={formatMessage('EXTENSIONSERVICE.ADD.TFM_SERVICE_NAME')}
            name="tfmServiceName"
            rules={[{ required: true, message: '' }]}
          >
            <SelectWithAddButton
              originalOptionsList={[
                ...(!defaultValues?.odhServiceProjCallservVo?.serviceName ||
                tfmServices?.find((item) => item?.value === defaultValues?.odhServiceProjCallservVo?.serviceName)
                  ? []
                  : [
                      {
                        label: defaultValues?.odhServiceProjCallservVo?.serviceName,
                        value: defaultValues?.odhServiceProjCallservVo?.serviceName,
                        key: getUUid(6),
                        state: 'new',
                        // 这里主要处理通过输入"新增"的tfmServiceName已经随着该extensionService提交成功了，但实际该tfmServiceName并没有入对应的表（非bug，后台逻辑就是不入表）。
                      },
                    ]),
                ...tfmServices,
              ]}
              parentForm={form}
              formFieldName="tfmServiceName"
            />
          </Form.Item>
        )}
        <Form.Item
          label={formatMessage('EXTENSIONSERVICE.ADD.DESCRIPTIONS')}
          name="comments"
          rules={[{ required: true, message: '' }]}
        >
          <Input.TextArea allowClear />
        </Form.Item>
      </Form>
    </Drawer>
  );
};

export default EditExtensionServiceDrawer;
