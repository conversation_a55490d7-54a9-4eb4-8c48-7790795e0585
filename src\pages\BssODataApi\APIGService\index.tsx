import styles from './index.less';
import useI18n from '@/hooks/useI8n';
import { Flex, Input, message, Spin } from 'antd';
import EmptyDatePage from '@/components/EmptyDataPage';
import DebugService from '@/pages/BssDebugService/DebugService';
import { LeftOutlined, SearchOutlined } from '@ant-design/icons';
import { useEffect, useMemo, useRef, useState } from 'react';
import CustomerCollpaseForAPIGService from './CustomerCollpaseForAPIGService';
import {
  deleteApigService,
  getAPIGServiceCatalog,
  getAPIGServiceDetail,
  getAPIGServiceList,
  getAPIGUseCase,
  queryServiceDetailByCode,
  refreshApigService,
} from '@/services';
import { cleanAPIGCatalogData } from '@/utils/cleanDataAmount';
import { APIGServiceCatalogItemType, IAPIGCatalog } from '@/services/typing';
import getUUid from '@/utils/getUUid';

interface BssSearchUseCasePropsType {
  onClose: () => void;
}

const APIGService = ({ onClose }: BssSearchUseCasePropsType) => {
  const { formatMessage } = useI18n();
  const [searchvalue, setSearchValue] = useState(''); // 搜索值
  const [APIGServiceCatalogList, setAPIGServiceCatalogList] = useState<APIGServiceCatalogItemType[]>([]); // 服务目录列表
  const [selectedAPIGServiceId, setSelectedAPIGServiceId] = useState<number>(); // 当前选择的APIG服务ID
  const [selectedAPIGServiceDetail, setSelectedAPIGServiceDetail] = useState<any>(); // 当前选择的APIG服务详情
  const [selectedServiceCode, setSelectedServiceCode] = useState<string>('');
  const [selectedServiceDetail, setSelectedServiceDetail] = useState<any>();
  const [selectedAPIGUseCaseDetail, setSelectedAPIGUseCaseDetail] = useState<any>();
  const [loading, setLoading] = useState<boolean>(false);
  const [afterCleanAPIGServiceList, setAfterCleanAPIGServiceList] = useState<IAPIGCatalog[]>([]);
  const [hasInitFirstSelect, setHasInitFirstSelect] = useState<boolean>(false); // 是否初始化过默认选择第一项
  const searchref = useRef<any>();

  // 搜索
  const handleSearch = (value: string) => {
    setSearchValue(value);
  };

  // 查询APIG的服务分类
  const queryAPIGServiceCatalog = async () => {
    try {
      const { success, data } = await getAPIGServiceCatalog();
      if (success) {
        setAPIGServiceCatalogList(data);
      }
    } catch (error) {
      console.error(error);
    }
  };

  // 查询APIG服务列表
  const queryAPIGServiceList = async () => {
    try {
      setLoading(true);
      const { success, data } = await getAPIGServiceList();
      if (success) {
        const afterClean = cleanAPIGCatalogData(JSON.parse(JSON.stringify(data)));
        setAfterCleanAPIGServiceList(afterClean);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // 查询APIG服务详情
  const queryAPIGServiceDetail = async (params: { apigServId: number }) => {
    try {
      setLoading(true);
      const { success, data } = await getAPIGServiceDetail(params);
      if (success) {
        setSelectedAPIGServiceDetail(data);
        setSelectedServiceCode(data?.serviceCode);
      }
    } catch (error) {
      console.error(error);
      setSelectedAPIGServiceDetail({});
      setSelectedServiceCode('');
      setSelectedServiceDetail({});
    } finally {
      setLoading(false);
    }
  };

  // 查询apig服务的usecase
  const queryAPIGUseCase = async (params: { apigServId: number }) => {
    try {
      setLoading(true);
      const { success, data } = await getAPIGUseCase(params);
      if (success) {
        setSelectedAPIGUseCaseDetail(data);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // 通过Service Code 查询debug信息
  const queryServiceDetail = async (params: { serviceCode: string }) => {
    try {
      setLoading(true);
      const { success, data } = await queryServiceDetailByCode(params);
      if (success) {
        setSelectedServiceDetail(data);
      }
    } catch (error) {
      console.error(error);
      setSelectedServiceDetail({});
    } finally {
      setLoading(false);
    }
  };

  // 删除APIG服务并更新数据
  const deleteApigUseCaseAndUpdateData = async (params: { apigServId: number }) => {
    try {
      setLoading(true);
      const { success } = await deleteApigService(params);
      if (success) {
        message.success(formatMessage('PROJECT.COMMON.DELETESUCCESS'));
        setHasInitFirstSelect(false); // 删除服务后重新初始化
        queryAPIGServiceList();
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  // 刷新APIG服务
  const refreshAPIGService = async () => {
    try {
      setLoading(true);
      if (selectedAPIGServiceId) {
        const { success } = await refreshApigService({ apigServId: selectedAPIGServiceId });
        if (success) {
          message.success(formatMessage('PROJECT.COMMON.REFRESHSUCCESS'));
          // 暂时不需要重新初始化
          // setHasInitFirstSelect(false); // 刷新服务后重新初始化
          // queryAPIGServiceList();
        }
      }
    } catch (error) {
      console.error(error);
    } finally {
      setLoading(false);
    }
  };

  let DebugServiceArea = useMemo(() => {
    return selectedServiceDetail && selectedAPIGServiceDetail && Reflect?.ownKeys(selectedServiceDetail)?.length > 0 ? (
      <div>
        <DebugService
          key={`DebugService-EditAPIGService-${getUUid(6)}`}
          saveUsType="APIGSERVICE"
          debugMode
          APIGServiceCatalogList={APIGServiceCatalogList}
          serviceCode={selectedServiceCode}
          serviceDetail={selectedServiceDetail}
          APIGServiceDetail={selectedAPIGServiceDetail}
          initSeletedValue={selectedAPIGUseCaseDetail}
          isEditAPIGService={true}
          hiddenSelectBtn
          onEditAPIGOk={() => {
            if (selectedAPIGServiceId) {
              queryAPIGServiceList();
              queryAPIGServiceDetail({ apigServId: selectedAPIGServiceId });
              queryAPIGUseCase({ apigServId: selectedAPIGServiceId });
            }
          }}
          refreshAPIGService={refreshAPIGService}
        />
      </div>
    ) : (
      <Flex align="center" justify="center" style={{ height: '100%' }}>
        <EmptyDatePage />
      </Flex>
    );
  }, [selectedServiceCode, selectedServiceDetail, selectedAPIGServiceDetail, selectedAPIGUseCaseDetail]);

  useEffect(() => {
    return () => {
      DebugServiceArea = <EmptyDatePage />;
    };
  }, []);

  useEffect(() => {
    // 查询APIG的服务分类
    queryAPIGServiceCatalog();
    // 查询APIG服务列表
    queryAPIGServiceList();
  }, []);

  useEffect(() => {
    if (selectedAPIGServiceId) {
      // 查询APIG服务详情
      queryAPIGServiceDetail({ apigServId: selectedAPIGServiceId });
      // 查询apig服务的usecase
      queryAPIGUseCase({ apigServId: selectedAPIGServiceId });
    } else {
      // 没有数据，重置所有状态
      setSelectedServiceCode('');
      setSelectedServiceDetail({});
      setSelectedAPIGServiceDetail({});
      setSelectedAPIGUseCaseDetail({});
    }
  }, [selectedAPIGServiceId]);

  useEffect(() => {
    if (selectedServiceCode) {
      // 通过Service Code 查询debug信息
      queryServiceDetail({ serviceCode: selectedServiceCode });
    }
  }, [selectedServiceCode]);

  return (
    <Spin wrapperClassName={styles.spinContainer} spinning={loading}>
      <div className={styles.bssSearchUseCase}>
        <div className={styles.header}>
          <LeftOutlined className={styles.returnIcon} onClick={onClose} />
          <span className={styles.returnText} onClick={onClose}>
            {formatMessage('SERVICEUSECASELIST.TITLE.APIGSERVICEUSECASELIST')}
          </span>
        </div>

        <div className={styles.bottom}>
          <div className={styles.leftMenu}>
            <div className={styles.searchInput}>
              <Input
                placeholder={formatMessage('MENU.SEARCH.PLACEHOLDER')}
                onPressEnter={() => handleSearch(searchref?.current?.input?.value)}
                allowClear
                autoComplete="off"
                onChange={(e) => {
                  if (!e?.target?.value) {
                    handleSearch('');
                  }
                }}
                suffix={
                  <SearchOutlined
                    style={{ cursor: 'pointer' }}
                    onClick={() => handleSearch(searchref?.current?.input?.value)}
                  />
                }
                ref={searchref}
              />
            </div>
            <div className={styles.menuList}>
              <CustomerCollpaseForAPIGService
                searchvalue={searchvalue}
                catalogData={afterCleanAPIGServiceList}
                hasInitFirstSelect={hasInitFirstSelect}
                setHasInitFirstSelect={setHasInitFirstSelect}
                onItemChange={(menu) => {
                  setSelectedAPIGServiceId(menu?.clickSubCatalog?.apigServId);
                }}
                deleteApigUseCaseAndUpdateData={deleteApigUseCaseAndUpdateData}
              />
            </div>
          </div>

          <div className={styles.rightContent}>
            <div style={{ width: '99%', height: '100%' }}>{DebugServiceArea}</div>
          </div>
        </div>
      </div>
    </Spin>
  );
};

export default APIGService;
