import useI18n from '@/hooks/useI8n';
import { Form, Input, Checkbox, Drawer, Space, Button, Spin } from 'antd';
import styles from './index.less';
import React, { useEffect, useImperativeHandle, useState } from 'react';
import { saveAsUseCasePropsType } from '@/services';

interface caseModalPropsType {
  title: string;
  open: boolean;
  setOpen: (e: any) => void;
  initSeletedValue: any;
  disabled: boolean;
  loading: boolean;
  onUseCaseModalSend: (e: saveAsUseCasePropsType) => void;
}

const EditUseCaseDrawer = React.forwardRef(
  (
    {
      title = '',
      open = false,
      setOpen = () => {},
      initSeletedValue,
      disabled = false,
      onUseCaseModalSend = () => {},
      loading = false,
    }: caseModalPropsType,
    ref,
  ) => {
    const { formatMessage } = useI18n();
    const [form] = Form.useForm();
    const [submitSpinning, setSubmitSpinning] = useState<boolean>(loading);

    const onCancel = () => {
      setOpen(false);
      form.resetFields();
    };

    const onOk = () => {
      form.validateFields().then((values) => {
        setSubmitSpinning(true);
        try {
          onUseCaseModalSend({
            ...values,
            includeResp: values?.includeResp ? 'Y' : 'N',
            useCaseId: initSeletedValue?.useCaseId,
          });
        } finally {
          setSubmitSpinning(false);
        }
      });
    };

    useEffect(() => {
      // open切换,EditUseCaseDrawer并没有被销毁,故Form的initialValues不会随initSeletedValue更新,这里手动更新
      form.setFieldsValue({
        useCaseName: initSeletedValue?.useCaseName,
        includeResp: initSeletedValue?.includeResp === 'Y' ? true : false,
        comments: initSeletedValue?.comments,
      });
    }, [initSeletedValue]);

    useImperativeHandle(ref, () => ({
      onCancel,
    }));

    return (
      <Drawer
        title={title}
        open={open}
        onClose={submitSpinning ? () => {} : onCancel}
        width={window.innerWidth * 0.6}
        footer={
          <Space style={{ float: 'right' }}>
            <Spin spinning={submitSpinning}>
              <Button type="primary" onClick={() => onOk()}>
                Submit
              </Button>
            </Spin>
            <Button disabled={submitSpinning} onClick={onCancel}>
              Cancel
            </Button>
          </Space>
        }
      >
        <Form
          form={form}
          labelCol={{ span: 8 }}
          wrapperCol={{ span: 16 }}
          initialValues={{ ...initSeletedValue, includeResp: initSeletedValue?.includeResp === 'Y' ? true : false }}
        >
          <Form.Item
            label={formatMessage('DEBUGSERVICE.USECASEMODAL.NAME')}
            name="useCaseName"
            className={styles.formItemLabel}
            rules={[
              {
                required: true,
                message: formatMessage('DEBUGSERVICE.USECASEMODAL.NAMETIP'),
              },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label={formatMessage('DEBUGSERVICE.USECASEMODAL.SAVERESPONSEMSG')}
            name="includeResp"
            className={styles.formItemLabel}
            valuePropName="checked"
          >
            <Checkbox disabled={disabled} />
          </Form.Item>
          <Form.Item
            label={formatMessage('DEBUGSERVICE.USECASEMODAL.COMMENTS')}
            name="comments"
            className={styles.formItemLabel}
          >
            <Input.TextArea />
          </Form.Item>
        </Form>
      </Drawer>
    );
  },
);

export default EditUseCaseDrawer;
