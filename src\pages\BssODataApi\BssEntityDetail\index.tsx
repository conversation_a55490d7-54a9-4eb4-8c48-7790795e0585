import React, { useState, useLayoutEffect, forwardRef, useImperativeHandle, useEffect } from 'react';
import { queryEntityDetailByEntityKey } from '@/services';
import { IDomainEntityRel, IEntityDto, IServiceList } from '@/services/typing.d';
import EntityReleationShip from './EntityReleationShip';
import EntityDescription from './EntityDescription';
import EntityProperties from './EntityProperties';
import EntityEvents from './EntityEvents';
import EntityServiceList from '@/components/EntityServices';
import { Button, Checkbox, Spin } from 'antd';
import useI18n from '@/hooks/useI8n';
import styles from './index.less';
import { BYDOMAIN } from '@/constants';
import { useModel } from 'umi';

interface IEntityDetailRef {
  reload: () => void;
}

interface IEntityDetail {
  entityKey?: string;
  onSelectService?: (serviceCode: string) => void;
  onOtherEntityClick?: (entityKey: string) => void;
}

const BssEntityDetail: React.ForwardRefRenderFunction<IEntityDetailRef, IEntityDetail> = (props, ref) => {
  const { entityKey, onSelectService, onOtherEntityClick } = props;
  const { formatMessage } = useI18n();
  const { has9E } = useModel('useSystemConfigDataModel');

  const [domainEntityRelData, setDomainEntityRelData] = useState<Partial<IDomainEntityRel>>(); // entity relationship data
  const [entityDtoData, setEntityDto] = useState<Partial<IEntityDto>>(); // entity dto data
  const [serviceListData, setServiceListData] = useState<Array<IServiceList>>([]); // entity serviceList data
  const [eventListData, setEventListData] = useState<Array<IServiceList>>([]); // entity eventList data
  const [spinning, setSpinning] = useState(false); // service loading
  const [isShowDeprecatedData, setIsShowDeprecatedData] = useState<boolean>(false); // 是否展示过时数据

  // 查询entity detail
  const queryEntityDetail = async () => {
    if (!entityKey) return;
    setSpinning(true);
    try {
      const { data: { domainEntityRel = {}, entityDto = {}, serviceList = [] } = {} } =
        await queryEntityDetailByEntityKey({
          entityKey,
        });
      setDomainEntityRelData(domainEntityRel);
      setEntityDto(entityDto);
      setServiceListData(serviceList);
      setEventListData(entityDto?.events ? entityDto.events || [] : []);
      setSpinning(false);
      // 优化 切换菜单 重置垂直滚动条
      (document as any).getElementById('bssEntityDetailContent').scrollTop = 0;
    } catch (error) {
      setSpinning(false);
    }
  };

  // 查询entity detail
  const handleRequest = () => {
    if (entityKey) {
      queryEntityDetail();
    } else {
      setDomainEntityRelData({});
      setEntityDto({});
      setServiceListData([]);
      setEventListData([]);
    }
  };

  // catalog详情提供对外刷新
  useImperativeHandle(ref, () => ({
    reload: handleRequest,
  }));

  // 查询entity detail
  useLayoutEffect(() => {
    handleRequest();
  }, [entityKey]);

  // 打开 event rule action界面
  const handleOpenRuleActionClick = () => {
    if ((window?.parent as any)?.fish) {
      (window.parent as any).fish?.popupView({
        url: 'rlc/modules/rlc-action/views/ActionDataListView.js',
        width: '100%',
        height: document.body.clientHeight + 50,
        position: {
          my: 'top',
          at: 'center',
          of: window.parent,
          collision: 'fit',
        },
        viewOption: {
          confApp: 'ODH',
          extAction: 'Y', // 过滤ACTION_TYPE_ID或TOPIC_ID小于0的记录
        },
      });
    }
  };

  useEffect(() => {
    setIsShowDeprecatedData(false);
  }, [serviceListData]);

  return (
    <Spin spinning={spinning} wrapperClassName={styles.spinContainer}>
      <div className={styles.bssEntityDetailContent} id="bssEntityDetailContent">
        {/* entity releationShip */}
        {entityDtoData?.domainObjectType === 'A' && (
          <div>
            <span className={styles.title}>{formatMessage('DOMAINDETAIL.RELA.TITLE')}</span>
            <EntityReleationShip
              domainEntityRelData={domainEntityRelData}
              entityDtoData={entityDtoData}
              serviceListData={serviceListData}
              eventListData={eventListData as any}
            />
          </div>
        )}
        {/* entity description */}
        <div className={styles.blockContent}>
          <span className={styles.title}>{formatMessage('DOMAINDETAIL.DESC.TITLE')}</span>
          <EntityDescription
            domainEntityRelData={domainEntityRelData}
            entityDtoData={entityDtoData}
            onOtherEntityClick={(entityKey) => onOtherEntityClick?.(entityKey)}
          />
        </div>
        {/* entity properties */}
        {entityDtoData?.entityType !== 'S' && (
          <div className={styles.blockContent}>
            <span className={styles.title}>{formatMessage('DOMAINDETAIL.PROPERTIES.TITLE')}</span>
            <EntityProperties
              entityDtoData={entityDtoData}
              onOtherEntityClick={(entityKey) => onOtherEntityClick?.(entityKey)}
            />
          </div>
        )}
        {/* entity service list */}
        <div className={styles.blockContent}>
          <span className={styles.title}>{formatMessage('DOMAINDETAIL.SERVICES.TITLE')}</span>
          <span className={styles.showDeprecated}>
            <Checkbox
              checked={isShowDeprecatedData}
              onChange={(e) => {
                setIsShowDeprecatedData(e.target.checked);
              }}
            >
              {formatMessage('DOMAINDETAIL.SERVICES.SHOWDEPRECATED')}
            </Checkbox>
          </span>
          <EntityServiceList
            fromType={BYDOMAIN}
            isShowDeprecatedData={isShowDeprecatedData}
            serviceListData={serviceListData}
            onSelectService={(serviceCode) => onSelectService?.(serviceCode)}
          />
        </div>
        {/* Entity Events */}
        {has9E && (
          <div className={styles.blockContent}>
            <div className={styles.blockContentTitle}>
              <span className={styles.title}>{formatMessage('DOMAINDETAIL.SERVICES.EVENTS')}</span>
              <Button className={styles.eventRuleActionBtn} type="primary" onClick={handleOpenRuleActionClick}>
                {formatMessage('DOMAINDETAIL.EVENT_RULE_ACTION')}
              </Button>
            </div>
            {/* 这里是演示数据 等待需求完善接口后再改 */}
            <EntityEvents data={eventListData as any} />
          </div>
        )}
      </div>
    </Spin>
  );
};

export default forwardRef(BssEntityDetail);
