import { defineConfig } from 'umi';

// 模拟本地portal
const rewriteArray = [
  'frm',
  'styles',
  'config.js',
  'main.js',
  'sysparams',
  'prod',
  'qt-web',
  'modules',
  'retrievePwd',
  'dmc',
  'login',
  'sys',
  'callservice.json',
  'portals',
  'menus',
  'stafforg',
  'notification',
  'bulletins',
  'portal',
  'usersAndStaff',
  'preLogin',
];

const EnvType = {
  ZSmart: 'http://10.10.178.78:8086/',
  TM: 'http://10.10.194.74/',
  UM: 'http://172.16.83.203/',
  SF: 'http://10.10.186.105/', // 印尼
  z14: 'http://10.10.178.14/',
  bol: 'http://192.168.193.1/',
  z184: 'http://10.10.178.184/',
  V9DC: 'http://172.16.22.251:80/', // http://172.16.22.251/portal-web/#busi-201
  V81E: 'http://10.10.178.78:8086/',
  GOMO: 'http://10.10.184.196:1080/', // 新电
};

const curType = 'TM';
const local = 'http://10.45.216.107:8080/';

const getTarget = () => {
  return EnvType[curType];
};

let portalWebProxy: Record<string, Record<any, any>> = {};
rewriteArray.forEach((i) => {
  portalWebProxy[`/${i}`] = {
    // target: `http://10.45.216.107:8081/portal-web/`,
    target: `${getTarget()}portal-web/`, // 常规环境使用
    // target: `${getTarget()}`, // V81E环境专用
    changeOrigin: true,
  };
});

const proxy = {
  '/portal-web': {
    target: getTarget(),
    changeOrigin: true,
  },
  '/odh-web/odh': {
    target: getTarget(),
    // target: local,
    changeOrigin: true,
  },
  '/odh-service': {
    target: getTarget(),
    // target: local,
    changeOrigin: true,
  },
  '/odh-web/easycode': {
    target: getTarget(),
    changeOrigin: true,
  },
  ...portalWebProxy,
};

export default defineConfig({
  publicPath: process.env.NODE_ENV === 'production' ? './' : '/',
  history: {
    type: 'hash',
  },
  proxy,
  routes: [
    { path: '/', component: 'index' },
    { path: '/openDataApi', component: './BssODataApi', layout: false },
    { path: '/eventsDetail', component: './BssEventsDetail', layout: false },
    { path: '/openDomainApi', component: './BssODomainApi', layout: false },
    { path: '/openErrorCodeApi', component: './BssOErrorCodeApi', layout: false },
    { path: '/hotfixManagement/openDomainApiHotfix', component: './BssODomainApi', layout: false },
    { path: '/hotfixManagement/openErrorCodeApiHotfix', component: './BssOErrorCodeApi', layout: false },
  ],
  npmClient: 'yarn',
  plugins: [
    '@umijs/plugins/dist/request',
    '@umijs/plugins/dist/locale',
    '@umijs/plugins/dist/antd',
    '@umijs/plugins/dist/model',
  ],
  model: {},
  request: {},
  locale: {
    default: 'en-US', // 'zh-CN' ｜ 'en-US'默认使用英文，会在运行时被覆盖
    baseNavigator: false,
  },
  antd: {
    theme: {
      token: {
        borderRadius: 0,
        controlHeight: 28,
        fontFamily: 'Nunito Sans',
      },
    },
  },
  chainWebpack(config) {
    config.module.rule('otf').test(/.otf$/).use('file-loader').loader('file-loader');
  },
});
