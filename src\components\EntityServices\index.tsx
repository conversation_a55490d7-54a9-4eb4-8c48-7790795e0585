import React, { useEffect, useMemo, useState } from 'react';
import { IServiceList } from '@/services/typing.d';
import useI18n from '@/hooks/useI8n';
import styles from './index.less';
import { useStore } from '@/store';
import ResizableTable from '../ResizableTable';
import { useTableFilter } from '@/hooks/useTableFilter';
import classNames from 'classnames';
import { BYCATALOG } from '@/constants';

interface IEntityServiceList {
  fromType: string; // BYCATALOG | BYDOMAIN
  isShowDeprecatedData: boolean; // 是否展示过时的服务
  spining?: boolean;
  serviceListData?: Array<IServiceList>;
  onSelectService?: (serviceCode: string) => void;
}

const RenderServicePath: React.FC<IServiceList> = (props) => {
  const { serviceMethod, servicePath } = props;
  switch (serviceMethod) {
    case 'GET':
      return (
        <div className={styles.dyStyleWrapper} title={`${serviceMethod} ${servicePath}`}>
          <span className={styles.dyStyleGET}>{serviceMethod}</span>
          <span>{servicePath}</span>
        </div>
      );
    case 'POST':
      return (
        <div className={styles.dyStyleWrapper} title={`${serviceMethod} ${servicePath}`}>
          <span className={styles.dyStylePOST}>{serviceMethod}</span>
          <span>{servicePath}</span>
        </div>
      );
    case 'PUT':
      return (
        <div className={styles.dyStyleWrapper} title={`${serviceMethod} ${servicePath}`}>
          <span className={styles.dyStyleGET}>{serviceMethod}</span>
          <span>{servicePath}</span>
        </div>
      );
    case 'PATCH':
      return (
        <div className={styles.dyStyleWrapper} title={`${serviceMethod} ${servicePath}`}>
          <span className={styles.dyStylePATCH}>{serviceMethod}</span>
          <span>{servicePath}</span>
        </div>
      );
    case 'DELETE':
      return (
        <div className={styles.dyStyleWrapper} title={`${serviceMethod} ${servicePath}`}>
          <span className={styles.dyStyleDELETE}>{serviceMethod}</span>
          <span>{servicePath}</span>
        </div>
      );

    default:
      return (
        <div className={styles.dyStyleWrapper} title={`${serviceMethod} ${servicePath}`}>
          <span>{serviceMethod}</span>
          <span>{servicePath}</span>
        </div>
      );
  }
};

const EntityServiceList: React.FC<IEntityServiceList> = (props) => {
  const { setIsAICCTypeShow } = useStore();
  const { serviceListData = [], spining = false, onSelectService, isShowDeprecatedData = false, fromType = '' } = props;
  const { formatMessage } = useI18n();
  const [finalDataSource, setFinalDataSource] = useState<any[]>([]); // 最终展示的数据，包括条件过滤和是否展示过时数据
  // 使用 useMemo 确保 dataSource 是稳定的引用
  const memoizedDataSource = useMemo(() => (Array.isArray(serviceListData) ? serviceListData : []), [serviceListData]);
  // 自定义hooks，支持表格列筛选并分页展示
  const { filteredDataSource, getColumnSearchProps, clearAllFilters } = useTableFilter(memoizedDataSource);

  // 针对特殊表格列，自定义过滤逻辑
  const customCatalogFilter = (item: any, obj: any) => {
    if (item) {
      const showData = `${item?.catalog} -> ${item?.group} -> ${item?.subCatalog}`;
      return showData
        .toString()
        .toLowerCase()
        .includes((Object.values(obj)[1] as string).toLowerCase());
    }
    return true;
  };
  // 针对特殊表格列，自定义过滤逻辑
  const customServicePathFilter = (item: any, obj: any) => {
    if (item) {
      const showData = `${item?.serviceMethod}${item?.servicePath}`;
      return showData
        .toString()
        .toLowerCase()
        .includes((Object.values(obj)[1] as string).toLowerCase());
    }
    return true;
  };
  const columns = [
    {
      dataIndex: 'serviceName',
      title: formatMessage('PROJECT.COMMON.SERVICENAME'),
      width: '30%',
      ellipsis: true,
      ...getColumnSearchProps('serviceName'),
      render: (_: string, record: any) => (
        <div
          className={classNames(styles.servbiceName, { [styles.deprecated]: record?.deprecated === 'Y' })}
          onClick={() => {
            setIsAICCTypeShow(false);
            onSelectService?.(record?.serviceKey);
          }}
        >
          {record?.serviceName}
        </div>
      ),
    },
    {
      dataIndex: fromType === BYCATALOG ? 'serviceEntity' : 'subCatalog',
      title: formatMessage(fromType === BYCATALOG ? 'PROJECT.COMMON.DOMAINENTITY' : 'PROJECT.COMMON.SERVICECATALOG'),
      width: '40%',
      ellipsis: true,
      ...(fromType === BYCATALOG
        ? getColumnSearchProps('serviceEntity')
        : getColumnSearchProps('subCatalog', customCatalogFilter)),
      render: (_: string, record: any) =>
        fromType === BYCATALOG
          ? record?.serviceEntity
          : `${record?.catalog} -> ${record?.group} -> ${record?.subCatalog}`,
    },
    {
      dataIndex: 'servicePath',
      title: formatMessage('PROJECT.COMMON.SERVICEPATH'),
      width: '30%',
      ellipsis: true,
      ...getColumnSearchProps('servicePath', customServicePathFilter),
      render: (_: string, record: any) => <RenderServicePath {...record} />,
    },
  ];

  useEffect(() => {
    // 重置表格列的筛选条件
    clearAllFilters();
  }, [memoizedDataSource]);

  //控制是否展示过时数据
  useEffect(() => {
    if (filteredDataSource?.length) {
      if (isShowDeprecatedData) {
        setFinalDataSource(filteredDataSource);
      } else {
        const dataWithoutDeprecated = filteredDataSource.filter((item: any) => item?.deprecated !== 'Y');
        setFinalDataSource(dataWithoutDeprecated || []);
      }
    } else {
      setFinalDataSource([]);
    }
  }, [filteredDataSource, isShowDeprecatedData]);

  return (
    <ResizableTable
      size="small"
      style={{ marginTop: '12px' }}
      columns={columns}
      dataSource={finalDataSource?.map((item: any, index: any) => ({
        ...item,
        rowKey: `row_${index}_${item.serviceCode}`,
      }))}
      rowKey="rowKey"
      pagination={false}
      loading={spining}
    />
  );
};

export default EntityServiceList;
