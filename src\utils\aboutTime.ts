// 构建[n天前00:00:00, 当日23:59:59]时间范围
export const getTimeRange = (days: number) => {
  // 获取当前时间对象（保持本地时区）
  const now = new Date(); // 实际开发中应使用 new Date()

  // 构建结束时间（当日23:59:59）
  const endDate = new Date(now);
  endDate.setHours(23, 59, 59, 999); // 精确到毫秒级

  // 构建开始时间（n天前00:00:00）
  const startDate = new Date(now);
  startDate.setDate(now.getDate() - days);
  startDate.setHours(0, 0, 0, 0);

  // 日期格式化函数（兼容单数字补零）
  const formatDate = (date: Date) => {
    const pad = (n: number) => n.toString().padStart(2, '0');
    return (
      `${date.getFullYear()}-${pad(date.getMonth() + 1)}-${pad(date.getDate())} ` +
      `${pad(date.getHours())}:${pad(date.getMinutes())}:${pad(date.getSeconds())}`
    );
  };

  return [formatDate(startDate), formatDate(endDate)];
};

 // 日期格式化工具函数（兼容本地时区）
export const formatLocalDate = (date: Date): string => {
  const padZero = (num: number) => num.toString().padStart(2, '0');
  return [
    [date.getFullYear(), padZero(date.getMonth() + 1), padZero(date.getDate())].join('-'),
    [padZero(date.getHours()), padZero(date.getMinutes()), padZero(date.getSeconds())].join(':'),
  ].join(' ');
};
