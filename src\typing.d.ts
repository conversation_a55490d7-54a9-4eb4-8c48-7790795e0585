import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ICatalog,
  ISubCatalog<PERSON>ist,
  ICatalogGroupList,
  IAPIGCatalog,
  IAPIGCatalogGroupList,
  IAPIGSubCatalogList
} from "@/services/typing.d";

export interface ICurrentActiveKey {
  clickGroup?: IGroupList;
  clickDomain?: IDomainList;
  clickEntity?: IEntityList;
}

export interface ICurrentActiveKeyCatalog {
  clickCatalog?: ICatalog;
  clickGroup?: ICatalogGroupList;
  clickSubCatalog?: ISubCatalogList;
}

export interface ICurrentActiveKeyAPIGCatalog {
  clickCatalog?: IAPIGCatalog;
  clickMenuPath?: string[];
  clickSubCatalog?: IAPIGSubCatalogList;
}

export interface ITableDataInterface {
  rowKey: string;
  in: 'H' | 'P' | 'Q' | 'B' | 'C';
  name: string;
  type: string;
  required: string;
  hidden?: string;
  example: string;
  avlValue: string;
  comments: string;
  isArray: string;
  value?: any;
  typeFormat?: string;
  dataModel?: any;
  children?: Array<ITableDataInterface>;
  contentType?: string;
}

export type RequestTableProps = {
  tableData: ITableDataInterface[];
  debugMode?: boolean;
  type?: string;
  updataRequestParamsFn?: (e: any) => void;
};

export interface breadCrumbsType{
  level?: number;
  title?: string;
  key?: string;
  id?: string | number;
  itemData?: any;
  isShow?: boolean;
  className?: string;
  onClick?: () => void;
};
