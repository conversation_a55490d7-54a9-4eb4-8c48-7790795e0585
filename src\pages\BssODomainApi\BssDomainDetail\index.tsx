import React, { useEffect, useRef, useState } from 'react';
import { Input, Select, Popconfirm, message, Space, Button, DatePicker, InputNumber, Tooltip, Dropdown } from 'antd';
import type { InputRef, PopconfirmProps } from 'antd';
import moment from 'moment';
import {
  FormOutlined,
  PlusOutlined,
  DownloadOutlined,
  ShopOutlined,
  SyncOutlined,
  DeleteOutlined,
  LogoutOutlined,
  ArrowRightOutlined,
  SearchOutlined,
  UploadOutlined,
  FieldTimeOutlined,
  CaretDownOutlined,
} from '@ant-design/icons';
import classNames from 'classnames';
import useI18n from '@/hooks/useI8n';
import {
  deleteDomainObject,
  qryDomainReleasedList,
  changeDomainStateToD,
  queryODomainObj,
  qryReleasedInfoByDomainId,
  getCurrentTestSeq,
  getRunningVersion,
  getTestVersion,
  switchDomainTestVersionNew,
  getDomainVersion,
} from '@/services/domainService';
import styles from './index.less';
import ChangeDomainStateModel from './ChangeDomainState';
import DownloadVersionModel from './DownloadVersion';
import AddDomainObjectDrawer from './AddDomainObject';
import EditDomainObjectDrawer from './EditDomainObject';
import ChangeVersionModal from './ChangeVersion';
import OperationIconTitle from '@/components/OperationIconTitle';
import myIcon from '@/static/iconfont/iconfont.css';
import ResizableTable from '@/components/ResizableTable';
import dayjs from 'dayjs';
import ImportFileModal from '@/components/ImportFile';
import { clearRedis, refreshCache } from '@/services';
import { isHotfix } from '@/global';
import { useModel } from 'umi';

interface IBssDomainDetail {
  selectedDomainNode?: any;
  isProduct: boolean;
  ignoreVersionSource: any;
  onSelectDomainObj: (domainObjData: any) => void; // domain 选择
  onRefresh: (currentSelectKey: string) => void; // 刷新缓存 通知上级刷新缓存
  setPageLoading: (loading: boolean) => void; // 页面loading
}
interface ISearchObj {
  version?: number;
  releaseNote?: string;
  releaseBeginTime?: string;
  releaseEndTime?: string;
}

const BssDomainDetail: React.FC<IBssDomainDetail> = (props) => {
  const { selectedDomainNode, ignoreVersionSource, isProduct, onSelectDomainObj, onRefresh, setPageLoading } = props;

  const { formatMessage } = useI18n();
  const { isConfigEnvironment } = useModel('useSystemConfigDataModel');
  const [openChangeDomainStateModel, setOpenChangeDomainStateModel] = useState<boolean>(false); // change domain state
  const [openDownloadModel, setOpenDownloadModel] = useState<boolean>(false); // download版本
  const [openAddDomainObjectDrawer, setOpenAddDomainObjectDrawer] = useState<boolean>(false); // 新增领域对象
  const [openEditDomainObjectDrawer, setOpenEditDomainObjectDrawer] = useState<boolean>(false); //修改领域对象
  const [openChangeVersionModal, setOpenChangeVersionModal] = useState<boolean>(false); // 切换版本
  const [selectedDomainObj, setSelectedDomainObj] = useState<any>({}); // 当前选择的领域对象行数据
  const [selectedVersionObj, setSelectedVersionObj] = useState<any>({}); // 当前选择的版本对象行数据
  const [importFileVisible, setImportFileVisible] = useState<boolean>(false); // 导入文件开关
  const [syncFlag, setSyncFlag] = useState<boolean>(false);
  const [pendingVersion, setPendingVersion] = useState<string>('');
  const [runningVersion, setRunningVersion] = useState<string>('');
  const [testingVersion, setTestingVersion] = useState<string>('');

  const [domainNode, setDomainNode] = React.useState<object>(); //

  let isTestingVersionMatched = false;
  let isRunningVersionMatched = false;
  let isPendingVersionMatched = false;

  const isRefreshTMFShow =
    selectedDomainNode?.appCode === 'custc' ||
    selectedDomainNode?.appCode === 'coc' ||
    selectedDomainNode?.appCode === 'test';

  const ODomainStateType = [
    {
      name: formatMessage('DOMAIN.STATE.DRAFT'),
      key: 'D',
    },
    {
      name: isHotfix ? formatMessage('DOMAIN.STATE.RELEASED') + ' Hotfix' : formatMessage('DOMAIN.STATE.RELEASED'),
      key: 'R',
    },
  ];

  const RefreshCacheType = [
    {
      key: 'O',
      label: 'Open Data API',
    },
    {
      key: 'D',
      label: 'Domain Dubbo',
    },
    {
      key: 'T',
      label: 'Domain Model Table',
    },
    isRefreshTMFShow
      ? {
          key: 'TFM',
          label: 'TFM Service',
        }
      : null,
  ];

  const TYPESOURCE = [
    {
      name: 'Aggregate',
      key: 'A',
    },
    {
      name: 'Collection',
      key: 'C',
    },
  ];

  const CreationSource = [
    {
      name: 'Product-Owned',
      key: 'P',
    },
    {
      name: 'Project-Customized',
      key: 'C',
    },
  ];
  const [selectDomainState, setSelectDomainState] = useState<string>(ODomainStateType[0].key); // draf or released
  const [domainStateStyle, setDomainStateStyle] = useState<string>('topContentDraf');
  // 表格，当前用于查询的信息
  const [infoUsedforSearching, setInfoUsedforSearching] = useState<any>({
    pageNo: 1,
    pageSize: 5,
  });
  const [versionSource, setVersionSource] = useState<any>({});
  const [domainTableData, setDomainTableData] = useState<any>([]);

  const searchInput = useRef<InputRef>(null);
  const [searchObj, setSearchObj] = useState<ISearchObj>();

  // 表格列查询按钮
  const handleSearch = (selectedKeys: string[], confirm: () => void, dataIndex: any) => {
    confirm();
    let searchParams = {};
    if (dataIndex === 'releasedDate') {
      searchParams = {
        ...searchObj,
        releaseBeginTime: selectedKeys[0],
        releaseEndTime: selectedKeys[1],
      };
    } else {
      searchParams = {
        ...searchObj,
        [dataIndex]: selectedKeys[0],
      };
    }
    setSearchObj(searchParams);
  };
  // 表格列重置按钮
  const handleReset = (clearFilters: () => void) => {
    clearFilters();
  };

  const getColumnSearchProps = (dataIndex: any) => ({
    filterDropdown: ({ setSelectedKeys, selectedKeys, confirm, clearFilters }: any) => (
      <div style={{ padding: 8 }} onKeyDown={(e) => e.stopPropagation()}>
        {dataIndex !== 'releasedDate' && (
          <Input
            ref={searchInput}
            placeholder={`${formatMessage('DOMAIN.SEARCH')} ${dataIndex}`}
            value={selectedKeys[0]}
            onChange={(e) => setSelectedKeys(e.target.value ? [e.target.value] : [])}
            onPressEnter={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
            style={{ marginBottom: 8, display: 'block' }}
          />
        )}
        {dataIndex === 'releasedDate' && (
          <DatePicker.RangePicker
            allowClear={false}
            showTime
            format="YYYY-MM-DD HH:mm:ss"
            value={
              selectedKeys.length === 2
                ? [dayjs(selectedKeys[0], 'YYYY-MM-DD HH:mm:ss'), dayjs(selectedKeys[1], 'YYYY-MM-DD HH:mm:ss')]
                : [null, null]
            }
            onChange={(dates, dateStrings) => setSelectedKeys(dateStrings)}
            style={{ marginBottom: 8, display: 'block' }}
          />
        )}
        <Space>
          <Button onClick={() => clearFilters && handleReset(clearFilters)} size="small" style={{ width: 90 }}>
            {formatMessage('DOMAIN.RESET')}
          </Button>
          <Button
            type="primary"
            onClick={() => handleSearch(selectedKeys as string[], confirm, dataIndex)}
            icon={<SearchOutlined />}
            size="small"
            style={{ width: 90 }}
          >
            {formatMessage('DOMAIN.SEARCH')}
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered: boolean) => <SearchOutlined style={{ color: filtered ? '#1677ff' : undefined }} />,
  });

  //领域对象表格
  const columns = [
    {
      dataIndex: 'domainObjName',
      title: formatMessage('PROJECT.COMMON.NAME'),
      width: '15%',
      ellipsis: true,
      render: (_: string, record: any) => (
        <div
          className={styles.domainObjName}
          onClick={() => {
            onSelectDomainObj?.(record);
          }}
        >
          {record?.domainObjName}
        </div>
      ),
    },
    {
      dataIndex: 'domainObjCode',
      title: formatMessage('PROJECT.COMMON.CODE'),
      width: '10%',
      ellipsis: true,
    },
    {
      dataIndex: 'domainObjType',
      title: formatMessage('PROJECT.COMMON.TYPE'),
      width: '15%',
      ellipsis: true,
      render: (_: string, record: any) => {
        return TYPESOURCE.filter((i) => i.key === record.domainObjType).map((i) => i.name);
      },
    },
    {
      dataIndex: 'creationSrc',
      title: formatMessage('PROJECT.COMMON.CREATIONSOURCE'),
      width: '15%',
      ellipsis: true,
      render: (_: string, record: any) => {
        return CreationSource.filter((i) => i.key === record.creationSrc).map((i) => i.name);
      },
    },
    // {
    //   dataIndex: 'aggregateRootEntity',
    //   title: 'Aggregate Root Entity',
    //   width: '15%',
    //   ellipsis: true,
    //   render: (_: string, record: any) => (
    //     <div
    //       className={styles.domainObjName}
    //       onClick={() => {
    //         onSelectDomainObj?.(record.aggregateRootEntity);
    //       }}
    //     >
    //       {record?.aggregateRootEntity?.entityName}
    //     </div>
    //   ),
    // },
    {
      dataIndex: 'comments',
      title: formatMessage('PROJECT.COMMON.DESCRIPTION'),
      width: '35%',
      ellipsis: true,
    },
    {
      dataIndex: '',
      title: formatMessage('PROJECT.COMMON.OPERATION'),
      width: '10%',
      ellipsis: true,
      render: (_: string, record: any) => {
        return (
          <div>
            <Tooltip title={formatMessage('PROJECT.COMMON.EDIT')}>
              <FormOutlined
                className={classNames(styles.iconStyle, {
                  [styles.hide]: selectDomainState !== 'D',
                })}
                onClick={() => {
                  setOpenEditDomainObjectDrawer(true);
                }}
              />
            </Tooltip>
            <Popconfirm
              title={formatMessage('DOMAIN.DELETE.OBJECT.TITLE')}
              description={formatMessage('DOMAIN.DELETE.OBJECT.DESCRIPTION')}
              onConfirm={deleteDomainObj}
              okText={formatMessage('PROJECT.COMMON.CONFIRM')}
              cancelText={formatMessage('PROJECT.COMMON.CANCEL')}
            >
              {isProduct ? (
                <Tooltip title={formatMessage('PROJECT.COMMON.DELETE')}>
                  <DeleteOutlined
                    className={classNames(styles.versionIcon2, {
                      [styles.hide]: selectDomainState !== 'D',
                    })}
                    onClick={() => {}}
                  />
                </Tooltip>
              ) : (
                <Tooltip title={formatMessage('PROJECT.COMMON.DELETE')}>
                  <DeleteOutlined
                    className={classNames(styles.versionIcon2, {
                      [styles.hide]: record?.creationSrc === 'P' || selectDomainState !== 'D',
                    })}
                    onClick={() => {}}
                  />
                </Tooltip>
              )}
            </Popconfirm>
          </div>
        );
      },
    },
  ];

  // 获取版本号
  const getVersionWithoutHotfix = (version: string) => {
    return version?.split(',')[0];
  };

  // 获取热更新版本号
  const getHotfixVersion = (version: string) => {
    return version?.split(',')[1];
  };

  //领域版本表格
  const versionColumns = [
    {
      dataIndex: 'seq',
      title: formatMessage('DOMAIN.VERSION'),
      width: '10%',
      ellipsis: true,
      ...getColumnSearchProps('version'),
      filteredValue: searchObj?.version ? [searchObj?.version] : null,
      render: (_: string, record: any) => {
        // 无论是否是hotfix界面，testingVersion等都是返回的一样的数据（和自己相比，例如若hotfix界面testingVersion返回的是184,4，则非hotfix界面testingVersion返回的也是184,4），故需要完全由前端来处理两个界面的版本号显示、版本标志显示、版本标志的颜色控制、提示信息等
        const { seq = '', hotfixSeq = '' } = record;
        // 处理当前记录的版本号
        const versionIsWithHotfix = hotfixSeq ? `${seq},${hotfixSeq}` : `${seq}`;

        // 创建图标渲染函数
        const renderVersionIcon = (version: string, versionType: 'test' | 'used' | 'pending') => {
          // 检查当前版本是否匹配
          if (!version || versionIsWithHotfix !== (hotfixSeq ? version : getVersionWithoutHotfix(version))) {
            return null;
          }

          // 计算提示文本和样式
          const baseVersion = getVersionWithoutHotfix(version);
          const hotfixVersion = getHotfixVersion(version);
          const isHotfixVersion = !hotfixSeq && hotfixVersion && baseVersion === `${seq}`;

          // 根据版本类型选择不同样式和提示文本，非hotfix界面，可能也存在'34,1'这种发布记录，所以需要判断是否已经匹配过
          if (versionType === 'test' && !isTestingVersionMatched) {
            isTestingVersionMatched = true;
            const tooltipTitle = `${formatMessage('DOMAINDETAIL.VERSION.TEST.TITLE')}${
              isHotfixVersion ? `${formatMessage('DOMAINDETAIL.VERSION.ISHOTFIX')}${seq}(${hotfixVersion})` : ''
            }`;

            return (
              <Tooltip title={tooltipTitle}>
                <span
                  className={classNames(`${myIcon.iconfont} ${myIcon['icon-test_version']} ${styles.versionIcon1}`, {
                    [styles.hotfixVersionColor]: isHotfixVersion,
                  })}
                ></span>
              </Tooltip>
            );
          } else if (versionType === 'used' && !isRunningVersionMatched) {
            isRunningVersionMatched = true;
            const tooltipTitle = `${formatMessage('DOMAINDETAIL.VERSION.USED.TITLE')}${
              isHotfixVersion ? `${formatMessage('DOMAINDETAIL.VERSION.ISHOTFIX')}${seq}(${hotfixVersion})` : ''
            }`;

            return (
              <Tooltip title={tooltipTitle}>
                <span
                  className={classNames(
                    `${myIcon.iconfont} ${myIcon['icon-current_version2']} ${styles.versionIcon2}`,
                    {
                      [styles.hotfixVersionColor]: isHotfixVersion,
                    },
                  )}
                ></span>
              </Tooltip>
            );
          } else if (versionType === 'pending' && testingVersion !== pendingVersion && !isPendingVersionMatched) {
            isPendingVersionMatched = true;
            return (
              <Tooltip title={formatMessage('DOMAINDETAIL.VERSION.PENDING.TITLE')}>
                <FieldTimeOutlined className={`${styles.versionIcon3}`} />
              </Tooltip>
            );
          }

          return null;
        };

        return (
          <div>
            {seq}
            {hotfixSeq ? `(${hotfixSeq})` : ''}
            {renderVersionIcon(testingVersion, 'test')}
            {renderVersionIcon(runningVersion, 'used')}
            {renderVersionIcon(pendingVersion, 'pending')}
          </div>
        );
      },
    },
    {
      dataIndex: 'releasedDate',
      title: formatMessage('DOMAIN.RELEASED.DATE'),
      width: '20%',
      ellipsis: true,
      render: (value: string, record: any) => {
        if (value) {
          return moment(value).format('YYYY-MM-DD HH:mm:ss');
        }
      },
      ...getColumnSearchProps('releasedDate'),
      filteredValue:
        searchObj?.releaseBeginTime && searchObj?.releaseEndTime
          ? [searchObj?.releaseBeginTime, searchObj?.releaseEndTime]
          : null,
    },
    {
      dataIndex: 'releasedByName',
      title: formatMessage('DOMAIN.OPERATOR'),
      width: '10%',
      ellipsis: true,
    },
    {
      dataIndex: 'comments',
      title: formatMessage('DOMAIN.RELEASED.NOTES'),
      width: '40%',
      ellipsis: true,
      ...getColumnSearchProps('releaseNote'),
      filteredValue: searchObj?.releaseNote ? [searchObj?.releaseNote] : null,
    },
    {
      dataIndex: '',
      title: formatMessage('PROJECT.COMMON.OPERATION'),
      width: '10%',
      ellipsis: true,
      render: (_: string, record: any) => {
        const { seq = '', hotfixSeq = '' } = record;
        const versionIsWithHotfix = hotfixSeq ? `${seq},${hotfixSeq}` : `${seq}`;
        return (
          <div>
            <Tooltip title={formatMessage('PROJECT.COMMON.DOWNLOAD')}>
              <DownloadOutlined
                className={styles.iconStyle}
                onClick={() => {
                  setOpenDownloadModel(true);
                }}
              />
            </Tooltip>

            {/* 切换版本确认框 */}
            <Popconfirm
              title={formatMessage('DOMAIN.CHANGE.VERSION')}
              description={
                <>
                  <span>{formatMessage('DOMAIN.CHANGE.VERSION.CONFIRM')}</span>
                  <div>
                    <input
                      type="checkbox"
                      checked={syncFlag}
                      onChange={() => setSyncFlag((prevAsyncFlag) => !prevAsyncFlag)}
                    />
                    <span style={{ marginLeft: '5px' }}>{formatMessage('DOMAIN.CHANGE.VERSION.NO_RESTART')}</span>
                  </div>
                </>
              }
              onConfirm={changeVersionConfirm}
              okText={formatMessage('PROJECT.COMMON.CONFIRM')}
              cancelText={formatMessage('PROJECT.COMMON.CANCEL')}
            >
              <Tooltip title={formatMessage('PROJECT.COMMON.CHANGEVERSION')}>
                <span
                  onClick={() => setSyncFlag(false)}
                  className={classNames(
                    styles.iconStyle,
                    styles.selectContent,
                    {
                      [styles.hide]: versionIsWithHotfix === pendingVersion,
                    },
                    myIcon.iconfont,
                    myIcon['icon-switch_version2'],
                  )}
                />
              </Tooltip>
            </Popconfirm>
          </div>
        );
      },
    },
  ];

  // 根据按钮组的选择刷新对应缓存
  const handleRefreshCache = async (key: string) => {
    setPageLoading(true);
    try {
      let response;
      if (key === 'O') {
        response = await refreshCache();
      } else {
        const params = key === 'D' ? { type: key } : { type: key, id: selectedDomainNode?.domainId };
        response = await clearRedis(params);
      }
      setPageLoading(false);

      const { success } = response;
      if (success) {
        message.success(formatMessage('DOMAIN.REFRESH.CACHE.SUCCESS'));
        onRefresh(selectedDomainNode?.key);
      } else {
        message.error(formatMessage('DOMAIN.REFRESH.CACHE.FAILED'));
      }
    } catch (error) {
      setPageLoading(false);
    }
  };

  const getVersionData = async (currentSeq?: any) => {
    setPageLoading(true);
    try {
      const param = {
        ...infoUsedforSearching,
        domainId: selectedDomainNode?.domainId,
        ...(searchObj || {}),
      };

      await qryDomainReleasedList(param).then((res) => {
        const { success, data } = res;
        if (success) {
          setVersionSource(data);
        } else {
          setVersionSource({});
        }
      });
    } finally {
      setPageLoading(false);
    }
  };

  // 创建通用的版本查询函数
  const queryVersion = async (
    queryFn: Function,
    params: object,
    setStateFn: React.Dispatch<React.SetStateAction<string>>,
    errorMsg: string = 'Error fetching version',
  ) => {
    try {
      const { success, data } = await queryFn(params);
      if (success && data) {
        // 兼容老版本,老版本data返回的是数字，新版本data返回的是字符串，且包含hotfix版本，例如'184,4'
        setStateFn(`${data}`);
      } else {
        setStateFn('');
      }
    } catch (error) {
      console.error(errorMsg, error);
      setStateFn('');
    }
  };

  // 查询当前未生效版本
  const queryDomainVersion = async (artifactId: string) => {
    if (artifactId !== '') {
      await queryVersion(getDomainVersion, { artifactId }, setPendingVersion, 'Error fetching domain version');
    }
  };

  // 查询测试版本
  const queryTestVersion = async () => {
    await queryVersion(
      getTestVersion,
      { appCode: selectedDomainNode?.appCode },
      setTestingVersion,
      'Error fetching test version',
    );
  };

  // 查询在用版本
  const queryRunningVersion = async () => {
    await queryVersion(
      getRunningVersion,
      {
        appContextPath: selectedDomainNode?.appContextPath || 'odh-service',
        appCode: selectedDomainNode?.appCode,
      },
      setRunningVersion,
      'Error fetching running version',
    );
  };

  const queryAllVersionFlag = async () => {
    //获取当前ArtifactId
    const { data } = await qryReleasedInfoByDomainId({ domainId: selectedDomainNode?.domainId });
    // 并行执行所有查询提高效率
    await Promise.all([
      data?.artifactId !== '' ? queryDomainVersion(data?.artifactId) : Promise.resolve(),
      queryTestVersion(),
      queryRunningVersion(),
    ]);
  };

  // 完整初始化数据函数（仅在首次选择领域时调用）
  const initAllData = async () => {
    setPageLoading(true);
    try {
      // 查询领域对象
      await initDomainTableData();
      await getVersionData();
      await queryAllVersionFlag();
    } finally {
      setPageLoading(false);
    }
  };

  const initDomainTableData = async () => {
    setPageLoading(true);
    try {
      if (selectedDomainNode?.domainId) {
        const { success, data } = await queryODomainObj(selectedDomainNode?.domainId);
        if (success) {
          setDomainTableData(data);
        } else {
          setDomainTableData([]);
        }
      }
    } finally {
      setPageLoading(false);
    }
  };

  const changeVersionConfirm: PopconfirmProps['onConfirm'] = async (e) => {
    try {
      const { seq, hotfixSeq } = selectedVersionObj;
      const param = {
        domainId: selectedVersionObj?.domainId,
        seq,
        autoRestart: syncFlag ? 'N' : 'Y',
        hotfixSeq: hotfixSeq || 0,
      };
      const { success = false } = await switchDomainTestVersionNew(param);
      if (success) {
        if (syncFlag) {
          setPendingVersion(hotfixSeq ? `${seq},${hotfixSeq}` : `${seq}`);
        } else {
          setOpenChangeVersionModal(true);
        }
      }
    } catch (error) {
      console.error('Error changing version:', error);
    }
  };

  const deleteDomainObj: PopconfirmProps['onConfirm'] = async (e) => {
    const domainObjId = selectedDomainObj?.domainObjId;
    try {
      const resultData = await deleteDomainObject(domainObjId);

      if (resultData?.success) {
        message.success(formatMessage('DOMAIN.DELETE.OBJECT.SUCCESS'));
        //删除领域对象后重新渲染数据
        onRefresh(selectedDomainNode?.key);
        initDomainTableData();
      }
    } catch (error) {
      console.error('Error deleting domain object:', error);
    }
  };

  //修改领域状态从R到D
  const changeStateRToD = async () => {
    try {
      if (!domainNode?.domainId) return;

      const resultData = await changeDomainStateToD({
        domainId: domainNode.domainId,
      });
      if (resultData?.success) {
        setSelectDomainState('D');
        //切换领域后刷新
        onRefresh(selectedDomainNode?.key);
      } else {
        setSelectDomainState('R');
      }
    } catch (error) {
      setSelectDomainState('R');
      console.error('Error changing domain state:', error);
    }
  };

  const handleOnChangeStateType = (value: string) => {
    if (value === 'D') {
      //当领域从released状态切换到draft状态
      changeStateRToD();
    } else if (value === 'R') {
      //当领域从Draft切换到released,打开弹窗
      setOpenChangeDomainStateModel(true);
    }
  };

  useEffect(() => {
    setDomainNode(selectedDomainNode);
    setSelectDomainState(selectedDomainNode?.state);
    // 初始化所有数据，包含获取ArtifactId
    initAllData();
    setSearchObj({});
  }, [selectedDomainNode]);

  //分页查询版本信息
  useEffect(() => {
    getVersionData();
  }, [infoUsedforSearching]);

  useEffect(() => {
    if (selectDomainState === 'D') {
      setDomainStateStyle('topContentDraf');
    } else {
      setDomainStateStyle('topContentRelease');
    }
  }, [selectDomainState]);

  return (
    <div className={styles.activeLineContainer}>
      {/* 头部区域 */}
      {selectedDomainNode?.path?.length > 0
        ? selectedDomainNode.path.map((item: any) => {
            return (
              <div className={styles[domainStateStyle]}>
                {item.level === 1 ? (
                  <>
                    <span className={styles.contentName}>{item.name}</span>
                    <Select
                      className={styles.selectContent}
                      style={{ width: isHotfix ? '150px' : '115px' }}
                      disabled={item.isDisable || !isConfigEnvironment} // 在使用环境不能切换领域的状态
                      value={selectDomainState}
                      onChange={handleOnChangeStateType}
                    >
                      {ODomainStateType.map((i) => (
                        <Select.Option key={i.key} value={i.key}>
                          {`${i.name}`}
                        </Select.Option>
                      ))}
                    </Select>
                    {/* 暂时写死只有V9DC环境才不能导入 */}
                    {window.location.hostname !== '*************' && (
                      <Button
                        className={styles.selectContent}
                        style={{
                          width: isHotfix ? '150px' : '115px',
                          borderRadius: '5px',
                          ...(isHotfix ? { color: 'red', borderColor: 'red' } : {}),
                        }}
                        onClick={() => setImportFileVisible(true)}
                      >
                        <UploadOutlined />
                        <span style={{ fontWeight: 'bold' }}>
                          {isHotfix ? formatMessage('DOMAIN.IMPORT.HOTFIX') : formatMessage('DOMAIN.IMPORT')}
                        </span>
                      </Button>
                    )}
                    <Dropdown.Button
                      className={styles.selectContent}
                      style={{ width: '115px' }}
                      menu={{
                        items: RefreshCacheType,
                        onClick: (item) => {
                          handleRefreshCache(item?.key);
                        },
                      }}
                      buttonsRender={() => [
                        <Button style={{ borderRadius: '5px 0 0 5px' }}>
                          {formatMessage('DOMAIN.REFRESH.CACHE')}
                        </Button>,
                        <Button style={{ borderRadius: '0 5px 5px 0' }}>
                          <CaretDownOutlined />
                        </Button>,
                      ]}
                    />
                  </>
                ) : (
                  <>
                    <ArrowRightOutlined className={styles.arrowRight} />
                    <span>{item.name}</span>
                  </>
                )}
              </div>
            );
          })
        : null}
      {/* 领域对象列表 */}
      <div className={styles.domainObjContent}>
        <OperationIconTitle
          title={formatMessage('DOMAIN.OBJECT.LIST')}
          type={selectDomainState === 'D' ? 'add' : ''}
          opt={formatMessage('PROJECT.COMMON.ADD')}
          handleClick={() => {
            setOpenAddDomainObjectDrawer(true);
          }}
        />
        <div className={styles.content}>
          <ResizableTable
            size="small"
            style={{ marginTop: '12px' }}
            columns={columns}
            dataSource={domainTableData || []}
            rowKey={(record: any) => record?.domainObjId}
            pagination={false}
            onRow={(record: any) => ({
              onClick: () => {
                setSelectedDomainObj(record);
              },
            })}
          />
        </div>
      </div>
      {/* 领域版本列表 */}
      <div className={styles.domainObjContent}>
        <OperationIconTitle title={formatMessage('DOMAIN.RELEASED.VERSIONS')} />
        <div className={styles.content}>
          <ResizableTable
            size="small"
            style={{ marginTop: '12px' }}
            columns={versionColumns}
            dataSource={(versionSource?.list || []).sort((a: any, b: any) => b.seq - a.seq)}
            rowKey={(record: any) => record?.domainObjId}
            pagination={{
              hideOnSinglePage: true,
              total: versionSource?.totalCount,
              current: versionSource?.pageNum,
              pageSize: versionSource?.pageSize as any,
              showTotal: (total) => formatMessage('PROJECT.COMMON.PAGINATION.TOTAL', { total }),
              onChange: (page: any, pageSize: any) => {
                setInfoUsedforSearching({
                  ...infoUsedforSearching,
                  pageSize,
                  pageNo: page,
                });
              },
            }}
            onRow={(record: any) => ({
              onClick: () => {
                setSelectedVersionObj(record);
              },
            })}
          />
        </div>
      </div>
      {/* 修改领域状态 */}
      <ChangeDomainStateModel
        selectDomainState={selectDomainState}
        open={openChangeDomainStateModel}
        onCancel={() => {
          setOpenChangeDomainStateModel(false);
        }}
        onOk={() => {
          setOpenChangeDomainStateModel(false);
          onRefresh(selectedDomainNode?.key);
          setSelectDomainState('R');
          // 发布成功后重新查询
          setInfoUsedforSearching({
            ...infoUsedforSearching,
            pageNo: 1,
          });
        }}
        domainId={domainNode?.domainId}
        selectedDomainNode={selectedDomainNode}
      />
      {/* 下载版本 */}
      <DownloadVersionModel
        selectedVersionObj={selectedVersionObj}
        open={openDownloadModel}
        onCancel={() => setOpenDownloadModel(false)}
        onOk={() => {
          setOpenDownloadModel(false);
          message.open({
            type: 'success',
            content: formatMessage('DOMAIN.DOWNLOAD.SUCCESS'),
          });
        }}
      />
      {/* 新增领域对象 */}
      <AddDomainObjectDrawer
        ignoreVersionSource={ignoreVersionSource}
        open={openAddDomainObjectDrawer}
        domainId={domainNode?.domainId}
        onCancel={() => setOpenAddDomainObjectDrawer(false)}
        isProduct={isProduct}
        onOk={() => {
          //新增对象成功后操作
          setOpenAddDomainObjectDrawer(false);
          onRefresh(selectedDomainNode?.key);
          initDomainTableData();
        }}
      />
      {/* 修改领域对象 */}
      <EditDomainObjectDrawer
        ignoreVersionSource={ignoreVersionSource}
        open={openEditDomainObjectDrawer}
        onCancel={() => setOpenEditDomainObjectDrawer(false)}
        domainId={domainNode?.domainId}
        initValue={selectedDomainObj}
        isProduct={isProduct}
        onOk={() => {
          //修改对象成功后操作
          setOpenEditDomainObjectDrawer(false);
          onRefresh(selectedDomainNode?.key);
          initDomainTableData();
        }}
      />
      {/* 确认切换版本*/}
      <ChangeVersionModal
        selectedDomainNode={selectedDomainNode}
        startStep={2}
        open={openChangeVersionModal}
        onCancel={() => setOpenChangeVersionModal(false)}
        onOk={() => {
          //成功切换版本后，刷新数据
          onRefresh(selectedDomainNode?.key);
          setOpenChangeVersionModal(false);
          setInfoUsedforSearching({
            ...infoUsedforSearching,
            pageNo: 1,
          });
        }}
      />
      {/*导入文件 */}
      <ImportFileModal
        open={importFileVisible}
        setVisible={setImportFileVisible}
        selectedDomainNode={selectedDomainNode}
        onOk={() => {
          //成功切换版本后，刷新数据
          onRefresh(selectedDomainNode?.key);
          setInfoUsedforSearching({
            ...infoUsedforSearching,
            pageNo: 1,
          });
        }}
      />
    </div>
  );
};

export default BssDomainDetail;
