/*
组件开发背景：odh项目需要拖动表格列的功能。从AntDesign 4.x开始，Table不支持拖动表格列的API，因此，该组件重新封装了Table组件。
补充：目前支持行拖拽功能。
组件使用说明：
1.该组件继承了AntDesign中Table组件的所有API（components除外），和Table使用一致。
2.使用拖动表格列宽度功能时，columns中每一列的宽度必填，不填默认设置为100px；每列都设置宽度且统一格式时，位置更新更精确。
width: string | number,建议使用精确数字或百分比，例如width: 100或width: '10%'，
否则宽度可能不生效(AntDesign的Table也不建议使用width : '100px')，会导致拖动功能不生效。
例：const columns = [
      {
        dataIndex: 'name',
        title: 'Name',
        width: 100,//width: '10%' //width : '100px'
        ellipsis: true,
      },
    ]
3.使用行拖拽功能时，rowDraggable传true，setDataSource传父组件中dataSource对应的setDataSource，
  且dataSource的每一项必须具备rowDraggableKey属性，且该属性值是`${index}`，否则拖拽功能不生效。
  例如父组件中：
  setSchemaPropDataSource(
    selectedSchema?.propList?.map((item: any, index: number) => ({ ...item, rowDraggableKey: `${index}` })) || [],
  );
*/

import { useState, useRef, useEffect, useMemo, useContext, createContext } from 'react';
import styled from 'styled-components';
import { Resizable } from 'react-resizable';
import { Button, Table } from 'antd';
import { HolderOutlined } from '@ant-design/icons';
import type { DragEndEvent } from '@dnd-kit/core';
import { DndContext } from '@dnd-kit/core';
import type { SyntheticListenerMap } from '@dnd-kit/core/dist/hooks/utilities';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import { arrayMove, SortableContext, useSortable, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { TableProps } from 'antd/es/table';

interface IResizableTable extends TableProps<any> {
  rowDraggable?: boolean; // 若要实现行拖拽，该属性必传
  setDataSource?: (dataSource: any[]) => void; // 若要实现行拖拽，该属性必传
}

interface RowContextProps {
  setActivatorNodeRef?: (element: HTMLElement | null) => void;
  listeners?: SyntheticListenerMap;
}

const RowContext = createContext<RowContextProps>({});

const DragHandle: React.FC = () => {
  const { setActivatorNodeRef, listeners } = useContext(RowContext);
  return (
    <Button
      type="text"
      size="small"
      icon={<HolderOutlined />}
      style={{ cursor: 'move' }}
      ref={setActivatorNodeRef}
      {...listeners}
    />
  );
};

// const initialData = [
//   { key: '1', name: 'John Brown', age: 32, address: 'Long text Long' },
//   { key: '2', name: 'Jim Green', age: 42, address: 'London No. 1 Lake Park' },
//   { key: '3', name: 'Joe Black', age: 32, address: 'Sidney No. 1 Lake Park' },
// ];

interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  'data-row-key': string;
}

const Row: React.FC<RowProps> = (props) => {
  const { attributes, listeners, setNodeRef, setActivatorNodeRef, transform, transition, isDragging } = useSortable({
    id: props['data-row-key'],
  });

  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Translate.toString(transform),
    transition,
    ...(isDragging ? { position: 'relative', zIndex: 9999 } : {}),
  };

  const contextValue = useMemo<RowContextProps>(
    () => ({ setActivatorNodeRef, listeners }),
    [setActivatorNodeRef, listeners],
  );

  return (
    <RowContext.Provider value={contextValue}>
      <tr {...props} ref={setNodeRef} style={style} {...attributes} />
    </RowContext.Provider>
  );
};

// 定义表格样式
const ResizeTable = styled.div`
  position: relative;
  background-color: #fff;
  overflow: hidden;
  .ant-table-thead {
    th {
      font-weight: 600 !important;
      user-select: none;
    }
  }
  .ant-table-cell {
    .superform-field-control-readonly {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      word-break: keep-all;
      display: block;
      line-height: 32px;
    }
  }
  .ant-table-thead,
  .ant-table-tbody {
    tr {
      td,
      th {
        padding: 8px;
      }
    }
  }
  .react-resizable {
    position: relative;
    background-clip: padding-box;
  }
  .react-resizable-handle {
    width: 10px;
    height: 100%;
    cursor: col-resize;
    position: absolute;
    bottom: 0;
    right: 0px;
    z-index: 1;
  }
`;

// 定义拖动线的样式
const StyledLine = styled.div`
  display: none;
  height: 100%;
  width: 1px;
  background: #bfbfbf;
  position: absolute;
  z-index: 999;
  left: 0px;
`;

// 定义可调整大小的表头组件
const ResizeableTitle = (props: any) => {
  const { onResize, width, datagridRef, stretchRef, ...restProps } = props;
  const [offset, setOffset] = useState(0);

  if (!width) {
    return <th {...restProps} />;
  }

  // 获取元素的绝对位置
  const getPosition = (element: any) => {
    let actualLeft = element.offsetLeft;
    let current = element.offsetParent;
    while (current !== null) {
      actualLeft += current.offsetLeft;
      current = current.offsetParent;
    }
    return { x: actualLeft };
  };

  // 拖动过程中调整列宽
  const handleResize = (e: any, { size }: any) => {
    setOffset(size.width - width);
    let parentNodeX = getPosition(datagridRef.current).x;
    let stretchDom = stretchRef.current;
    stretchDom.style.display = 'block';
    stretchDom.style.left = `${e.clientX - parentNodeX + 2}px`;
  };

  // 拖动结束时更新列宽
  const handleResizeStop = (e: any, data: any) => {
    setOffset(0);
    let stretchDom = stretchRef.current;
    stretchDom.style.display = 'none';
    stretchDom.style.left = `0px`;
    onResize(e, data);
  };

  const _ResizableSpan = (
    <span
      className="react-resizable-handle"
      style={{ transform: `translateX(${offset}px)` }}
      onClick={(e) => {
        e.stopPropagation();
        e.preventDefault();
      }}
    />
  );

  return (
    <Resizable
      width={width + offset}
      handle={_ResizableSpan}
      height={0}
      onResize={handleResize}
      onResizeStop={handleResizeStop}
      draggableOpts={{ enableUserSelectHack: false }}
    >
      <th {...restProps} />
    </Resizable>
  );
};

// 可调整列宽度和拖拽行的表格组件
const ResizableTable: React.FC<IResizableTable> = (props: any) => {
  const { columns: initialColumns, rowDraggable = false, dataSource, setDataSource, ...restProps } = props;
  const [cols, setCols] = useState(initialColumns);

  const datagridRef = useRef<HTMLDivElement | null>(null);
  const stretchRef = useRef(null);
  const [resizableTableWidth, setResizableTableWidth] = useState<number | null>(null);
  // const [dataSource, setDataSource] = useState<any[]>(restProps?.dataSource || []);

  const rowHandleWidth = 40; // 可拖拽行手柄的宽度

  // 格式化列宽
  const formatColumnWidth = (percentString: string, resizableTableWidth: any) => {
    // 检查是否包含 'px'
    if (percentString.endsWith('px')) {
      // 去掉 'px' 并返回数值
      return parseFloat(percentString.replace('px', ''));
    }

    // 检查是否包含 '%'
    if (percentString.endsWith('%')) {
      // 去掉 '%' 并转换为小数，与resizableTableWidth相乘得到对应百分比的列宽
      return (resizableTableWidth * parseFloat(percentString.replace('%', ''))) / 100;
    }

    // 如果既不是 'px' 也不是 '%'，则格式错误
    console.log('Unknown format:', percentString);
    return null;
  };

  const components = {
    header: {
      cell: ResizeableTitle,
    },
    ...(rowDraggable ? { body: { row: Row } } : {}),
  };

  const handleResize =
    (index: any) =>
    (e: any, { size }: any) => {
      const nextColumns = [...cols];
      const currentWidth = nextColumns[index].width;
      // 计算当前列最大可拖动宽度，确保相邻列不小于最小宽度（50 像素）
      const maxWidth = currentWidth + (nextColumns[index + 1]?.width || 0) - 50;

      // 如果拖动宽度小于最小宽度或大于最大宽度，则不进行调整
      if (size.width < 50 || size.width > maxWidth) {
        return;
      }

      // 计算宽度差值
      const widthDiff = size.width - currentWidth;
      // 更新当前列的宽度
      nextColumns[index] = {
        ...nextColumns[index],
        width: size.width,
      };

      // 如果存在相邻列，调整相邻列的宽度以保持总宽度不变
      if (nextColumns[index + 1]) {
        nextColumns[index + 1] = {
          ...nextColumns[index + 1],
          width: nextColumns[index + 1].width - widthDiff,
        };
      }

      // 更新列状态
      setCols(nextColumns);
    };

  const columns = useMemo(() => {
    // 根据rowDraggable选择是否添加可拖动的手柄列
    const sortableColumn = rowDraggable
      ? { rowDraggableKey: 'sort', align: 'center', width: rowHandleWidth, render: () => <DragHandle /> }
      : null;

    return [
      sortableColumn, // 可能为 null
      ...cols.map((col: any, index: any) => ({
        ...col,
        onHeaderCell: (column: any) => ({
          width: column.width,
          stretchRef,
          datagridRef,
          onResize: handleResize(index), // 为每个列头设置拖动事件处理器
        }),
      })),
    ].filter(Boolean); // 移除任何 falsy 值
  }, [cols, rowDraggable]);

  // 行拖动结束
  const onDragEnd = ({ active, over }: DragEndEvent) => {
    if (active.id !== over?.id && setDataSource) {
      setDataSource((prevState: any) => {
        const activeIndex = prevState.findIndex((record: any) => record.rowDraggableKey === active?.id);
        const overIndex = prevState.findIndex((record: any) => record.rowDraggableKey === over?.id);
        return arrayMove(prevState, activeIndex, overIndex);
      });
    }
  };

  // 初始化真实可拖动表格宽度，添加resize事件
  useEffect(() => {
    const handleResizeWidth = () => {
      if (datagridRef?.current) {
        const realWidth = datagridRef?.current?.offsetWidth - rowHandleWidth; // 减去行手柄宽度
        if (rowDraggable && realWidth && realWidth > 0) {
          setResizableTableWidth(realWidth);
        } else {
          setResizableTableWidth(datagridRef?.current?.offsetWidth);
        }
      }
    };

    handleResizeWidth();

    window.addEventListener('resize', handleResizeWidth);
    return () => {
      window.removeEventListener('resize', handleResizeWidth);
    };
  }, []);

  useEffect(() => {
    if (resizableTableWidth) {
      // 初始化列宽，将百分比转为对应的精确数字
      const formatWidthList = initialColumns?.map((column: any) => {
        const width = column?.width;
        if (width && typeof width === 'number') {
          return { ...column, width };
        } else if (width && typeof width === 'string') {
          // 如果是百分比，则计算实际宽度，否则传入的width的格式错误
          const formatWidth = formatColumnWidth(width, resizableTableWidth);
          return { ...column, width: formatWidth !== null ? formatWidth : null };
        }
        return { ...column, width: 100 }; // 初始未设置宽度，则默认100px，必须有列宽才能进行拖动
      });
      setCols(formatWidthList);
    }
  }, [initialColumns, resizableTableWidth]);

  return rowDraggable ? (
    <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={onDragEnd}>
      <SortableContext items={dataSource.map((i: any) => i.rowDraggableKey)} strategy={verticalListSortingStrategy}>
        <ResizeTable ref={datagridRef}>
          <Table
            {...restProps} // 传递所有其他属性给 Table 组件
            rowKey="rowDraggableKey"
            columns={columns}
            dataSource={dataSource}
            components={components}
          />
          <StyledLine ref={stretchRef} style={{ bottom: 0 }} />
        </ResizeTable>
      </SortableContext>
    </DndContext>
  ) : (
    <ResizeTable ref={datagridRef}>
      <Table
        {...restProps} // 传递所有其他属性给 Table 组件
        columns={columns}
        dataSource={dataSource}
        components={components}
      />
      <StyledLine ref={stretchRef} style={{ bottom: 0 }} />
    </ResizeTable>
  );
};

export default ResizableTable;
