import React, { useState } from 'react';
import { Input, AutoComplete, Form } from 'antd';

const { TextArea } = Input;

interface IAutocompleteTextArea {
  suggestions: object;
  form: any; // 依赖的表单
  formItemName: string; // 依赖的表单字段
  formItemValue?: string; // 解决父组件的Form.Item是动态生成的情况下导致没有默认值的问题
}

const AutocompleteTextAreaInForm: React.FC<IAutocompleteTextArea> = (props) => {
  const { suggestions, form, formItemName, formItemValue = '' } = props;
  const [options, setOptions] = useState<{ value: string; label: string }[]>([]);

  const getSuggestions = (input: string) => {
    const keys = input.split('.'); // 分割输入以处理嵌套
    let currentLevel = JSON.parse(JSON.stringify(suggestions));

    // 遍历输入的每一层，找到当前层级的建议
    for (const key of keys) {
      if (key) {
        if (typeof currentLevel[key] === 'object') {
          currentLevel = currentLevel[key]; // 进入下一层
        } else {
          return []; // 如果没有找到，返回空数组
        }
      }
    }

    // 返回当前层级的所有键
    return Object.keys(currentLevel).map((item: any) => ({ key: item, type: typeof currentLevel[item] }));
  };

  const handleInputChange = (value: string) => {
    // 只有在输入的最后一个字符是 '.' 时才获取建议
    if (value.endsWith('.')) {
      const suggestionList = getSuggestions(value);
      setOptions(
        suggestionList.map((suggestion) => ({
          value: suggestion.key,
          label: `${suggestion.key} (${suggestion.type})`,
        })),
      );
    } else {
      setOptions([]); // 清空建议
    }
  };

  const handleSelect = (selectedValue: string) => {
    // 获取当前输入框的值
    const currentValue = form.getFieldValue(formItemName) || '';
    const newValue = `${currentValue}${selectedValue}`; // 将选中的值直接拼接到输入值后面
    form.setFieldsValue({ [formItemName]: newValue }); // 更新输入框的值
    setOptions([]); // 清空建议
  };

  return (
    <AutoComplete options={options} onSelect={handleSelect} style={{ width: '100%' }}>
      <Form.Item
        name={formItemName}
        initialValue={formItemValue} // 在这里对动态生成的form.item加initialValue默认值才生效，在Form加或者使用form.setFieldValue都不生效
      >
        <TextArea onChange={(e) => handleInputChange(e.target.value)} rows={1} />
      </Form.Item>
    </AutoComplete>
  );
};

export default AutocompleteTextAreaInForm;
