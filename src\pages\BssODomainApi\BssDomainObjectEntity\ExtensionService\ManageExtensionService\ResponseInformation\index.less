.requestInfoType:hover {
  cursor: pointer;
}
.iconStyle {
  color: #47e;
  cursor: pointer;
}
.versionIcon1 {
  margin-left: 20px;
  color: #21f17f;
  cursor: pointer;
}
.versionIcon2 {
  margin-left: 20px;
  color: #47e;
  cursor: pointer;
}
.hide {
  display: none;
}

.deleteIcon {
  margin-left: 5px;
  color: #47e;
  cursor: pointer;
}

.moreErrorCode {
  display: flex;
  justify-content: space-between;
  .avlValueText {
    flex: 3;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .moreText {
    flex: 1;
    color: #47e;
    cursor: pointer;

  }

}

.hiddenParam {
  text-decoration-line:line-through
}

.strongText {
  font-size: 16px;
  font-weight: bold;
}