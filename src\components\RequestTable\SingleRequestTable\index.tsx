import React, { useState, useEffect, useReducer, useContext, useMemo } from 'react';
import { Flex, Input, Select, Switch } from 'antd';
import useI18n from '@/hooks/useI8n';
import { DyColumnInMorePop } from '@/components';
import cloneDeep from 'lodash/cloneDeep';
import UpdataRequestParamsContext from '@/context/UpdataRequestParamsContext';
import { modifyTableValueFn, insertObjFn, getFormatterDataFn, regExp } from '@/utils/modifyTableValue';
import { parseLinkFromString } from '@/utils/common';
import { ITableDataInterface, RequestTableProps } from '@/typing';
import classNames from 'classnames';
import styles from './index.less';
import ResizableTable from '@/components/ResizableTable';
import { useTableFilter } from '@/hooks/useTableFilter';

interface columnType {
  dataIndex: string;
  title: string;
  width?: number | string;
  [k: string]: any;
}

const SingleRequestTable: React.FC<RequestTableProps> = ({
  tableData = [],
  debugMode = false as boolean | any,
  type = '',
}) => {
  const { formatMessage } = useI18n();
  const [selectedInputTypeObj, setSelectedInputTypeObj] = useState<Record<string, boolean>>({});

  const UpdataContext = useContext(UpdataRequestParamsContext);

  const onInputTypeChange = (checked: boolean, rowKey: string) => {
    setSelectedInputTypeObj((prev) => ({
      ...prev,
      [rowKey]: checked,
    }));
  };

  const RequiredSource: any = [
    { name: 'Yes', key: 'Y' },
    { name: 'No', key: 'N' },
  ];

  const [dataSource, setDataSource] = useState<ITableDataInterface[] | any>(cloneDeep(tableData));

  const [, forceComponentUpdateDispatch] = useReducer((count) => {
    return count + 1;
  }, 0);
  // 使用 useMemo 确保 dataSource 是稳定的引用
  const memoizedDataSource = useMemo(() => (Array.isArray(dataSource) ? dataSource : []), [dataSource]);
  // 自定义hooks，支持表格列筛选并分页展示
  const { filteredDataSource, getColumnSearchProps, getColumnEnumSearchProps } = useTableFilter(memoizedDataSource);

  // 针对特殊表格列，自定义过滤逻辑
  const customTypeFilter = (item: any, obj: any) => {
    if (item) {
      let showData = '';
      if (item?.typeFormat) {
        showData = `${item?.type}<${item?.typeFormat}>`;
      }
      showData = item?.type;
      return (showData || '')
        .toString()
        .toLowerCase()
        .includes((Object.values(obj)[1] as string).toLowerCase());
    }
    return true;
  };

  const baseColumns: columnType[] = [
    {
      dataIndex: 'name',
      title: formatMessage('PROJECT.COMMON.NAME'),
      width: '15%',
      ...(type !== 'B' && !debugMode ? getColumnSearchProps('name') : {}),
    },
    {
      dataIndex: 'type',
      title: formatMessage('PROJECT.COMMON.TYPE'),
      width: '12%',
      ...(type !== 'B' && !debugMode ? getColumnSearchProps('type', customTypeFilter) : {}),
      render: (_: any, record: any) => {
        if (record?.typeFormat) {
          return `${record?.type}<${record?.typeFormat}>`;
        }
        return record?.type;
      },
    },
    {
      dataIndex: 'required',
      title: formatMessage('PROJECT.COMMON.REQUIRED'),
      width: '8%',
      render: (_: string, record: any) => {
        if (record?.required) {
          const matchedItem = RequiredSource.find((i: { key: string; name: string }) => i.key === record.required);
          return matchedItem ? matchedItem.name : '';
        }
      },
      ...(type !== 'B' && !debugMode ? getColumnEnumSearchProps('required', RequiredSource) : {}),
    },
    {
      dataIndex: 'avlValue',
      title: formatMessage('SERVICEDETAIL.REQUEST.AVLVALUE'),
      ellipsis: true,
      className: 'avlValueColumn',
      width: '15%',
      ...(type !== 'B' && !debugMode ? getColumnSearchProps('avlValue') : {}),
      render: (value: string) => {
        const pattern = /^(range|length)[\(\[]/;
        if (value && !pattern.test(value)) {
          // range(、range[、length(、length[ 字符
          return <DyColumnInMorePop columnClaassName="avlValueColumn" value={value} popContentType="array" />;
        }
        return value;
      },
    },
    {
      dataIndex: 'example',
      title: formatMessage('PROJECT.COMMON.EXAMPLE'),
      width: '10%',
      ellipsis: true,
      ...(type !== 'B' && !debugMode ? getColumnSearchProps('example') : {}),
      render: (value: any) => {
        if (typeof value === 'string') {
          return value;
        }
        return '';
      },
    },
    {
      dataIndex: 'comments',
      title: formatMessage('PROJECT.COMMON.DESC'),
      ellipsis: true,
      width: '20%',
      className: 'descriptionColumn',
      ...(type !== 'B' && !debugMode ? getColumnSearchProps('comments') : {}),
      render: (value: string) => {
        return (
          <DyColumnInMorePop
            columnClaassName="descriptionColumn"
            value={parseLinkFromString(value || '')}
            popContentType="string"
          />
        );
      },
    },
  ];

  const columns = useMemo(() => {
    if (debugMode) {
      const newObj = {
        dataIndex: 'value',
        title: formatMessage('PROJECT.COMMON.VALUE'),
        width: 340,
        render: (text: string, record: any, index: number) => {
          const {
            children = '',
            avlValue = '',
            example = '',
            isArray = '',
            rowKey = '',
            value,
            name = '',
            type: colType = '',
            typeFormat = '',
          } = record;

          if (!children) {
            if (colType === 'Boolean') {
              return (
                <Select
                  options={[
                    { value: 'true', label: 'true' },
                    { value: 'false', label: 'false' },
                  ]}
                  style={{ width: 250 }}
                  value={value}
                  allowClear
                  onChange={(value: any) => {
                    const newDataSrc = modifyTableValueFn({
                      colName: 'value',
                      rowKey,
                      dataSource,
                      value: value ? [value] : '',
                    });
                    UpdataContext.updataRequestParamsFn?.({
                      [type]: newDataSrc,
                    });
                    setDataSource(newDataSrc);
                  }}
                />
              );
            }
            if (typeFormat) {
              return (
                <Input
                  onChange={(e: any) => {
                    const value = e.target.value;
                    const newDataSrc = modifyTableValueFn({
                      colName: 'value',
                      rowKey,
                      dataSource,
                      value: value ? value : [],
                    });
                    UpdataContext.updataRequestParamsFn?.({
                      [type]: newDataSrc,
                    });
                    setDataSource(newDataSrc);
                  }}
                  style={{ width: 250 }}
                />
              );
            }
            return avlValue && !regExp.test(avlValue) ? (
              (() => {
                const values: any = getFormatterDataFn(avlValue);
                if (isArray === 'Y') {
                  return (
                    <Flex gap="5px">
                      {selectedInputTypeObj[rowKey] ? (
                        <Input
                          onChange={(e: any) => {
                            const value = e.target.value;
                            const newDataSrc = modifyTableValueFn({
                              colName: 'value',
                              rowKey,
                              dataSource,
                              value: value ? value : [],
                            });
                            UpdataContext.updataRequestParamsFn?.({
                              [type]: newDataSrc,
                            });
                            setDataSource(newDataSrc);
                          }}
                          style={{ width: 250 }}
                        />
                      ) : (
                        <Select
                          allowClear
                          options={values}
                          value={value ? value : []}
                          className={classNames(styles.debugServiceSelect, {
                            [styles.setBorderColor]: UpdataContext.arrUsedforVerification?.includes(name),
                          })}
                          style={{ width: 250 }}
                          mode="tags"
                          onChange={(value: any) => {
                            const newDataSrc = modifyTableValueFn({
                              colName: 'value',
                              rowKey,
                              dataSource,
                              value: value ? value : [],
                            });
                            UpdataContext.updataRequestParamsFn?.({
                              [type]: newDataSrc,
                            });
                            setDataSource(newDataSrc);
                          }}
                        />
                      )}
                      <Switch
                        checkedChildren="Input"
                        unCheckedChildren="Select"
                        checked={selectedInputTypeObj[rowKey] || false}
                        onChange={(checked) => onInputTypeChange(checked, rowKey)}
                      />
                    </Flex>
                  );
                }

                return (
                  <Select
                    options={values}
                    value={value}
                    style={{ width: 250 }}
                    allowClear
                    onChange={(value: any) => {
                      const newDataSrc = modifyTableValueFn({
                        colName: 'value',
                        rowKey,
                        dataSource,
                        value: value ? [value] : '',
                      });
                      UpdataContext.updataRequestParamsFn?.({
                        [type]: newDataSrc,
                      });
                      setDataSource(newDataSrc);
                    }}
                  />
                );
              })()
            ) : (
              <Input
                value={value !== undefined ? value : example}
                style={{
                  width: 250,
                  borderColor: UpdataContext.arrUsedforVerification?.includes(name) ? 'red' : '',
                }}
                allowClear
                onChange={(e) => {
                  const newDataSrc = modifyTableValueFn({
                    colName: 'value',
                    rowKey,
                    dataSource,
                    value: e.target.value,
                  });

                  UpdataContext.updataRequestParamsFn?.({
                    [type]: newDataSrc,
                  });
                  setDataSource(newDataSrc);

                  // 强制更新，触发渲染
                  forceComponentUpdateDispatch();
                }}
              />
            );
          }
          return null;
        },
      };

      const newArray = insertObjFn({
        array: baseColumns,
        newObj,
        idx: 3,
      });

      return newArray;
    } else {
      return baseColumns;
    }
  }, [
    debugMode,
    type,
    UpdataContext.updataRequestParamsFn,
    UpdataContext.arrUsedforVerification,
    selectedInputTypeObj,
  ]);

  useEffect(() => {
    if (UpdataContext.DebugBackFillingInfo && dataSource.length) {
      const temp = dataSource?.map((el: any) => {
        UpdataContext.DebugBackFillingInfo.forEach((_el: any) => {
          if (el.name === _el.paramName && el.in === _el.reqIn) {
            if (el?.isArray === 'Y') {
              if (_el.paramValue) {
                el.value = _el.paramValue.split(',');
              } else {
                el.value = '';
              }
            } else {
              el.value = _el.paramValue;
            }
          }
        });
        return el;
      });
      setDataSource(temp);
    }
  }, [UpdataContext.DebugBackFillingInfo]);

  useEffect(() => {
    return () => {
      // 返回 detail 页面时数据恢复
      UpdataContext.updataRequestParamsFn?.({
        [type]: cloneDeep(tableData),
      });
      setDataSource(cloneDeep(tableData));
    };
  }, [debugMode, type]);

  return (
    <ResizableTable
      key="hasData"
      size="small"
      columns={columns}
      dataSource={filteredDataSource}
      pagination={false}
      rowKey={(record: any) => record?.rowKey}
    />
  );
};

export default SingleRequestTable;
