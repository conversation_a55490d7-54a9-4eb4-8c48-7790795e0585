import React, { useState, useRef } from 'react';
import useI18n from '@/hooks/useI8n';
import { Modal, Steps, Button, message } from 'antd';
import { FileType, GenerateSuccess } from '@/components';
import { generateFile, deleteFile, downloadFile } from '@/services';
import styles from './index.less';
import { IServiceDetail } from '@/services/typing';

interface IDownLoadCurrentFileModal {
  selectType: string;
  open: boolean;
  serviceDetail: IServiceDetail;
  onCancel: () => void;
}

const DownLoadCurrentFileModal: React.FC<IDownLoadCurrentFileModal> = (props) => {
  const { open, selectType, serviceDetail, onCancel } = props;

  const { formatMessage } = useI18n();
  const step2FormRef = useRef<any>(); // step2 ref
  const [current, setCurrent] = useState<number>(0); // current step
  const [hasGenerateFileName, setHasFasGenerateFileName] = useState(''); // 后端文件名
  const [loading, setLoading] = useState<boolean>(false); // button loading
  const [selectKeys, setSelectKeys] = useState<string[]>([]); // select keys

  const steps = [
    {
      step: 0,
      title: formatMessage('DOWNLOAD.MODAL.SELECTFILE'),
      content: <FileType ref={step2FormRef} />,
    },
    {
      step: 1,
      title: formatMessage('DOWNLOAD.MODAL.DOWNLOADFILE'),
      content: <GenerateSuccess />,
    },
  ];

  // 下一步
  const handleNext = () => {
    setCurrent(current + 1);
  };

  // modal close
  const onClose = async (deleteFlag?: boolean) => {
    setCurrent(0);
    setHasFasGenerateFileName('');
    setSelectKeys([]);
    onCancel?.();
    // 删除文件
    if (hasGenerateFileName && deleteFlag) {
      await deleteFile({
        fileName: hasGenerateFileName,
      });
    }
  };

  // 生成下载文件
  const handleGenerateFile = async () => {
    const selectKeys = [serviceDetail.serviceCode];
    const formValues = step2FormRef?.current?.formRef?.getFieldsValue();
    const params = {
      selectCodes: selectKeys,
      selectType: 'service',
      ...(formValues || {}),
      exampleFlag: formValues?.exampleFlag || false,
    };
    setLoading(true);
    try {
      const { data } = await generateFile(params);
      setHasFasGenerateFileName(data || '');
      setLoading(false);
      if (data) {
        handleNext();
      }
    } catch (error) {
      setLoading(false);
    }
  };

  // 下载文件
  const handleDownload = async () => {
    // 下载文件
    const response: any = await downloadFile({
      fileName: hasGenerateFileName,
    });
    // 提取文件名并解码成中文
    const contentDisposition = response.headers['content-disposition'];
    let fileName = ''; // 默认文件名
    if (contentDisposition) {
      const regex = /fileName=([^;]+)/;
      const match = regex.exec(contentDisposition);
      if (match) {
        fileName = decodeURI(match[1]);
      }
    }
    // a标签
    const link = document.createElement('a');
    // blob
    const objectUrl = window.URL.createObjectURL(new Blob([response?.data]));
    link.style.display = 'none';
    link.href = objectUrl;
    // fileName
    link.download = fileName;
    document.body.appendChild(link);
    link.click();
    // // 适当释放url
    window.URL.revokeObjectURL(objectUrl);
    link.remove();
    onClose(false);
    message.open({
      type: 'success',
      content: 'The file was downloaded successfully!',
    });
  };

  // footer
  const loadCurrentFooter = () => {
    switch (current) {
      case 0:
        return (
          <Button type="primary" onClick={handleGenerateFile} loading={loading}>
            {formatMessage('PROJECT.COMMON.GENERATE')}
          </Button>
        );
      case 1:
        return (
          <Button type="primary" loading={loading} onClick={handleDownload}>
            {formatMessage('PROJECT.COMMON.DOWNLOAD')}
          </Button>
        );
      default:
        return <></>;
    }
  };

  return (
    <Modal
      width={750}
      destroyOnClose
      open={open}
      forceRender={open}
      title={formatMessage('PROJECT.COMMON.DOWNLOAD')}
      onCancel={() => onClose(true)}
      footer={loadCurrentFooter}
      keyboard={false}
      maskClosable={false}
    >
      {/* steps */}
      <div className={styles.stepsContainer}>
        <Steps size="small" current={current} items={steps.map((item) => ({ key: item.title, title: item.title }))} />
      </div>
      {/* steps content */}
      <div className={styles.contentContainer}>
        {steps.map((i) => (
          <div key={i.step} style={i.step !== current ? { display: 'none' } : {}}>
            {i.content}
          </div>
        ))}
      </div>
    </Modal>
  );
};

export default DownLoadCurrentFileModal;
