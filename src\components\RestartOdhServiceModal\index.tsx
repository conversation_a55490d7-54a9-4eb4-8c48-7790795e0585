import ChangeVersionModal from '@/pages/BssODomainApi/BssDomainDetail/ChangeVersion';
import { restartOdhService } from '@/services/domainService';
import { Button, Flex, message, Modal, Spin } from 'antd';
import { useState } from 'react';
import useI18n from '@/hooks/useI8n';

interface IRestartOdhServiceModal {
  open: boolean;
  setVisible: any;
}

const RestartOdhServiceModal: React.FC<IRestartOdhServiceModal> = (props) => {
  const { open, setVisible } = props;
  const { formatMessage } = useI18n();
  const [spining, setSpining] = useState<boolean>(false);
  const [openChangeVersionModal, setOpenChangeVersionModal] = useState<boolean>(false);

  const onSubmit = async () => {
    setSpining(true);
    try {
      const { success } = await restartOdhService();
      if (success) {
        setVisible(false);
        setOpenChangeVersionModal(true);
      } else {
        message.error(formatMessage('RESTART.FAILED'));
      }
    } catch (error) {
      console.error('An error occurred:', error);
      message.error(formatMessage('RESTART.ERROR'));
    } finally {
      setSpining(false);
    }
  };

  return (
    <>
      <Modal
        title={formatMessage('RESTART.TITLE')}
        open={open}
        width={500}
        onCancel={() => setVisible(false)}
        footer={
          <Flex gap="1rem" justify="end">
            <Button onClick={() => setVisible(false)}>{formatMessage('PROJECT.COMMON.CANCEL')}</Button>
            <Spin spinning={spining}>
              <Button onClick={onSubmit} type="primary">
                {formatMessage('PROJECT.COMMON.CONFIRM')}
              </Button>
            </Spin>
          </Flex>
        }
      >
        <span>{formatMessage('RESTART.CONFIRM')}</span>
      </Modal>
      {/* 查询当前操作阶段*/}
      <ChangeVersionModal
        startStep={3}
        open={openChangeVersionModal}
        onCancel={() => setOpenChangeVersionModal(false)}
        onOk={() => {
          setOpenChangeVersionModal(false);
        }}
      />
    </>
  );
};
export default RestartOdhServiceModal;
