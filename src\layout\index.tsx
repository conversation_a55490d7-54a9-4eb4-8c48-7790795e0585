import React, { useEffect } from 'react';
import { Navigate, Outlet, useLocation, useRouteLoaderData } from 'react-router-dom';
import { Layout, Watermark } from 'antd';
import api from '../api/index';
import styles from './index.module.less'
import NavHeader from '../components/NavHeader';
import NavFooter from '../components/NavFooter';
import SideMenu from '../components/Menu';
import { useStore } from '../store';

const { Content, Sider } = Layout;

const App: React.FC = () => {
    const updateUserInfo = useStore(state => state.getUserInfo)
    const collapsed = useStore(state => state.collapsed)
    const { pathname } = useLocation()

    useEffect(() => {
        getUserInfoFC()
    }, [])
    const getUserInfoFC = async () => {
        const data = await api.getUserInfo()
        updateUserInfo(data)
    }

    const data = useRouteLoaderData('layout') as any
    const staticPath = ['/welcome', '/403', '/404']
    if (!data.menuPathList.includes(pathname) && !staticPath.includes(pathname)) {
        return <Navigate to='/403 ' />
    }


    return (
        <Watermark content='react'>
            <Layout>
                <Sider collapsed={collapsed}>
                    <SideMenu />
                </Sider>
                <Layout>
                    <NavHeader />
                    <Content className={styles.content}>
                        <div className={styles.wrapper}>
                            <Outlet />
                        </div>
                        <NavFooter />
                    </Content>
                </Layout>
            </Layout>
        </Watermark>
    );
};

export default App;