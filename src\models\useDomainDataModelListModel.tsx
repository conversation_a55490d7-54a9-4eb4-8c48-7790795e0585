import { useEffect, useState } from 'react';
import { getDomainDataModelClassesService, getDomainTableNameService } from '@/services/dataModelService';

interface IClassesInfo {
  modelClass?: string;
  modelClassName?: string;
  comments?: string;
}

const useDomainDataModelListModel = () => {
  const [classesInfo, setClassesInfo] = useState<IClassesInfo[]>([]);
  const [tableInfo, setTableInfo] = useState<string[]>([]);

  const getDomainDataModelClassesFn = async () => {
    try {
      await getDomainDataModelClassesService().then((res) => {
        if (res?.success) {
          setClassesInfo(res?.data || []);
        }
      });
    } catch (error) {
      //
    }
  };

  const getDomainTableInfoFn = async (domainId: number) => {
    try {
      await getDomainTableNameService({ domainId }).then((res) => {
        const { success = false, data = [] } = res;
        if (success) {
          setTableInfo(data);
        }
      });
    } catch (error) {
      //
    }
  };

  useEffect(() => {
    getDomainDataModelClassesFn();
  }, []);

  return {
    classesInfo,
    tableInfo,
    getDomainTableInfoFn,
  };
};

export default useDomainDataModelListModel;
