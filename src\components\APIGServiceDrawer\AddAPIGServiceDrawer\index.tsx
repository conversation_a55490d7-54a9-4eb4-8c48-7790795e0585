{
  /**该部分需求说明：
  新增APIG服务时：
  1.debug页面如果参数（目前仅需支持query参数）有值，打开该弹框，会自动生成OData参数。
  2.无法直接增、删APIG参数，只能通过解析Access Relative URL和OData参数得到APIG参数。
  3.解析规则：Access Relative URL中${}里的内容，OData参数value中$()里的内容。
  修改APIG服务时：
  1.OData参数和APIG参数首先从接口中获取，如果更改Access Relative URL或者OData参数，则重新解析。
*/
}
import useI18n from '@/hooks/useI8n';
import { Form, Input, Drawer, Space, Button, Spin, Flex, Select, TreeSelect, Checkbox, message } from 'antd';
import styles from './index.less';
import React, { useCallback, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { qryApiBaseInfoByApiId, qryServiceListByCataLogId, saveAsAPIGPropsType } from '@/services';
import ODataParamsTable from '../ODataParamsTable';
import APIGParamsTable from '../APIGParamsTable';
import getUUid from '@/utils/getUUid';
import debounce from 'lodash/debounce';
import { APIGServiceCatalogItemType } from '@/services/typing';
import { useModel } from 'umi';
import SelectWithAddButton from '@/components/SelectWithAddButton';
import { ODH_SUFFIX } from '@/pages/BssODataApi/APIGService/CustomerCollpaseForAPIGService/const';

interface caseDrawerPropsType {
  title: string;
  open: boolean;
  loading: boolean;
  dataToBeSent: { [k: string]: any };
  serviceDetail: any;
  APIGServiceCatalogList: APIGServiceCatalogItemType[];
  setOpen: (e: any) => void;
  onAPIGServiceDrawerSend: (e: saveAsAPIGPropsType) => void;
}

interface IDataItem {
  key: string;
  name: string;
  value: string;
  isEdit: boolean;
  reqIn: string;
}

interface INameListItem {
  name: string;
  hasDefaultValue: boolean;
}

interface OptionType {
  title: string;
  value: number;
  key: number;
  titlePath: string;
  children?: OptionType[]; // 子选项
  selectable?: boolean; // 是否可选择
}

interface OptionItemType {
  label: string;
  value: string | number;
  key: string | number;
  state: string;
  isODHApi?: string; // T / F
}

interface APIGServiceItemByCatalogIdType {
  apiId: number;
  apiVersionId: number;
  apiMethod: string;
  version: string;
  serviceUrl?: string;
  accessRelativeUrl?: string;
  isODHApi: string; // T / F
}

interface SelectWithAddButtonRef {
  resetNewOptionList: () => void;
  setNewOptionsList: (newOptionsList: OptionItemType[]) => void;
}

const AddAPIGServiceDrawer = React.forwardRef(
  (
    {
      title = '',
      open = false,
      loading = false,
      dataToBeSent = {},
      serviceDetail = {},
      APIGServiceCatalogList = [],
      setOpen = () => {},
      onAPIGServiceDrawerSend = () => {},
    }: caseDrawerPropsType,
    ref,
  ) => {
    const { formatMessage } = useI18n();
    const [form] = Form.useForm();
    const { systemConfigData } = useModel('useSystemConfigDataModel');
    const { product: isProduct } = systemConfigData;

    const [APIGParamOriginData, setAPIGParamOriginData] = useState<any>([]);
    const [APIGParamDataFromOData, setAPIGParamDataFromOData] = useState<any>([]);
    const [APIGParamDataFromUrl, setAPIGParamDataFromUrl] = useState<any>([]);
    const [paramList, setParamList] = useState<any>([]);
    const [APIGParamList, setAPIGParamList] = useState<any>([]);

    const [APIGServiceListByCatalogId, setAPIGServiceListByCatalogId] = useState<APIGServiceItemByCatalogIdType[]>([]);
    const [APIGServiceMethodList, setAPIGServiceMethodList] = useState<OptionItemType[]>([]);
    const [APIGServiceVersionList, setAPIGServiceVersionList] = useState<OptionItemType[]>([]);
    const [isShowCurrentAccessRelativeUrl, setIsShowCurrentAccessRelativeUrl] = useState<boolean>(false);
    const [isShowOverwriteFlag, setIsShowOverwriteFlag] = useState<boolean>(false);
    const [APIMethodTip, setAPIMethodTip] = useState<string>('');

    const selectVersionRef = useRef<SelectWithAddButtonRef>(null);

    const accessRelativeUrl = Form.useWatch('accessRelativeUrl', form) || '';
    const currentApiCatalogId = Form.useWatch('apiCatalogId', form);
    const currentApiMethod = Form.useWatch('apiMethod', form);
    const currentVersion = Form.useWatch('apiVersion', form);

    const PrefixURL = '/api/transferRest';

    // 从子组件ODataParamsTable获取数据
    const handleAPIGParamOriginDataChange = (data: any) => {
      setAPIGParamOriginData(data);
    };

    // 从子组件APIGParamsTable获取数据
    const handleAPIGParamListChange = (data: any) => {
      setAPIGParamList(data);
    };

    // 搜索catalog
    const handleCatalogFilter = (inputValue: string, treeNode: any) => {
      // 检查当前节点是否为叶子节点
      const isLeafNode = !treeNode.isParent;

      // 检查节点标题是否包含输入值
      const isMatch = treeNode.title.toLowerCase().includes(inputValue.toLowerCase());

      // 只有叶子节点才会被显示
      return isLeafNode && isMatch;
    };

    // 递归生成树形结构的选项
    const generateTreeOptions = (
      catalogList: APIGServiceCatalogItemType[],
      parentPath: string[] = [],
      isFirstLevel: boolean = true,
    ): OptionType[] => {
      return catalogList.map((parent) => {
        const currentPath = [...parentPath, parent.catalogName];

        const option: OptionType = {
          title: parent.catalogName,
          titlePath: currentPath.join('->'), // 添加完整路径
          value: parent.catalogId,
          key: parent.catalogId,
          selectable: isFirstLevel ? false : true, // 只有第一层不可选择
        };

        // 如果有子目录，递归生成子选项
        if (parent.catalogChildNodes && parent.catalogChildNodes.length > 0) {
          option.children = generateTreeOptions(parent.catalogChildNodes, currentPath, false);
        }

        return option;
      });
    };

    // 关闭抽屉
    const onCancel = () => {
      setOpen(false);
      setAPIGParamDataFromOData([]);
      setAPIGParamDataFromUrl([]);
      form.resetFields();
    };

    // 删除APIMethod后缀
    const deleteAPIGMethodSuffix = (apiMethod: string) => {
      return apiMethod?.endsWith(ODH_SUFFIX) ? apiMethod.replace(new RegExp(`${ODH_SUFFIX}$`), '') : apiMethod;
    };

    // 提交
    const onOk = () => {
      form.validateFields().then((values) => {
        try {
          const oDataParam = APIGParamOriginData.filter((item: IDataItem) => item.reqIn === 'Q')
            .map((item: IDataItem) => `${item?.name}=${item?.value}`)
            .join('&');
          const existItem = APIGServiceMethodList.find((item) => item.value === currentApiMethod);
          const existVersionItem = APIGServiceVersionList.find((item) => item.value === currentVersion);

          const params = {
            ...values,
            domainName: serviceDetail?.group,
            odataParam: oDataParam || '',
            paramList: APIGParamList || [],
            apiVersion: existVersionItem ? existVersionItem.label : currentVersion,
            apiId: existItem ? existItem.value : '',
            overwriteFlag: values?.overwriteFlag ? 'Y' : 'N',
            newVersionFlag: existVersionItem ? 'N' : 'Y',
            apiMethod: existItem ? deleteAPIGMethodSuffix(existItem.label) : deleteAPIGMethodSuffix(currentApiMethod), // 入库时需去掉后缀
            isODHApi: existItem ? existItem.isODHApi : 'T',
          };
          console.log(params, 'params');

          onAPIGServiceDrawerSend(params);
        } finally {
        }
      });
    };

    // 使用正则表达式提取 $() 中的内容
    const extractValues = (list: IDataItem[]) => {
      // 对value中存在$()的项，将$()内的内容提取出来
      const results = list.map((item) => {
        const matches = { reqIn: item.reqIn, nameList: [] as INameListItem[] };
        const regex = /\$\((.+?)\)/g;
        let match;
        // 若name为$a,value为$(a),则将a解析出来,并且hasDefaultValue为true
        if (item.name.replace(/^\$(.*)/, '$($1)') === item.value && (match = regex.exec(item.value)) !== null) {
          matches.nameList.push({ name: match[1], hasDefaultValue: true });
        } else {
          // 否则将每个$()内的内容提取出来,并且hasDefaultValue为false
          while ((match = regex.exec(item.value)) !== null) {
            matches.nameList.push({ name: match[1], hasDefaultValue: false });
          }
        }
        return matches;
      });
      return results;
    };

    // 使用正则表达式提取 {} 中的内容
    const extractContent = (str: string) => {
      const matches = str.match(/\{(.*?)\}/g);
      return matches ? matches.map((match) => match.slice(1, -1)) : [];
    };

    // 根据OData的数据源，生成APIG服务参数
    const generateFromOData = (APIGOriginData: IDataItem[]) => {
      const itemsWithDollarParentheses = APIGOriginData.filter((item: IDataItem) => /\$\(.+?\)/.test(item.value));
      // 提取 $() 中的内容并处理
      const extractedValues = extractValues(itemsWithDollarParentheses) || [];
      let newDataSource: any[] = [];
      extractedValues.forEach((item: { reqIn: string; nameList: INameListItem[] }) => {
        item?.nameList.forEach((i: INameListItem) => {
          if (i.hasDefaultValue) {
            const matchedItem = paramList.find((i: any) => i?.name === `$${item?.nameList[0]?.name}`);
            if (matchedItem) {
              const newItem = {
                key: getUUid(12),
                reqIn: matchedItem.reqIn,
                paramName: matchedItem.name.replace('$', ''),
                isArray: matchedItem.isArray,
                avlValue: matchedItem.avlValue,
                required: matchedItem.required,
                paramType: matchedItem.dataType,
                example: matchedItem.example,
                comments: matchedItem.comments,
              };
              newDataSource.push(newItem);
            }
          } else {
            const newItem = {
              key: getUUid(12),
              reqIn: item?.reqIn,
              paramName: i.name,
              isArray: 'N',
              avlValue: null,
              required: 'Y',
              paramType: 'String',
              example: null,
              comments: null,
            };
            newDataSource.push(newItem);
          }
        });
      });
      setAPIGParamDataFromOData(newDataSource);
    };

    // 根据Access Relative URL生成APIG服务的path参数
    const generateFromURL = () => {
      // 新增APIG Service， 打开弹框后，直接根据Service Path生成APIG服务path参数
      const extractedContent = extractContent(accessRelativeUrl) || [];
      let newDataSource: any[] = [];
      extractedContent.forEach((item: string) => {
        const matchedItem = paramList.find((i: any) => i?.name === item);
        if (matchedItem) {
          const newItem = {
            key: getUUid(12),
            reqIn: 'P',
            paramName: matchedItem.name.replace('$', ''),
            isArray: matchedItem.isArray,
            avlValue: matchedItem.avlValue,
            required: matchedItem.required,
            paramType: matchedItem.dataType,
            example: matchedItem.example,
            comments: matchedItem.comments,
          };
          newDataSource.push(newItem);
        } else {
          const newItem = {
            key: getUUid(12),
            reqIn: 'P',
            paramName: item,
            isArray: 'N',
            avlValue: null,
            required: 'Y',
            paramType: 'String',
            example: null,
            comments: null,
          };
          newDataSource.push(newItem);
        }
      });
      setAPIGParamDataFromUrl(newDataSource);
    };

    // 根据catalogId查询已有的apig服务接口
    const getServiceListByCataLogId = async (catalogId: number) => {
      try {
        if (catalogId) {
          const { success, data } = await qryServiceListByCataLogId({ catalogId });
          if (success) {
            setAPIGServiceListByCatalogId(data?.result || []);
            // 根据item.apiId去重，得到Method列表
            const filteredList = (data?.result || []).reduce(
              (acc: OptionItemType[], item: APIGServiceItemByCatalogIdType) => {
                const existingItem = acc.find((i) => i.value === item.apiId);
                if (!existingItem) {
                  acc.push({
                    label: item?.isODHApi === 'T' ? `${item.apiMethod}${ODH_SUFFIX}` : item.apiMethod,
                    value: item.apiId,
                    key: item.apiId,
                    state: 'original',
                    isODHApi: item.isODHApi,
                  });
                }
                return acc;
              },
              [],
            );
            setAPIGServiceMethodList(filteredList);
          }
        }
      } catch (error) {
        console.error(error);
      }
    };

    // 根据apiId查询apig服务基本信息，设置Api Name
    const setApiName = async (apiId: number) => {
      try {
        if (apiId) {
          const { success, data } = await qryApiBaseInfoByApiId({ apiId });
          if (success) {
            form.setFieldValue('apiName', data?.result?.apiName || '');
          }
        }
      } catch (error) {
        console.error(error);
      }
    };

    // 获取Method对应的版本列表
    const getVersion = (apiId: number) => {
      if (apiId) {
        const filteredList = APIGServiceListByCatalogId.filter((item) => item.apiId === apiId).map((item) => ({
          label: item.version,
          value: item.apiVersionId,
          key: item.apiVersionId,
          state: 'original',
        }));
        setAPIGServiceVersionList(filteredList || []);
      }
    };

    // 调用子组件SelectWithAddButton的setNewOptionsList方法
    const setVersionNewList = (newList: OptionItemType[]) => {
      if (selectVersionRef.current) {
        selectVersionRef.current.setNewOptionsList(newList);
      }
    };

    // 根据oDataFlag，获取accessRelativeUrl, 如果oDataFlag为Y，则返回originalAccessRelativeUrl, 否则去掉路径中/odh以及后面两个斜杠中的内容
    const getAccessRelativeUrlByODataFlag = (odataFlag: string, originalAccessRelativeUrl: string) => {
      if (odataFlag === 'Y') {
        return originalAccessRelativeUrl;
      } else {
        const result = originalAccessRelativeUrl.replace(/^(.*?)\/odh\/[^/]+\/[^/]+(\/.*)$/, '$1$2');
        return result || '';
      }
    };

    useImperativeHandle(ref, () => ({
      onCancel,
    }));

    // 使用 useCallback 和 debounce 创建防抖函数
    const debouncedGenerateFromURL = useCallback(debounce(generateFromURL, 1000), [accessRelativeUrl, paramList]);

    useEffect(() => {
      debouncedGenerateFromURL();
      // 清理函数，取消未执行的防抖调用
      return () => {
        debouncedGenerateFromURL.cancel();
      };
    }, [debouncedGenerateFromURL]);

    useEffect(() => {
      if (serviceDetail) {
        const list = serviceDetail?.parameters
          ? serviceDetail?.parameters?.filter((item: any) => item?.reqIn !== 'B')
          : [];
        setParamList(list);
        const filteredUrl = getAccessRelativeUrlByODataFlag(serviceDetail?.odataFlag, serviceDetail?.servicePath || '');
        form.setFieldValue('accessRelativeUrl', filteredUrl);
      }
      if (open) {
        generateFromOData(APIGParamOriginData);
        setAPIMethodTip('');
      }
    }, [open, serviceDetail]);

    useEffect(() => {
      if (currentApiCatalogId) {
        getServiceListByCataLogId(currentApiCatalogId);
      } else {
        setAPIGServiceMethodList([]);
      }
      form.setFieldValue('apiMethod', '');
      setAPIMethodTip('');
    }, [currentApiCatalogId]);

    useEffect(() => {
      if (currentApiMethod) {
        setAPIMethodTip('');
        const existItem = APIGServiceMethodList.find((item) => item.value === currentApiMethod);
        if (existItem) {
          if (isProduct && existItem?.isODHApi === 'F') {
            message.warning(formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.CANNOTOVERWRITEPRODUCT'));
            form.setFieldValue('apiMethod', '');
            return;
          }
          if (!isProduct && existItem?.isODHApi === 'F') {
            // 如果当前是项目环境，允许覆盖isODHApi = F的API Method，需要在API Method下方给出覆盖提示
            setAPIMethodTip(formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.WILLOVERWRITE'));
          }
          // 如果选择的是已存在的API，则在输入框中默认展示出APIG的API Name信息
          setApiName(existItem?.value as number);
          // 选择的是已存在的Method，则直接获取版本列表
          getVersion(currentApiMethod);
          setVersionNewList([]);
        } else {
          // 新增API Method时，需要在API Method下面增一个提示信息
          setAPIMethodTip(formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.WILLADDNEW'));
          // 如果是新增的API，且以.odh结尾，则将结尾的.odh替换为Odh
          const formattedApiMethod = currentApiMethod.endsWith(ODH_SUFFIX)
            ? currentApiMethod.replace(new RegExp(`${ODH_SUFFIX}$`), '')
            : currentApiMethod;
          form.setFieldValue('apiName', formattedApiMethod);
          // 选择的是新添加的Method，则初始化版本列表
          setIsShowCurrentAccessRelativeUrl(false);
          setAPIGServiceVersionList([]);
          setVersionNewList([{ label: '1.0', value: '1.0', key: getUUid(6), state: 'new' }]);
        }
      } else {
        form.setFieldValue('apiName', '');
        setAPIGServiceVersionList([]);
        setVersionNewList([]);
      }
      form.setFieldValue('apiVersion', '');
    }, [currentApiMethod, APIGServiceMethodList]);

    useEffect(() => {
      if (currentVersion) {
        const existItem = APIGServiceMethodList.find((item) => item.value === currentApiMethod);
        const existVersionItem = APIGServiceVersionList.find((item) => item.value === currentVersion);
        if (existItem && existVersionItem) {
          // 选择的Method和Version都已存在，覆盖的是isODHApi = T的API Method，则不需要展示Overwrite APIG Development Conversion
          if (existItem?.isODHApi === 'T') {
            setIsShowOverwriteFlag(false);
          } else {
            setIsShowOverwriteFlag(true);
          }
          const filteredItem = APIGServiceListByCatalogId.find(
            (item) => item.apiId === currentApiMethod && item.apiVersionId === currentVersion,
          );
          if (filteredItem?.accessRelativeUrl) {
            // 且该Version的accessRelativeUrl不为空，则显示currentAccessRelativeUrl
            setIsShowCurrentAccessRelativeUrl(true);
            form.setFieldValue('currentAccessRelativeUrl', `${PrefixURL}${filteredItem?.accessRelativeUrl}`);

            const filteredUrl = getAccessRelativeUrlByODataFlag(
              serviceDetail?.odataFlag,
              filteredItem?.accessRelativeUrl || '',
            );
            // 如果选择的是已有的API Method，且当前API Version对应的Current Access Relative URL不为空，那么Access Relative URL展示的值与Current Access Relative URL相同，且可以修改
            form.setFieldValue('accessRelativeUrl', filteredUrl);
          } else {
            setIsShowCurrentAccessRelativeUrl(false);
            const filteredUrl = getAccessRelativeUrlByODataFlag(
              serviceDetail?.odataFlag,
              serviceDetail?.servicePath || '',
            );
            form.setFieldValue('accessRelativeUrl', filteredUrl);
          }
        } else {
          setIsShowCurrentAccessRelativeUrl(false);
          setIsShowOverwriteFlag(false);
        }
      } else {
        setIsShowCurrentAccessRelativeUrl(false);
        setIsShowOverwriteFlag(false);
      }
      form.setFieldValue('overwriteFlag', '');
    }, [currentVersion, APIGServiceMethodList, APIGServiceVersionList]);

    return (
      <Drawer
        title={title}
        open={open}
        onClose={loading ? () => {} : onCancel}
        // width={window.innerWidth * 0.6}
        width={1000}
        footer={
          <Space style={{ float: 'right' }}>
            <Spin spinning={loading}>
              <Button type="primary" onClick={() => onOk()}>
                {formatMessage('PROJECT.COMMON.SUBMIT')}
              </Button>
            </Spin>
            <Button disabled={loading} onClick={onCancel}>
              {formatMessage('PROJECT.COMMON.CANCEL')}
            </Button>
          </Space>
        }
      >
        <Form form={form} labelCol={{ span: 8, className: styles.formItemLabel }} wrapperCol={{ span: 14 }}>
          <Form.Item
            label={formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.APICATALOG')}
            name="apiCatalogId"
            rules={[
              { required: true, message: formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.PLEASESELECTAPICATALOG') },
            ]}
          >
            <TreeSelect
              showSearch
              allowClear
              treeDefaultExpandAll
              treeNodeLabelProp="titlePath" // 输入框中显示 titlePath 而不是 title
              filterTreeNode={handleCatalogFilter} // 过滤函数
              treeData={generateTreeOptions(APIGServiceCatalogList)}
            />
          </Form.Item>
          <Form.Item
            label={formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.APIMETHOD')}
            name="apiMethod"
            rules={[
              {
                required: true,
                message: formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.PLEASESELECTAPIMETHOD'),
              },
              {
                pattern: /^[a-zA-Z0-9_.-]+$/,
                message: formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.ONLYLETTERS'),
              },
            ]}
            extra={APIMethodTip}
          >
            <SelectWithAddButton
              originalOptionsList={APIGServiceMethodList}
              parentForm={form}
              formFieldName="apiMethod"
              customInputRules={[
                {
                  validator: (value) => !value.endsWith(ODH_SUFFIX),
                  message: formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.CANNOTENDWITH', { suffix: ODH_SUFFIX }),
                },
                {
                  validator: () => currentApiCatalogId,
                  message: formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.SELECTCATALOGFIRST'),
                },
              ]}
              customInputAddonAfter={{ node: <span>{ODH_SUFFIX}</span>, value: ODH_SUFFIX }}
            />
          </Form.Item>
          <Form.Item
            label={formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.APINAME')}
            name="apiName"
            rules={[
              {
                required: true,
                message: formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.PLEASEINPUTAPINAME'),
              },
              {
                pattern: /^[a-zA-Z0-9_.-]+$/,
                message: formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.ONLYLETTERS'),
              },
            ]}
          >
            <Input />
          </Form.Item>
          <Form.Item
            label={formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.APIVERSION')}
            name="apiVersion"
            rules={[
              {
                required: true,
                message: formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.PLEASESELECTAPIVERSION'),
              },
            ]}
          >
            <SelectWithAddButton
              ref={selectVersionRef}
              originalOptionsList={APIGServiceVersionList}
              parentForm={form}
              formFieldName="apiVersion"
              customInputRules={[
                {
                  validator: () => currentApiMethod,
                  message: formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.SELECTMETHODFIRST'),
                },
              ]}
            />
          </Form.Item>
          {isShowCurrentAccessRelativeUrl && (
            <Form.Item
              label={formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.CURRENTACCESSRELATIVEURL')}
              name="currentAccessRelativeUrl"
            >
              <Input disabled />
            </Form.Item>
          )}
          <Form.Item label={formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.ACCESSRELATIVEURL')} style={{ flex: 1 }}>
            <Flex>
              <Form.Item>
                <Input disabled value={PrefixURL} />
              </Form.Item>
              <Form.Item
                name="accessRelativeUrl"
                style={{ flex: 3 }}
                rules={[
                  {
                    required: true,
                  },
                ]}
              >
                <Input />
              </Form.Item>
            </Flex>
          </Form.Item>
          {isShowOverwriteFlag && (
            <Form.Item
              label={formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.OVERWRITEAPIGDEVCONVERSION')}
              name="overwriteFlag"
              valuePropName="checked"
            >
              <Checkbox />
            </Form.Item>
          )}
          {serviceDetail?.odataFlag === 'Y' && (
            <>
              <Form.Item label={formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.ODATAPARAM')} name="odataParam">
                <div>
                  <ODataParamsTable
                    isEditAPIGService={false}
                    paramList={paramList.filter((item: any) => item?.reqIn === 'Q' || item?.reqIn === 'H')}
                    dataToBeSent={dataToBeSent}
                    getAPIGParamsOriginData={handleAPIGParamOriginDataChange}
                    generateFromOData={generateFromOData}
                  />
                </div>
              </Form.Item>
              <Form.Item label={formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.APIGPARAM')} name="paramList">
                <APIGParamsTable
                  APIGParamDataFromOData={APIGParamDataFromOData}
                  APIGParamDataFromUrl={APIGParamDataFromUrl}
                  getAPIGParamList={handleAPIGParamListChange}
                  isEditAPIGService={false}
                  isGenerateFromUrl={true}
                  setAPIGParamDataFromOData={setAPIGParamDataFromOData}
                />
              </Form.Item>
            </>
          )}
          <Form.Item label={formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.COMMENTS')} name="comments">
            <Input.TextArea />
          </Form.Item>
          <Form.Item label={formatMessage('DEBUGSERVICE.APIGSERVICEDRAWER.AIAUTOTESTPROMPT')} name="aiAutoTestPrompt">
            <Input.TextArea rows={6} />
          </Form.Item>
        </Form>
      </Drawer>
    );
  },
);

export default AddAPIGServiceDrawer;
