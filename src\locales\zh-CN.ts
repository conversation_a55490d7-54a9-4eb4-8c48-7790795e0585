export default {
  'PROJECT.COMMON.CONFIRM': '确认',
  'PROJECT.COMMON.SEARCH': '查询',
  'PROJECT.COMMON.RESET': '重置',
  'PROJECT.COMMON.DESC': '描述',
  'PROJECT.COMMON.NAME': '名称',
  'PROJECT.COMMON.TYPE': '类型',
  'PROJECT.COMMON.EXAMPLE': '示例',
  'PROJECT.COMMON.CATALOG': '目录',
  'PROJECT.COMMON.REQUIRED': '必填',
  'PROJECT.COMMON.HIDDEN': '隐藏',
  'PROJECT.COMMON.REQUIREDANDHIDDENBOTHYESNOTE': "当'必填'是'Yes'时，'隐藏'就不能是'Yes'。",
  'PROJECT.COMMON.REFRESH': '刷新',
  'PROJECT.COMMON.DOWNLOAD': '下载',
  'PROJECT.COMMON.AICCPROMPT': 'AICC 提示信息选择',
  'PROJECT.COMMON.ERRORCODE': '错误码',
  'PROJECT.COMMON.APIGSERVICE': 'APIG 服务',
  'PROJECT.COMMON.SEARCHUSECASE': '搜索测试用例',
  'PROJECT.COMMON.NEXT': '下一步',
  'PROJECT.COMMON.PREVIOUS': '上一步',
  'PROJECT.COMMON.GENERATE': '生成文件',
  'PROJECT.COMMON.SERVICENAME': '服务名称',
  'PROJECT.COMMON.SERVICECATALOG': '服务目录',
  'PROJECT.COMMON.DOMAINENTITY': '领域实体',
  'PROJECT.COMMON.SERVICEPATH': '服务路径',
  'PROJECT.COMMON.VALUE': '值',
  'PROJECT.COMMON.CREATIONSOURCE': '创建来源',
  'PROJECT.COMMON.DESCRIPTION': '描述',
  'PROJECT.COMMON.BELONGDOMAINOBJECT': '所属领域对象',
  'PROJECT.COMMON.NODATA': '暂无数据',
  'PROJECT.COMMON.EDIT': '编辑',
  'PROJECT.COMMON.DELETE': '删除',
  'PROJECT.COMMON.COPY': '拷贝',
  'PROJECT.COMMON.ADDSUCCESS': '新增成功!',
  'PROJECT.COMMON.EDITSUCCESS': '修改成功!',
  'PROJECT.COMMON.DELETESUCCESS': '删除成功!',
  'PROJECT.COMMON.REFRESHSUCCESS': '刷新成功!',
  'PROJECT.COMMON.CHANGEVERSION': '切换版本',
  'PROJECT.COMMON.PAGINATION.TOTAL': '共 {total} 条',
  'PROJECT.COMMON.GETDESCBYPROPNAME': '提示：描述可根据名称自动生成，请根据实际情况填写。',
  'PROJECT.COMMON.OPERATION': '操作',
  'PROJECT.COMMON.DOMAIN': '领域',
  'MENU.SEARCH.PLACEHOLDER': '输入关键字',
  'MENU.TITLE.TITLE': '目录',
  'MENU.SELECT.CATALOG': '目录查询',
  'MENU.SELECT.DOMAIN': '领域查询',
  'DOMAINDETAIL.RELA.TITLE': '实体关系',
  'DOMAINDETAIL.RELA.LEGEND': '图例：',
  'DOMAINDETAIL.DESC.TITLE': '实体描述',
  'DOMAINDETAIL.DESC.ENTITYNAME': '实体名称',
  'DOMAINDETAIL.DESC.PARENTENTITY': '父级实体',
  'DOMAINDETAIL.DESC.CHILDENTITY': '子级实体',
  'DOMAINDETAIL.PROPERTIES.TITLE': '实体属性',
  'DOMAINDETAIL.PROPERTIES.FEATURES': '支持功能',
  'DOMAINDETAIL.PROPERTIES.PRIMARY': '主键',
  'DOMAINDETAIL.PROPERTIES.EXACT': '精确搜索',
  'DOMAINDETAIL.PROPERTIES.FUZZY': '模糊搜索',
  'DOMAINDETAIL.PROPERTIES.INDEX': '索引',
  'DOMAINDETAIL.PROPERTIES.CANFILTER': '可过滤',
  'DOMAINDETAIL.PROPERTIES.MANDATORY': '必填',
  'DOMAINDETAIL.PROPERTIES.CANORDER': '可排序',
  'DOMAINDETAIL.PROPERTIES.ENUM': '枚举',
  'DOMAINDETAIL.SERVICES.TITLE': '实体服务',
  'DOMAINDETAIL.SERVICES.SHOWDEPRECATED': '展示过时的服务',
  'DOMAINDETAIL.SERVICES.EVENTS': '实体事件',
  'DOMAINDETAIL.EVENT_RULE_ACTION': '事件规则操作',
  'SERVICEDETAIL.TITLE.TITLE': '服务详情',
  'SERVICEDETAIL.REQUEST.TITLE': '请求信息',
  'SERVICEDETAIL.REQUEST.URL': 'URL',
  'SERVICEDETAIL.REQUEST.TITLEEXAMPLE': '请求示例',
  'SERVICEDETAIL.REQUEST.AVLVALUE': '可用值',
  'SERVICEDETAIL.RESPONSE.TITLE': '响应信息',
  'SERVICEDETAIL.RESPONSE.TITLEEXAMPLE': '响应示例',
  'SERVICEDETAIL.RESPONSE.TITLEBODY': '响应体',
  'SERVICEDETAIL.RESPONSE.TITLEHEADER': '响应标头',
  'SERVICEDETAIL.REQUEST.EXAMPLENOTE':
    '注意：Mockjs生成的模拟请求参数是模拟数据，可能与实际接口规范不完全一致。请在使用前验证兼容性。',
  'SERVICEDETAIL.RESPONSE.EXAMPLENOTE':
    '注意：Mockjs生成的模拟响应参数是模拟数据，可能与实际接口规范不完全一致。请在使用前验证兼容性。',
  'SERVICEDETAIL.RESPONSE.TITLDOMAIN': '领域实体',
  'SERVICEDETAIL.HOVERWRAPPER.DEBUGSERVICE': '调试服务',
  'SERVICEDETAIL.HOVERWRAPPER.DOWNLOADDOC': '下载文件',
  'DEBUGSERVICE.URL.SEND': '发送',
  'DEBUGSERVICE.URL.NOTE': '注意：示例地址是服务发布后的请求地址，而不是此处发送的地址。',
  'DEBUGSERVICE.URL.SAVE': '保存为...',
  'DEBUGSERVICE.URL.USECASE': '用例',
  'DEBUGSERVICE.URL.APIGSERVICE': 'APIG 服务',
  'DEBUGSERVICE.URL.SELECTUSECASE': '选择用例',
  'DEBUGSERVICE.APIGSERVICEDRAWER.TITLE': '保存为APIG服务',
  'DEBUGSERVICE.APIGSERVICEDRAWER.EDITTITLE': '编辑APIG服务',
  'DEBUGSERVICE.APIGSERVICEDRAWER.APICATALOG': 'API目录',
  'DEBUGSERVICE.APIGSERVICEDRAWER.APIMETHOD': 'API方法',
  'DEBUGSERVICE.APIGSERVICEDRAWER.APINAME': 'API名称',
  'DEBUGSERVICE.APIGSERVICEDRAWER.APIVERSION': 'API版本',
  'DEBUGSERVICE.APIGSERVICEDRAWER.CURRENTACCESSRELATIVEURL': '当前访问相对URL',
  'DEBUGSERVICE.APIGSERVICEDRAWER.ACCESSRELATIVEURL': '访问相对URL',
  'DEBUGSERVICE.APIGSERVICEDRAWER.OVERWRITEAPIGDEVCONVERSION': '覆盖APIG开发转换',
  'DEBUGSERVICE.APIGSERVICEDRAWER.ODATAPARAM': 'OData参数',
  'DEBUGSERVICE.APIGSERVICEDRAWER.APIGPARAM': 'APIG参数',
  'DEBUGSERVICE.APIGSERVICEDRAWER.COMMENTS': '备注',
  'DEBUGSERVICE.APIGSERVICEDRAWER.AIAUTOTESTPROMPT': 'AI自动测试提示',
  'DEBUGSERVICE.APIGSERVICEDRAWER.NOTEMPTYTIP': '名称和值不能为空。',
  'DEBUGSERVICE.APIGSERVICEDRAWER.GENERATEAPIGPARAM.SUCCESS': '成功生成APIG参数。',
  'DEBUGSERVICE.APIGSERVICEDRAWER.GENERATEAPIGPARAM.NODATA': '没有可用数据。',
  'DEBUGSERVICE.APIGSERVICEDRAWER.APIGPARAMDRAWER.TITLE': '编辑API服务参数',
  'DEBUGSERVICE.APIGSERVICEDRAWER.EDIT': '编辑服务',
  'DEBUGSERVICE.APIGSERVICEDRAWER.REFRESHSERVICE': '刷新服务',
  'DEBUGSERVICE.APIGSERVICEDRAWER.CANNOTOVERWRITEPRODUCT': '无法覆盖产品的API。',
  'DEBUGSERVICE.APIGSERVICEDRAWER.WILLOVERWRITE': '您将覆盖APIG上的此服务。',
  'DEBUGSERVICE.APIGSERVICEDRAWER.WILLADDNEW': '您将在APIG上添加一个新服务。',
  'DEBUGSERVICE.APIGSERVICEDRAWER.PLEASESELECTAPICATALOG': '请选择API目录',
  'DEBUGSERVICE.APIGSERVICEDRAWER.PLEASESELECTAPIMETHOD': '请选择API方法',
  'DEBUGSERVICE.APIGSERVICEDRAWER.ONLYLETTERS': '只允许字母、数字、下划线(_)、点(.)和连字符(-)',
  'DEBUGSERVICE.APIGSERVICEDRAWER.CANNOTENDWITH': '输入值不能以{suffix}结尾',
  'DEBUGSERVICE.APIGSERVICEDRAWER.SELECTCATALOGFIRST': '请先选择API目录',
  'DEBUGSERVICE.APIGSERVICEDRAWER.PLEASEINPUTAPINAME': '请输入API名称',
  'DEBUGSERVICE.APIGSERVICEDRAWER.PLEASESELECTAPIVERSION': '请选择API版本',
  'DEBUGSERVICE.APIGSERVICEDRAWER.SELECTMETHODFIRST': '请先选择API方法',
  'DEBUGSERVICE.USECASEMODAL.EDIT': '编辑用例',
  'DEBUGSERVICE.USECASEMODAL.TITLE': '保存为用例',
  'DEBUGSERVICE.USECASEMODAL.EDITTITLE': '编辑用例',
  'DEBUGSERVICE.USECASEMODAL.NAME': '用例名称',
  'DEBUGSERVICE.USECASEMODAL.NAMETIP': '请输入用例名称',
  'DEBUGSERVICE.USECASEMODAL.SAVERESPONSEMSG': '保存响应信息',
  'DEBUGSERVICE.USECASEMODAL.COMMENTS': '评论',
  'DEBUGSERVICE.USECASEBESELECTEDMODAL.QUERY': '查询',
  'DEBUGSERVICE.USECASEBESELECTEDMODAL.SELECTUSECASE': '选择用例',
  'DEBUGSERVICE.USECASEBESELECTEDMODAL.USECASENAME': '用例名称',
  'DEBUGSERVICE.USECASEBESELECTEDMODAL.INCLUDERESPONSE': '是否包含响应信息',
  'DEBUGSERVICE.USECASEBESELECTEDMODAL.COMMENTS': '评论',
  'DEBUGSERVICE.USECASEBESELECTEDMODAL.OPERATION': '操作',
  'DEBUGSERVICE.USECASEBESELECTEDMODAL.CANCEL': '取消',
  'DEBUGSERVICE.USECASEBESELECTEDMODAL.OK': '确定',
  'DEBUGSERVICE.USECASEBESELECTEDMODAL.CONFIRMTIP': '确定要删除这个用例吗？',
  'DEBUGSERVICE.REQUEST.EMPTY': '没有请求参数',
  'DEBUGSERVICE.RESPONSESTATUSTABLE.TITLE': '状态',
  'DEBUGSERVICE.RESPONSESTATUSTABLE.CALLRESULT': '调用结果',
  'DEBUGSERVICE.RESPONSESTATUSTABLE.STATUSCODE': '状态码',
  'DEBUGSERVICE.RESPONSESTATUSTABLE.RESPTIME': '响应时间',
  'DEBUGSERVICE.RESPONSESTATUSTABLE.STATUSDESC': '状态描述',
  'SERVICEUSECASELIST.TITLE.SERVICEUSECASELIST': '服务用例列表',
  'SERVICEUSECASELIST.TITLE.APIGSERVICEUSECASELIST': 'APIG服务用例列表',
  'SERVICEUSECASELIST.FORM.CREATEDATE': '创建日期',
  'SERVICEUSECASELIST.FORM.PLACEHOLDER': '输入关键词',
  'DOWNLOAD.MODAL.SELECTENTITY': '选择实体',
  'DOWNLOAD.MODAL.SELECTCATALOG': '选择目录',
  'DOWNLOAD.MODAL.TITLEDOMAIN': '领域实体',
  'DOWNLOAD.MODAL.TITLECATALOG': ' 目录名称',
  'DOWNLOAD.MODAL.SELECTFILE': '选择文件类型',
  'DOWNLOAD.MODAL.DOWNLOADFILE': '下载文件',
  'DOWNLOAD.FORM.FILETYPE': '文档类型',
  'DOWNLOAD.FORM.VERSION': 'Swagger 版本',
  'DOWNLOAD.FORM.EXAMPLE': '生成并携带消息示例',
  'DOWNLOAD.GENERATE.SUCCESSFULTEXT': '文档已成功生成',
  'DOWNLOAD.GENERATE.SUCCESSFULTIPS': '提示: 请点击"下载"按钮来获取文件.',
  'DOWNLOAD.TITLE.CONFIRM': '确定刷新缓存吗?',
  'DOWNLOAD.TITLE.SUPPORTED': '支持的过滤类型',
  'DOWNLOAD.TITLE.SEEMORE': '查看更多',
  'DOWNLOAD.TITLE.COMPARISON': '比较运算符',
  'DOWNLOAD.TITLE.LOGICAL': '逻辑运算符',
  'DOWNLOAD.TITLE.STRINGFUNCTIONS': '字符串函数',
  'DATAMODELLIST.TABLE.TITLE': '数据模型列表',
  'DATAMODELLIST.TABLE.ADD': '新增',
  'DATAMODELLIST.TABLE.NAME': '名称',
  'DATAMODELLIST.TABLE.CODE': '编码',
  'DATAMODELLIST.TABLE.TYPE': '类型',
  'DATAMODELLIST.TABLE.CREATIONSOURCE': '创建来源',
  'DATAMODELLIST.TABLE.DESC': '描述',
  'DATAMODELLIST.TABLE.OPT': '操作',
  'DATAMODELLIST.TABLE.DELTIP': '确定要删除这个数据模型吗？',
  'DATAMODELLIST.TABLE.IGNOREVER': '忽略版本',
  'DATAMODELLIST.DRAWER.ADDTITLE': '增加数据模型',
  'DATAMODELLIST.DRAWER.EDITTITLE': '修改数据模型',
  'DATAMODELLIST.DRAWER.CLASSIFICATION': '类别',
  'DATAMODELLIST.DRAWER.TABLE': '表格',
  'DATAMODELLIST.DRAWER.VIEWSQL': '视图 SQL',
  'DATAMODELLIST.DRAWER.SUBMIT': '确认',
  'DATAMODELLIST.DRAWER.CANCEL': '取消',
  'DATAMODELLISTDETAIL.INFO.TITLE': '基本信息',
  'DATAMODELLISTDETAIL.INFO.OPT': '修改',
  'DATAMODELLISTDETAIL.VIEWSQL.TITLE': '查看 SQL',
  'DATAMODELLISTDETAIL.VIEWSQL.PROVIEW': '生产视图',
  'DATAMODELLISTDETAIL.VIEWSQL.QUERYSQL': '查询 SQL',
  'DATAMODELLISTDETAIL.ENTITIES.TITLE': '使用这个数据模型的实体',
  'DATAMODELLISTDETAIL.ENTITIES.NAME': '名称',
  'DATAMODELLISTDETAIL.ENTITIES.CODE': '编号',
  'DATAMODELLISTDETAIL.ENTITIES.CREATIONSOURCE': '创建来源',
  'DATAMODELLISTDETAIL.ENTITIES.ENTITYTYPE': '实体类型',
  'DATAMODELLISTDETAIL.ENTITIES.DESC': '描述',
  'DATAMODELLISTDETAIL.ENUMS.TITLE': '使用这个数据模型的枚举',
  'DATAMODELLISTDETAIL.ENUMS.NAME': '名称',
  'DATAMODELLISTDETAIL.ENUMS.CODE': '编号',
  'DATAMODELLISTDETAIL.ENUMS.CREATIONSOURCE': '创建来源',
  'DATAMODELLISTDETAIL.ENUMS.ENTITYTYPE': '实体类型',
  'DATAMODELLISTDETAIL.ENUMS.DESC': '描述',
  'DATAMODELLISTDETAIL.ADD.SUCCESS': '成功新增数据模型.',
  'DATAMODELLISTDETAIL.EDIT.SUCCESS': '成功修改数据模型.',
  'DATAMODELLISTDETAIL.DELETE.SUCCESS': '成功删除数据模型.',

  'ENUMMANAGEMENTLIST.TABLE.TITLE': '枚举列表',
  'ENUMMANAGEMENTLIST.TABLE.ADD': '新增',
  'ENUMMANAGEMENTLIST.TABLE.NAME': '名称',
  'ENUMMANAGEMENTLIST.TABLE.TYPE': '类型',
  'ENUMMANAGEMENTLIST.TABLE.CREATIONSOURCE': '创建来源',
  'ENUMMANAGEMENTLIST.TABLE.DESC': '描述',
  'ENUMMANAGEMENTLIST.TABLE.OPT': '操作',
  'ENUMMANAGEMENTLIST.TABLE.VALUE': '值',
  'ENUMMANAGEMENTLIST.TABLE.DELTIP': '确定要删除这个枚举吗？',
  'ENUMMANAGEMENTLIST.DRAWER.ADDTITLE': '增加枚举',
  'ENUMMANAGEMENTLIST.DRAWER.EDITTITLE': '修改枚举',
  'ENUMMANAGEMENTLIST.DRAWER.CONSTANTLIST': '常量列表',
  'ENUMMANAGEMENTLIST.DRAWER.DATAMODEL': '数据模型',
  'ENUMMANAGEMENTLIST.DRAWER.VALUECOLUMN': '值列',
  'ENUMMANAGEMENTLIST.DRAWER.NAMECOLUMN': '名称列',
  'ENUMMANAGEMENTLIST.ENUMS.TITLE': '使用这个枚举的实体属性',
  'ENUMMANAGEMENTLIST.ENUMS.ENTITYNAME': '实体名称',
  'ENUMMANAGEMENTLIST.ENUMS.PROPERTYNAME': '属性名称',
  'ENUMMANAGEMENTLIST.ENUMS.PROPERTYTYPE': '属性类型',
  'ENUMMANAGEMENTLIST.ENUMS.CREATIONSOURCE': '创建来源',
  'ENUMMANAGEMENTLIST.ENUMS.DESC': '描述',
  'ENUMMANAGEMENTLIST.ADD.SUCCESS': '成功新增枚举.',
  'ENUMMANAGEMENTLIST.EDIT.SUCCESS': '成功修改枚举.',
  'ENUMMANAGEMENTLIST.DELETE.SUCCESS': '成功删除枚举.',

  'DOMAINDETAIL.VERSION.TEST.TITLE': '测试版本',
  'DOMAINDETAIL.VERSION.USED.TITLE': '在用版本',
  'DOMAINDETAIL.VERSION.PENDING.TITLE': '待生效版本',
  'DOMAINDETAIL.VERSION.ISHOTFIX': '是热更新： ',

  'ERRORCODE.FILTERCONDITIONS.TITLE': '筛选条件',
  'ERRORCODE.COMMON.APPLYDOMAIN': '应用领域',
  'ERRORCODE.COMMON.APPLYDOMAIN.NOTE': `产品环境只能选择“All”，项目环境不能选择“All”`,
  'ERRORCODE.COMMON.ERRORCODE': '错误码',
  'ERRORCODE.COMMON.ERRORREASON': '错误原因',
  'ERRORCODE.COMMON.REASONPARAMETER.NOTE': '注意：错误原因中可使用 {0}、{1}等定义变量。',
  'ERRORCODE.COMMON.CREATIONSOURCE': '创建来源',
  'ERRORCODE.COMMON.RELATEDSERVICE': '关联服务',
  'ERRORCODE.ERRORCODELIST.TITLE': '错误码列表',
  'ERRORCODE.ADD': '新增',
  'ERRORCODE.ADD.TITLE': '新增错误码',
  'ERRORCODE.EDIT.TITLE': '修改错误码',
  'ERRORCODE.RELATED.TITLE': '已关联的服务列表',
  'ERRORCODE.RELATED.SERVICENAME': '服务名称',
  'ERRORCODE.RELATED.HTTPSTATUS': 'HTTP 状态',
  'ERRORCODE.RELATED.DOMAINENTITY': '领域实体',
  'ERRORCODE.DEFAULT.MATCHCONDITIONS': '默认匹配条件',
  'ERRORCODE.DEFAULT.MATCHCONDITIONS.NO_VALUE': '您不能使用没有值的默认匹配条件。',
  'ERRORCODE.DEFAULT.REASONPARAMETER': '默认原因参数',
  'ERRORCODE.EXPREASON.MATCHRESPONSESTATUS': '匹配响应状态',
  'ERRORCODE.EXPREASON.USEDEFALUTMATCHCONDITIONS': '使用默认匹配条件',
  'ERRORCODE.EXPREASON.MATCHCONDITIONS': '匹配条件',
  'ERRORCODE.EXPREASON.MATCHCONDITIONS.REQUIRED': '请输入匹配条件。',
  'ERRORCODE.EXPREASON.MATCHPRIORITY': '匹配优先级',
  'ERRORCODE.EXPREASON.REASONPARAMETER': '原因参数',
  'ERRORCODE.OTHERERRORDETAIL.TITLE': '查看其他错误详情',
  'ERRORCODE.OTHERERRORDETAIL.LIST.TITLE': '其他错误列表',
  'ERRORCODE.OTHERERRORDETAIL.TRACEID': '跟踪ID',
  'ERRORCODE.OTHERERRORDETAIL.ERRORRESPONSE': '错误响应',
  'ERRORCODE.OTHERERRORDETAIL.SERVICENAME': '服务名称',
  'ERRORCODE.OTHERERRORDETAIL.SERVICEPATH': '服务路径',
  'ERRORCODE.OTHERERRORDETAIL.RELEASEDVERSION': '已发布的版本',
  'ERRORCODE.OTHERERRORDETAIL.HAPPENEDDATE': '发生时间',
  'ERRORCODE.OTHERERRORDETAIL.OTHERERRORLIST': '其他错误列表',
  'ERRORCODE.OTHERERRORDETAIL.RECORD': '记录其他错误信息',
  'ERRORCODE.OTHERERRORDETAIL.RECORD.EXPIRED_TIME': ' ( 注意：该开关将在 {expiredTime} 时关闭 )',
  'ERRORCODE.OTHERERRORDETAIL.ERRORRESPONSEINFOMATION': '错误响应信息',
  'COPYSCHEMA.TITLE': '拷贝Schema',
  'COPYSCHEMA.NAME.VALID': '名称中不能包含空格',
  'COPYSCHEMA.NAME.INVALID_FORMAT': '开头是大写字母，只能包含英文字母、数字和下划线',

  // 过滤组件国际化
  'FILTER.COLUMN.OPERATOR': '运算符',
  'FILTER.COLUMN.DESCRIPTION': '描述',
  'FILTER.COLUMN.FUNCTION': '函数',

  // 比较运算符
  'FILTER.COMPARISON.EQUAL': '等于',
  'FILTER.COMPARISON.NOT_EQUAL': '不等于',
  'FILTER.COMPARISON.GREATER_THAN': '大于',
  'FILTER.COMPARISON.GREATER_EQUAL': '大于等于',
  'FILTER.COMPARISON.LESS_THAN': '小于',
  'FILTER.COMPARISON.LESS_EQUAL': '小于等于',
  'FILTER.COMPARISON.IS_MEMBER': '是成员之一',

  // 逻辑运算符
  'FILTER.LOGICAL.AND': '逻辑与',
  'FILTER.LOGICAL.OR': '逻辑或',
  'FILTER.LOGICAL.NOT': '逻辑非',

  // 字符串函数
  'FILTER.STRING.CONCAT': '连接两个字符串的函数.',
  'FILTER.STRING.CONTAINS': '字符串包含另一个字符串.',
  'FILTER.STRING.STARTSWITH': '字符串以另一个字符串开头.',
  'FILTER.STRING.ENDSWITH': '字符串以另一个字符串结尾.',
  'FILTER.STRING.INDEXOF': '返回字符串的位置.',
  'FILTER.STRING.LENGTH': '返回字符串的长度.',
  'FILTER.STRING.SUBSTRING': '截取字符串并返回新的子字符串.',
  'FILTER.STRING.TOLOWER': '将字符串转换为小写.',
  'FILTER.STRING.TOUPPER': '将字符串转换为大写.',
  'FILTER.STRING.TRIM': '去除字符串前后的空格.',

  // 服务编排组件
  'SERVICE.ORCHESTRATE.INVALID_SOURCE': '不合法的消息来源:',
  'SERVICE.ORCHESTRATE.RECEIVED_MESSAGE': '收到返回消息:',
  'SERVICE.ORCHESTRATE.UNKNOWN_TYPE': '未知的消息类型:',

  // 脚本管理组件
  'SCRIPT.SAVE.SUCCESS': '保存成功!',
  'SCRIPT.SAVE.FAILURE': '保存失败!',
  'SCRIPT.EXECUTE_AFTER_RESPONSE_STATUS': '响应状态后执行',
  'SCRIPT.CODE_SNIPPET': '代码片段',
  'PROJECT.COMMON.SAVE': '保存',
  'PROJECT.COMMON.CANCEL': '取消',
  'SCRIPT.DESCRIPTION': '您可以输入QLExpress脚本，它将在调用实现服务{timing}运行。',
  'SCRIPT.BEFORE': '之前',
  'SCRIPT.AFTER': '之后',
  'SCRIPT.SNIPPETS_TITLE': '代码片段',

  // 导入文件组件
  'IMPORT.FILE.SELECT_WARNING': '请选择一个文件上传。',
  'IMPORT.FILE.SUCCESS': '导入成功！',
  'IMPORT.FILE.FAILURE': '导入失败。',
  'IMPORT.FILE.ERROR': '导入过程中发生错误。',
  'IMPORT.FILE.TITLE': '导入域{type}文件(.jar)',
  'IMPORT.FILE.HOTFIX': '热修复',
  'IMPORT.FILE.CONFIGURATION': '配置',
  'IMPORT.FILE.DRAG_TEXT': '点击或拖拽域{type}文件(.jar)到此区域上传',
  'IMPORT.FILE.HOTFIX_WARNING': '导入域热修复文件时，现有的热修复数据将被清除。',
  'IMPORT.FILE.SWITCH_VERSION': '文件导入成功后，发布域版本并将域测试版本切换到新发布的版本。',

  // 主页和文档页面
  'HOMEPAGE.WELCOME': 'Yay！欢迎使用 umi！',
  'HOMEPAGE.START_GUIDE': '开始使用，请编辑 pages/index.tsx 并保存以重新加载。',
  'DOCS.INTRO': '这是 umi 文档。',

  // UnusablePage组件
  'UNUSABLE.TIP': '提示',
  'UNUSABLE.ENVIRONMENT_NOT_CONFIG': '当前环境不是配置环境，无法使用热修复功能！',

  // 域对象详情
  'DOMAIN.BASIC_INFO': '基本信息',
  'DOMAIN.ENTITY_LIST': '实体列表',
  'DOMAIN.ENTITY.DELETE_SUCCESS': '成功删除领域实体！',
  'PROJECT.COMMON.ADD': '新增',
  'PROJECT.COMMON.CODE': '编码',

  // BssDomainObjectDetail组件额外翻译
  'DOMAIN.ENTITY.DELETE_CONFIRM': '删除实体',
  'DOMAIN.ENTITY.DELETE_DESCRIPTION': '确定要删除该实体吗？',
  'DOMAIN.ENTITY.TYPE': '实体类型',
  'DOMAIN.ENTITY.RELATIONSHIP': '实体关系',
  'DOMAIN.TYPE.AGGREGATE': '聚合',
  'DOMAIN.TYPE.COLLECTION': '集合',
  'DOMAIN.ENTITY.TYPE.SERVICEHOLDER': '服务持有者',
  'DOMAIN.ENTITY.TYPE.AGGREGATEROOT': '聚合根',
  'DOMAIN.ENTITY.TYPE.ENTITY': '实体',
  'DOMAIN.STATE.DRAFT': '草稿',
  'DOMAIN.STATE.RELEASED': '已发布',
  'DOMAIN.CREATION.PRODUCT': '产品拥有',
  'DOMAIN.CREATION.PROJECT': '项目自定义',
  'DOMAIN.OPERATION': '操作',

  // AddDomainEntity组件翻译
  'DOMAIN.ENTITY.ADD.TITLE': '新增领域实体',
  'DOMAIN.ENTITY.FORM.NAME': '名称',
  'DOMAIN.ENTITY.FORM.CODE': '编码',
  'DOMAIN.ENTITY.FORM.CODE.VALIDATOR': '编码格式为驼峰格式，只能包含英文字母和数字，以大写字母开头',
  'DOMAIN.ENTITY.FORM.CREATION_SOURCE': '创建来源',
  'DOMAIN.ENTITY.FORM.IGNORE_VERSION': '忽略版本',
  'DOMAIN.ENTITY.FORM.ENTITY_TYPE': '实体类型',
  'DOMAIN.ENTITY.FORM.DEFAULT_PAGE_SIZE': '默认页面大小',
  'DOMAIN.ENTITY.FORM.DATA_MODEL': '数据模型',
  'DOMAIN.ENTITY.FORM.ROOT_ENTITY': '根实体',
  'DOMAIN.ENTITY.FORM.DESCRIPTION': '描述',
  'DOMAIN.ENTITY.ADD.SUCCESS': '成功新增领域实体！',
  'PROJECT.COMMON.SUBMIT': '提交',

  // EditDomainEntity组件翻译
  'DOMAIN.ENTITY.EDIT.TITLE': '编辑领域实体',
  'DOMAIN.ENTITY.EDIT.SUCCESS': '成功编辑领域实体！',

  // EditDomainObject组件翻译
  'DOMAIN.OBJECT.EDIT.TITLE': '编辑领域对象',
  'DOMAIN.OBJECT.EDIT.SUCCESS': '成功编辑领域对象！',
  'DOMAIN.OBJECT.FORM.NAME': '名称',
  'DOMAIN.OBJECT.FORM.NAME.REQUIRED': '请输入名称！',
  'DOMAIN.OBJECT.FORM.CODE': '编码',
  'DOMAIN.OBJECT.FORM.CODE.REQUIRED': '请输入编码！',
  'DOMAIN.OBJECT.FORM.CODE.VALIDATOR': '请输入正确的编码！',
  'DOMAIN.OBJECT.FORM.TYPE': '类型',

  // AddDomainObject组件翻译
  'DOMAIN.OBJECT.ADD.TITLE': '新增领域对象',
  'DOMAIN.OBJECT.ADD.SUCCESS': '成功新增领域对象！',
  'DOMAIN.OBJECT.FORM.CODE.VALIDATOR_LOWERCASE': '编码格式为驼峰格式，只能包含英文字母和数字，以小写字母开头。',

  // EntityProp组件国际化
  'ENTITYPROP.TITLE': '实体属性',
  'ENTITYPROP.ADD.TITLE': '新增实体属性',
  'ENTITYPROP.EDIT.TITLE': '编辑实体属性',
  'ENTITYPROP.COLUMN_NAME': '列名',
  'ENTITYPROP.COLUMN_NAME.FORMAT': '只允许使用大写字母、数字和下划线(_)。',
  'ENTITYPROP.NAME.NOSPACES': '实体属性名称不能包含空格',
  'ENTITYPROP.ALLOWABLE_VALUES': '可用值',
  'ENTITYPROP.MIN': '最小值',
  'ENTITYPROP.MAX': '最大值',
  'ENTITYPROP.PRECISION': '精度',
  'ENTITYPROP.SCALE': '小数位',
  'ENTITYPROP.DEFAULT_VALUE': '默认值',
  'ENTITYPROP.INDEX_FUNCTION': '索引函数',
  'ENTITYPROP.SENSITIVE_DATA_LEVEL': '敏感数据级别',
  'ENTITYPROP.IGNORE_VERSION': '忽略版本',

  // Add the new keys that were referenced but missing
  'ERRORCODE.EXPREASON.NO_CONFIG_NEEDED': '无需配置，系统有默认匹配逻辑。',
  'ERRORCODE.EXPREASON.PRIORITY_LIMIT': '只允许输入1~99。',
  'ERRORCODE.EXPREASON.PRIORITY_NOTE': '注意：输入值越小，优先级越高。',

  // RestartOdhServiceModal组件
  'RESTART.TITLE': '重启 Odh 服务',
  'RESTART.FAILED': '重启失败。',
  'RESTART.ERROR': '重启过程中发生错误。',
  'RESTART.CONFIRM': '您确定要重启 odh 服务吗？',

  // ErrorModal组件
  'ERROR.TITLE': '错误',
  'ERROR.YOU_CAN': '您可以',
  'ERROR.COPY_ERROR_LOG': '复制错误日志',
  'ERROR.CONTACT_ADMIN': '并在需要时联系管理员',
  'ERROR.COPY_SUCCESS': '复制成功！',
  'ERROR.TRACE_ID': '追踪 ID',
  'ERROR.STACK': '堆栈',
  'ERROR.MORE': '更多',

  // APIGParamsTable组件
  'APIG.PARAM.NAME': '名称',
  'APIG.PARAM.TYPE': '类型',
  'APIG.PARAM.REQUIRED': '必填',
  'APIG.PARAM.YES': '是',
  'APIG.PARAM.NO': '否',
  'APIG.PARAM.ALLOWABLE_VALUES': '可用值',
  'APIG.PARAM.FORMAT': '格式',
  'APIG.PARAM.DESCRIPTIONS': '描述',

  // EditParamDrawer组件
  'APIG.DRAWER.EMPTY': '空',
  'APIG.DRAWER.LENGTH': '长度',
  'APIG.DRAWER.FORMAT': '格式',
  'APIG.DRAWER.VALUES': '值列表',
  'APIG.DRAWER.RANGE': '范围',
  'APIG.DRAWER.IS_ARRAY': '是否数组',
  'APIG.DRAWER.MIN': '最小值',
  'APIG.DRAWER.MAX': '最大值',
  'APIG.DRAWER.EXAMPLE': '示例',

  // RequestTable组件
  'REQUEST.HEADER': 'Header',
  'REQUEST.PATH': 'Path',
  'REQUEST.QUERY': 'Query',
  'REQUEST.BODY': 'Body',
  'REQUEST.CONTENT_TYPE': 'Content Type',

  // ResponseTable组件
  'RESPONSE.SUCCESS': '成功',
  'RESPONSE.FAILED': '失败',

  // EnumManagementDrawer组件
  'ENUM.FORM.PLEASE_ENTER_VALUE': '请输入值',
  'ENUM.FORM.PLEASE_ENTER_NAME': '请输入名称',
  'ENUM.FORM.DOMAIN_MODEL_NOTE': '注意：只能选择"领域模型"类型的数据模型。',

  // DebugService组件
  'DEBUGSERVICE.ERROR.CANNOT_BE_NULL': '不能为空',
  'DEBUGSERVICE.USECASE.ADD_SUCCESS': '成功新增用例！',
  'DEBUGSERVICE.USECASE.EDIT_SUCCESS': '成功修改用例！',
  'DEBUGSERVICE.APIGSERVICE.ADD_SUCCESS': '成功新增APIG服务！',
  'DEBUGSERVICE.APIGSERVICE.EDIT_SUCCESS': '成功修改APIG服务！',

  // ErrorCode组件
  'ERRORCODE.EXPORT.TOOLTIP': '基于筛选条件',
  'ERRORCODE.EXPORT.BUTTON': '导出(.xls)',
  'ERRORCODE.COMMON.ALL': '全部',
  'ERRORCODE.LIST.CREATED_DATE': '创建日期',
  'ERRORCODE.LIST.UPDATE_DATE': '更新日期',
  'ERRORCODE.DELETE.TITLE': '删除错误码',
  'ERRORCODE.DELETE.CONFIRM': '确定要删除此记录吗？',
  'ERRORCODE.DELETE.YES': '是',
  'ERRORCODE.DELETE.NO': '否',

  // BssDomainDetail组件国际化
  'DOMAIN.OBJECT.LIST': '领域对象列表',
  'DOMAIN.RELEASED.VERSIONS': '已发布版本',
  'DOMAIN.VERSION': '版本',
  'DOMAIN.RELEASED.DATE': '发布日期',
  'DOMAIN.OPERATOR': '操作人',
  'DOMAIN.RELEASED.NOTES': '发布说明',
  'DOMAIN.DELETE.OBJECT.TITLE': '删除领域对象',
  'DOMAIN.DELETE.OBJECT.DESCRIPTION': '确定要删除该领域对象吗？',
  'DOMAIN.DELETE.OBJECT.SUCCESS': '成功删除领域对象！',
  'DOMAIN.CHANGE.VERSION': '切换版本',
  'DOMAIN.CHANGE.VERSION.CONFIRM': '确定要切换版本吗？',
  'DOMAIN.CHANGE.VERSION.NO_RESTART': '不重启odh服务',
  'DOMAIN.REFRESH.CACHE.SUCCESS': '刷新缓存成功！',
  'DOMAIN.REFRESH.CACHE': '刷新缓存',
  'DOMAIN.IMPORT': '导入',
  'DOMAIN.IMPORT.HOTFIX': '导入热修复',
  'DOMAIN.SEARCH': '搜索',
  'DOMAIN.RESET': '重置',
  'DOMAIN.DOWNLOAD.SUCCESS': '文件下载成功！',
  'DOMAIN.REFRESH.CACHE.FAILED': '刷新缓存失败。',
  'DOMAIN.REFRESH.CACHE.OPEN_DATA_API': 'Open Data API',
  'DOMAIN.REFRESH.CACHE.DOMAIN_DUBBO': 'Domain Dubbo',
  'DOMAIN.REFRESH.CACHE.DOMAIN_MODEL_TABLE': 'Domain Model Table',
  'DOMAIN.REFRESH.CACHE.TFM_SERVICE': 'TFM Service',
  'DOMAIN.PUBLIC_SENSITIVE': '公共敏感',

  // ExpandEntity 相关国际化
  'EXPANDENTITY.TITLE': '扩展实体',
  'EXPANDENTITY.RELATIONSHIP_TYPE': '关系类型',
  'EXPANDENTITY.EXPAND_ENTITY': '扩展实体',
  'EXPANDENTITY.ASSOCIATED_RELATIONSHIP': '关联关系',
  'EXPANDENTITY.EXPAND_NAME': '扩展名称',
  'EXPANDENTITY.CREATION_SOURCE': '创建来源',
  'EXPANDENTITY.DESCRIPTION': '描述',
  'EXPANDENTITY.OPERATION': '操作',
  'EXPANDENTITY.DELETE.TITLE': '删除实体',
  'EXPANDENTITY.DELETE.DESCRIPTION': '确定要删除此记录吗？',
  'EXPANDENTITY.DELETE.YES': '是',
  'EXPANDENTITY.DELETE.NO': '否',
  'EXPANDENTITY.DELETE.SUCCESS': '成功删除扩展实体！',

  // AddExpandEntity 相关国际化
  'EXPANDENTITY.ADD.TITLE': '新增扩展实体',
  'EXPANDENTITY.ADD.SUBMIT': '提交',
  'EXPANDENTITY.ADD.CANCEL': '取消',
  'EXPANDENTITY.ADD.CURRENT_ENTITY': '当前实体',
  'EXPANDENTITY.ADD.RELATIONSHIP_TYPE': '关系类型',
  'EXPANDENTITY.ADD.EXPAND_ENTITY': '扩展实体',
  'EXPANDENTITY.ADD.ASSOCIATED_RELATIONSHIP': '关联关系',
  'EXPANDENTITY.ADD.ADD_RELATIONSHIP': '+ 新增关联关系',
  'EXPANDENTITY.ADD.EXPAND_NAME': '扩展名称',
  'EXPANDENTITY.ADD.CREATION_SOURCE': '创建来源',
  'EXPANDENTITY.ADD.IGNORE_VERSION': '忽略版本',
  'EXPANDENTITY.ADD.DESCRIPTIONS': '描述',
  'EXPANDENTITY.ADD.SUCCESS': '成功新增扩展实体！',
  'EXPANDENTITY.ADD.RELATIONSHIP_REQUIRED': '请至少输入一个关联关系！',
  'EXPANDENTITY.ADD.RELATIONSHIP_DUPLICATE': '存在相同的关联关系，请检查！',
  'EXPANDENTITY.ADD.NAME_FORMAT_ERROR': '扩展名称格式为驼峰格式，只能包含英文字母和数字，以小写字母开头。',

  // EditExpandEntity 相关国际化
  'EXPANDENTITY.EDIT.TITLE': '编辑扩展实体',
  'EXPANDENTITY.EDIT.SUCCESS': '成功编辑扩展实体！',

  // EntityEvents 相关国际化
  'ENTITYEVENTS.EVENT_NAME': '事件名称',
  'ENTITYEVENTS.EVENT_CODE': '事件编码',
  'ENTITYEVENTS.REMARKS': '备注',

  // EntityEvent 相关国际化
  'ENTITYEVENT.TITLE': '实体事件',
  'ENTITYEVENT.EVENT_NAME': '事件名称',
  'ENTITYEVENT.EVENT_CODE': '事件编码',
  'ENTITYEVENT.CREATION_SOURCE': '创建来源',
  'ENTITYEVENT.DESCRIPTION': '描述',
  'ENTITYEVENT.OPERATION': '操作',
  'ENTITYEVENT.DELETE.TITLE': '删除实体事件',
  'ENTITYEVENT.DELETE.DESCRIPTION': '确定要删除此记录吗？',
  'ENTITYEVENT.DELETE.YES': '是',
  'ENTITYEVENT.DELETE.NO': '否',
  'ENTITYEVENT.DELETE.SUCCESS': '成功删除实体事件！',

  // AddEntityEvent 相关国际化
  'ENTITYEVENT.ADD.TITLE': '新增实体事件',
  'ENTITYEVENT.ADD.SUBMIT': '提交',
  'ENTITYEVENT.ADD.CANCEL': '取消',
  'ENTITYEVENT.ADD.EVENT_NAME': '事件名称',
  'ENTITYEVENT.ADD.RLC_EVENT_CATALOG': 'RLC事件目录',
  'ENTITYEVENT.ADD.RLC_EVENT_NAME': 'RLC事件名称',
  'ENTITYEVENT.ADD.CREATION_SOURCE': '创建来源',
  'ENTITYEVENT.ADD.IGNORE_VERSION': '忽略版本',
  'ENTITYEVENT.ADD.DESCRIPTIONS': '描述',
  'ENTITYEVENT.ADD.SUCCESS': '成功新增实体事件！',

  // EditEntityEvent 相关国际化
  'ENTITYEVENT.EDIT.TITLE': '编辑实体事件',
  'ENTITYEVENT.EDIT.SUCCESS': '成功编辑实体事件！',

  // ExtensionService 相关国际化
  'EXTENSIONSERVICE.TITLE': '扩展服务',
  'EXTENSIONSERVICE.SERVICE_NAME': '服务名称',
  'EXTENSIONSERVICE.SERVICE_CATALOG': '服务目录',
  'EXTENSIONSERVICE.SERVICE_SUBCATALOG': '服务子目录',
  'EXTENSIONSERVICE.CREATION_SOURCE': '创建来源',
  'EXTENSIONSERVICE.IMPLEMENT_TYPE': '实现类型',
  'EXTENSIONSERVICE.SERVICE_PATH': '服务路径',
  'EXTENSIONSERVICE.OPERATION': '操作',
  'EXTENSIONSERVICE.DELETE.TITLE': '删除扩展服务',
  'EXTENSIONSERVICE.DELETE.DESCRIPTION': '确定要删除此记录吗？',
  'EXTENSIONSERVICE.DELETE.YES': '是',
  'EXTENSIONSERVICE.DELETE.NO': '否',
  'EXTENSIONSERVICE.DELETE.SUCCESS': '成功删除扩展服务！',
  'EXTENSIONSERVICE.DELETE.FAIL': '删除扩展服务失败！',
  'EXTENSIONSERVICE.TRANSFER': '迁移',
  'EXTENSIONSERVICE.TRANSFER.TITLE': '迁移扩展服务',
  'EXTENSIONSERVICE.TRANSFER.CURRENT_ENTITY': '当前实体',
  'EXTENSIONSERVICE.TRANSFER.TARGET_ENTITY': '目标实体',
  'EXTENSIONSERVICE.TRANSFER.TARGET_ENTITY_REQUIRED': '请选择目标实体',
  'EXTENSIONSERVICE.TRANSFER.SERVICES': '服务',
  'EXTENSIONSERVICE.TRANSFER.SERVICE_REQUIRED': '请选择至少一个要迁移的服务',
  'EXTENSIONSERVICE.TRANSFER.SOURCE_SERVICES': '当前实体服务',
  'EXTENSIONSERVICE.TRANSFER.TARGET_SERVICES': '目标实体服务',
  'EXTENSIONSERVICE.TRANSFER.SUBMIT': '提交',
  'EXTENSIONSERVICE.TRANSFER.CANCEL': '取消',
  'EXTENSIONSERVICE.TRANSFER.SUCCESS': '迁移扩展服务成功',
  'EXTENSIONSERVICE.TRANSFER.FAIL': '迁移扩展服务失败',
  'EXTENSIONSERVICE.TRANSFER.WARNING':
    '将服务迁移到其它领域实体之后，服务的Restful API路径会发生变化，请谨慎处理。迁移完成后，',

  'EXTENSIONSERVICE.TRANSFER.WARNING_HIGHLIGHT': '请注意修改发布到APIG上的服务路径。',
  'EXTENSIONSERVICE.TRANSFER.ITEM_UNIT': '项',
  'EXTENSIONSERVICE.TRANSFER.ITEMS_UNIT': '项',

  // AddExtensionService 相关国际化
  'EXTENSIONSERVICE.ADD.TITLE': '新增扩展服务',
  'EXTENSIONSERVICE.ADD.SUBMIT': '提交',
  'EXTENSIONSERVICE.ADD.CANCEL': '取消',
  'EXTENSIONSERVICE.ADD.SERVICE_NAME': '服务名称',
  'EXTENSIONSERVICE.ADD.ENTITY': '实体',
  'EXTENSIONSERVICE.ADD.CREATION_SOURCE': '创建来源',
  'EXTENSIONSERVICE.ADD.DEPRECATED': '已弃用',
  'EXTENSIONSERVICE.ADD.MATCH_VERSION': '匹配版本',
  'EXTENSIONSERVICE.ADD.SERVICE_CATALOG': '服务目录',
  'EXTENSIONSERVICE.ADD.SERVICE_PATH': '服务路径',
  'EXTENSIONSERVICE.ADD.IMPLEMENT_TYPE': '实现类型',
  'EXTENSIONSERVICE.ADD.DUBBO_CLASS_PATH': 'Dubbo类路径',
  'EXTENSIONSERVICE.ADD.DUBBO_METHOD': 'Dubbo方法',
  'EXTENSIONSERVICE.ADD.RESTFUL_API_PATH': 'Restful API路径',
  'EXTENSIONSERVICE.ADD.TFM_SERVICE_NAME': 'TFM服务名称',
  'EXTENSIONSERVICE.ADD.DESCRIPTIONS': '描述',
  'EXTENSIONSERVICE.ADD.SUCCESS': '成功新增实体服务！',
  'EXTENSIONSERVICE.ADD.FAIL': '新增实体服务失败。',
  'EXTENSIONSERVICE.ADD.ERROR': '新增实体服务时发生错误。',
  'EXTENSIONSERVICE.ADD.DUBBO_METHOD_ERROR': '此Dubbo方法不支持GET方法，请选择其他方法。',
  'EXTENSIONSERVICE.ADD.RESTFUL_METHOD_ERROR': 'Restful方法只能支持与Restful方法同名的方法或POST方法，请选择其他方法。',

  // EditExtensionService 相关国际化
  'EXTENSIONSERVICE.EDIT.SERVICE_PATH_NOTE':
    '注意：修改服务的路径或Method会导致这个服务项目扩展的信息丢失，请谨慎处理。',
  'EXTENSIONSERVICE.EDIT.TITLE': '编辑扩展服务',
  'EXTENSIONSERVICE.EDIT.SUCCESS': '成功编辑实体服务！',
  'EXTENSIONSERVICE.EDIT.FAIL': '编辑实体服务失败。',
  'EXTENSIONSERVICE.EDIT.ERROR': '编辑实体服务时发生错误。',
  'EXTENSIONSERVICE.EDIT.REFRESH': '刷新请求API参考',
  'EXTENSIONSERVICE.EDIT.REFRESH_NOTE':
    '注意：选择"是"将从dubbo客户端jar读取最新的请求和响应信息，并覆盖您已修改的配置。',

  // ManageExtensionService 相关国际化
  'MANAGEEXTENSIONSERVICE.BASIC_INFORMATION': '基本信息',
  'MANAGEEXTENSIONSERVICE.EDIT': '编辑',
  'MANAGEEXTENSIONSERVICE.IMPORT_BY_SWAGGER': '通过Swagger导入',
  'MANAGEEXTENSIONSERVICE.SERVICE_ORCHESTRATE': '服务编排',
  'MANAGEEXTENSIONSERVICE.NAME': '名称',
  'MANAGEEXTENSIONSERVICE.SERVICE_CATALOG': '服务目录',
  'MANAGEEXTENSIONSERVICE.SERVICE_SUBCATALOG': '服务子目录',
  'MANAGEEXTENSIONSERVICE.CREATION_SOURCE': '创建来源',
  'MANAGEEXTENSIONSERVICE.SERVICE_METHOD': '服务方法',
  'MANAGEEXTENSIONSERVICE.SERVICE_PATH': '服务路径',
  'MANAGEEXTENSIONSERVICE.IMPLEMENT_TYPE': '实现类型',
  'MANAGEEXTENSIONSERVICE.DUBBO_CLASS_PATH': 'Dubbo类路径',
  'MANAGEEXTENSIONSERVICE.DUBBO_METHOD': 'Dubbo方法',
  'MANAGEEXTENSIONSERVICE.RESTFUL_SERVICE_PATH': 'Restful服务路径',
  'MANAGEEXTENSIONSERVICE.HTTP_METHOD': 'Http方法',
  'MANAGEEXTENSIONSERVICE.DESCRIPTIONS': '描述',
  'MANAGEEXTENSIONSERVICE.IMPLEMENT_TYPE.NEW': '新建',
  'MANAGEEXTENSIONSERVICE.IMPLEMENT_TYPE.DUBBO': 'Dubbo API投影',
  'MANAGEEXTENSIONSERVICE.IMPLEMENT_TYPE.RESTFUL': 'Restful API投影',
  'MANAGEEXTENSIONSERVICE.IMPLEMENT_TYPE.CALL_SERVICE': '调用服务投影',
  'MANAGEEXTENSIONSERVICE.DEBUG': '调试',

  // ManageExtensionService 常量相关国际化
  'MANAGEEXTENSIONSERVICE.CONST.YES': '是',
  'MANAGEEXTENSIONSERVICE.CONST.NO': '否',
  'MANAGEEXTENSIONSERVICE.CONST.JSON': 'json',
  'MANAGEEXTENSIONSERVICE.CONST.FORM_DATA': 'form-data',
  'MANAGEEXTENSIONSERVICE.CONST.BINARY': '二进制',
  'MANAGEEXTENSIONSERVICE.CONST.PRODUCT_OWNED': '产品所有',
  'MANAGEEXTENSIONSERVICE.CONST.PROJECT_CUSTOMIZED': '项目定制',
  'MANAGEEXTENSIONSERVICE.CONST.SINGLE_DATA': '单条数据',
  'MANAGEEXTENSIONSERVICE.CONST.ARRAY_DATA': '数组数据',
  'MANAGEEXTENSIONSERVICE.CONST.NO_DATA': '无数据',
  'MANAGEEXTENSIONSERVICE.CONST.SUCCESS': '成功',
  'MANAGEEXTENSIONSERVICE.CONST.EXCEPTION': '异常',

  // ManageExtensionService 格式类型相关国际化
  'MANAGEEXTENSIONSERVICE.FORMAT_TYPE.PASSWORD': '密码',
  'MANAGEEXTENSIONSERVICE.FORMAT_TYPE.BYTE': '字节',
  'MANAGEEXTENSIONSERVICE.FORMAT_TYPE.EMAIL': '邮箱',
  'MANAGEEXTENSIONSERVICE.FORMAT_TYPE.BINARY': '二进制',
  'MANAGEEXTENSIONSERVICE.FORMAT_TYPE.FLOAT': '浮点数',
  'MANAGEEXTENSIONSERVICE.FORMAT_TYPE.DOUBLE': '双精度',
  'MANAGEEXTENSIONSERVICE.FORMAT_TYPE.DATE_TIME': '日期时间',
  'MANAGEEXTENSIONSERVICE.FORMAT_TYPE.DATE': '日期',
  'MANAGEEXTENSIONSERVICE.FORMAT_TYPE.UTC_TIME': 'UTC时间',

  // RequestInformation 和 AddRequestInformation 相关国际化
  'MANAGEEXTENSIONSERVICE.REQUEST.PARAM': '参数',
  'MANAGEEXTENSIONSERVICE.REQUEST.ADD.SUCCESS': '成功新增请求参数！',
  'MANAGEEXTENSIONSERVICE.REQUEST.UPDATE.SUCCESS': '成功修改请求参数！',
  'MANAGEEXTENSIONSERVICE.REQUEST.OPERATION.ERROR': '{operation}请求参数时发生错误。',
  'MANAGEEXTENSIONSERVICE.REQUEST.PARAM.NAME.SPACE.ERROR': '服务参数名称不能包含空格',
  'MANAGEEXTENSIONSERVICE.REQUEST.PARAM.IS_ARRAY': '是否数组',
  'MANAGEEXTENSIONSERVICE.REQUEST.PARAM.ALLOWABLE_VALUES': '允许值',
  'MANAGEEXTENSIONSERVICE.REQUEST.PARAM.DEFAULT_VALUE': '默认值',
  'MANAGEEXTENSIONSERVICE.SCHEMA.ADD.SUCCESS': '成功新增Schema！',
  'MANAGEEXTENSIONSERVICE.SCHEMA.NAME.INVALID': '模式名称必须以大写字母开头，并且只能包含字母、数字和下划线。',
  'MANAGEEXTENSIONSERVICE.SCHEMA.PLACEHOLDER': '请输入项目',
  'MANAGEEXTENSIONSERVICE.SCHEMA.NEW': '新建Schema',
  'MANAGEEXTENSIONSERVICE.SCHEMA.EDIT.NOTES': '注意：提交后，您可以点击参数表中的类型列中的Schema来修改其信息。',
  'MANAGEEXTENSIONSERVICE.NOTES': '注意',
  'MANAGEEXTENSIONSERVICE.MIN': '最小值',
  'MANAGEEXTENSIONSERVICE.MAX': '最大值',
  'MANAGEEXTENSIONSERVICE.MIN.ERROR': '最小值为 {min}',
  'MANAGEEXTENSIONSERVICE.VALUES.PLACEHOLDER': '输入多个值请用英文逗号分隔。',
  'MANAGEEXTENSIONSERVICE.REQUEST.DELETE.SUCCESS': '成功删除请求参数！',
  'MANAGEEXTENSIONSERVICE.REQUEST.DELETE.TITLE': '删除请求参数',
  'MANAGEEXTENSIONSERVICE.REQUEST.DELETE.DESCRIPTION': '确定要删除请求参数吗？',
  'MANAGEEXTENSIONSERVICE.REQUEST.PARAMETERS': '参数',
  'MANAGEEXTENSIONSERVICE.REQUEST.CONTENT_TYPE': '内容类型',
  'MANAGEEXTENSIONSERVICE.REQUEST.BODY.CHANGE.TITLE': '确认更改',
  'MANAGEEXTENSIONSERVICE.REQUEST.BODY.CHANGE.DESCRIPTION': '如果更改选择，之前的主体参数将被清除。确定要更改选择吗？',
  'MANAGEEXTENSIONSERVICE.REQUEST.BEFORE_CALL_SERVICE_SCRIPT': '服务调用前脚本',
  'MANAGEEXTENSIONSERVICE.REQUEST.INFORMATION': '请求信息',
  'PROJECT.COMMON.YES': '是',
  'PROJECT.COMMON.NO': '否',

  // ResponseInformation 和 AddResponseInformation 相关国际化
  'MANAGEEXTENSIONSERVICE.RESPONSE.ADD.TITLE': '新增响应{Header}参数',
  'MANAGEEXTENSIONSERVICE.RESPONSE.ADD.SUCCESS': '成功新增响应{Header}参数！',
  'MANAGEEXTENSIONSERVICE.RESPONSE.ADD.ERROR': '新增响应{Header}参数时发生错误。',
  'MANAGEEXTENSIONSERVICE.RESPONSE.HTTP_STATUS': 'HTTP状态码',
  'MANAGEEXTENSIONSERVICE.RESPONSE.CONTENT_TYPE': '内容类型',
  'MANAGEEXTENSIONSERVICE.RESPONSE.DATA': '响应数据',
  'MANAGEEXTENSIONSERVICE.RESPONSE.DELETE.SUCCESS': '成功删除响应{Header}参数！',
  'MANAGEEXTENSIONSERVICE.RESPONSE.DELETE.TITLE': '删除响应{Header}参数',
  'MANAGEEXTENSIONSERVICE.RESPONSE.DELETE.DESCRIPTION': '确定要删除响应{Header}参数吗？',
  'MANAGEEXTENSIONSERVICE.RESPONSE.MORE': '更多',
  'MANAGEEXTENSIONSERVICE.RESPONSE.INFORMATION': '响应信息',
  'MANAGEEXTENSIONSERVICE.RESPONSE.AFTER_SERVICE_SCRIPT': '服务响应后脚本',
  'MANAGEEXTENSIONSERVICE.RESPONSE.EDIT.TITLE': '编辑响应{Header}参数',
  'MANAGEEXTENSIONSERVICE.RESPONSE.UPDATE.SUCCESS': '成功修改响应{Header}参数！',
  'MANAGEEXTENSIONSERVICE.RESPONSE.UPDATE.ERROR': '修改响应{Header}参数时发生错误。',
  'MANAGEEXTENSIONSERVICE.RESPONSE.SUCCESS_STATUS_EXISTS': '已存在成功状态码 {code}。',

  // ResponseErrorCodeList 相关国际化
  'MANAGEEXTENSIONSERVICE.RESPONSE.ERRORCODE.CODE': '编码',
  'MANAGEEXTENSIONSERVICE.RESPONSE.ERRORCODE.REASON': '原因',
  'MANAGEEXTENSIONSERVICE.RESPONSE.ERRORCODE.PRIORITY': '优先级',
  'MANAGEEXTENSIONSERVICE.RESPONSE.ERRORCODE.DELETE.SUCCESS': '成功删除错误码！',
  'MANAGEEXTENSIONSERVICE.RESPONSE.ERRORCODE.DELETE.ERROR': '删除错误码失败！',
  'MANAGEEXTENSIONSERVICE.RESPONSE.ERRORCODE.DELETE.TITLE': '删除错误码',
  'MANAGEEXTENSIONSERVICE.RESPONSE.ERRORCODE.DELETE.DESCRIPTION': '确定要删除此记录吗？',
  'MANAGEEXTENSIONSERVICE.RESPONSE.ERRORCODE.SET.TITLE': '设置错误码允许值',
  'PROJECT.COMMON.OK': '确定',

  // SchemaManagement 相关国际化
  'SCHEMAMANAGEMENT.TITLE': 'Schema 管理',
  'SCHEMAMANAGEMENT.BASIC_INFO': '基本信息',
  'SCHEMAMANAGEMENT.SCHEMA_PROPERTIES': 'Schema 属性',
  'SCHEMAMANAGEMENT.COMPOSITION_SCHEMAS': '组合 Schemas',
  'SCHEMAMANAGEMENT.ADD': '新增',
  'SCHEMAMANAGEMENT.ADD_BY_CODE': '通过代码新增',
  'SCHEMAMANAGEMENT.DELETE_SCHEMA_CONFIRM': '确定要删除这个 Schema 吗？',
  'SCHEMAMANAGEMENT.DELETE_SCHEMA_NOTE':
    '注意：如果该 Schema 是通过读取 Dubbo 或 Swagger 生成的，请不要删除它。您可以将 Hidden 设置为 Yes。因为删除后，再次读取 Dubbo 或 Swagger 时，该属性仍会被添加。',
  'SCHEMAMANAGEMENT.DELETE_SCHEMA_PROPERTY_TITLE': '删除 Schema 属性',
  'SCHEMAMANAGEMENT.DELETE_SCHEMA_PROPERTY_CONFIRM': '确定要删除这个 Schema 属性吗？',
  'SCHEMAMANAGEMENT.DELETE_SCHEMA_PROPERTY_NOTE':
    '注意：如果该 Schema 是通过读取 Dubbo 或 Swagger 生成的，请不要删除它。您可以将 Hidden 设置为 Yes。因为删除后，再次读取 Dubbo 或 Swagger 时，该属性仍会被添加。',
  'SCHEMAMANAGEMENT.UNSAVED_CHANGES': 'Schema 属性尚未保存！',
  'SCHEMAMANAGEMENT.UNSAVED_CHANGES_CLEAR': '如果点击"确定"，未保存的数据将被清除。',
  'SCHEMAMANAGEMENT.MODIFY_SUCCESS': '成功修改 Schema 信息！',
  'SCHEMAMANAGEMENT.MODIFY_FAILED': '修改 Schema 信息失败。',
  'SCHEMAMANAGEMENT.ERROR_OCCURRED': '发生错误。',
  'SCHEMAMANAGEMENT.DELETE_ERROR': '删除 Schema 时发生错误。',
  'SCHEMAMANAGEMENT.QUERY_ERROR': '查询 Schema 信息时发生错误。',
  'SCHEMAMANAGEMENT.SEARCH': '搜索',
  'SCHEMAMANAGEMENT.SUBMIT': '提交',
  'SCHEMAMANAGEMENT.CANCEL': '取消',
  'SCHEMAMANAGEMENT.ADD_PROPERTY': '新增属性',
  'SCHEMAMANAGEMENT.EDIT_PROPERTY': '编辑属性',
  'SCHEMAMANAGEMENT.NAME_EXISTS': '名称已存在，请更改。',
  'SCHEMAMANAGEMENT.NEW_SCHEMA': '新建 Schema',
  'SCHEMAMANAGEMENT.PLEASE_ENTER_ITEM': '请输入项目',
  'SCHEMAMANAGEMENT.NOTES': '注意',
  'SCHEMAMANAGEMENT.SCHEMA_MODIFY_INSTRUCTIONS': '提交后，您可以点击参数表中的 Type 列中的 schema 来修改其信息。',
  'SCHEMAMANAGEMENT.VALUES_PLACEHOLDER': '多个值用英文逗号分隔输入。',

  // AddByCode 相关国际化
  'SCHEMAMANAGEMENT.ADD_BY_CODE.TITLE': '通过 Java 代码新增 Schema 属性',
  'SCHEMAMANAGEMENT.ADD_BY_CODE.JAVA_CODE': 'Java 代码',
  'SCHEMAMANAGEMENT.ADD_BY_CODE.SUCCESS': '成功新增参数！',

  // Schema属性相关国际化
  'SCHEMAPROPERTY.NAME': '名称',
  'SCHEMAPROPERTY.TYPE': '类型',
  'SCHEMAPROPERTY.REQUIRED': '必填',
  'SCHEMAPROPERTY.ALLOWABLE_VALUES': '可用值',
  'SCHEMAPROPERTY.EXAMPLE': '示例',
  'SCHEMAPROPERTY.DESCRIPTION': '描述',
  'SCHEMAPROPERTY.OPERATION': '操作',
  'SCHEMAPROPERTY.ORDER': '顺序',
  'SCHEMAPROPERTY.MIN': '最小值',
  'SCHEMAPROPERTY.MAX': '最大值',
  'SCHEMAPROPERTY.HIDDEN': '隐藏',
  'SCHEMAPROPERTY.IS_ARRAY': '是否数组',
  'SCHEMAPROPERTY.DEFAULT_VALUE': '默认值',
  'SCHEMAPROPERTY.SENSITIVE_DATA_LEVEL': '敏感数据级别',
  'SCHEMAPROPERTY.DESCRIPTIONS': '描述',
};
