import { But<PERSON>, Drawer, Space } from 'antd';
import useI18n from '@/hooks/useI8n';
import { useEffect, useState } from 'react';
import CodePrompt from '@/components/CodePrompt';
import { OtherErrorListItemType } from '../../types';

interface IErrorRespInfoDrawer {
  open: boolean;
  selectedOtherError: OtherErrorListItemType;
  setOpen: (open: boolean) => void;
}
const ErrorRespInfoDrawer: React.FC<IErrorRespInfoDrawer> = (props) => {
  const { open, selectedOtherError, setOpen } = props;
  const { formatMessage } = useI18n();
  const [errRespInfo, setErrRespInfo] = useState<string>('');

  const onClose = () => {
    setOpen(false);
  };

  useEffect(() => {
    const info = {
      ...(selectedOtherError?.chainKey ? { chainKey: selectedOtherError.chainKey } : {}),
      ...(selectedOtherError?.dubboClass ? { dubboClass: selectedOtherError.dubboClass } : {}),
      ...(selectedOtherError?.dubboMethod ? { dubboMethod: selectedOtherError.dubboMethod } : {}),
      ...(selectedOtherError?.restServiceMethod ? { restServiceMethod: selectedOtherError.restServiceMethod } : {}),
      ...(selectedOtherError?.restServicePath ? { restServicePath: selectedOtherError.restServicePath } : {}),
      ...(selectedOtherError?.tfmServiceName ? { tfmServiceName: selectedOtherError.tfmServiceName } : {}),
      ...(selectedOtherError?.errResp ? { errResp: selectedOtherError.errResp } : {}),
    };
    const stringifyInfo = JSON.stringify(info, null, 2);
    setErrRespInfo(stringifyInfo);
  }, [selectedOtherError]);

  return (
    <Drawer
      title={formatMessage('ERRORCODE.OTHERERRORDETAIL.ERRORRESPONSEINFOMATION')}
      onClose={onClose}
      maskClosable={false}
      open={open}
      width={800}
      footer={
        <Space style={{ float: 'right' }}>
          <Button onClick={onClose}>{formatMessage('PROJECT.COMMON.CANCEL')}</Button>
        </Space>
      }
    >
      <CodePrompt
        language="json"
        readOnly={true}
        id={selectedOtherError?.errLogIdStr}
        editable={false}
        basicSetup={{
          lineNumbers: false,
          highlightActiveLine: false,
          highlightActiveLineGutter: false,
        }}
        value={errRespInfo}
        allSchema={[]}
      />
    </Drawer>
  );
};
export default ErrorRespInfoDrawer;
