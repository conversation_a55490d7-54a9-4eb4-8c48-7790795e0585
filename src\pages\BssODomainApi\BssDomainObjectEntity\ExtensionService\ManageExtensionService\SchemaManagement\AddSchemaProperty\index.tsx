import React, { useEffect, useRef, useState } from 'react';
import { Button, Drawer, Form, Input, Select, Space, message, InputNumber, Divider, InputRef, Flex } from 'antd';
import { addSchemaByDomainObjId } from '@/services/entityService';
import { EnumSourceProps, SensitiveLeveType } from '../../../../types';
import { useModel } from 'umi';
import flatteningDomainSchemaList from '@/utils/flatteningDomainSchemaList';
import { CommonYesOrNoSource, ParamTypeSource } from '../../const';
import { autoFillDescByPropName } from '@/utils/common';
import useI18n from '@/hooks/useI8n';
import { PublicSensitiveLevelId } from '@/pages/BssODomainApi/const';
interface IAddRequestInformationDrawer {
  sensitiveLevelList: SensitiveLeveType[];
  operateType: string;
  domainSchemaList: any[];
  enumSource: EnumSourceProps[];
  open: boolean;
  onCancel: () => void;
  onOk: (data: any) => void;
  selectedParam?: any;
  selectedRootNode: any;
  schemaPropDataSource: any;
  isProjectAndProductOwned: boolean;
  updateDomainSchemaList: () => Promise<void>;
}

interface optionProps {
  label: string;
  value: string;
}

interface DomainSchemaObj {
  domainObjId: number;
  domainObjName: string;
  schemaList: Array<any>;
}

const AddSchemaProperty: React.FC<IAddRequestInformationDrawer> = (props) => {
  const {
    open,
    domainSchemaList = [],
    enumSource,
    onCancel,
    onOk,
    selectedParam = {},
    operateType,
    selectedRootNode,
    schemaPropDataSource,
    isProjectAndProductOwned,
    sensitiveLevelList = [],
    updateDomainSchemaList,
  } = props;
  const { formatMessage } = useI18n();
  const [form] = Form.useForm();
  const [formValues, setFormValues] = useState<any>({}); // formValues
  const [avlValueType, setAvlValueType] = useState<string>('');
  const [avlValueTypeSource, setAvlValueTypeSource] = useState<optionProps[]>([]);
  const [avlValueTypeValue, setAvlValueTypeValue] = useState<string>('');
  const [disabled, setDisabled] = useState<boolean>(false);
  const [required, setRequired] = useState<boolean>(false);
  const [defaultValues, setDeFaultValues] = useState<any>({});
  const { currentDomainObjId } = useModel('manageExtensionServiceModel');
  const addSchemaInputRef = useRef<InputRef>(null);
  const [newSchemaName, setNewSchemaName] = useState('');
  const [selectedSchemaObj, setSelectedSchemaObj] = useState<any>({});
  const [isSchemaNameValid, setIsSchemaNameValid] = useState<boolean>(true);

  const currentRequired = Form.useWatch('required', form);
  const currentHidden = Form.useWatch('hidden', form);

  const getSchemaPropFormatTypeSource = (type: string) => {
    switch (type) {
      case 'String':
        return [
          { label: 'password', value: 'password' },
          { label: 'byte', value: 'byte' },
          { label: 'email', value: 'email' },
          { label: 'binary', value: 'binary' },
        ];
      case 'Number':
        return [
          { label: 'float', value: 'float' },
          { label: 'double', value: 'double' },
        ];
      case 'Datetime':
        return [
          { label: 'date-time', value: 'date-time' },
          { label: 'date', value: 'date' },
          { label: 'utc-time', value: 'utc-time' },
        ];
      default:
        return [];
    }
  };
  const FormatSource = getSchemaPropFormatTypeSource(avlValueType);

  // 弹框关闭
  const onClose = async () => {
    setAvlValueTypeValue('');
    form.resetFields();
    onCancel?.();
  };

  const getStringBetween = (str: string, start: string, end: string) => {
    // 正则表达式匹配两个指定字符之间的内容
    const reg = /\[(\S*)\]/;
    if ((str.match(reg) || []).length > 1) {
      return (str.match(reg) || [])[1];
    }
    return '';
  };

  const onAddSchemaNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = event.target.value;
    setNewSchemaName(value);

    // 校验输入值,开头是大写字母，只能包含英文字母、数字和下划线
    const isValid = /^[A-Z][A-Za-z0-9_]*$/.test(value);
    setIsSchemaNameValid(isValid);
  };

  // 入参校验，构造入参
  const checkServiceReqParamData = () => {
    form.validateFields().then(() => {
      // 构造入参
      const formFieldsValue = form.getFieldsValue();
      let param = {
        ...selectedParam,
        ...formFieldsValue,
      };
      // PublicSensitiveLevelId为前台自定义的不存在于后台表中的sensitiveLevelId，表示该值不会入库。
      if (param?.sensitiveLevelId === PublicSensitiveLevelId) delete param?.sensitiveLevelId;
      // 新增时会生成随机id，根据id判断是否是修改
      if (operateType === 'Add') {
        // 校验是否重名
        const isNameSame = (schemaPropDataSource || []).some(
          (item: any) => item.name?.toLowerCase() === formFieldsValue.name?.toLowerCase(),
        );
        if (isNameSame) {
          message.warning(formatMessage('SCHEMAMANAGEMENT.NAME_EXISTS'));
          return;
        }
        param = {
          ...param,
          id: `${Date.now()}-${Math.floor(Math.random() * 10000)}`,
          creationSrc: isProjectAndProductOwned ? 'C' : 'P',
        };
      } else {
        // 校验是否重名
        const isNameSame = (schemaPropDataSource || [])
          ?.filter((item: any) => item.name?.toLowerCase() !== selectedParam?.name?.toLowerCase())
          ?.some((item: any) => item.name?.toLowerCase() === formFieldsValue?.name?.toLowerCase());
        if (isNameSame) {
          message.warning(formatMessage('SCHEMAMANAGEMENT.NAME_EXISTS'));
          return;
        }
        if (selectedParam?.propId) {
          param = {
            ...param,
            propId: selectedParam?.propId,
          };
        } else {
          param = {
            ...param,
            id: selectedParam?.id,
          };
        }
      }

      if (!ParamTypeSource.includes(param.type)) {
        // 如果是从后端获取的属性，第一次编辑时删除refSchemaInfo，后续都以refSchemaId和refSchemaName为准渲染对应的Schema
        if (param?.refSchemaInfo) {
          delete param?.refSchemaInfo;
        }
        param = {
          ...param,
          refSchemaId: selectedSchemaObj?.schemaId,
          refSchemaName: selectedSchemaObj?.schemaName,
          type: 'Ref',
        };
      } else {
        delete param?.refSchemaId;
        delete param?.refSchemaInfo;
      }
      if (param.avlValueType) {
        switch (param.avlValueType) {
          case 'Length':
            param.avlValue = `Length[${param.min},${param.max}]`;
            break;

          case 'Enum':
            param.avlValue = enumSource.find((i: any) => `${i?.label}` === `${param.enum}`)?.value;
            param.enumId = enumSource.find((i: any) => `${i?.label}` === `${param.enum}`)?.value;
            break;

          case 'Empty':
            param.avlValue = '';
            break;
          case 'Range':
            param.avlValue = `Range[${param.min},${param.max}]`;
            break;
          case 'Format':
            param.avlValue = param.typeFormat;
            param = {
              ...param,
              typeFormat: param.typeFormat,
            };
            break;
          case 'Values':
            param.avlValue = param.values;
            break;

          default:
            param.avlValue = '';
            break;
        }
      }
      if (param?.avlValueType !== 'Enum') {
        delete param?.enumId;
      }
      if (param?.avlValueType !== 'Format') {
        delete param?.typeFormat;
      }

      // 遍历 param 对象的所有属性
      Object.keys(param).forEach((key) => {
        // 根据后端需求，如果属性值为空，则设置为 null
        if (param[key] === '') {
          param[key] = null;
        }
      });
      // console.log(param);

      onOk(param);
    });
  };

  // 通用的重置表单字段函数
  const resetFormFields = (resetAvlValueType = true) => {
    // 重置所有相关字段
    const resetFields: any = {
      typeFormat: undefined,
      min: undefined,
      max: undefined,
      values: undefined,
      enum: undefined,
    };

    if (resetAvlValueType) {
      resetFields.avlValueType = '';
      setAvlValueTypeValue('');
    }

    form.setFieldsValue(resetFields);
  };

  const onPropTypeChange = (value: any) => {
    const flatDomainSchemaList = flatteningDomainSchemaList(domainSchemaList);
    const schemaObj = flatDomainSchemaList?.find((item: any) => item?.schemaId === value);
    setSelectedSchemaObj(schemaObj);

    setAvlValueType(value);

    // 重置avlValueTypeValue相关字段
    resetFormFields();

    setDisabled(false);
    setRequired(true);
    switch (value) {
      case 'String':
        setAvlValueTypeSource([
          { label: 'Empty', value: 'Empty' },
          { label: 'Length', value: 'Length' },
          { label: 'Format', value: 'Format' },
          { label: 'Enum', value: 'Enum' },
          { label: 'Values', value: 'Values' },
        ]);
        break;

      case 'Boolean':
        setAvlValueTypeSource([{ label: 'Empty', value: 'Empty' }]);
        break;

      case 'Long':
        setAvlValueTypeSource([
          { label: 'Empty', value: 'Empty' },
          { label: 'Length', value: 'Length' },
          { label: 'Range', value: 'Range' },
          { label: 'Enum', value: 'Enum' },
          { label: 'Values', value: 'Values' },
        ]);
        break;
      case 'Integer':
        setAvlValueTypeSource([
          { label: 'Empty', value: 'Empty' },
          { label: 'Length', value: 'Length' },
          { label: 'Range', value: 'Range' },
          { label: 'Enum', value: 'Enum' },
          { label: 'Values', value: 'Values' },
        ]);
        break;
      case 'Number':
        setAvlValueTypeSource([
          { label: 'Empty', value: 'Empty' },
          { label: 'Length', value: 'Length' },
          { label: 'Range', value: 'Range' },
          { label: 'Format', value: 'Format' },
          { label: 'Values', value: 'Values' },
        ]);
        break;
      case 'Datetime':
        setAvlValueTypeSource([
          { label: 'Empty', value: 'Empty' },
          { label: 'Format', value: 'Format' },
          { label: 'Values', value: 'Values' },
        ]);
        break;
      case 'Object':
        setAvlValueTypeSource([{ label: 'Empty', value: 'Empty' }]);
        break;
      default:
        setAvlValueTypeSource([{ label: 'Empty', value: 'Empty' }]);
        setRequired(false);
        break;
    }
  };

  const onAvlValueTypeChange = (value: any) => {
    setAvlValueTypeValue(value);

    // 重置avlValueTypeValue相关字段，但保留avlValueType
    resetFormFields(false);
  };

  const handleEnumFilter = (input: any, option: any) => {
    return option?.value?.toLowerCase()?.indexOf(input?.toLowerCase()) >= 0;
  };

  // 新增Schema
  const addSchema = async (e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>) => {
    e.preventDefault();

    if (isSchemaNameValid) {
      if (currentDomainObjId) {
        const resultData = await addSchemaByDomainObjId({ schemaName: newSchemaName, domainObjId: currentDomainObjId });
        if (resultData?.success) {
          message.success(formatMessage('PROJECT.COMMON.ADDSUCCESS'));
          await updateDomainSchemaList();
          setTimeout(() => {
            addSchemaInputRef.current?.focus();
          }, 0);
        }
      }
    } else {
      message.warning(formatMessage('COPYSCHEMA.NAME.INVALID_FORMAT'));
    }
  };

  useEffect(() => {
    if (newSchemaName) {
      const flatDomainSchemaList = flatteningDomainSchemaList(domainSchemaList);
      const schemaObj = flatDomainSchemaList?.find((item: any) => item?.schemaName === newSchemaName);
      form.setFieldValue('type', schemaObj?.schemaId); // 新建Schema后默认选中
      onPropTypeChange(schemaObj?.schemaId);
      setNewSchemaName('');
    }
  }, [domainSchemaList]);

  useEffect(() => {
    if (currentRequired === 'Y' && currentHidden === 'Y') {
      form.setFieldValue('hidden', 'N');
      message.warning(formatMessage('PROJECT.COMMON.REQUIREDANDHIDDENBOTHYESNOTE'));
    }
  }, [currentRequired, currentHidden]);

  useEffect(() => {
    // 深拷贝行数据后初始化值
    if (selectedParam && open) {
      const defaultValues: any = {};

      Object.assign(defaultValues, selectedParam);

      // PublicSensitiveLevelId为前台自定义的不存在于后台表中的sensitiveLevelId，表示该值不会入库。
      if (!defaultValues?.sensitiveLevelId) {
        defaultValues.sensitiveLevelId = PublicSensitiveLevelId;
      }

      if (defaultValues?.type) {
        if (!ParamTypeSource.includes(defaultValues.type)) {
          // 如果是非基本类型，且存在refSchemaInfo，则该数据是从后端获取的；
          if (defaultValues?.refSchemaInfo) {
            defaultValues.type = defaultValues?.refSchemaInfo?.schemaId;
            setSelectedSchemaObj({
              ...selectedSchemaObj,
              schemaId: defaultValues?.refSchemaInfo?.schemaId,
              schemaName: defaultValues?.refSchemaInfo?.schemaName,
            });
          } else {
            // 如果是非基本类型，但不存在refSchemaInfo，则该数据是通过'Add'添加的，或者原本是从后端获取的数据，且已经修改过，但还未调接口入库；
            defaultValues.type = defaultValues?.schemaId;
            setSelectedSchemaObj({
              ...selectedSchemaObj,
              schemaId: defaultValues?.refSchemaId,
              schemaName: defaultValues?.refSchemaName,
            });
          }
          onPropTypeChange(defaultValues.type);
        } else {
          // 6种数据基本类型，处理avlValue字段的回显问题
          const { avlValue, typeFormat } = defaultValues;
          if ((avlValue === '' || avlValue === null) && !typeFormat) {
            defaultValues.avlValueType = 'Empty';
          } else if (defaultValues.enumId && `${avlValue}` === `${defaultValues.enumId}`) {
            defaultValues.avlValueType = 'Enum';
            defaultValues.enum = enumSource.find((i: any) => `${i.value}` === `${avlValue}`)?.label;
          } else if (typeof avlValue === 'string' && avlValue?.toLowerCase().startsWith('length')) {
            defaultValues.avlValueType = 'Length';
            const betweenVal = getStringBetween(avlValue, '[', ']')?.split(',');
            defaultValues.min = betweenVal[0];
            defaultValues.max = betweenVal[1];
          } else if (typeof avlValue === 'string' && avlValue?.toLowerCase().startsWith('range')) {
            defaultValues.avlValueType = 'Range';
            const betweenVal = getStringBetween(avlValue, '[', ']')?.split(',');
            defaultValues.min = betweenVal[0];
            defaultValues.max = betweenVal[1];
          } else if (typeFormat) {
            defaultValues.avlValueType = 'Format';
            defaultValues.typeFormat = typeFormat;
          } else {
            defaultValues.avlValueType = 'Values';
            defaultValues.values = avlValue;
          }
          onPropTypeChange(defaultValues.type);
          onAvlValueTypeChange(defaultValues.avlValueType);
        }
      }
      if (operateType === 'Add') {
        defaultValues.isArray = 'N';
        defaultValues.required = 'N';
        defaultValues.hidden = 'N';
      }
      setDeFaultValues(defaultValues);
    }
  }, [selectedParam, open, domainSchemaList]); // onPropTypeChange依赖domainSchemaList

  return (
    <Drawer
      title={`${
        operateType === 'Add'
          ? formatMessage('SCHEMAMANAGEMENT.ADD_PROPERTY')
          : formatMessage('SCHEMAMANAGEMENT.EDIT_PROPERTY')
      }`}
      onClose={onClose}
      maskClosable={false}
      open={open}
      width={720}
      afterOpenChange={() => {
        form.resetFields();
      }}
      footer={
        <Space style={{ float: 'right' }}>
          <Button type="primary" onClick={checkServiceReqParamData}>
            {formatMessage('PROJECT.COMMON.OK')}
          </Button>
          <Button onClick={onClose}>{formatMessage('PROJECT.COMMON.CANCEL')}</Button>
        </Space>
      }
    >
      <Form
        form={form}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
        initialValues={defaultValues}
        onValuesChange={(cValues, alValues) => {
          setFormValues(alValues);
        }}
      >
        <Form.Item
          label={formatMessage('SCHEMAPROPERTY.NAME')}
          name="name"
          rules={[
            { required: true, message: '' },
            {
              validator: (_, value) => {
                if (!value) return Promise.resolve(); // required 已经校验，这里防止报错
                if (/\s/.test(value)) {
                  return Promise.reject(new Error("The property name can't contain spaces."));
                }
                if (/-/.test(value)) {
                  return Promise.reject(new Error("The property name can't contain '-'."));
                }
                if (/^\d+$/.test(value)) {
                  return Promise.reject(new Error("The property name can't be purely numeric."));
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <Input
            allowClear
            onBlur={() => autoFillDescByPropName(form, 'name', 'description')}
            // disabled={operateType !== 'Add' && isProjectAndProductOwned && selectedParam?.creationSrc !== 'C'}
          />
        </Form.Item>
        <Form.Item
          label={formatMessage('SCHEMAPROPERTY.IS_ARRAY')}
          name="isArray"
          rules={[{ required: true, message: '' }]}
        >
          <Select
            showSearch
            options={CommonYesOrNoSource}
            disabled={operateType !== 'Add' && isProjectAndProductOwned && selectedParam?.creationSrc !== 'C'}
          />
        </Form.Item>
        <Form.Item
          label={formatMessage('SCHEMAPROPERTY.REQUIRED')}
          name="required"
          rules={[{ required: true, message: '' }]}
        >
          <Select showSearch options={CommonYesOrNoSource} />
        </Form.Item>
        <Form.Item
          label={formatMessage('SCHEMAPROPERTY.HIDDEN')}
          name="hidden"
          rules={[{ required: true, message: '' }]}
        >
          <Select showSearch options={CommonYesOrNoSource} />
        </Form.Item>
        <Form.Item
          label={formatMessage('SCHEMAPROPERTY.TYPE')}
          name="type"
          rules={[{ required: true, message: '' }]}
          extra={
            avlValueType &&
            !ParamTypeSource.includes(avlValueType) &&
            formatMessage('MANAGEEXTENSIONSERVICE.SCHEMA.EDIT.NOTES')
          }
        >
          <Select
            disabled={operateType !== 'Add' && isProjectAndProductOwned && selectedParam?.creationSrc !== 'C'}
            options={[
              ...ParamTypeSource.map((item) => ({ label: item, value: item, key: item })),
              ...domainSchemaList.map((item: DomainSchemaObj) => ({
                label: <span>{item?.domainObjName}</span>,
                value: item?.domainObjName,
                options: item?.schemaList.map((i) => ({
                  label: i?.schemaName,
                  value: i?.schemaId,
                  key: i?.schemaId,
                })),
              })),
            ]}
            onChange={onPropTypeChange}
            showSearch
            filterOption={(input, option) => {
              // 基本类型直接过滤
              if (option?.label && typeof option.label === 'string') {
                return option.label.toLowerCase().includes(input.toLowerCase());
              }
              // 组标题不参与过滤，总是显示
              return false;
            }}
            dropdownRender={(menu) => (
              <>
                {menu}
                <Divider style={{ margin: '8px 0' }} />
                <Space style={{ padding: '0 8px 4px' }}>
                  <Input
                    value={newSchemaName}
                    placeholder={formatMessage('SCHEMAMANAGEMENT.PLEASE_ENTER_ITEM')}
                    ref={addSchemaInputRef}
                    onChange={onAddSchemaNameChange}
                    onKeyDown={(e) => e.stopPropagation()}
                  />
                  <Button type="text" onClick={addSchema}>
                    + {formatMessage('SCHEMAMANAGEMENT.NEW_SCHEMA')}
                  </Button>
                </Space>
              </>
            )}
          />
        </Form.Item>
        {/* avlValueType 如果为复杂类型则不展示'Example'和'Allowable Values' */}
        {!avlValueType || ParamTypeSource.includes(avlValueType) ? (
          <>
            <Form.Item label={formatMessage('SCHEMAPROPERTY.ALLOWABLE_VALUES')} style={{ marginBottom: 0 }}>
              <Form.Item
                name="avlValueType"
                style={{ display: 'inline-block', width: 'calc(33% - 5px)' }}
                rules={[{ required, message: '' }]}
              >
                <Select options={avlValueTypeSource} onChange={onAvlValueTypeChange} disabled={disabled} />
              </Form.Item>
              {avlValueTypeValue === 'Length' ? (
                <Form.Item
                  name="min"
                  style={{ display: 'inline-block', width: 'calc(33%)', marginLeft: '5px' }}
                  rules={[
                    { required: true, message: '' },
                    {
                      validator: (_, value) => {
                        const minValue = formValues?.required !== 'Y' ? 0 : 1;
                        if (value < minValue) {
                          return Promise.reject(new Error(`Min is ${minValue}`));
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <InputNumber
                    addonBefore={formatMessage('SCHEMAPROPERTY.MIN')}
                    precision={0}
                    min={formValues?.required !== 'Y' ? 0 : 1}
                    max={formValues.max}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              ) : null}
              {avlValueTypeValue === 'Length' ? (
                <Form.Item
                  name="max"
                  style={{ display: 'inline-block', width: 'calc(33%)', marginLeft: '5px' }}
                  rules={[{ required: true, message: '' }]}
                >
                  <InputNumber
                    addonBefore={formatMessage('SCHEMAPROPERTY.MAX')}
                    precision={0}
                    min={formValues.min}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              ) : null}
              {avlValueTypeValue === 'Enum' ? (
                <Form.Item
                  name="enum"
                  style={{ display: 'inline-block', width: 'calc(66% + 5px)', marginLeft: '5px' }}
                  rules={[{ required: true, message: '' }]}
                >
                  <Select showSearch filterOption={handleEnumFilter}>
                    {enumSource?.map((item: any) => (
                      <Select.Option key={item.value} value={item.label}>
                        {item.label}
                      </Select.Option>
                    ))}
                  </Select>
                </Form.Item>
              ) : null}
              {avlValueTypeValue === 'Format' ? (
                <Form.Item
                  name="typeFormat"
                  style={{ display: 'inline-block', width: 'calc(66% + 5px)', marginLeft: '5px' }}
                  rules={[{ required: true, message: '' }]}
                >
                  <Select options={FormatSource} />
                </Form.Item>
              ) : null}
              {avlValueTypeValue === 'Values' ? (
                <Form.Item
                  name="values"
                  style={{ display: 'inline-block', width: 'calc(66% + 5px)', marginLeft: '5px' }}
                  rules={[{ required: true, message: '' }]}
                >
                  <Input placeholder={formatMessage('SCHEMAMANAGEMENT.VALUES_PLACEHOLDER')} />
                </Form.Item>
              ) : null}
              {avlValueTypeValue === 'Range' ? (
                <Form.Item
                  name="min"
                  style={{ display: 'inline-block', width: 'calc(33%)', marginLeft: '5px' }}
                  rules={[{ required: true, message: '' }]}
                >
                  <InputNumber
                    addonBefore={formatMessage('SCHEMAPROPERTY.MIN')}
                    precision={0}
                    max={formValues.max}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              ) : null}
              {avlValueTypeValue === 'Range' ? (
                <Form.Item
                  name="max"
                  style={{ display: 'inline-block', width: 'calc(33%)', marginLeft: '5px' }}
                  rules={[{ required: true, message: '' }]}
                >
                  <InputNumber
                    addonBefore={formatMessage('SCHEMAPROPERTY.MAX')}
                    precision={0}
                    min={formValues.min}
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              ) : null}
            </Form.Item>
            <Form.Item label={formatMessage('SCHEMAPROPERTY.DEFAULT_VALUE')} name="defValue">
              <Input />
            </Form.Item>
            <Form.Item label={formatMessage('SCHEMAPROPERTY.EXAMPLE')} name="example">
              <Input allowClear />
            </Form.Item>
          </>
        ) : null}
        <Form.Item label={formatMessage('SCHEMAPROPERTY.SENSITIVE_DATA_LEVEL')} name="sensitiveLevelId">
          <Select>
            {sensitiveLevelList.map((i) => (
              <Select.Option key={i.sensitiveLevelId} value={i.sensitiveLevelId}>
                {i.sensitiveLevelName}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          label={formatMessage('SCHEMAPROPERTY.DESCRIPTIONS')}
          name="description"
          rules={[{ required: true, message: '' }]}
          extra={formatMessage('PROJECT.COMMON.GETDESCBYPROPNAME')}
        >
          <Input.TextArea allowClear onBlur={() => autoFillDescByPropName(form, 'name', 'description')} />
        </Form.Item>
      </Form>
    </Drawer>
  );
};
export default AddSchemaProperty;
