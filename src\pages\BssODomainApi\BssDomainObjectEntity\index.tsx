import React, { useEffect, useState } from 'react';
import { Form, Row, Col } from 'antd';
import { queryODomainEntityDetail, queryODomainObjDetail } from '@/services/domainService';
import styles from './index.less';
import EditDomainEntityDrawer from '../BssDomainObjectDetail/EditDomainEntity';
import EntityProperty from './EntityProperty';
import ExpandEntity from './ExpandEntity';
import ExtensionService from './ExtensionService';
import EntityEvent from './EntityEvent';
import OperationIconTitle from '@/components/OperationIconTitle';
import BssDomainTopContent from '../BssDomainTopContent';
import { SelectedRootNode, SensitiveLeveType } from './types';
import ManageExtensionService from './ExtensionService/ManageExtensionService';
import { useModel } from 'umi';
import { qryAllTfmServices, qrySensitiveLevelList } from '@/services/entityService';
import { PublicSensitiveLevelId } from '../const';
import { CommonOptionItemType } from '@/components/SelectWithAddButton/type';
import useI18n from '@/hooks/useI8n';

interface IBssDomainObjectEntity {
  selectedDomainNode?: any; // 当前节点
  selectedRootNode: SelectedRootNode; // 根节点
  isProduct: boolean;
  ignoreVersionSource: any;
  indexFunctionSource: any;
  onRefresh: (currentSelectKey: any) => void; // 刷新缓存 通知上级刷新缓存
}

const BssDomainObjectEntity: React.FC<IBssDomainObjectEntity> = (props) => {
  const { ignoreVersionSource, indexFunctionSource, selectedDomainNode, selectedRootNode, isProduct, onRefresh } =
    props;

  const [form] = Form.useForm();
  const { formatMessage } = useI18n();
  const [domainObjEntityDetail, setDomainObjEntityDetail] = React.useState<any>(); // 当前领域对象详情信息

  // 实体关系相关参数
  const [openEditDomainEntityDrawer, setOpenEditDomainEntityDrawer] = useState<boolean>(false); // 修改领域实体

  const [basicInfo, setBasicInfo] = useState<any>({});

  const { isManageExtensionService, setCurrentDomainObjId } = useModel('manageExtensionServiceModel');
  const { has9E } = useModel('useSystemConfigDataModel');
  const [extensionServiceInfo, setExtensionServiceInfo] = useState<any>({});
  const [domainObjDetail, setDomainObjDetail] = React.useState<any>();
  const [tfmServices, setTfmServices] = useState<CommonOptionItemType[]>([]);
  const [sensitiveLevelList, setSensitiveLevelList] = useState<SensitiveLeveType[]>([]); // 敏感等级列表

  const CreationSource = [
    { name: 'Product-Owned', key: 'P' },
    { name: 'Project-Customized', key: 'C' },
  ];

  const EntityTypeSource = [
    // A: AggregateRoot, S: ServiceHolder, E: Entity
    { name: 'AggregateRoot', key: 'A' },
    { name: 'ServiceHolder', key: 'S' },
    { name: 'Entity', key: 'E' },
  ];

  // 基础信息
  const MarginTop = { marginTop: 15 };

  const ColSpan = ({ value = '' }) => (
    <p
      style={{ marginLeft: 16, marginBottom: 0, textOverflow: 'ellipsis', overflow: 'hidden', whiteSpace: 'nowrap' }}
      title={value}
    >
      {value}
    </p>
  );

  const ColShow = ({ label, value }: any) => {
    return (
      <Col span={8} style={{ display: 'flex', flexDirection: 'row' }}>
        <Col
          style={{
            display: 'flex',
            justifyContent: 'flex-end',
            fontWeight: 600,
          }}
          span={9}
        >
          {label}
        </Col>
        <Col span={15}>
          <ColSpan value={value} />
        </Col>
      </Col>
    );
  };

  const LongColSpan = ({ label, value }: any) => {
    return (
      <>
        <Col
          span={3}
          style={{
            display: 'flex',
            justifyContent: 'flex-end',
            fontWeight: 600,
          }}
        >
          {label}
        </Col>
        <Col span={21}>
          <ColSpan value={value} />
        </Col>
      </>
    );
  };

  const queryDomainObjEntityDetail = async () => {
    const { success, data } = await queryODomainEntityDetail(selectedDomainNode?.entityId);
    form.resetFields();
    if (success) {
      setDomainObjEntityDetail(data);
      setCurrentDomainObjId(data?.domainObjId);
      setBasicInfo(data);
    } else {
      setDomainObjEntityDetail({});
      setBasicInfo({});
    }
  };

  const queryDomainObjDetail = async () => {
    const { success, data } = await queryODomainObjDetail(selectedDomainNode?.domainObjId);
    if (success) {
      setDomainObjDetail(data);
    } else {
      setDomainObjDetail({});
    }
  };

  const queryTfmServices = async () => {
    const { success, data } = await qryAllTfmServices({ domainId: selectedRootNode?.domainId });
    if (success) {
      const originalData = (data || [])?.map((item: string, index: number) => ({
        label: item,
        value: item,
        key: `${item}-${index}`,
        state: 'original',
      }));
      setTfmServices(originalData);
    } else {
      setTfmServices([]);
    }
  };

  // 查询敏感数据级别列表
  const querySensitiveLevelList = async () => {
    const publicSensitiveLevel = {
      comments: '',
      sensitiveLevelCode: '',
      sensitiveLevelName: 'Public Sensitive',
      sensitiveLevelId: PublicSensitiveLevelId, // PublicSensitiveLevelId为前台自定义的不存在于后台表中的sensitiveLevelId，表示该值不会入库。
    };
    try {
      const { success, data } = await qrySensitiveLevelList();
      if (success) {
        setSensitiveLevelList([publicSensitiveLevel, ...data]);
      } else {
        setSensitiveLevelList([publicSensitiveLevel]);
      }
    } catch (error) {
      setSensitiveLevelList([publicSensitiveLevel]);
    }
  };
  useEffect(() => {
    if (
      selectedRootNode?.appCode === 'custc' ||
      selectedRootNode?.appCode === 'coc' ||
      selectedRootNode?.appCode === 'test'
    ) {
      queryTfmServices();
    }
  }, [selectedRootNode]);

  useEffect(() => {
    // 查询领域对象详情
    queryDomainObjEntityDetail();
    queryDomainObjDetail();
    // 查询敏感数据级别列表
    querySensitiveLevelList();
  }, [selectedDomainNode]);

  const getExtensionServiceValue = (data: any) => {
    setExtensionServiceInfo(data);
  };

  return (
    <div className={styles.activeLineContainer}>
      {/* 头部区域  */}
      <BssDomainTopContent
        serviceInfo={isManageExtensionService ? extensionServiceInfo : ''}
        selectedDomainNode={selectedDomainNode}
        onSelectDomianData={(data) => {
          onRefresh?.(data.key);
        }}
      />
      {isManageExtensionService ? (
        <ManageExtensionService
          sensitiveLevelList={sensitiveLevelList}
          ignoreVersionSource={ignoreVersionSource}
          extensionServiceInfo={extensionServiceInfo}
          isProduct={isProduct}
          selectedRootNode={selectedRootNode}
          selectedEntity={selectedDomainNode}
          tfmServices={tfmServices}
          updateServiceInfo={getExtensionServiceValue}
        />
      ) : (
        <>
          {/* 领域实体基础信息 */}
          <div className={styles.domainObjContent}>
            <OperationIconTitle
              title={formatMessage('DOMAIN.BASIC_INFO')}
              type={selectedRootNode?.state === 'D' ? 'edit' : ''}
              opt={formatMessage('PROJECT.COMMON.EDIT')}
              handleClick={() => {
                setOpenEditDomainEntityDrawer(true);
              }}
            />
            <div className={styles.content}>
              <Row style={MarginTop}>
                <ColShow label={formatMessage('PROJECT.COMMON.NAME')} value={basicInfo?.entityName} />
                <ColShow label={formatMessage('PROJECT.COMMON.CODE')} value={basicInfo?.entityCode} />
                <ColShow
                  label={formatMessage('PROJECT.COMMON.CREATIONSOURCE')}
                  value={CreationSource.filter((i) => i.key === basicInfo?.creationSrc).map((i) => i.name)}
                />
              </Row>
              <Row style={MarginTop}>
                <ColShow
                  label={formatMessage('DOMAIN.ENTITY.TYPE')}
                  value={EntityTypeSource.filter((i) => i.key === basicInfo?.entityType).map((i) => i.name)}
                />
                <ColShow label={formatMessage('DOMAIN.ENTITY.FORM.DEFAULT_PAGE_SIZE')} value={basicInfo?.defaultPage} />
                {basicInfo?.entityType !== 'S' && (
                  <ColShow label={formatMessage('DOMAIN.ENTITY.FORM.DATA_MODEL')} value={basicInfo?.modelName} />
                )}
              </Row>
              <Row style={MarginTop}>
                <LongColSpan label={formatMessage('PROJECT.COMMON.DESCRIPTION')} value={basicInfo?.comments} />
              </Row>
            </div>
          </div>
          {/* 领域实体属性列表 */}
          {basicInfo?.entityType !== 'S' && (
            <div className={styles.domainObjContent}>
              <EntityProperty
                sensitiveLevelList={sensitiveLevelList}
                indexFunctionSource={indexFunctionSource}
                ignoreVersionSource={ignoreVersionSource}
                isProduct={isProduct}
                selectedRootNode={selectedRootNode}
                selectedEntity={selectedDomainNode}
              />
            </div>
          )}

          {/* 实体扩展列表 */}
          {(domainObjDetail?.domainObjType !== 'C' || basicInfo?.entityType === 'E') && (
            <div className={styles.domainObjContent}>
              <ExpandEntity
                ignoreVersionSource={ignoreVersionSource}
                isProduct={isProduct}
                selectedRootNode={selectedRootNode}
                selectedEntity={selectedDomainNode}
              />
            </div>
          )}
          {/* 实体扩展服务管理 */}
          <div className={styles.domainObjContent}>
            <ExtensionService
              ignoreVersionSource={ignoreVersionSource}
              isProduct={isProduct}
              selectedRootNode={selectedRootNode}
              selectedEntity={selectedDomainNode}
              tfmServices={tfmServices}
              handleManage={getExtensionServiceValue}
            />
          </div>
          {/* 实体事件管理 */}
          {has9E && (
            <div className={styles.domainObjContent}>
              <EntityEvent
                ignoreVersionSource={ignoreVersionSource}
                isProduct={isProduct}
                selectedRootNode={selectedRootNode}
                entityId={selectedDomainNode?.entityId}
              />
            </div>
          )}
          {/* 修改领域实体 */}
          <EditDomainEntityDrawer
            domainObjType={domainObjDetail?.domainObjType}
            ignoreVersionSource={ignoreVersionSource}
            open={openEditDomainEntityDrawer}
            domainObjId={selectedDomainNode?.domainObjId}
            initValue={domainObjEntityDetail}
            onCancel={() => setOpenEditDomainEntityDrawer(false)}
            isProduct={isProduct}
            onOk={() => {
              // 修改实体成功后操作
              setOpenEditDomainEntityDrawer(false);
              onRefresh(selectedDomainNode?.key);
              queryDomainObjEntityDetail();
            }}
          />
        </>
      )}
    </div>
  );
};

export default BssDomainObjectEntity;
