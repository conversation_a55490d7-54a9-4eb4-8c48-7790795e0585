/**
 * 用于展示 服务详情 的 悬浮框
 */

import { AppstoreFilled } from "@ant-design/icons";
import { Dropdown, MenuProps } from "antd";
import styles from "./index.less";

type HoverWrapperProps = {
  onMenuClick?: ({ key }: { key: string }) => void;
  menuItems?: MenuProps["items"];
  isHidden?: boolean;
};

const HoverWrapper = ({
  onMenuClick = () => {},
  menuItems = [],
  isHidden = false,
}: HoverWrapperProps) => {
  return (
    <>
      {!isHidden && (
        <div className={styles.hoverWrapper}>
          <Dropdown
            menu={{ items: menuItems, onClick: onMenuClick as any }}
            placement="top"
          >
            <div className={styles.appstoreWrapper}>
              <AppstoreFilled className={styles.appstoreIcon} />
            </div>
          </Dropdown>
        </div>
      )}
    </>
  );
};

export default HoverWrapper;
