import { ErrorCodeListDataType, GetErrorCodeListType } from '@/pages/BssOErrorCodeApi/ErrorCodeManagement/types';
import { GetOtherErrorListType, OtherErrorListDataType } from '@/pages/BssOErrorCodeApi/OtherErrorDetail/types';
import { getErrorCodeList, getOtherErrorList } from '@/services/errorCodeService';
import { useState } from 'react';

export default function useErrorCodeModel() {
  // error code
  const [queryErrorCodeListParams, setQueryErrorCodeListParams] = useState<GetErrorCodeListType>({
    pageNo: 1,
    pageSize: 10,
    isRelated: 'Y',
  });
  const [errorCodeListData, setErrorCodeListData] = useState<ErrorCodeListDataType>({
    list: [],
    pageNo: 1,
    pageSize: 10,
    totalCount: 0,
    totalPage: 1,
  });
  const [errorCodeSpinning, setErrorCodeSpinning] = useState<boolean>(false);

  // other error
  const [queryOtherErrorListParams, setQueryOtherErrorListParams] = useState<GetOtherErrorListType>({
    pageNo: 1,
    pageSize: 10,
  });
  const [otherErrorListData, setOtherErrorListData] = useState<OtherErrorListDataType>({
    list: [],
    pageNo: 1,
    pageSize: 10,
    totalCount: 0,
    totalPage: 1,
  });
  const [otherErrorSpinning, setOtherErrorSpinning] = useState<boolean>(false);
  const [isShowOtherErrorDetail, setIsShowOtherErrorDetail] = useState<boolean>(false); // 是否展示其他错误详情

  const queryErrorCodeList = async (param: GetErrorCodeListType) => {
    try {
      setErrorCodeSpinning(true);
      const params = {
        ...param,
        // 不传domainId，查所有数据；传domainId，且domainId传空时，查询Apply Domain是All的数据；其余情况查domainId对应的数据
        ...(param?.domainId ? { domainId: param.domainId === 'all' ? '' : param.domainId } : {}),
      };
      const { success, data } = await getErrorCodeList(params);
      if (success) {
        setErrorCodeListData(data);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setErrorCodeSpinning(false);
    }
  };

  const queryOtherErrorList = async (param: GetOtherErrorListType) => {
    try {
      setOtherErrorSpinning(true);
      const { success, data } = await getOtherErrorList(param);
      if (success) {
        setOtherErrorListData(data);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setOtherErrorSpinning(false);
    }
  };
  return {
    queryErrorCodeListParams,
    setQueryErrorCodeListParams,
    errorCodeListData,
    setErrorCodeListData,
    errorCodeSpinning,
    setErrorCodeSpinning,
    queryErrorCodeList,
    queryOtherErrorListParams,
    setQueryOtherErrorListParams,
    otherErrorListData,
    setOtherErrorListData,
    otherErrorSpinning,
    setOtherErrorSpinning,
    queryOtherErrorList,
    isShowOtherErrorDetail,
    setIsShowOtherErrorDetail,
  };
}
