import React, { ReactNode } from 'react';
import useI18n from '@/hooks/useI8n';
import { DyColumnInMorePop } from '@/components';
import ResizableTable from '@/components/ResizableTable';

export interface ITableDataInterface {
  contentType: ReactNode;
  rowKey: string;
  name: string;
  type: string;
  example: string;
  comments: string;
  respCode: string;
  value?: string;
  children?: Array<ITableDataInterface>;
  typeFormat?: string;
  dataModel?: any;
}

export type ResponseTableProps = {
  tableData: ITableDataInterface[];
  isHeader?: boolean;
};

const SingleResponseTable: React.FC<ResponseTableProps> = (props) => {
  const { tableData = [], isHeader = false } = props;
  const { formatMessage } = useI18n();

  const singlRequestTableColumns = [
    {
      dataIndex: 'name',
      title: formatMessage('PROJECT.COMMON.NAME'),
      width: '20%',
    },
    {
      dataIndex: 'type',
      title: formatMessage('PROJECT.COMMON.TYPE'),
      width: '20%',
      render: (_: any, record: any) => {
        if (record?.typeFormat) {
          return `${record?.type}<${record?.typeFormat}>`;
        }
        if (record?.type === 'Void') {
          return 'No Data';
        }
        return record?.type;
      },
    },
    {
      dataIndex: 'avlValue',
      title: formatMessage('SERVICEDETAIL.REQUEST.AVLVALUE'),
      ellipsis: true,
      className: 'avlValueColumnResponse',
      width: isHeader ? '20%' : '40%',
      render: (value: string) => {
        const pattern = /^(range|length)[\(\[]/;
        if (value && !pattern.test(value)) {
          // range(、range[、length(、length[ 字符
          return <DyColumnInMorePop columnClaassName="avlValueColumnResponse" value={value} popContentType="array" />;
        }
        return value;
      },
    },
    isHeader
      ? {
          dataIndex: 'example',
          width: '20%',
          title: formatMessage('PROJECT.COMMON.EXAMPLE'),
          ellipsis: true,
        }
      : null,
    {
      dataIndex: 'comments',
      width: '20%',
      title: formatMessage('PROJECT.COMMON.DESC'),
      ellipsis: true,
    },
  ];

  return (
    <ResizableTable
      key="hasData"
      size="small"
      columns={singlRequestTableColumns.filter((item) => !!item)} // 过滤掉null
      dataSource={tableData}
      pagination={false}
      rowKey={(record: any) => record?.rowKey}
    />
  );
};

export default SingleResponseTable;
