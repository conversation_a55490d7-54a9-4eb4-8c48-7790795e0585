export const getFormatTypeSource = (type: string) => {
  switch (type) {
    case 'String':
      return [
        { label: 'password', value: 'password' },
        { label: 'byte', value: 'byte' },
        { label: 'email', value: 'email' },
        { label: 'binary', value: 'binary' },
      ];
    case 'Number':
      return [
        { label: 'float', value: 'float' },
        { label: 'double', value: 'double' },
      ];
    case 'Datetime':
      return [
        { label: 'date-time', value: 'date-time' },
        { label: 'date', value: 'date' },
        { label: 'utc-time', value: 'utc-time' },
      ];
    default:
      return [];
  }
};
