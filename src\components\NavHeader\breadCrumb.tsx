import { Breadcrumb } from "antd"
import { useEffect, useState } from "react"
import { useLocation, useRouteLoaderData } from "react-router-dom"
import { getBreadTree } from "../../utils"

export default function BreadCrumb() {
    const { pathname } = useLocation()
    const [breadList, setBreadList] = useState<any>([])
    const data = useRouteLoaderData('layout')

    useEffect(() => {
        const list = getBreadTree(data.menuList, pathname, [])
        setBreadList([
            <a href="/welcome">首页</a>, ...list
        ].map((item) => {
            return {
                title: item
            }
        }))
    }, [pathname])

    return (
        <Breadcrumb items={breadList} style={{ marginLeft: 10 }} />
    )
}