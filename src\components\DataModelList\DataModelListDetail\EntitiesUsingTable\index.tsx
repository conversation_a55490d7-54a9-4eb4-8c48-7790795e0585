import OperationIconTitle from '@/components/OperationIconTitle';
import useI18n from '@/hooks/useI8n';
import { TableColumnsType } from 'antd';
import { EntityTypeSource, CreationSource } from '../../const';
import ResizableTable from '@/components/ResizableTable';
import { useTableFilter } from '@/hooks/useTableFilter';
import { useEffect, useMemo } from 'react';

interface IEntitiesUsing {
  entitiesDataSource: any[];
}

const EntitiesUsingTable: React.FC<IEntitiesUsing> = ({ entitiesDataSource = [] }) => {
  const { formatMessage } = useI18n();
  // 使用 useMemo 确保 dataSource 是稳定的引用
  const memoizedDataSource = useMemo(
    () => (Array.isArray(entitiesDataSource) ? entitiesDataSource : []),
    [entitiesDataSource],
  );
  // 自定义hooks，支持表格列筛选并分页展示
  const { filteredDataSource, getColumnSearchProps, getColumnEnumSearchProps, clearAllFilters } =
    useTableFilter(memoizedDataSource);

  useEffect(() => {
    // 重置表格列的筛选条件
    clearAllFilters();
  }, [memoizedDataSource]);

  const columns: TableColumnsType = [
    {
      dataIndex: 'entityName',
      width: '20%',
      ellipsis: true,
      title: formatMessage('DATAMODELLISTDETAIL.ENTITIES.NAME'),
      ...getColumnSearchProps('entityName'),
    },
    {
      dataIndex: 'entityCode',
      width: '15%',
      ellipsis: true,
      title: formatMessage('DATAMODELLISTDETAIL.ENTITIES.CODE'),
      ...getColumnSearchProps('entityCode'),
    },
    {
      dataIndex: 'creationSrc',
      width: '15%',
      ellipsis: true,
      title: formatMessage('DATAMODELLISTDETAIL.ENTITIES.CREATIONSOURCE'),
      render: (_: string, record: any) => {
        const matchedItem = CreationSource.find((i: { key: string; name: string }) => i.key === record.creationSrc);
        return matchedItem ? matchedItem.name : '';
      },
      ...getColumnEnumSearchProps('creationSrc', CreationSource),
    },
    {
      dataIndex: 'entityType',
      width: '15%',
      ellipsis: true,
      title: formatMessage('DATAMODELLISTDETAIL.ENTITIES.ENTITYTYPE'),
      render: (_: string, record: any) => {
        const matchedItem = EntityTypeSource.find((i: { key: string; name: string }) => i.key === record.entityType);
        return matchedItem ? matchedItem.name : '';
      },
      ...getColumnEnumSearchProps('entityType', EntityTypeSource),
    },
    {
      dataIndex: 'comments',
      width: '35%',
      ellipsis: true,
      title: formatMessage('DATAMODELLISTDETAIL.ENTITIES.DESC'),
      ...getColumnSearchProps('comments'),
    },
  ];

  return (
    <div>
      <OperationIconTitle title={formatMessage('DATAMODELLISTDETAIL.ENTITIES.TITLE')} />
      <ResizableTable
        size="small"
        columns={columns}
        pagination={{
          hideOnSinglePage: true,
          pageSize: 5,
          showTotal: (total) => formatMessage('PROJECT.COMMON.PAGINATION.TOTAL', { total }),
        }}
        rowKey="key"
        dataSource={filteredDataSource?.map((el: any, index: number) => ({
          ...el,
          key: `entitiesUsingTable-${index}-${el?.entityCode}`,
        }))}
      />
    </div>
  );
};

export default EntitiesUsingTable;
