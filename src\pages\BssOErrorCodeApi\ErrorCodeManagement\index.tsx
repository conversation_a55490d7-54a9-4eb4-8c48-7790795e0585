import { Spin } from 'antd';
import React, { createContext, useEffect, useState } from 'react';
import styles from './index.less';
import FilterConditions from './FilterConditions';
import ErrorCodeList from './ErrorCodeList';
import { queryODomainTreeData } from '@/services/domainService';
import { useModel } from 'umi';
import { useForm } from 'antd/es/form/Form';
import { isHotfix } from '@/global';

interface IErrorCodeManagementProps {
  openFromBssODataApi?: boolean; // 是否从BssODataApi页面打开
}

const ErrorCodeManagement: React.FC<IErrorCodeManagementProps> = (props) => {
  const { openFromBssODataApi = false } = props;
  const {
    errorCodeSpinning,
    errorCodeListData,
    setErrorCodeSpinning,
    setIsShowOtherErrorDetail,
    setQueryErrorCodeListParams,
    queryErrorCodeList,
  } = useModel('useErrorCodeModel');
  const { systemConfigData } = useModel('useSystemConfigDataModel');
  const [isProduct, setIsProduct] = useState<boolean>(false); // 系统配置项（产品环境or项目定制环境）

  const [odhDomainTreeData, setOdhDomainTreeData] = useState<any[]>([]);

  const [filterConditionsForm] = useForm();

  const initDomainData = async () => {
    try {
      setErrorCodeSpinning(true);
      const { success, data } = await queryODomainTreeData();
      if (success) {
        setOdhDomainTreeData(data.odhDomainList || []);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setErrorCodeSpinning(false);
    }
  };

  const handleReset = async (params: { isRelated: boolean }) => {
    const isRelatedFlag = params?.isRelated ? 'Y' : 'N';
    filterConditionsForm.resetFields();
    filterConditionsForm.setFieldValue('isRelated', isRelatedFlag); // 是否查询关联服务
    setQueryErrorCodeListParams({
      pageNo: 1,
      pageSize: 10,
      isRelated: isRelatedFlag,
    });
    queryErrorCodeList({
      pageNo: 1,
      pageSize: 10,
      isRelated: isRelatedFlag,
    });
  };

  useEffect(() => {
    initDomainData();
  }, []);

  useEffect(() => {
    setIsProduct(systemConfigData?.product || false);
  }, [systemConfigData]);

  return (
    <Spin wrapperClassName={styles.spinContainer} spinning={errorCodeSpinning}>
      <div className={styles.activeLineContainer}>
        {/* hotfix界面增加背景颜色，简单的遮罩层方案即可 */}
        {isHotfix && !openFromBssODataApi && <div className={styles.mask}></div>}
        <FilterConditions
          odhDomainTreeData={odhDomainTreeData}
          openFromBssODataApi={openFromBssODataApi}
          filterConditionsForm={filterConditionsForm}
          handleReset={handleReset}
        />
        <ErrorCodeList
          odhDomainTreeData={odhDomainTreeData}
          isProduct={isProduct}
          errorCodeListData={errorCodeListData}
          openFromBssODataApi={openFromBssODataApi}
          setIsShowOtherErrorDetail={setIsShowOtherErrorDetail}
          handleReset={handleReset}
        />
      </div>
    </Spin>
  );
};
export default ErrorCodeManagement;
