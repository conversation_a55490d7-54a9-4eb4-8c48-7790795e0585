import styles from './index.less';
import useI18n from '@/hooks/useI8n';

const UnusablePage = () => {
  const { formatMessage } = useI18n();

  return (
    <div className={styles.unusablePage}>
      <div className={styles.unusablePageContent}>
        <div className={styles.contentTip}>{formatMessage('UNUSABLE.TIP')}</div>
        <div className={styles.contentText}>{formatMessage('UNUSABLE.ENVIRONMENT_NOT_CONFIG')}</div>
      </div>
    </div>
  );
};

export default UnusablePage;
