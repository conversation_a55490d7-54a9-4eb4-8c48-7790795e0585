import OperationIconTitle from '@/components/OperationIconTitle';
import styles from './index.less';
import { Button, Col, Flex, Row, Spin } from 'antd';
import { useEffect, useState } from 'react';
import EditExtensionServiceDrawer from '../EditExtensionService';
import {
  qryEntityServiceList,
  qryServiceRootPath,
  queryDubboInterfaceDetails,
  queryServiceCatgList,
  queryServiceSubCatgList,
  qrySchemaListByDomainId,
  qryEnumInfoList,
  qryAllHttpStatus,
  qryExtensionServiceRequestInfoByServiceId,
} from '@/services/entityService';
import RequestInformation from './RequestInformation';
import ResponseInformation from './ResponseInformation';
import AddBySwaggerModal from '@/components/AddBySwagger';
import { BranchesOutlined, BugOutlined, UploadOutlined } from '@ant-design/icons';
import classNames from 'classnames';
import { ServiceOrchestrate } from '@/components/ServiceOrchestrate';
import { getErrorSchemaList } from '@/services/errorCodeService';
import { ErrorSchemaType, SensitiveLeveType } from '../../types';
import { CommonOptionItemType } from '@/components/SelectWithAddButton/type';
import useI18n from '@/hooks/useI8n';
import DebugExtensionService from './DebugExtensionService';
import myIcon from '@/static/iconfont/iconfont.css';

interface ManageExtensionServiceProps {
  updateServiceInfo: (serviceInfo: any) => void;
  extensionServiceInfo: any;
  selectedEntity: any; // 当前节点
  selectedRootNode: any; // 根节点
  isProduct: boolean;
  ignoreVersionSource: any;
  tfmServices: CommonOptionItemType[];
  sensitiveLevelList: SensitiveLeveType[];
}

const ManageExtensionService = (props: ManageExtensionServiceProps) => {
  const {
    updateServiceInfo,
    ignoreVersionSource,
    selectedEntity,
    selectedRootNode,
    isProduct = false,
    extensionServiceInfo,
    tfmServices = [],
    sensitiveLevelList = [],
  } = props;
  const { formatMessage } = useI18n();

  const [spining, setSpining] = useState<boolean>(false); // menu loading
  const [openEditExtensionServiceDrawer, setOpenEditExtensionServiceDrawer] = useState<boolean>(false); // 修改扩展服务属性
  const [selectedExtService, setSelectedExtService] = useState<any>({}); // 当前选中的实体扩展服务行数据
  const [serviceCatgSource, setServiceCatgSource] = useState<any>([]); // 服务分类
  const [serviceSubCatgSource, setServiceSubCatgSource] = useState<any>([]); // 服务子分类
  const [dubboClassSource, setDubboClassSource] = useState<any>([]); // dubbo class数据源
  const [serviceRootPath, setServiceRootPath] = useState<string>('');
  const [domainSchemaList, setDomainSchemaList] = useState<any>([]);
  const [errorSchemaList, setErrorSchemaList] = useState<ErrorSchemaType[]>([]); // 异常schema列表
  const [addSwaggerVisible, setAddSwaggerVisible] = useState<boolean>(false);
  const [openServiceOrchestrate, setOpenServiceOrchestrate] = useState<boolean>(false);
  const [openDebugExtensionService, setOpenDebugExtensionService] = useState<boolean>(false);
  const [enumSource, setEnumSource] = useState<any>([]);
  const [allHttpStatusList, setAllHttpStatusList] = useState<any>([]);
  const [requestInformation, setRequestInformation] = useState<any>({});
  const { httpMethod } = extensionServiceInfo;

  // 项目定制环境(systemConfig为false)且修改的记录"定义来源"（CREATION_SRC）为P（Product-Owned）
  const isProjectAndProductOwned = !isProduct && extensionServiceInfo?.creationSrc === 'P';

  // 基础信息
  const CreationSource = [
    { name: 'Product-Owned', key: 'P' },
    { name: 'Project-Customized', key: 'C' },
  ];

  const MarginTop = { marginTop: 15 };
  const ColMarginTop = { marginTop: -3 };

  const ColSpan = ({ value = '' }) => (
    <p
      style={{ marginLeft: 16, marginBottom: 0, textOverflow: 'ellipsis', overflow: 'hidden', whiteSpace: 'nowrap' }}
      title={value}
    >
      {value}
    </p>
  );

  const ColShow = ({ label, value }: any) => {
    return (
      <Col span={8} style={{ display: 'flex', flexDirection: 'row' }}>
        <Col
          style={{
            display: 'flex',
            justifyContent: 'flex-end',
            fontWeight: 600,
          }}
          span={9}
        >
          {label}
        </Col>
        <Col span={15}>
          <ColSpan value={value} />
        </Col>
      </Col>
    );
  };
  const MethodColShow = ({ label, value }: any) => {
    return (
      <Col span={8} style={{ display: 'flex', flexDirection: 'row' }}>
        <Col
          style={{
            display: 'flex',
            justifyContent: 'flex-end',
            fontWeight: 600,
          }}
          span={9}
        >
          {label}
        </Col>
        <Col span={15} style={{ marginTop: -3 }}>
          <ColSpan value={value} />
        </Col>
      </Col>
    );
  };
  const LongColSpan = ({ label, value }: any) => {
    return (
      <>
        <Col
          span={3}
          style={{
            display: 'flex',
            justifyContent: 'flex-end',
            fontWeight: 600,
          }}
        >
          {label}
        </Col>
        <Col span={21}>
          <ColSpan value={value} />
        </Col>
      </>
    );
  };

  const queryServiceCatg = async () => {
    setSpining(true);
    try {
      const { success, data } = await queryServiceCatgList();
      if (success) {
        setServiceCatgSource(data || []);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setSpining(false);
    }
  };

  const queryServiceSubCatg = async () => {
    setSpining(true);
    try {
      const { success = false, data = [] } = await queryServiceSubCatgList();
      if (success) {
        data.forEach((i: any) => {
          i.label = i.serviceSubCatgName;
          i.value = i.serviceSubCatgName;
        });
        setServiceSubCatgSource(data);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setSpining(false);
    }
  };

  const queryExtensionServices = async () => {
    setSpining(true);
    try {
      const { success, data } = await qryEntityServiceList(selectedEntity.entityId);
      if (success) {
        const newServiceInfo = data?.find((item: any) => item?.serviceId === extensionServiceInfo?.serviceId);
        updateServiceInfo(newServiceInfo);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setSpining(false);
    }
  };

  const queryDubboClassMethodList = async () => {
    setSpining(true);
    try {
      const { success, data } = await queryDubboInterfaceDetails({ fuzzyKey: '' });
      if (success) {
        setDubboClassSource(data);
      } else {
        setDubboClassSource([]);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setSpining(false);
    }
  };

  const queryServiceRootPath = async () => {
    if (selectedEntity?.entityId) {
      const { success, data } = await qryServiceRootPath({ entityId: selectedEntity?.entityId });
      if (success) {
        setServiceRootPath(data);
      }
    }
  };

  // 查询领域下schema列表
  const querySchemaList = async () => {
    setSpining(true);
    try {
      const { success, data } = await qrySchemaListByDomainId({ domainId: selectedRootNode?.domainId });
      if (success) {
        setDomainSchemaList(data);
      }
    } catch (erroe) {
      console.error(erroe);
    } finally {
      setSpining(false);
    }
  };

  // 查询异常schema列表
  const queryErrorSchemaList = async () => {
    setSpining(true);
    try {
      const { success, data } = await getErrorSchemaList();
      if (success) {
        setErrorSchemaList(data);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setSpining(false);
    }
  };

  // 查询所有的状态码
  const queryAllHttpStatus = async () => {
    setSpining(true);
    try {
      const { success, data } = await qryAllHttpStatus();
      if (success) {
        setAllHttpStatusList(data);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setSpining(false);
    }
  };

  // 查询枚举字段列表
  const queryEnumList = async () => {
    setSpining(true);
    try {
      const param: any = {
        domainId: selectedRootNode?.domainId,
        pageNo: 1,
        pageSize: 100,
      };
      const { success, data } = await qryEnumInfoList(param);
      if (success) {
        const dataSource = data?.list.map((item: any) => ({ label: item.enumName, value: item.enumId }));
        setEnumSource(dataSource || []);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setSpining(false);
    }
  };

  const getRealPath = (record: any) => {
    const { servicePath, serviceCatg, serviceVer } = record;
    // 拼接namespace和version
    const suffix = serviceCatgSource.filter((i: any) => i.serviceCatg === serviceCatg)[0]?.nameSpace || '';
    const rootPath = suffix ? `${serviceRootPath}/${suffix}` : serviceRootPath;
    let lastPath;
    if (servicePath) {
      lastPath = servicePath.substring(serviceRootPath.length + 1, servicePath.length);
    }
    if (serviceVer) {
      return `${rootPath}/${serviceVer}/${lastPath}`;
    } else {
      return `${rootPath}/${lastPath}`;
    }
  };

  // 查询请求参数信息
  const queryRequestInfo = async () => {
    setSpining(true);
    try {
      const param: any = {
        serviceId: extensionServiceInfo?.serviceId,
      };
      const { success, data } = await qryExtensionServiceRequestInfoByServiceId(param);
      if (success) {
        setRequestInformation(data);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setSpining(false);
    }
  };

  useEffect(() => {
    // 查询服务分类
    queryServiceCatg();
    // 查询服务子分类
    queryServiceSubCatg();
    // 查询dubbo class和method
    queryDubboClassMethodList();
    // 查询领域下schema列表
    querySchemaList();
    // 查询异常schema列表
    queryErrorSchemaList();
    // 查询所有的状态码
    queryAllHttpStatus();
    // 查询枚举字段列表
    queryEnumList();
  }, []);

  useEffect(() => {
    // 查询扩展服务详情
    queryExtensionServices();
    // 查询实体服务根路径
    queryServiceRootPath();
  }, [selectedEntity?.entityId]);

  const getImplTypeName = (type: string) => {
    let implName = '';
    switch (type) {
      case 'N':
        implName = 'Orchestration';
        break;
      case 'D':
        implName = 'Projection Dubbo API';
        break;
      case 'R':
        implName = 'Projection Restful API';
        break;
      case 'S':
        implName = 'Projection Call Service';
        break;
      default:
        implName = 'Orchestration';
        break;
    }
    return implName;
  };

  const renderHttpMethod = (method: string) => {
    let methodElement;
    switch (method) {
      case 'GET':
        methodElement = (
          <div className={styles.dyStyleWrapper} title={method}>
            <span className={styles.dyStyleGET}>{method}</span>
          </div>
        );
        break;
      case 'POST':
        methodElement = (
          <div className={styles.dyStyleWrapper} title={method}>
            <span className={styles.dyStylePOST}>{method}</span>
          </div>
        );
        break;
      case 'PUT':
        methodElement = (
          <div className={styles.dyStyleWrapper} title={method}>
            <span className={styles.dyStyleGET}>{method}</span>
          </div>
        );
        break;
      case 'PATCH':
        methodElement = (
          <div className={styles.dyStyleWrapper} title={method}>
            <span className={styles.dyStylePATCH}>{method}</span>
          </div>
        );
        break;
      case 'DELETE':
        methodElement = (
          <div className={styles.dyStyleWrapper} title={method}>
            <span className={styles.dyStyleDELETE}>{method}</span>
          </div>
        );
        break;
      default:
        methodElement = (
          <div className={styles.dyStyleWrapper} title={method}>
            <span>{method}</span>
          </div>
        );
        break;
    }
    return methodElement;
  };

  return (
    <Spin spinning={spining}>
      <div>
        {/* 扩展服务基础信息 */}
        <div className={styles.domainObjContent}>
          <Flex align="center" gap={'1rem'}>
            <OperationIconTitle
              title={formatMessage('MANAGEEXTENSIONSERVICE.BASIC_INFORMATION')}
              type={selectedRootNode?.state === 'D' ? 'edit' : ''}
              opt={formatMessage('MANAGEEXTENSIONSERVICE.EDIT')}
              handleClick={() => {
                setOpenEditExtensionServiceDrawer(true);
                setSelectedExtService(extensionServiceInfo);
              }}
            />
            {!isProjectAndProductOwned && extensionServiceInfo?.implType !== 'D' && (
              <div
                className={classNames(styles.uploadBySwagger, {
                  [styles.hide]: selectedRootNode?.state !== 'D',
                })}
                onClick={() => {
                  setAddSwaggerVisible(true);
                }}
              >
                <UploadOutlined />
                <span style={{ marginLeft: '4px', fontWeight: 'bold' }}>
                  {formatMessage('MANAGEEXTENSIONSERVICE.IMPORT_BY_SWAGGER')}
                </span>
              </div>
            )}
            {extensionServiceInfo?.implType === 'N' && (
              <div
                className={classNames(styles.uploadBySwagger)}
                onClick={() => {
                  setOpenServiceOrchestrate(true);
                }}
              >
                <BranchesOutlined />
                <span style={{ marginLeft: '4px', fontWeight: 'bold' }}>
                  {formatMessage('MANAGEEXTENSIONSERVICE.SERVICE_ORCHESTRATE')}
                </span>
              </div>
            )}
            <div
              className={classNames(styles.uploadBySwagger)}
              onClick={() => {
                setOpenDebugExtensionService(true);
              }}
            >
              <span className={`${myIcon.iconfont} ${myIcon['icon-debug']}`} />
              <span style={{ marginLeft: '4px', fontWeight: 'bold' }}>
                {formatMessage('MANAGEEXTENSIONSERVICE.DEBUG')}
              </span>
            </div>
          </Flex>
          <div className={styles.content}>
            <Row style={MarginTop}>
              <ColShow label={formatMessage('MANAGEEXTENSIONSERVICE.NAME')} value={extensionServiceInfo?.serviceName} />
              <ColShow
                label={formatMessage('MANAGEEXTENSIONSERVICE.SERVICE_CATALOG')}
                value={
                  serviceCatgSource.filter((item: any) => item?.serviceCatg === extensionServiceInfo?.serviceCatg)[0]
                    ?.serviceCatgName
                }
              />
              <ColShow
                label={formatMessage('MANAGEEXTENSIONSERVICE.SERVICE_SUBCATALOG')}
                value={
                  serviceSubCatgSource.filter(
                    (item: any) => item?.serviceSubCatg === extensionServiceInfo?.serviceSubCatg,
                  )[0]?.serviceSubCatgName
                }
              />
            </Row>
            <Row style={MarginTop}>
              <ColShow
                label={formatMessage('MANAGEEXTENSIONSERVICE.CREATION_SOURCE')}
                value={CreationSource.filter((i) => i.key === extensionServiceInfo?.creationSrc).map((i) => i.name)}
              />
              <MethodColShow
                label={formatMessage('MANAGEEXTENSIONSERVICE.SERVICE_METHOD')}
                value={renderHttpMethod(httpMethod)}
              />
              <ColShow
                label={formatMessage('MANAGEEXTENSIONSERVICE.SERVICE_PATH')}
                value={getRealPath(extensionServiceInfo)}
              />
            </Row>
            <Row style={MarginTop}>
              <ColShow
                label={formatMessage('MANAGEEXTENSIONSERVICE.IMPLEMENT_TYPE')}
                value={getImplTypeName(extensionServiceInfo?.implType)}
              />
              {extensionServiceInfo?.implType === 'D' && (
                <>
                  <ColShow
                    label={formatMessage('MANAGEEXTENSIONSERVICE.DUBBO_CLASS_PATH')}
                    value={extensionServiceInfo?.odhServiceProjDubboVo?.dubboClass}
                  />
                  <ColShow
                    label={formatMessage('MANAGEEXTENSIONSERVICE.DUBBO_METHOD')}
                    value={extensionServiceInfo?.odhServiceProjDubboVo?.dubboMethod}
                  />
                </>
              )}
              {extensionServiceInfo?.implType === 'R' && (
                <>
                  <ColShow
                    label={formatMessage('MANAGEEXTENSIONSERVICE.RESTFUL_SERVICE_PATH')}
                    value={extensionServiceInfo?.odhServiceProjRestVo?.servicePath}
                  />
                  <MethodColShow
                    label={formatMessage('MANAGEEXTENSIONSERVICE.HTTP_METHOD')}
                    value={renderHttpMethod(extensionServiceInfo?.odhServiceProjRestVo?.serviceMethod)}
                  />
                </>
              )}
            </Row>
            <Row style={MarginTop}>
              <LongColSpan
                label={formatMessage('MANAGEEXTENSIONSERVICE.DESCRIPTIONS')}
                value={extensionServiceInfo?.comments}
              />
            </Row>
          </div>
        </div>
        <div className={styles.domainObjContent}>
          <RequestInformation
            queryRequestInfo={queryRequestInfo}
            requestInformation={requestInformation}
            sensitiveLevelList={sensitiveLevelList}
            isProjectAndProductOwned={isProjectAndProductOwned}
            enumSource={enumSource}
            domainSchemaList={domainSchemaList}
            isProduct={isProduct}
            selectedRootNode={selectedRootNode}
            selectedService={extensionServiceInfo}
            updateDomainSchemaList={async () => {
              await querySchemaList();
              await queryErrorSchemaList();
            }}
          />
        </div>
        <div className={styles.domainObjContent}>
          <ResponseInformation
            sensitiveLevelList={sensitiveLevelList}
            isProjectAndProductOwned={isProjectAndProductOwned}
            allHttpStatusList={allHttpStatusList}
            enumSource={enumSource}
            domainSchemaList={domainSchemaList}
            errorSchemaList={errorSchemaList}
            isProduct={isProduct}
            selectedRootNode={selectedRootNode}
            selectedService={extensionServiceInfo}
            updateDomainSchemaList={async () => {
              await querySchemaList();
              await queryErrorSchemaList();
            }}
          />
        </div>
      </div>
      {openServiceOrchestrate && (
        <ServiceOrchestrate
          extensionServiceInfo={extensionServiceInfo}
          setOpenServiceOrchestrate={setOpenServiceOrchestrate}
        />
      )}
      {/* 修改扩展服务 */}
      <EditExtensionServiceDrawer
        ignoreVersionSource={ignoreVersionSource}
        initValues={selectedExtService}
        open={openEditExtensionServiceDrawer}
        serviceCatgSource={serviceCatgSource}
        serviceSubCatgSource={serviceSubCatgSource}
        dubboClassSource={dubboClassSource}
        serviceRootPath={serviceRootPath}
        onCancel={() => setOpenEditExtensionServiceDrawer(false)}
        selectedEntity={selectedEntity}
        isProduct={isProduct as boolean}
        selectedRootNode={selectedRootNode}
        tfmServices={tfmServices}
        onOk={() => {
          // 修改扩展服务成功后，刷新当前表格
          setOpenEditExtensionServiceDrawer(false);
          queryServiceSubCatg();
          queryExtensionServices();
        }}
      />
      <AddBySwaggerModal
        selectedService={extensionServiceInfo}
        open={addSwaggerVisible}
        setVisible={setAddSwaggerVisible}
        updateDomainSchemaList={() => {
          querySchemaList();
          queryErrorSchemaList();
        }}
      />
      {openDebugExtensionService && (
        <DebugExtensionService
          selectedRootNode={selectedRootNode}
          selectedService={extensionServiceInfo}
          requestInformation={requestInformation}
          onClose={() => {
            setOpenDebugExtensionService(false);
          }}
        />
      )}
    </Spin>
  );
};

export default ManageExtensionService;
