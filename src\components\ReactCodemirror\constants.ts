import { json } from '@codemirror/lang-json';
import { php } from '@codemirror/lang-php';
import { python } from '@codemirror/lang-python';
import { sql } from '@codemirror/lang-sql';
import { yaml } from '@codemirror/lang-yaml';
import { StreamLanguage } from '@codemirror/language';
import { java } from '@codemirror/legacy-modes/mode/clike';
import { ruby } from '@codemirror/legacy-modes/mode/ruby';
import { Extension } from '@uiw/react-codemirror';

export const languages = new Map<string, Extension[]>()
  .set('json', [json()])
  .set('java', [StreamLanguage.define(java)])
  .set('python', [python()])
  .set('ruby', [StreamLanguage.define(ruby)])
  .set('sql', [sql()])
  .set('yaml', [yaml()])
  .set('php', [php()]);
