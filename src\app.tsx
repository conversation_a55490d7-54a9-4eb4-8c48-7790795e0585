import { RequestConfig, RequestOptions, AxiosResponse } from '@@/plugin-request/types';
import createSignature from '@/utils/signcode';
import { notification, Modal } from 'antd';
import ErrorModal from '@/components/ErrorModal';
import '../public/font.css';
import { isHotfix, portalLanguage } from './global';

// 处理业务报错
const handleBusinessError: (response: AxiosResponse) => any = (response) => {
  const { data, status } = response;
  if ((status === 200 && data?.success === false) || status === 500) {
    (Modal as any).create({
      errCode: data?.errorCode || '',
      errMessage: data?.errorMessage || '',
      traceId: data?.traceId || '',
    });
    return Promise.reject(data);
  }
  return response;
};

// 请求头处理
const handleRequest: (url: string, config: RequestOptions) => any = (url, config) => {
  // 获取 sign codes
  const token = sessionStorage.getItem('X-CSRF-TOKEN') || localStorage.getItem('X-CSRF-TOKEN');
  const signCodes = createSignature(config, token);
  const signHeaders: Record<string, string> = {};
  Object.entries(signCodes).forEach(([key, value]) => {
    signHeaders[key] = value;
  });
  // 增加sign codes headers
  const customizedConfig = {
    ...(config || {}),
    headers: {
      ...(config?.headers || {}),
      ...(signHeaders || {}),
      'X-Csrf-Token': token,
      // @ts-ignore - 忽略类型检查
      menuId: window?.top?.portal?.appGlobal?.get('currentMenu')?.menuId || '',
      ...(isHotfix ? { 'H-hotfix': true } : {}),
    },
  };
  return {
    url,
    options: customizedConfig,
  };
};

// 白名单
const whiteList = [/^\/\w+\/odh\/runningVersion\?appCode=\w+$/];

export const request: RequestConfig = {
  errorConfig: {
    errorHandler: (error: any, opts: RequestOptions) => {
      const { status } = error.response || {};
      const { data } = error.response || {};
      const requestUrl = error?.config?.url || ''; // 获取当前请求的 URL

      // 检查是否在白名单中
      const isWhitelisted = whiteList.some((item) => {
        if (typeof item === 'string') {
          // 如果是字符串，直接比较
          return item === requestUrl;
        } else if (item instanceof RegExp) {
          // 如果是正则表达式，使用 test 方法匹配
          return item.test(requestUrl);
        }
        return false; // 其他类型不处理
      });

      if (isWhitelisted) {
        // 如果在白名单中，直接返回响应而不抛出错误
        return error.response;
      }

      if (status === 403) {
        // 403 鉴权失败
        notification.error({
          message: 'Error',
          description: 'Identification permissions failed',
        });
        throw error;
      } else if (status) {
        // 其他异常码 请求失败
        notification.error({
          message: 'Error',
          description: 'Request failed',
        });
        throw error;
      }
    },
    errorThrower: () => {},
  },
  requestInterceptors: [handleRequest],
  responseInterceptors: [handleBusinessError],
};

export function rootContainer(container: any) {
  // 应用挂载后延迟设置语言
  setTimeout(() => {
    try {
      // 在应用挂载后动态导入并使用setLocale，避免直接导入报错
      import('umi').then((umi) => {
        if (portalLanguage === 'zh') {
          umi.setLocale('zh-CN');
        } else {
          umi.setLocale('en-US');
        }
      });
    } catch (e) {
      console.error('设置语言失败', e);
    }
  }, 300);

  return (
    <>
      {container}
      {/* 异常modal处理 */}
      <ErrorModal />
    </>
  );
}
