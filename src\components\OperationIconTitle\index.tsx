import { PlusOutlined, FormOutlined } from "@ant-design/icons";
import BlockTitle from "../BlockTitle";
import styles from './index.less'
import { useMemo } from "react";
import classNames from "classnames";
import React from 'react';

interface ExtraButtonProps {
  text: string;
  onClick: () => void;
  className?: string;
  icon?: React.ReactNode; // 添加自定义图标属性
}

interface IOperationIconTitle {
  title?: string;
  opt?: string;
  handleClick?: () => void;
  type?: string;
  className?: string;
  extraButtons?: ExtraButtonProps[];
}

interface OperationIconProps {
  className?: string;
  handleClick?: () => void;
  opt?: string;
  style?: React.CSSProperties;
  type?: string;
  icon?: React.ReactNode;
}

export const OperationIcon: React.FC<OperationIconProps> = ({
  className = '',
  handleClick = () => {},
  opt = '',
  style = {},
  type = '',
  icon,
}) => {
  const currentIcon = useMemo(() => {
    // 如果提供了自定义图标，则使用自定义图标
    if (icon) {
      // 直接返回图标，不尝试添加className
      return icon;
    }

    // 否则使用默认图标
    switch (type) {
      case 'edit':
        return <FormOutlined className={styles.optIcon} />;
      case 'add':
        return <PlusOutlined className={styles.optIcon} />;
      default:
        return '';
    }
  }, [type, icon]);

  return (
    <div
      className={classNames(className, styles.operationIcon)}
      onClick={handleClick}
      style={{
        display: 'inline-block',
        marginLeft: 8,
        cursor: 'pointer',
        ...style,
      }}
    >
      {currentIcon}
      <span className={styles.operationSpan}>{opt}</span>
    </div>
  );
};

const OperationIconTitle: React.FC<IOperationIconTitle> = ({
  title = '',
  opt = '',
  handleClick = () => {},
  type = '',
  className = '',
  extraButtons = [],
}) => {
  return (
    <div>
      <BlockTitle>
        {title}
        {type && <OperationIcon handleClick={handleClick} opt={opt} type={type} className={className} />}
        {extraButtons.map((button, index) => (
          <OperationIcon
            key={index}
            handleClick={button.onClick}
            opt={button.text}
            className={button.className || className}
            icon={button.icon}
          />
        ))}
      </BlockTitle>
    </div>
  );
};

export default OperationIconTitle;
