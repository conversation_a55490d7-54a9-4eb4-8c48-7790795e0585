.dyStyleWrapper {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  span:nth-child(1) {
    border-radius: 2px 2px 2px 2px;
    opacity: 1;
    font-size: 14px;
    font-family: Nunito Sans;
    font-weight: 400;
    line-height: 22px;
    padding: 0 12px;
  }
  span:nth-child(2) {
    margin-left: 8px;
  }
  .dyStylePOST {
    background: #e1faea;
    border: 1px solid #6ce0a0;
    color: #00bb66;
  }
  .dyStyleGET {
    border: 1px solid #709efa;
    background: #edf5ff;
    color: #4477ee;
  }
  .dyStylePATCH {
    background: #f9f0ff;
    border: 1px solid #d3adf7;
    color: #531dab;
  }
  .dyStyleDELETE {
    background: #f9f0ff;
    border: 1px solid #f7adc3;
    color: #ab1d1d;
  }
}
.servbiceName {
  color: #4477EE;
  cursor: pointer;
}
.deprecated {
  text-decoration-line:line-through
}
