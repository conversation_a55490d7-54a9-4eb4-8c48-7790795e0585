// 扁平化领域Schema列表
const flatteningDomainSchemaList = (domainSchemaList: any) => {
  const flatDomainSchemaList = domainSchemaList
    .map((i: any) => {
      if (i?.schemaList) {
        const schemaListWithDomainObj = i?.schemaList?.map((j: any) => {
          return {
            ...j,
            domainObjId: i?.domainObjId,
            domainObjName: i?.domainObjName,
          };
        });
        return schemaListWithDomainObj; //{schemaId:number,schemaName:string,domainObjId:number,domainObjName:string}
      }
      return;
    })
    ?.flat();
  return flatDomainSchemaList;
};
export default flatteningDomainSchemaList;
