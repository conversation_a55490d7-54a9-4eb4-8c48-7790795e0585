import React, { useEffect, useState } from 'react';
import type { FC } from 'react';
import classNames from 'classnames';
import { PlusCircleOutlined, MinusCircleOutlined, ReloadOutlined } from '@ant-design/icons';
import { getLeftDistanche } from './util';
import useSyncCallback from '@/hooks/useSyncCallback';
import styles from './index.less';

interface IRenderSvgByInnerHTML {
  innerHTML: string; // innerHtml
  baseRate?: number; // 放大缩小倍率
}

const RenderSvgByInnerHTML: FC<IRenderSvgByInnerHTML> = (props) => {
  const { innerHTML, baseRate = 0.3 } = props;
  const [initSvgInfo, setInitSvgInfo] = useState({
    width: 0,
    height: 0,
  }); // 记录svg原始宽高
  const [rate, setRate] = useState(1); // 记录放大比例
  const [parentStyle, setParentStyle] = useState<Record<string, string | number>>({}); // parent style
  const [childStyle, setChildStyle] = useState<Record<string, string | number>>({}); // child style

  // 父元素起始样式
  // const beforeParentStyle = {
  //   width: '100%',
  //   height: '100%',
  //   minHeight: 'inherit',
  // };
  // 父元素进行中样式
  const afetrParentStyle = {
    overflow: 'hidden',
    position: 'relative',
    // height: 'to be caculate'
  };

  // // 字元素起始样式
  // const beforeChildStyle = {
  //   // width: 'to be caculate'
  //   // height: 'to be caculate'
  // };

  // 字元素进行中样式
  const afterChildStyle = {
    position: 'absolute',
    // width: 'to be caculate'
    // height 'to be caculate'
    // left: 'to be caculate'
    // top: 'to be caculate'
  };

  // 处理元素的mousedown mousemove mouseup
  const handleTargetMove = (eventStart: any = {}) => {
    let startFlag = true;
    let hasMoveX = 0;
    let hasMoveY = 0;

    // 起始坐标
    const startX = eventStart.clientX;
    const startY = eventStart.clientY;
    const moveHandler = (eventEnd: any = {}) => {
      // 结束坐标
      const endX = eventEnd.clientX;
      const endY = eventEnd.clientY;

      // 判断 是否超出了可移动的区域 可移动区域是指包裹svg的区域 #targetContainer
      const {
        left: areaLeft,
        right: areaRight,
        top: areaTop,
        bottom: areaBottom,
      } = (document.querySelector('#targetContainer') as any).getBoundingClientRect();
      if (endX < areaLeft || endX > areaRight || endY < areaTop || endY > areaBottom || !startFlag) {
        startFlag = false;
        return;
      }

      // 移动距离
      const distanceX = endX - startX - hasMoveX;
      const distanceY = endY - startY - hasMoveY;

      // 记录上一次的移动距离
      hasMoveX = endX - startX;
      hasMoveY = endY - startY;

      // 获取当前元素图片的偏移
      const currentX = getLeftDistanche((document.querySelector('#targetContainer svg') as any).style.left || '0px');
      const currentY = getLeftDistanche((document.querySelector('#targetContainer svg') as any).style.top || '0px');

      // 更新svg图形的偏移量
      setChildStyle({
        ...(childStyle || {}),
        left: currentX + distanceX,
        top: currentY + distanceY,
      });
    };
    document.addEventListener('mousemove', moveHandler);
    document.addEventListener('mouseup', () => {
      document.removeEventListener('mousemove', moveHandler);
    });
  };

  // handle move async params
  const handleTargetMoveParams = useSyncCallback((event) => handleTargetMove(event));

  // handle move async
  const handleTargetMoveSync = (e: any) => {
    handleTargetMoveParams(e);
  };

  // 放大事件 以baseRate的速率增加
  const zoomOut = () => {
    const newRate = rate + baseRate;
    setRate(newRate);
    const widthAfterZoom = initSvgInfo.width * newRate;
    const heightAfterZoom = initSvgInfo.height * newRate;
    setChildStyle({
      ...(childStyle || {}),
      width: widthAfterZoom,
      height: heightAfterZoom,
    });
  };

  // 缩小事件 以baseRate的速率减少
  const zoomIn = () => {
    const newRate = rate - baseRate;
    setRate(newRate);
    const widthAfterZoom = initSvgInfo.width * newRate;
    const heightAfterZoom = initSvgInfo.height * newRate;
    setChildStyle({
      ...(childStyle || {}),
      width: widthAfterZoom,
      height: heightAfterZoom,
    });
  };

  // 重置事件
  const zoomReset = () => {
    // 获取父元素的宽度
    const containerWidth = document.querySelector('#targetContainer')?.clientWidth || 0;

    // 根据图片和容器的比例做不同的操作
    if (initSvgInfo.width < containerWidth) {
      const initLeftDistance = (containerWidth - initSvgInfo.width) / 2;
      setChildStyle({
        ...(childStyle || {}),
        width: initSvgInfo.width,
        height: initSvgInfo.height,
        left: initLeftDistance,
        top: 10,
      });
    } else {
      setChildStyle({
        ...(childStyle || {}),
        width: initSvgInfo.width,
        height: initSvgInfo.height,
        left: 0,
        top: 10,
      });
    }

    setRate(1);
  };

  // init事件
  const handleInit = () => {
    // 获取父元素的宽度
    const containerWidth = document.querySelector('#targetContainer')?.clientWidth || 0;

    // 获取图片高 设置给父元素 #targetContainer
    const svgWidth = document.querySelector('#targetContainer svg')?.clientWidth || 0;
    const svgHeight = document.querySelector('#targetContainer svg')?.clientHeight || 0;

    // 根据图片和容器的比例做不同的操作
    if (svgWidth < containerWidth) {
      const initLeftDistance = (containerWidth - svgWidth) / 2;
      setChildStyle({
        ...afterChildStyle,
        width: svgWidth,
        height: svgHeight,
        left: initLeftDistance,
        top: 10,
      });
    } else {
      setChildStyle({
        ...afterChildStyle,
        width: svgWidth,
        height: svgHeight,
        left: 0,
        top: 10,
      });
    }
    setParentStyle({
      ...afetrParentStyle,
      height: svgHeight + 20,
      minHeight: 150,
    });
    setInitSvgInfo({
      width: svgWidth,
      height: svgHeight,
    });

    // 销毁 + Reset
    const parentTarget: any = document.querySelector('#targetContainer');
    parentTarget?.addEventListener('mousedown', handleTargetMoveSync);
  };

  // 图片切换
  const destroy = () => {
    setInitSvgInfo({
      width: 0,
      height: 0,
    });
    setRate(1);
    setChildStyle({});
    setParentStyle({});

    // 针对父元素监听移动 相比较直接监听svg 效果会好一点(svg的空白区域也可以拖拽移动)
    const parentTarget: any = document.querySelector('#targetContainer');
    parentTarget?.removeEventListener('mousedown', handleTargetMoveSync);
  };

  // 初始化
  useEffect(() => {
    handleInit();
    return () => {
      destroy();
    };
  }, [innerHTML]);

  // 由于svg是个代码不可见元素 所以这里监听实现 "变量 =》 元素样式变化"的转换
  useEffect(() => {
    const keys = Object.keys(childStyle);
    keys.forEach((key) => {
      (document.querySelector('#targetContainer svg') as any).style[key] = childStyle?.[key];
    });
  }, [childStyle]);

  return innerHTML ? (
    <div className={styles.svgContainer}>
      <PlusCircleOutlined className={styles.operationIcon} onClick={zoomOut} />
      <MinusCircleOutlined className={styles.operationIcon} onClick={zoomIn} />
      <ReloadOutlined className={styles.operationIcon} onClick={zoomReset} />
      <div
        id="targetContainer"
        className={classNames(styles.svgContainer, styles.svgTagetContainer)}
        dangerouslySetInnerHTML={{ __html: innerHTML }}
        style={parentStyle}
      />
    </div>
  ) : (
    <></>
  );
};

export default RenderSvgByInnerHTML;
