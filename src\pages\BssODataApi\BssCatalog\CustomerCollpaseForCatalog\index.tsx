import React, { useEffect, useState } from 'react';
import { ICatalog } from '@/services/typing.d';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import classNames from 'classnames';
import { recursionGetFirstCatalog } from '@/utils/recursionGetFirstSelect';
import { ICurrentActiveKeyCatalog as ICurrentActiveKey } from '@/typing.d';
import { judgeExistInCatalog } from '@/utils/judgeCurrentKeyInData';
import { searchCatalogByText } from '@/utils/searchDataByKeyWords';
import styles from '../CustomerCollpaseForDomain/index.less';

interface CustomerCollpaseForDomain {
  menuRefreshFlag: any; // 目录刷新控制
  completeRefreash: () => void; // 目录刷新结束响应事件
  catalogData: Array<ICatalog>; // 接口数据
  searchvalue: string; // 搜索值
  onItemChange?: (entity: ICurrentActiveKey) => void; // 选中项
  onRefresh: (currentActiveKey: ICurrentActiveKey) => void; // 刷新缓存 通知上级刷新缓存
}

const CustomerCollpaseForCatalog: React.FC<CustomerCollpaseForDomain> = (props) => {
  const { catalogData, searchvalue, menuRefreshFlag, onItemChange, onRefresh, completeRefreash } = props;

  const [hasInitFirstSelect, setHasInitFirstSelect] = useState<boolean>(false); // 是否初始化过默认选择第一项
  const [currentActiveKey, setCurrentActiveKey] = useState<ICurrentActiveKey>(); // 当前活跃的item
  const [collpaseKey, setCollpaseKey] = useState<Record<string, Record<string, boolean>>>({}); // 记录当前树的展开活跃key
  const [catalogDataAfterSearch, setCatalogDataAfterSearch] = useState<Array<ICatalog>>([]); // 搜索之后的值

  // 一级目录点击
  const handleFirstCollpase = (firstKey: string) => {
    if (collpaseKey[firstKey]) {
      const { [firstKey]: deleteKey, ...otherKey } = collpaseKey;
      setCollpaseKey(otherKey);
    } else {
      setCollpaseKey({
        ...collpaseKey,
        [firstKey]: {},
      });
    }
  };

  // 二级目录的点击
  const handleSecondCollpase = (firstKey: string, secondKey: string) => {
    setCollpaseKey({
      ...collpaseKey,
      [firstKey]: {
        ...(collpaseKey?.[firstKey] || {}),
        [secondKey]: !collpaseKey?.[firstKey]?.[secondKey],
      },
    });
  };

  // 通过关键字搜索
  const searchByText = (searchvalue: string) => {
    const resultsAfterSearch = searchCatalogByText(searchvalue, catalogData);
    return resultsAfterSearch;
  };

  // 处理搜索
  const handleSearch = (catalogData: Array<ICatalog>, searchvalue: string) => {
    if (searchvalue) {
      // 关键字搜索 忽略大小写
      const resultsAfterSearch = searchByText(searchvalue);
      setCatalogDataAfterSearch(resultsAfterSearch);
      // 搜索完默认展开所有的key
      let collpasekeyAfterSearch: Record<string, Record<string, boolean>> = {};
      resultsAfterSearch.forEach((catalogItem) => {
        collpasekeyAfterSearch[catalogItem?.catalog] = {};
        catalogItem.groupList.forEach((groupItem) => {
          collpasekeyAfterSearch[catalogItem?.catalog][groupItem?.groupName] = true;
        });
      });
      setCollpaseKey(collpasekeyAfterSearch);
    } else {
      // 无搜索 重置选中项的展开情况
      setCatalogDataAfterSearch(catalogData);
      if (
        !(
          currentActiveKey?.clickCatalog?.catalog &&
          currentActiveKey?.clickGroup?.groupName &&
          currentActiveKey?.clickSubCatalog?.subCatalog
        )
      ) {
        return;
      }
      setCollpaseKey({
        [currentActiveKey?.clickCatalog?.catalog]: {
          [currentActiveKey?.clickGroup?.groupName]: true,
        },
      });
    }
  };

  // 默认展示 + 搜索
  // 初始化 展示第一个符合条件的
  // 已初始化 应用手动输入的搜索值
  // 无数据 置空
  useEffect(() => {
    if (catalogData?.length && !hasInitFirstSelect) {
      // 初始化 查找第一个符合条件的选项
      const catalogFirstResult = recursionGetFirstCatalog(catalogData);
      setCatalogDataAfterSearch(catalogData);
      setCollpaseKey({
        [catalogFirstResult?.catalog?.catalog || '']: {
          [catalogFirstResult?.group?.groupName || '']: true,
        },
      });
      setCurrentActiveKey({
        clickCatalog: catalogFirstResult?.catalog,
        clickGroup: catalogFirstResult?.group,
        clickSubCatalog: catalogFirstResult?.subCatalog,
      });
      setHasInitFirstSelect(true);
    } else if (catalogData?.length && hasInitFirstSelect) {
      handleSearch(catalogData, searchvalue);
    } else {
      setCatalogDataAfterSearch([]);
      setCollpaseKey({});
    }
  }, [catalogData, searchvalue]);

  // 计算当前选中和选中目录的关系 =》刷新
  // 刷新后data + key 都存在
  //   当前的选中项目是不是在新的数据里面
  //     存在 刷新 Tip: 存在的条件是父集目录+子集完全一致，防止调整目录位置引起的数据对不上的场景
  //     不在 重新取第一项
  // 刷新后data + !key
  //   重新取第一项
  // 刷新后!data
  //   无数据 置空
  useEffect(() => {
    if (!menuRefreshFlag?.flag) return;
    // 计算当前选中和选中目录的关系 =》刷新
    const catalogData = menuRefreshFlag?.data || [];
    if (currentActiveKey?.clickSubCatalog?.subCatalog && catalogData?.length) {
      // 选中项是不是在新的数据里面
      const isExist = judgeExistInCatalog(currentActiveKey, catalogData);
      if (isExist) {
        // 存在 刷新
        onRefresh?.(currentActiveKey);
      } else {
        // 不存在 重新取第一项
        const catalogFirstResult = recursionGetFirstCatalog(catalogData);
        setCollpaseKey({
          ...(collpaseKey || {}),
          [catalogFirstResult?.catalog?.catalog || '']: {
            ...(collpaseKey?.[catalogFirstResult?.catalog?.catalog || ''] || {}),
            [catalogFirstResult?.group?.groupName || '']: true,
          },
        });
        setCurrentActiveKey({
          clickCatalog: catalogFirstResult?.catalog,
          clickGroup: catalogFirstResult?.group,
          clickSubCatalog: catalogFirstResult?.subCatalog,
        });
      }
    } else if (!currentActiveKey?.clickSubCatalog?.subCatalog && catalogData?.length) {
      // 刷新后data + !key
      const catalogFirstResult = recursionGetFirstCatalog(catalogData);
      // 重新取第一项
      setCollpaseKey({
        ...(collpaseKey || {}),
        [catalogFirstResult?.catalog?.catalog || '']: {
          ...(collpaseKey?.[catalogFirstResult?.catalog?.catalog || ''] || {}),
          [catalogFirstResult?.group?.groupName || '']: true,
        },
      });
      setCurrentActiveKey({
        clickCatalog: catalogFirstResult?.catalog,
        clickGroup: catalogFirstResult?.group,
        clickSubCatalog: catalogFirstResult?.subCatalog,
      });
    } else {
      // 无数据 置空详情
      onItemChange?.({});
      setCurrentActiveKey({});
    }
    completeRefreash?.();
  }, [menuRefreshFlag]);

  // 通知父级别当前项切换
  useEffect(() => {
    if (currentActiveKey?.clickSubCatalog) {
      onItemChange?.(currentActiveKey);
    }
  }, [currentActiveKey]);

  return (
    <>
      {catalogDataAfterSearch.map((catalogItem) => (
        //   一级区域
        <div className={styles.firstContent} key={catalogItem?.catalog}>
          {/* 一级title */}
          <div
            key={`${catalogItem?.catalog}-header`}
            className={styles.lineContainer}
            onClick={() => handleFirstCollpase(catalogItem.catalog)}
          >
            <span className={classNames(styles.groupTitle, styles.groupFirstTitle)}>{catalogItem.catalog}</span>
            <div className={styles.groupOtherInfo}>
              <span className={styles.amount}>{catalogItem.serviceAmount}</span>
              {!collpaseKey?.[catalogItem.catalog] ? (
                <DownOutlined className={styles.collpaseIcon} />
              ) : (
                <UpOutlined className={styles.collpaseIcon} />
              )}
            </div>
          </div>
          {/* 二级区域 */}
          {catalogItem?.groupList?.length ? (
            <div
              key={`${catalogItem?.catalog}-group`}
              className={classNames(styles.secondContent, {
                [styles.hide]: !collpaseKey?.[catalogItem.catalog],
              })}
            >
              {catalogItem.groupList.map((catalogGroupItem) => (
                <React.Fragment key={catalogGroupItem.groupName}>
                  {/* 二级title */}
                  <div
                    className={styles.lineContainer}
                    onClick={() => handleSecondCollpase(catalogItem.catalog, catalogGroupItem.groupName)}
                  >
                    <span className={classNames(styles.groupTitle, styles.groupSecondTitle)}>
                      {catalogGroupItem.groupName}
                    </span>
                    <div className={styles.groupOtherInfo}>
                      <span className={styles.amount}>{catalogGroupItem.serviceAmount}</span>
                      {!collpaseKey?.[catalogItem.catalog]?.[catalogGroupItem.groupName] ? (
                        <DownOutlined className={styles.collpaseIcon} />
                      ) : (
                        <UpOutlined className={styles.collpaseIcon} />
                      )}
                    </div>
                  </div>
                  {catalogGroupItem?.subCatalogList?.length ? (
                    <div
                      className={classNames(styles.thirdContent, {
                        [styles.hide]: !collpaseKey?.[catalogItem.catalog]?.[catalogGroupItem.groupName],
                      })}
                    >
                      {/* 三级title */}
                      {catalogGroupItem.subCatalogList.map((subCatalogItem) => (
                        <div
                          key={subCatalogItem.subCatalog}
                          onClick={() =>
                            setCurrentActiveKey({
                              clickCatalog: catalogItem,
                              clickGroup: catalogGroupItem,
                              clickSubCatalog: subCatalogItem,
                            })
                          }
                          className={classNames(styles.lineContainer, styles.thirdLineContainer, {
                            [styles.activeLineContainer]:
                              subCatalogItem.subCatalog === currentActiveKey?.clickSubCatalog?.subCatalog &&
                              subCatalogItem?.currentPathList?.join('-') ===
                                currentActiveKey?.clickSubCatalog?.currentPathList?.join('-'),
                          })}
                        >
                          <span className={classNames(styles.groupTitle, styles.groupThirdTitle)}>
                            {subCatalogItem.subCatalog}
                          </span>
                          <span className={classNames(styles.amount, styles.thirdAmount)}>
                            {subCatalogItem.serviceAmount}
                          </span>
                        </div>
                      ))}
                    </div>
                  ) : null}
                </React.Fragment>
              ))}
            </div>
          ) : null}
        </div>
      ))}
    </>
  );
};

export default CustomerCollpaseForCatalog;
