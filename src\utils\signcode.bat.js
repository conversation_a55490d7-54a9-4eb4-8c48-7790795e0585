/* eslint-disable no-lonely-if,no-console,no-param-reassign,func-names,no-void,no-undef */
/**
 * signcode原始代码
 */

// eslint-disable-next-line import/no-amd, no-unused-vars
define(['frm/v81e/js/fish-bss-ext', 'frm/fish-desktop/third-party/crypto/sha256'], bssext => {
  let origURL;
  const getUuid = len => {
    let res = '';
    const str = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    const strLen = str.length;
    for (let i = 0; i < len; i += 1) {
      res += str.charAt(Math.floor(Math.random() * strLen));
    }
    return res;
  };

  const getSigUrl = function (configUrl) {
    let sendUrl = configUrl;
    // 前面是否带参
    const pIndex = origURL.indexOf('?');
    // url是否带参
    const sIndex = configUrl.indexOf('?');
    // 前面不带参 后面带参
    if (pIndex === -1 && sIndex > -1) {
      sendUrl = origURL + decodeURIComponent(configUrl.substring(sIndex));
    } else if (pIndex > -1 && sIndex > -1) { // 前面带参 后面带参
      sendUrl = origURL + decodeURIComponent(configUrl.substring(origURL.length));
    }
    return sendUrl.replace(/\+/g, ' ');
  };

  // 生成数字签名对象
  const createSignature = function (config, token) {
    const signatureEnable = sessionStorage.getItem('signature_enable');
    if (signatureEnable) {
      let dataStr = '';
      const sendUrl = getSigUrl(config.url);
      const path = `/${sendUrl.replace('/portal/api', '')}`;

      if (config.data && config.contentType === 'application/json') {
        dataStr = config.data;
      }

      const salt = sessionStorage.getItem('salt');
      const timeDiff = sessionStorage.getItem('server_time_diff') || '0';

      const appTimeStamp = new Date().valueOf() + parseInt(timeDiff, 10);
      const appNonce = getUuid(12);
      const signStr = [appTimeStamp, appNonce, path, dataStr, token, salt].filter(item => !!item).join('');
      const appSignature = fish.SHA256(signStr).toString();

      return {
        'app-timestamp': appTimeStamp,
        'app-nonce': appNonce,
        'app-signature': appSignature,
      };
    }
    return {};
  };

  const callService = function (type, url, data, success, webroot, errorFunc, isAsync) {
    let option = {};
    let showMask = '';
    if (fish.isObject(url)) {
      // 处理第一个参数是option的场景
      option = url;
      url = option?.url;
      data = option?.data;
      success = option?.success;
      webroot = option?.webroot;
      errorFunc = option?.errorFunc;
      isAsync = option?.isAsync;
      showMask = option?.showMask;
    } else if (fish.isFunction(data)) {
      // 第一个参数是url,第二个参数是回调函数,没有参数
      isAsync = errorFunc;
      errorFunc = webroot;
      webroot = success;
      success = data;
      data = null;
    }

    const newUrl = (webroot || '') + (fish.restPrefix ? `${fish.restPrefix}/` : '') + url;
    origURL = newUrl;
    if (data) {
      if (type !== 'GET' && type !== 'POSTBASIC') {
        if (fish.isEmpty(data)) {
          if (fish.isArray(data)) {
            data = '[]';
          } else {
            data = '{}';
          }
        } else {
          data = JSON.stringify(data);
        }
      }
    } else {
      data = null;
    }
    // var disableKeyboard = false
    // if (window?.top?.portal?.appGlobal?.get('projectCode') && window?.top?.portal?.appGlobal?.get('projectCode')?.toUpperCase() === 'POST'){
    //     disableKeyboard = true
    // }

    const ajaxOption = {
      type,
      url: newUrl,
      data,
      success(re) {
        success && success(re);
      },
      // disableKeyboard: disableKeyboard,
      async: isAsync !== false,
      showError: false,
      showMask: showMask !== false,
      beforeSend(xhr, config) {
        const token = window?.top?.portal?.appGlobal?.get('_csrf') || '';
        const csrfHeader = window?.top?.portal?.appGlobal?.get('_csrf_header') || '';
        const signHeader = createSignature(config, token);
        const signHeaderKeys = Object.keys(signHeader);
        if (signHeaderKeys.length) {
          signHeaderKeys.forEach(curSignHeaderKey => {
            const curSignHeaderVal = signHeader[curSignHeaderKey];
            xhr.setRequestHeader(curSignHeaderKey, curSignHeaderVal);
          });
        }
        if (csrfHeader) {
          xhr.setRequestHeader(csrfHeader, token);
        }
      },
      error(xhr, status, error) {
        // 这里只是一个方法没有完整的生命周期引入i18n也获取不了，暂时从appGlobal中获取
        error = !error ? window?.top?.portal?.appGlobal?.get('commoni18n')[window?.top?.portal?.appGlobal?.get('language')].COMMON_AJAX_DEFAULT_ERROR_MSG : error;
        const traceId = xhr ? xhr.getResponseHeader('ITRACING_TRACE_ID') : null;
        // #1586 url中包含xss攻击得情况
        url = fish.escape(newUrl);
        let errorObj = {};
        if (xhr && xhr.responseText) {
          try {
            errorObj = JSON.parse(xhr.responseText);
          } catch (e) {
            // 调用者指定了异常处理函数
            if (errorFunc && fish.isFunction(errorFunc)) {
              errorFunc(xhr, status, error);
              return;
            }
            if (traceId) {
              errorObj = {};
              errorObj.title = 'Error';
              errorObj.traceId = traceId;
              errorObj.message = xhr.responseText;
              fish.bsserror(errorObj, null, args => {
                console.log(args);
              });
            } else {
              fish.error(xhr.responseText); // responseText: "This session has been expired (possibly due to multiple concurrent logins being attempted as the same user)."
            }
            // eslint-disable-next-line consistent-return
            return false;
          }
          if (errorObj.type !== void 0 && errorObj.type !== 0) {
            // 锁屏报错不处理
            if (errorObj.code === '46420001') {
              return;
            }
            if (errorObj.code === 'S-SYS-00027') {
              // Session过期
              if (window?.top?.portal?.appGlobal?.get('currentStatus') !== 'sessionTimeOut') {
                window?.top?.portal?.appGlobal?.set('currentStatus', 'sessionTimeOut');
                return;
              }
            } else if (errorObj.code === 'S-SYS-00029') {
              // 账号被挤下线
              if (window?.top?.portal?.appGlobal?.get('currentStatus') !== 'beenSqueezedOffline') {
                window?.top?.portal?.appGlobal?.set('currentStatus', 'beenSqueezedOffline');
                return;
              }
            }
          }
        }

        // 调用者指定了异常处理函数
        if (errorFunc && fish.isFunction(errorFunc)) {
          errorFunc(xhr, status, error);
          return;
        }
        if (xhr && xhr.responseText) {
          if (errorObj) {
            if (errorObj.type === void 0) {
              // 请求异常
              console.error(xhr.responseText);
              let msg;
              if (JSON.parse(xhr.responseText) && JSON.parse(xhr.responseText).message) {
                msg = JSON.parse(xhr.responseText).message;
              } else {
                msg = error;
              }
              if (traceId || errorObj.traceId || errorObj.transactionId) {
                if (!Object.prototype.toString.call(errorObj).toLowerCase() === '[object object]') {
                  errorObj = {};
                }
                errorObj.title = `Error${errorObj.code ? `: Code[${errorObj.code}]` : ''}`;
                errorObj.traceId = errorObj.traceId || traceId || errorObj.transactionId;
                errorObj.message = errorObj.message || msg;
                fish.bsserror(errorObj, null, args => {
                  window.console.log(args);
                });
              } else {
                fish.error({
                  title: `Ajax ${status}`,
                  message: msg,
                });
              }
            } else if (errorObj.type === 0) {
              // 业务异常
              if (traceId || errorObj.traceId || errorObj.transactionId) {
                if (!Object.prototype.toString.call(errorObj).toLowerCase() === '[object object]') {
                  errorObj = {};
                }
                errorObj.title = `Warning${errorObj.code ? `: Code[${errorObj.code}]` : ''}`;
                errorObj.traceId = errorObj.traceId || traceId || errorObj.transactionId;
                // eslint-disable-next-line prefer-arrow-callback
                fish.bsswarn(errorObj, null, function (args) {
                  window.console.log(args);
                });
              } else {
                fish.warn(`${errorObj.code} : ${errorObj.message}`);
              }
            } else {
              // 系统异常
              if (errorObj.code !== 'S-SYS-00027' && errorObj.code !== 'S-SYS-00029') {
                if (traceId || errorObj.traceId || errorObj.transactionId) {
                  if (!Object.prototype.toString.call(errorObj).toLowerCase() === '[object object]') {
                    errorObj = {};
                  }
                  errorObj.title = `Error${errorObj.code ? `: Code[${errorObj.code}]` : ''}`;
                  errorObj.traceId = errorObj.traceId || traceId || errorObj.transactionId;
                  fish.bsserror(errorObj, null, args => {
                    window.console.log(args);
                  });
                } else {
                  fish.error(errorObj.message);
                }
              }
            }
          } else if (traceId) {
            errorObj = {};
            errorObj.title = `Ajax ${status}`;
            errorObj.traceId = traceId;
            errorObj.message = error;
            fish.bsserror(errorObj, null, args => {
              console.log(args);
            });
          } else {
            fish.error({
              title: `Ajax ${status}`,
              message: error,
            });
          }
        } else {
          // 请求异常
          if (traceId) {
            errorObj = {};
            errorObj.title = `Ajax ${status}`;
            errorObj.traceId = traceId;
            errorObj.message = error;
            fish.bsserror(errorObj, null, args => {
              // eslint-disable-next-line no-console
              console.log(args);
            });
          } else if (xhr.readyState === 0) {
            // 断网
            // eslint-disable-next-line no-restricted-globals
            location.reload();
          } else {
            fish.error({
              // eslint-disable-next-line prefer-template
              title: 'Ajax ' + status,
              message: error,
            });
          }
        }
      },
      // #72 先暂停跨域请求，IE8，IE9有问题
      xhrFields: {
        withCredentials: true,
      },
      cache: false,
    };
    $.extend(true, ajaxOption, fish.omit(option, 'url', 'data'));

    if (type !== 'GET') {
      if (type === 'POSTBASIC') {
        ajaxOption.type = 'POST';
      } else {
        ajaxOption.contentType = 'application/json';
        ajaxOption.processData = false;
      }
    }

    return fish.ajax(ajaxOption);
  };

  /**
   * GET /collection：返回资源对象的列表（数组）
   * GET /collection/{id}：返回单个资源对象
   * POST /collection：返回新生成的资源对象
   * PUT /collection： 修改完整的资源对象
   * PATCH /collection/{id}：修改资源对象的部分属性
   * DELETE /collection/{id}：删除资源对象
   * */

  fish.get = function (url, data, success, webroot, errorFunc) {
    return callService('GET', url, data, success, webroot, errorFunc);
  };

  fish.post = function (url, data, success, webroot, errorFunc) {
    return callService('POST', url, data, success, webroot, errorFunc);
  };

  fish.put = function (url, data, success, webroot, errorFunc) {
    return callService('PUT', url, data, success, webroot, errorFunc);
  };

  fish.patch = function (url, data, success, webroot, errorFunc) {
    return callService('PATCH', url, data, success, webroot, errorFunc);
  };

  fish.remove = function (url, data, success, webroot, errorFunc) {
    return callService('DELETE', url, data, success, webroot, errorFunc);
  };

  /**
   * @since V9.0.7.1
   * 提供基础的form表单的提交方式,与原post的区别在于不是以json方式交互
   * contentType类型为默认值application/x-www-form-urlencoded
   * 后台spring mvc的rest接口不能通过@requestBody获取，需要改成@requestParam
   * 这种方式对于单数据，如string类型的参数处理起来比较方便
   */
  fish.postBasic = function (url, data, success, webroot, errorFunc) {
    return callService('POSTBASIC', url, data, success, webroot, errorFunc);
  };

  fish.postsync = function (url, data, success, webroot, errorFunc) {
    return fish.postSync(url, data, success, webroot, errorFunc);
  };

  fish.postSync = function (url, data, success, webroot, errorFunc) {
    return callService('POST', {
      url,
      data,
      success,
      webroot,
      errorFunc,
      async: false,
    });
  };

  fish.postDataType = function (url, data, success, webroot, errorFunc) {
    return callService('POST', {
      url,
      data,
      success,
      webroot,
      errorFunc,
      async: false,
      dataType: 'text',
    });
  };

  fish.callService = function (type, url, data, success, webroot, errorFunc, isAsync) {
    return callService(type, url, data, success, webroot, errorFunc, isAsync);
  };
});
