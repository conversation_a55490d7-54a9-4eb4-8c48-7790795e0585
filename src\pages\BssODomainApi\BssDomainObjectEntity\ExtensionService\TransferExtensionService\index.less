.disabledItem {
  color: rgba(0, 0, 0, 0.25);
  cursor: not-allowed;
  background-color: #f5f5f5;
}

.warningHighlight {
  color: #ff4d4f;
}

.targetServiceNote {
  margin-top: 8px;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.45);
  font-style: italic;
}

/* 自定义穿梭框样式 */
:global {
  .ant-transfer-list-content-item {
    line-height: 12px;
    span {
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
      display: inline-block;
      width: 100%;
    }
  }

  .ant-transfer-list-content-item-text {
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
