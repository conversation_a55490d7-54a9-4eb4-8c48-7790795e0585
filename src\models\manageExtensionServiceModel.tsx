import { useState } from 'react';

const manageExtensionServiceModel = () => {
  const [isManageExtensionService, setIsManageExtensionService] = useState<boolean>(false); // 是否是管理扩展服务
  const [currentDomainObjId, setCurrentDomainObjId] = useState<number>(); // 当前领域对象Id
  const [openSchemaManagementFromReq, setOpenSchemaManagementFromReq] = useState<boolean>(false);
  const [openSchemaManagementFromResp, setOpenSchemaManagementFromResp] = useState<boolean>(false);
  const [isImportBySwaggerSuccess, setIsImportBySwaggerSuccess] = useState<boolean>(false);
  return {
    isManageExtensionService,
    setIsManageExtensionService,
    currentDomainObjId,
    setCurrentDomainObjId,
    openSchemaManagementFromReq,
    setOpenSchemaManagementFromReq,
    openSchemaManagementFromResp,
    setOpenSchemaManagementFromResp,
    isImportBySwaggerSuccess,
    setIsImportBySwaggerSuccess,
  };
};

export default manageExtensionServiceModel;
