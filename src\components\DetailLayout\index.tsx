import React from "react";
import { LeftOutlined } from "@ant-design/icons";
import { history } from "umi";
import styles from "./index.less";

type IDetailLayout = {
  title: string;
  onReturn: () => void;
  children?: React.ReactNode;
  hidden?: boolean;
  style?: React.CSSProperties;
};

const DetailLayout: React.FC<IDetailLayout> = (props) => {
  const { title, onReturn, children, hidden = false, style = {} } = props;

  return (
    <div
      className={styles.detailContainer}
      style={{ ...style }}
      hidden={hidden}
    >
      <div className={styles.header}>
        <LeftOutlined
          className={styles.returnIcon}
          onClick={() => onReturn?.()}
        />
        <span className={styles.returnText} onClick={() => onReturn?.()}>
          {title}
        </span>
      </div>
      <div className={styles.detailContent}>{children}</div>
    </div>
  );
};

export default DetailLayout;
