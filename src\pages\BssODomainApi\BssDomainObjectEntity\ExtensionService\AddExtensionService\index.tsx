import React, { useEffect, useState } from 'react';
import { Button, Drawer, Form, Input, Select, Space, message, AutoComplete, Spin, Divider } from 'antd';
import { addDomainEntityService } from '@/services/entityService';
import { SelectedEntityProps, SelectedRootNode } from '../../types';
import getUUid from '@/utils/getUUid';
import SelectWithAddButton from '@/components/SelectWithAddButton';
import { CommonOptionItemType } from '@/components/SelectWithAddButton/type';
import { useModel } from 'umi';
import useI18n from '@/hooks/useI8n';

interface IAddExtensionServiceDrawer {
  isProduct: boolean;
  selectedEntity: SelectedEntityProps;
  serviceCatgSource: any;
  serviceSubCatgSource: any;
  dubboClassSource: any;
  serviceRootPath: string;
  open: boolean;
  ignoreVersionSource: any;
  selectedRootNode: SelectedRootNode; // 根节点
  tfmServices: CommonOptionItemType[];
  onCancel: () => void;
  onOk: () => void;
}
type VersionItem = {
  comments: string;
  serviceCatg: string;
  verCode: string;
};

const AddExtensionServiceDrawer: React.FC<IAddExtensionServiceDrawer> = (props) => {
  const {
    open,
    ignoreVersionSource,
    selectedEntity,
    selectedRootNode,
    isProduct,
    serviceCatgSource = [],
    serviceSubCatgSource,
    dubboClassSource,
    serviceRootPath,
    tfmServices = [],
    onCancel,
    onOk,
  } = props;
  const { formatMessage } = useI18n();
  const [form] = Form.useForm();
  const [formValues, setFormValues] = useState<any>({});
  const [required, setRequired] = useState<boolean>(false);
  const [dubboMethodSource, setDubboMethodSource] = useState<any>([]);
  const [catgVerVOList, setCatgVerVOList] = useState<any>([]);
  const [selectedImplType, setSelectedImplType] = useState<string>('');
  const [spining, setSpining] = useState<boolean>(false);
  const [isSimpleParameter, setIsSimpleParameter] = useState<string>(''); //Dubbo Method是否为简单类型
  const { has81E } = useModel('useSystemConfigDataModel');

  const isShowProjectionCallService =
    selectedRootNode?.appCode === 'custc' || // Customer
    selectedRootNode?.appCode === 'coc' || // Order
    (selectedRootNode?.appCode === 'cpc' && has81E) || // Offer
    (selectedRootNode?.appCode === 'sic' && has81E) || // Resource
    selectedRootNode?.appCode === 'test';

  const CommonSource = [
    {
      label: 'No',
      value: 'N',
    },
    {
      label: 'Yes',
      value: 'Y',
    },
  ];

  const CREATIONSOURCE = [
    { name: 'Product-Owned', key: 'P' },
    { name: 'Project-Customized', key: 'C' },
  ];

  const HttpMethodSource = [
    // PUT、POST、PATCH、DELETE
    { label: 'GET', value: 'GET' },
    { label: 'PUT', value: 'PUT' },
    { label: 'POST', value: 'POST' },
    { label: 'PATCH', value: 'PATCH' },
    { label: 'DELETE', value: 'DELETE' },
  ];
  const ImplementTypeSource = [
    { label: 'Orchestration', value: 'N' },
    { label: 'Projection Dubbo API', value: 'D' },
    { label: 'Projection Restful API', value: 'R' },
    ...(isShowProjectionCallService ? [{ label: 'Projection Call Service', value: 'S' }] : []),
  ];

  const initValues = {
    creationSrc: isProduct ? 'P' : 'C',
    entityName: selectedEntity?.entityName,
    rootPath: serviceRootPath,
  };

  // modal close
  const onClose = async () => {
    form.resetFields();
    setSelectedImplType('');
    onCancel?.();
  };

  const addExtensionService = async (param: any) => {
    setSpining(true);
    try {
      // 调用新增实体扩展服务接口
      const resultData = await addDomainEntityService({ entityId: selectedEntity.entityId }, param);

      if (resultData?.success) {
        message.success(formatMessage('EXTENSIONSERVICE.ADD.SUCCESS'));
        form.resetFields();
        onOk?.();
      } else {
        message.error(formatMessage('EXTENSIONSERVICE.ADD.FAIL'));
      }
    } catch (error) {
      console.error('An error occurred:', error);
      message.error(formatMessage('EXTENSIONSERVICE.ADD.ERROR'));
    } finally {
      setSpining(false);
    }
  };

  const checkExtServiceData = () => {
    form.validateFields().then((values) => {
      // 构造入参
      const param = {
        ...values,
        matchVer: values?.matchVer?.join(','),
        entityId: selectedEntity.entityId,
      };
      const filterResult = serviceSubCatgSource.filter((item: any) => item.serviceSubCatgName === param.serviceSubCatg);
      if (filterResult.length > 0) {
        param.serviceSubCatg = filterResult[0].serviceSubCatg;
      } else {
        param.serviceSubCatgName = param.serviceSubCatg;
        delete param.serviceSubCatg;
      }
      if (param.implType === 'D') {
        const dubboMethodList = param.dubboMethod.split('-');
        const simpleParam = dubboMethodList[2] === 'false' ? 'N' : 'Y';
        param.odhServiceProjDubboVo = {
          dubboClass: param.dubboClass,
          dubboMethod: dubboMethodList[0],
          simpleParam,
          methodParamClass: dubboMethodList[3],
        };
      }
      if (param.implType === 'R') {
        param.odhServiceProjRestVo = {
          servicePath: param.restfulApiPath,
          serviceMethod: param.restfulApiHttpMethod,
        };
      }
      if (param.implType === 'S') {
        param.odhServiceProjCallservVo = {
          serviceName: param.tfmServiceName,
        };
      }

      addExtensionService(param);
    });
  };

  const onImplTypeChange = (value: string) => {
    setIsSimpleParameter('');
    setSelectedImplType(value);
    if (value === 'D') {
      setRequired(true);
    } else {
      setRequired(false);
    }
    form.setFieldsValue({
      dubboClass: '',
      dubboMethod: '',
      restfulApiHttpMethod: '',
      restfulApiPath: '',
      tfmServiceName: '',
    });
  };

  const onDubboClassChange = (value: any) => {
    form.setFieldsValue({
      dubboMethod: '',
    });
    const methodData = dubboClassSource.filter((i: any) => i.interfaceClass === value);
    if (methodData?.length > 0) {
      const newSource = methodData[0]?.methods?.map((item: any) => {
        const paramTypes = item?.parameterTypes?.map((param: any) => {
          const parts = param?.split('.');
          const lastPart = parts[parts.length - 1];
          return lastPart;
        });
        return {
          ...item,
          paramString: `(${paramTypes?.join(',')})`,
        };
      });
      setDubboMethodSource(newSource || []);
    }
  };

  // 获取Service Catalog对应的版本列表
  const createVersionList = (serviceCatalog: any, serviceCatalogSource: any) => {
    const list = serviceCatalogSource
      .filter((i: any) => i.serviceCatg === serviceCatalog)[0]
      ?.catgVerVOList.map((item: VersionItem) => item?.verCode) // 提取版本号
      .filter(Boolean) // 过滤掉 undefined
      .map((verCode: string) => ({
        label: verCode,
        value: verCode,
      }))
      .sort((a: any, b: any) => {
        const versionA = a.label.replace('v', '');
        const versionB = b.label.replace('v', '');
        return versionA - versionB; // 按照数字顺序排序
      });

    return list;
  };

  useEffect(() => {
    setCatgVerVOList([]);
    setIsSimpleParameter('');
  }, [open]);

  const handleSelectServiceCatg = (value: any) => {
    // 拼接后缀
    const suffix = serviceCatgSource.filter((i: any) => i.serviceCatg === value)[0]?.nameSpace || '';
    if (suffix) {
      form.setFieldValue('rootPath', `${initValues.rootPath}/${suffix}`);
    } else {
      form.setFieldValue('rootPath', initValues.rootPath);
    }

    const versionList = createVersionList(value, serviceCatgSource);

    if (!versionList.length) {
      form.setFieldValue('serviceVer', '');
    } else {
      form.setFieldValue('serviceVer', versionList[0].value);
    }
    setCatgVerVOList(versionList);
  };

  const onServicePathChange = (e: any) => {
    const { value } = e.target;
    // 去除所有空格
    const newValue = value.replace(/\s/g, '');
    // 直接修改目标的值
    form.setFieldValue('servicePath', newValue);
  };

  const onRestfulApiPathathChange = (e: any) => {
    const { value } = e.target;
    // 去除所有空格
    const newValue = value.replace(/\s/g, '');
    // 直接修改目标的值
    form.setFieldValue('restfulApiPath', newValue);
  };

  const onDubboMethodChange = (value: any) => {
    const isSimpleParam = value?.split('-')[2];
    setIsSimpleParameter(isSimpleParam);
  };

  useEffect(() => {
    // Dubbo 方法如果为复杂类型，则不能选择GET方法
    if (isSimpleParameter === 'false' && formValues?.httpMethod === 'GET' && selectedImplType === 'D') {
      form.setFieldValue('httpMethod', '');
      message.warning(formatMessage('EXTENSIONSERVICE.ADD.DUBBO_METHOD_ERROR'));
    }

    // 配置rest服务时，odh服务的httpMethod只能配置成和实际对端的httpMethod一样或者是POST
    if (formValues?.restfulApiHttpMethod && formValues?.httpMethod) {
      const isHttpMethodValidWithRestful = [formValues?.restfulApiHttpMethod, 'POST'].includes(formValues?.httpMethod);
      if (!isHttpMethodValidWithRestful) {
        form.setFieldValue('httpMethod', '');
        message.warning(formatMessage('EXTENSIONSERVICE.ADD.RESTFUL_METHOD_ERROR'));
      }
    }
  }, [isSimpleParameter, formValues]);

  return (
    <Drawer
      title={formatMessage('EXTENSIONSERVICE.ADD.TITLE')}
      onClose={onClose}
      maskClosable={false}
      open={open}
      width={960}
      afterOpenChange={() => {
        form.resetFields();
      }}
      footer={
        <Space style={{ float: 'right' }}>
          <Spin spinning={spining}>
            <Button type="primary" onClick={checkExtServiceData}>
              {formatMessage('EXTENSIONSERVICE.ADD.SUBMIT')}
            </Button>
          </Spin>
          <Button onClick={onClose}>{formatMessage('EXTENSIONSERVICE.ADD.CANCEL')}</Button>
        </Space>
      }
    >
      <Form
        form={form}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
        initialValues={initValues}
        onValuesChange={(cValues, alValues) => {
          setFormValues(alValues);
        }}
      >
        <Form.Item
          label={formatMessage('EXTENSIONSERVICE.ADD.SERVICE_NAME')}
          name="serviceName"
          rules={[{ required: true, message: '' }]}
        >
          <Input allowClear />
        </Form.Item>
        <Form.Item label={formatMessage('EXTENSIONSERVICE.ADD.ENTITY')} name="entityName">
          <Input disabled />
        </Form.Item>
        <Form.Item label={formatMessage('EXTENSIONSERVICE.ADD.CREATION_SOURCE')} name="creationSrc">
          <Select disabled>
            {CREATIONSOURCE.map((i) => (
              <Select.Option key={i.key} value={i.key}>
                {i.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          label={formatMessage('EXTENSIONSERVICE.ADD.DEPRECATED')}
          name="deprecated"
          rules={[{ required: true, message: '' }]}
          initialValue="N"
        >
          <Select options={CommonSource} />
        </Form.Item>
        {isProduct ? (
          <Form.Item
            label={formatMessage('EXTENSIONSERVICE.ADD.MATCH_VERSION')}
            name="matchVer"
            rules={[{ required: true, message: '' }]}
          >
            <Select mode="multiple">
              {/* 需求#11408424，仅将扩展服务的ignoreVer字段改为matchVer字段，source还是取ignoreVersionSource */}
              {ignoreVersionSource?.map((i: any) => (
                <Select.Option key={i.key} value={i.key}>
                  {i.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        ) : null}

        <Form.Item
          label={formatMessage('EXTENSIONSERVICE.ADD.SERVICE_CATALOG')}
          style={{ marginBottom: 0 }}
          rules={[{ required: true, message: '' }]}
        >
          <Form.Item
            name="serviceCatg"
            style={{ display: 'inline-block', width: 'calc(33%)' }}
            rules={[{ required: true, message: '' }]}
          >
            <Select onSelect={handleSelectServiceCatg}>
              {/* 如果当前是产品环境，服务大类（Service Catalog）只能选择TMF Service和System Service，如果当前是项目环境，服务大类只能选择Extended Service */}
              {serviceCatgSource
                ?.filter((item: any) => (isProduct ? item?.serviceCatg !== 'E' : item?.serviceCatg === 'E'))
                .map((i: any) => (
                  <Select.Option key={i.serviceCatg} value={i.serviceCatg}>
                    {i.serviceCatgName}
                  </Select.Option>
                ))}
            </Select>
          </Form.Item>
          <Form.Item
            name="serviceSubCatg"
            style={{ display: 'inline-block', width: 'calc(66% - 5px)', marginLeft: '5px' }}
            rules={[{ required: true, message: '' }]}
          >
            <SelectWithAddButton
              originalOptionsList={serviceSubCatgSource}
              parentForm={form}
              formFieldName="serviceSubCatg"
            />
          </Form.Item>
        </Form.Item>

        <Form.Item
          label={formatMessage('EXTENSIONSERVICE.ADD.SERVICE_PATH')}
          style={{ marginBottom: 0 }}
          rules={[{ required: true, message: '' }]}
        >
          <Form.Item
            name="httpMethod"
            style={{ display: 'inline-block', width: 'calc(20%)' }}
            rules={[{ required: true, message: '' }]}
          >
            <Select options={HttpMethodSource} />
          </Form.Item>
          <Form.Item name="rootPath" style={{ display: 'inline-block', width: 'calc(35%)' }}>
            <Input disabled />
          </Form.Item>
          <Form.Item name="serviceVer" style={{ display: 'inline-block', width: 'calc(13%)' }}>
            <Select disabled={!catgVerVOList.length} options={catgVerVOList} showSearch />
          </Form.Item>
          <Form.Item name="servicePath" style={{ display: 'inline-block', width: 'calc(31%)' }}>
            <Input allowClear onChange={onServicePathChange} />
          </Form.Item>
        </Form.Item>

        <Form.Item
          label={formatMessage('EXTENSIONSERVICE.ADD.IMPLEMENT_TYPE')}
          name="implType"
          rules={[{ required: true, message: '' }]}
        >
          <Select options={ImplementTypeSource} onChange={onImplTypeChange} />
        </Form.Item>
        {selectedImplType === 'D' && (
          <>
            <Form.Item
              label={formatMessage('EXTENSIONSERVICE.ADD.DUBBO_CLASS_PATH')}
              name="dubboClass"
              rules={[{ required, message: '' }]}
            >
              <Select onChange={onDubboClassChange} showSearch disabled={!required}>
                {dubboClassSource.map((i: any) => (
                  <Select.Option key={i.interfaceClass} value={i.interfaceClass}>
                    {i.interfaceClass}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item
              label={formatMessage('EXTENSIONSERVICE.ADD.DUBBO_METHOD')}
              name="dubboMethod"
              rules={[{ required, message: '' }]}
            >
              <Select showSearch disabled={!required} onChange={onDubboMethodChange}>
                {dubboMethodSource.map((i: any) => (
                  <Select.Option
                    key={`${i.methodName}${i?.paramString}`}
                    value={`${i.methodName}-${i?.paramString}-${i?.simpleParameter}-${i?.parameterTypes?.join(',')}`}
                  >
                    {`${i.methodName} ${i?.paramString}`}
                  </Select.Option>
                ))}
              </Select>
            </Form.Item>
          </>
        )}
        {selectedImplType === 'R' && (
          <Form.Item
            label={formatMessage('EXTENSIONSERVICE.ADD.RESTFUL_API_PATH')}
            style={{ marginBottom: 0 }}
            rules={[{ required: true, message: '' }]}
          >
            <Form.Item
              name="restfulApiHttpMethod"
              style={{ display: 'inline-block', width: 'calc(20%)' }}
              rules={[{ required: true, message: '' }]}
            >
              <Select options={HttpMethodSource} />
            </Form.Item>
            <Form.Item
              name="restfulApiPath"
              style={{ display: 'inline-block', width: 'calc(80%)' }}
              rules={[{ required: true, message: '' }]}
            >
              <Input allowClear onChange={onRestfulApiPathathChange} />
            </Form.Item>
          </Form.Item>
        )}
        {selectedImplType === 'S' && (
          <Form.Item
            label={formatMessage('EXTENSIONSERVICE.ADD.TFM_SERVICE_NAME')}
            name="tfmServiceName"
            rules={[{ required: true, message: '' }]}
          >
            <SelectWithAddButton originalOptionsList={tfmServices} parentForm={form} formFieldName="tfmServiceName" />
          </Form.Item>
        )}
        <Form.Item
          label={formatMessage('EXTENSIONSERVICE.ADD.DESCRIPTIONS')}
          name="comments"
          rules={[{ required: true, message: '' }]}
        >
          <Input.TextArea allowClear />
        </Form.Item>
      </Form>
    </Drawer>
  );
};
export default AddExtensionServiceDrawer;
