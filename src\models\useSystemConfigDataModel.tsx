import { getSystemConfigInfo } from '@/services/domainService';
import { useEffect, useState } from 'react';

export default function useSystemConfigDataModel() {
  const [systemConfigData, setSystemConfigData] = useState<SystemConfigDataType>({
    projectCode: '',
    environmentVersion: '',
    product: false,
    configEnv: '',
  }); // 系统配置项
  const [has9E, setHas9E] = useState<boolean>(false); // environmentVersion是否存在9E环境
  const [has81E, setHas81E] = useState<boolean>(false); // environmentVersion是否存在81E环境
  const [isConfigEnvironment, setisConfigEnvironment] = useState<boolean>(false); // 是否是odh配置环境（true:配置环境、false:使用环境）

  // 获取环境基本信息
  const initSystemConfigData = async () => {
    await getSystemConfigInfo().then((res) => {
      const { success, data } = res;
      if (success) {
        setSystemConfigData(data);
      }
    });
  };

  // 判断是否存在指定环境
  const judgeEnvironmentVersion = (environmentVersions: string = '', targetVersion: string) => {
    return (
      typeof environmentVersions === 'string' && environmentVersions?.split(',')?.some((item) => item === targetVersion)
    );
  };

  useEffect(() => {
    const { environmentVersion = '', configEnv } = systemConfigData;
    setHas9E(judgeEnvironmentVersion(environmentVersion, '9E'));
    setHas81E(judgeEnvironmentVersion(environmentVersion, '81E'));
    setisConfigEnvironment(configEnv === 'true');
  }, [systemConfigData]);

  return { systemConfigData, has9E, has81E, isConfigEnvironment, initSystemConfigData };
}
