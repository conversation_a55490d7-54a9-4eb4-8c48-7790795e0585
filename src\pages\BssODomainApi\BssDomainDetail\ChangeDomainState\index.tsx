import React, { useState, useRef, useEffect } from 'react';
import useI18n from '@/hooks/useI8n';
import { Modal, Form, Button, message, Input } from 'antd';
import { qryReleasedInfoByDomainId, releasedVersion } from '@/services/domainService';
import ChangeVersionModal from '../ChangeVersion';
import { isHotfix } from '@/global';

interface IChangeDomainStateModel {
  selectedDomainNode: any;
  selectDomainState: string;
  domainId: number;
  open: boolean;
  onCancel: () => void;
  onOk: () => void;
}

const ChangeDomainStateModel: React.FC<IChangeDomainStateModel> = (props) => {
  const { open, selectDomainState, domainId, onCancel, onOk, selectedDomainNode } = props;

  const { formatMessage } = useI18n();

  const [form] = Form.useForm();
  const [initValues, setInitValues] = useState<any>({});
  const [formValues, setFormValues] = useState<any>({ initValues }); // formValues
  const [confirmLoading, setConfirmLoading] = useState(false);
  const [asyncFlag, setAsyncFlag] = useState<boolean>(false);
  const [openChangeVersionModal, setOpenChangeVersionModal] = useState<boolean>(false);

  // modal close
  const onClose = () => {
    form.resetFields();
    onCancel?.();
  };

  // 点击OK  确认更改state
  const onOkChange = async () => {
    setConfirmLoading(true);
    try {
      const resultData = await releasedVersion({
        domainId: domainId,
        comments: formValues.comments,
        autoSwitch: asyncFlag ? 'Y' : 'N',
      });
      if (resultData?.success) {
        if (asyncFlag) {
          setOpenChangeVersionModal(true);
        } else {
          onOk?.();
        }
      } else {
        message.error(resultData?.errorMessage);
      }
      setConfirmLoading(false);
    } catch (error) {
      setConfirmLoading(false);
    }
  };

  const checkData = () => {
    form.validateFields().then(() => {
      onOkChange();
    });
  };

  useEffect(() => {
    //查询领域发布信息
    if (open) {
      setAsyncFlag(false);
      async function initVersionReleasedData() {
        const { success, data } = await qryReleasedInfoByDomainId({ domainId });

        if (success) {
          form.setFieldsValue({ ...data, comments: '' });
        } else {
          message.error(data?.errorMessage);
        }
      }
      initVersionReleasedData();
    }
  }, [open]);

  return (
    <>
      <Modal
        width={750}
        destroyOnClose
        open={open}
        forceRender={open}
        title={`Released Domain ${isHotfix ? 'Hotfix ' : ''}Version`}
        onCancel={() => onClose()}
        onOk={() => checkData()}
        cancelButtonProps={{
          style: { display: 'none' },
        }}
        keyboard={false}
        maskClosable={false}
        confirmLoading={confirmLoading}
      >
        {/* content */}
        <div>
          <Form
            form={form}
            labelCol={{ span: 6 }}
            wrapperCol={{ span: 18 }}
            initialValues={initValues}
            onValuesChange={(cValues, alValues) => {
              setFormValues(alValues);
            }}
          >
            <Form.Item label="ArtifactId" name="artifactId">
              <Input allowClear disabled={true} style={{ border: 'none' }} />
            </Form.Item>
            <Form.Item label="Version" name="seq">
              <Input allowClear disabled={true} style={{ border: 'none' }} />
            </Form.Item>
            {isHotfix && (
              <Form.Item label="Hotfix" name="hotfixSeq">
                <Input allowClear disabled={true} style={{ border: 'none' }} />
              </Form.Item>
            )}
            <Form.Item label="Notes" name="comments" rules={[{ required: true, message: '' }]}>
              <Input.TextArea allowClear disabled={confirmLoading} />
            </Form.Item>
          </Form>
          <div style={{ marginLeft: '10rem' }}>
            <input
              type="checkbox"
              checked={asyncFlag}
              onChange={() => setAsyncFlag((prevAsyncFlag) => !prevAsyncFlag)}
            />
            <span style={{ marginLeft: '5px' }}>
              Do you want to switch version after released domain version successful ?
            </span>
          </div>
        </div>
      </Modal>
      {/* 查询当前操作阶段*/}
      <ChangeVersionModal
        selectedDomainNode={selectedDomainNode}
        startStep={1}
        open={openChangeVersionModal}
        onCancel={() => setOpenChangeVersionModal(false)}
        onOk={() => {
          setOpenChangeVersionModal(false);
          //成功切换版本后，重新查询版本信息，渲染表格
          onOk?.();
        }}
      />
    </>
  );
};

export default ChangeDomainStateModel;
