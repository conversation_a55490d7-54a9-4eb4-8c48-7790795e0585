import React, { useState, useRef, useEffect } from 'react';
import useI18n from '@/hooks/useI8n';
import { Modal, Form, Button, message, Select } from 'antd';
import { downloadJarFile, downloadZipFile } from '@/services/domainService';

interface IDownloadVersionModel {
  selectedVersionObj: {
    releasedId: number;
    srcFileIsExist?: boolean;
    [key: string]: any;
  };
  open: boolean;
  onCancel: () => void;
  onOk: () => void;
}

const DownloadVersionModel: React.FC<IDownloadVersionModel> = (props) => {
  const { open, selectedVersionObj, onCancel, onOk } = props;

  const { formatMessage } = useI18n();

  const [form] = Form.useForm();
  const [formValues, setFormValues] = useState<any>({}); // formValues

  const DOCUMENTTYPE = [
    {
      label: 'Domain Configuration File(.jar)',
      value: 'jar',
    },
    {
      label: 'Domain Configuration Source(.zip)',
      value: 'zip',
    },
  ];

  // 根据 srcFileIsExist 过滤可用的下载选项
  const availableDocumentTypes = DOCUMENTTYPE.filter(
    (item) => item.value !== 'zip' || selectedVersionObj?.srcFileIsExist,
  );

  // modal close
  const onClose = async () => {
    onCancel?.();
  };

  // 点击OK  确认更改state
  const handleChangeState = async () => {
    const params = {
      releasedId: selectedVersionObj.releasedId,
    };

    if (formValues.fileType === 'jar') {
      const response: any = await downloadJarFile(params);
      // 提取文件名并解码成中文
      const contentDisposition = response.headers['content-disposition'];
      let fileName = ''; // 默认文件名
      if (contentDisposition) {
        const regex = /filename=([^;]+)/;
        const match = regex.exec(contentDisposition);
        if (match) {
          fileName = match[1].replace(/(^")|("$)/g, '');
        }
      }
      // a标签
      const link = document.createElement('a');
      // blob
      const objectUrl = window.URL.createObjectURL(new Blob([response?.data]));
      link.style.display = 'none';
      link.href = objectUrl;
      // fileName
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      // // 适当释放url
      window.URL.revokeObjectURL(objectUrl);
      link.remove();
      onOk?.();
    } else if (formValues.fileType === 'zip') {
      const response: any = await downloadZipFile(params);
      // 提取文件名并解码成中文
      const contentDisposition = response.headers['content-disposition'];
      let fileName = ''; // 默认文件名
      if (contentDisposition) {
        const regex = /filename=([^;]+)/;
        const match = regex.exec(contentDisposition);
        if (match) {
          fileName = match[1].replace(/(^")|("$)/g, '');
        }
      }
      // a标签
      const link = document.createElement('a');
      // blob
      const objectUrl = window.URL.createObjectURL(new Blob([response?.data]));
      link.style.display = 'none';
      link.href = objectUrl;
      // fileName
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      // // 适当释放url
      window.URL.revokeObjectURL(objectUrl);
      link.remove();
      onOk?.();
    }
  };

  const initValues = {
    fileType: 'jar',
  };

  useEffect(() => {
    setFormValues(initValues);

    // 当打开模态窗且源文件不可用时，确保表单值为jar
    if (open && selectedVersionObj?.srcFileIsExist === false) {
      form.setFieldValue('fileType', 'jar');
    }
  }, [open, selectedVersionObj]);

  // footer
  const loadCurrentFooter = () => {
    return (
      <Button type="primary" onClick={handleChangeState}>
        Download
      </Button>
    );
  };

  return (
    <Modal
      width={600}
      destroyOnClose
      open={open}
      forceRender={open}
      title="Download Domain File"
      onCancel={() => onClose()}
      onOk={() => handleChangeState()}
      footer={loadCurrentFooter}
      keyboard={false}
      maskClosable={false}
    >
      {/* content */}
      <div>
        <Form
          form={form}
          labelCol={{ span: 6 }}
          wrapperCol={{ span: 18 }}
          initialValues={initValues}
          onValuesChange={(cValues, alValues) => {
            setFormValues(alValues);
          }}
        >
          <Form.Item label="File Type" name="fileType">
            <Select>
              {availableDocumentTypes.map((i) => (
                <Select.Option key={i.value} value={i.value}>
                  {i.label}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};

export default DownloadVersionModel;
