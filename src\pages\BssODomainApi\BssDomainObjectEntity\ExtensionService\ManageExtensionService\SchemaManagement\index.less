.container {
  display: flex;
  gap: 1rem;
  height: 100%;
  overflow-y: auto;  /* 垂直方向出现滚动条 */
  align-items: stretch; /* 使子项高度匹配容器高度 */
}
.contLeft {
  flex: 1;
  overflow: auto; /* 可选：在子元素上也可以设置滚动 */
}
.contDivider {
  margin-left: -16px;
  width: 5px;
  background-color: rgb(246, 244, 244);
  flex-shrink: 0; /* 确保 Divider 不会缩小 */
}
.contRight {
  flex: 3;
  overflow: auto; /* 可选：在子元素上也可以设置滚动 */
}
.iconStyle {
  margin-left: 20px;
  color: #47e;
  cursor: pointer;
}
.textStyle {
  color: #47e;
  cursor: pointer;
}
.textDisableStyle {
  color: rgb(116, 118, 120);
  cursor: pointer;
}
.hide {
  display: none;
}
.deleteIcon {
  opacity: 0;
  margin-left: 5px;
  color: #47e;
  cursor: pointer;
}
.treeNode :hover .deleteIcon {
  opacity: 100%;
}
.hiddenProperty {
  text-decoration-line:line-through
}
.siteTreeSearchValue {
  background-color: yellow; /* 使用黄色背景来高亮显示 */
  font-weight: bold;        /* 粗体显示 */
  color: black;             /* 字体颜色为黑色 */
  padding: 2px;             /* 添加一些内边距以更好地突出显示 */
  border-radius: 3px;       /* 圆角边框 */
}
