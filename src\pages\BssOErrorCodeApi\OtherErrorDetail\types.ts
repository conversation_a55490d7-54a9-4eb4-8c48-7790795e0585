

export interface OtherErrorListItemType {
  beginDate: any,
  chainKey: any,
  dubboClass: string,
  dubboMethod: string,
  endDate: any,
  errLogId: number,
  errLogIdStr: string;
  errResp: string,
  happenedDate: string,
  httpMethod: string,
  implType: string,
  partId: number,
  restServiceMethod: any,
  restServicePath: any,
  serviceName: string,
  servicePath: string,
  tfmServiceName: any,
  traceId: string,
}

export interface OtherErrorListDataType {
  list: OtherErrorListItemType[];
  pageNo: number;
  pageSize: number;
  totalCount: number;
  totalPage: number;
}

export interface newErrorCodeType {
  domainId?: number;
  errorCode: string;
  errorReason: string;
  defaultMatchConditions?: string;
  defaultReasonParams?: string[];
}

export interface GetOtherErrorListType {
  traceId?: string;
  serviceName?: string;
  servicePath?: string;
  beginDate?: string;
  endDate?: string;
  errResp?: string;
  pageNo: number;
  pageSize: number;
}
