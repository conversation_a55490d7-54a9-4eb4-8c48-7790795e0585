.entityDesContent {
  margin-top: 12px;
  .entityDesList {
    .entityDesItem {
      display: flex;
      align-items: stretch;
      font-family: Nunito Sans;
      font-weight: 400;
      color: #2d3040;
      .title {
        font-family: Nunito Sans-Bold;
        width: 150px;
        background-color: #f5f6f7;
        font-weight: bold;
        padding: 6px 12px;
        display: flex;
        align-items: center;
        line-height: 22px;
      }
      .content {
        flex: 1;
        font-size: 14px;
        padding: 6px 12px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 22px;
      }
      .linkContent {
        overflow: unset;
        text-overflow: unset;
        white-space: unset;
        display: unset;
      }
      .singleItemEntity {
        color: #4477ee;
        cursor: pointer;
      }
      .singleItemEntity:not(:first-child) {
        margin-left: 12px;
      }
    }
    .entityDesItem:nth-child(1),
    .entityDesItem:nth-child(2),
    .entityDesItem:nth-child(3),
    .entityDesItem:nth-child(4) {
      .title {
        border-left: 1px solid #f0f0f0;
        border-top: 1px solid #f0f0f0;
        border-right: 1px solid #f0f0f0;
      }
      .content {
        border-top: 1px solid #f0f0f0;
        border-right: 1px solid #f0f0f0;
      }
    }
    .entityDesItem:nth-child(4) {
      .title,
      .content {
        border-bottom: 1px solid #f0f0f0;
      }
    }
  }
}
