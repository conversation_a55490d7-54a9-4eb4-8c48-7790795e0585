export default {
  'PROJECT.COMMON.CONFIRM': 'Confirm',
  'PROJECT.COMMON.SEARCH': 'Search',
  'PROJECT.COMMON.RESET': 'Reset',
  'PROJECT.COMMON.DESC': 'Description',
  'PROJECT.COMMON.NAME': 'Name',
  'PROJECT.COMMON.TYPE': 'Type',
  'PROJECT.COMMON.EXAMPLE': 'Example',
  'PROJECT.COMMON.CATALOG': 'Catalog',
  'PROJECT.COMMON.REQUIRED': 'Required',
  'PROJECT.COMMON.HIDDEN': 'Hidden',
  'PROJECT.COMMON.REQUIREDANDHIDDENBOTHYESNOTE': "When 'Required' is 'Yes', the 'Hidden' can not be 'Yes'.",
  'PROJECT.COMMON.REFRESH': 'Refresh',
  'PROJECT.COMMON.DOWNLOAD': 'Download',
  'PROJECT.COMMON.AICCPROMPT': 'AICC Prompt',
  'PROJECT.COMMON.ERRORCODE': 'Error Code',
  'PROJECT.COMMON.APIGSERVICE': 'APIG Service',
  'PROJECT.COMMON.SEARCHUSECASE': 'Search Use Case',
  'PROJECT.COMMON.NEXT': 'Next',
  'PROJECT.COMMON.PREVIOUS': 'Previous',
  'PROJECT.COMMON.GENERATE': 'Generate',
  'PROJECT.COMMON.SERVICENAME': 'Service Name',
  'PROJECT.COMMON.SERVICECATALOG': 'Service Catalog',
  'PROJECT.COMMON.DOMAINENTITY': 'Domain Entity',
  'PROJECT.COMMON.SERVICEPATH': 'Service Path',
  'PROJECT.COMMON.VALUE': 'Value',
  'PROJECT.COMMON.CREATIONSOURCE': 'Creation Source',
  'PROJECT.COMMON.DESCRIPTION': 'Description',
  'PROJECT.COMMON.BELONGDOMAINOBJECT': 'Belong Domain Object',
  'PROJECT.COMMON.NODATA': 'No Data',
  'PROJECT.COMMON.EDIT': 'Edit',
  'PROJECT.COMMON.DELETE': 'Delete',
  'PROJECT.COMMON.COPY': 'Copy',
  'PROJECT.COMMON.ADDSUCCESS': 'Added successfully!',
  'PROJECT.COMMON.EDITSUCCESS': 'Edited successfully!',
  'PROJECT.COMMON.DELETESUCCESS': 'Deleted successfully!',
  'PROJECT.COMMON.REFRESHSUCCESS': 'Refresh successfully!',
  'PROJECT.COMMON.CHANGEVERSION': 'Change Version',
  'PROJECT.COMMON.PAGINATION.TOTAL': 'Total {total} items',
  'PROJECT.COMMON.GETDESCBYPROPNAME':
    'Notes： The descriptions can be automatically generated based on the name, please fill in according to your actual situation.',
  'PROJECT.COMMON.SUBMIT': 'Submit',
  'PROJECT.COMMON.CANCEL': 'Cancel',
  'PROJECT.COMMON.OPERATION': 'Operation',
  'PROJECT.COMMON.DOMAIN': 'Domain',
  'MENU.SEARCH.PLACEHOLDER': 'Input keyword',
  'MENU.TITLE.TITLE': 'Catalogue',
  'MENU.SELECT.CATALOG': 'By Catalog',
  'MENU.SELECT.DOMAIN': 'By Domain',
  'DOMAINDETAIL.RELA.TITLE': 'Entity Relationship',
  'DOMAINDETAIL.RELA.LEGEND': 'Legend：',
  'DOMAINDETAIL.DESC.TITLE': 'Entity Description',
  'DOMAINDETAIL.DESC.ENTITYNAME': 'Entity Name',
  'DOMAINDETAIL.DESC.PARENTENTITY': 'Parent Entity',
  'DOMAINDETAIL.DESC.CHILDENTITY': 'Subordinate Entity',
  'DOMAINDETAIL.PROPERTIES.TITLE': 'Entity Properties',
  'DOMAINDETAIL.PROPERTIES.FEATURES': 'Support Features',
  'DOMAINDETAIL.PROPERTIES.PRIMARY': 'Primary Key',
  'DOMAINDETAIL.PROPERTIES.EXACT': 'Exact Search',
  'DOMAINDETAIL.PROPERTIES.FUZZY': 'Fuzzy Search',
  'DOMAINDETAIL.PROPERTIES.INDEX': 'Index',
  'DOMAINDETAIL.PROPERTIES.CANFILTER': 'Can Filter',
  'DOMAINDETAIL.PROPERTIES.MANDATORY': 'Mandatory',
  'DOMAINDETAIL.PROPERTIES.CANORDER': 'Can Order',
  'DOMAINDETAIL.PROPERTIES.ENUM': 'Enum',
  'DOMAINDETAIL.SERVICES.TITLE': 'Entity Services',
  'DOMAINDETAIL.SERVICES.SHOWDEPRECATED': 'Show Deprecated Service',
  'DOMAINDETAIL.SERVICES.EVENTS': 'Entity Events',
  'DOMAINDETAIL.EVENT_RULE_ACTION': 'Event Rule Action',
  'SERVICEDETAIL.TITLE.TITLE': 'Service Detail',
  'SERVICEDETAIL.REQUEST.TITLE': 'Request Information',
  'SERVICEDETAIL.REQUEST.URL': 'URL',
  'SERVICEDETAIL.REQUEST.TITLEEXAMPLE': 'Request Example',
  'SERVICEDETAIL.REQUEST.AVLVALUE': 'Allowable Values',
  'SERVICEDETAIL.RESPONSE.TITLE': 'Response Information',
  'SERVICEDETAIL.RESPONSE.TITLEEXAMPLE': 'Response Example',
  'SERVICEDETAIL.RESPONSE.TITLEBODY': 'Response Body',
  'SERVICEDETAIL.RESPONSE.TITLEHEADER': 'Response Header',
  'SERVICEDETAIL.REQUEST.EXAMPLENOTE':
    'Note: The mock request parameters generated by Mockjs are simulated data and may not fully align with the actual interface specifications. Please verify compatibility before use.',
  'SERVICEDETAIL.RESPONSE.EXAMPLENOTE':
    'Note: The mock response parameters generated by Mockjs are simulated data and may not fully align with the actual interface specifications. Please verify compatibility before use.',
  'SERVICEDETAIL.RESPONSE.TITLDOMAIN': 'Domain Entity',
  'SERVICEDETAIL.HOVERWRAPPER.DEBUGSERVICE': 'Debug Service',
  'SERVICEDETAIL.HOVERWRAPPER.DOWNLOADDOC': 'Download Document',
  'DEBUGSERVICE.URL.SEND': 'Send',
  'DEBUGSERVICE.URL.NOTE':
    'Notes: The example address is the request address after the service is released, not the address sent here.',
  'DEBUGSERVICE.URL.SAVE': 'Save as...',
  'DEBUGSERVICE.URL.USECASE': 'Use Case',
  'DEBUGSERVICE.URL.APIGSERVICE': 'APIG Service',
  'DEBUGSERVICE.URL.SELECTUSECASE': 'Select Use Case',
  'DEBUGSERVICE.APIGSERVICEDRAWER.TITLE': 'Save As APIG Service',
  'DEBUGSERVICE.APIGSERVICEDRAWER.EDITTITLE': 'Edit APIG Service',
  'DEBUGSERVICE.APIGSERVICEDRAWER.APICATALOG': 'API Catalog',
  'DEBUGSERVICE.APIGSERVICEDRAWER.APIMETHOD': 'API Method',
  'DEBUGSERVICE.APIGSERVICEDRAWER.APINAME': 'API Name',
  'DEBUGSERVICE.APIGSERVICEDRAWER.APIVERSION': 'API Version',
  'DEBUGSERVICE.APIGSERVICEDRAWER.CURRENTACCESSRELATIVEURL': 'Current Access Relative URL',
  'DEBUGSERVICE.APIGSERVICEDRAWER.ACCESSRELATIVEURL': 'Access Relative URL',
  'DEBUGSERVICE.APIGSERVICEDRAWER.OVERWRITEAPIGDEVCONVERSION': 'Overwrite APIG Development Conversion',
  'DEBUGSERVICE.APIGSERVICEDRAWER.ODATAPARAM': 'OData Parameters',
  'DEBUGSERVICE.APIGSERVICEDRAWER.APIGPARAM': 'APIG Parameters',
  'DEBUGSERVICE.APIGSERVICEDRAWER.COMMENTS': 'Comments',
  'DEBUGSERVICE.APIGSERVICEDRAWER.AIAUTOTESTPROMPT': 'AI Auto Test Prompt',
  'DEBUGSERVICE.APIGSERVICEDRAWER.NOTEMPTYTIP': 'The Name and Value can not be empty.',
  'DEBUGSERVICE.APIGSERVICEDRAWER.GENERATEAPIGPARAM.SUCCESS': 'Succeed in generating APIG parameters.',
  'DEBUGSERVICE.APIGSERVICEDRAWER.GENERATEAPIGPARAM.NODATA': 'No data is available.',
  'DEBUGSERVICE.APIGSERVICEDRAWER.APIGPARAMDRAWER.TITLE': 'Edit API Service Parameter',
  'DEBUGSERVICE.APIGSERVICEDRAWER.EDIT': 'Edit Service',
  'DEBUGSERVICE.APIGSERVICEDRAWER.REFRESHSERVICE': 'Refresh Service',
  'DEBUGSERVICE.APIGSERVICEDRAWER.CANNOTOVERWRITEPRODUCT': 'Cannot overwrite the API of the product.',
  'DEBUGSERVICE.APIGSERVICEDRAWER.WILLOVERWRITE': 'You will overwrite this service on APIG.',
  'DEBUGSERVICE.APIGSERVICEDRAWER.WILLADDNEW': 'You will add a new service on APIG.',
  'DEBUGSERVICE.APIGSERVICEDRAWER.PLEASESELECTAPICATALOG': 'Please select API Catalog',
  'DEBUGSERVICE.APIGSERVICEDRAWER.PLEASESELECTAPIMETHOD': 'Please select API Method',
  'DEBUGSERVICE.APIGSERVICEDRAWER.ONLYLETTERS':
    'Only letters, numbers, underscores (_), dots (.), and hyphens (-) are allowed.',
  'DEBUGSERVICE.APIGSERVICEDRAWER.CANNOTENDWITH': 'The input value cannot end with {suffix}',
  'DEBUGSERVICE.APIGSERVICEDRAWER.SELECTCATALOGFIRST': 'Please select API Catalog first.',
  'DEBUGSERVICE.APIGSERVICEDRAWER.PLEASEINPUTAPINAME': 'Please input API Name',
  'DEBUGSERVICE.APIGSERVICEDRAWER.PLEASESELECTAPIVERSION': 'Please select API Version',
  'DEBUGSERVICE.APIGSERVICEDRAWER.SELECTMETHODFIRST': 'Please select API Method first.',
  'DEBUGSERVICE.USECASEMODAL.EDIT': 'Edit Use Case',
  'DEBUGSERVICE.USECASEMODAL.TITLE': 'Save As Use Case',
  'DEBUGSERVICE.USECASEMODAL.EDITTITLE': 'Edit Use Case',
  'DEBUGSERVICE.USECASEMODAL.NAME': 'Use Case Name',
  'DEBUGSERVICE.USECASEMODAL.NAMETIP': 'Please input use case name',
  'DEBUGSERVICE.USECASEMODAL.SAVERESPONSEMSG': 'Save Response Message',
  'DEBUGSERVICE.USECASEMODAL.COMMENTS': 'Comments',
  'DEBUGSERVICE.USECASEBESELECTEDMODAL.QUERY': 'Query',
  'DEBUGSERVICE.USECASEBESELECTEDMODAL.SELECTUSECASE': 'Select Use Case',
  'DEBUGSERVICE.USECASEBESELECTEDMODAL.USECASENAME': 'Use Case Name',
  'DEBUGSERVICE.USECASEBESELECTEDMODAL.INCLUDERESPONSE': 'Include Response',
  'DEBUGSERVICE.USECASEBESELECTEDMODAL.COMMENTS': 'Comments',
  'DEBUGSERVICE.USECASEBESELECTEDMODAL.OPERATION': 'Operation',
  'DEBUGSERVICE.USECASEBESELECTEDMODAL.CANCEL': 'Cancel',
  'DEBUGSERVICE.USECASEBESELECTEDMODAL.OK': 'OK',
  'DEBUGSERVICE.USECASEBESELECTEDMODAL.CONFIRMTIP': 'Are you sure to delete the use case?',
  'DEBUGSERVICE.REQUEST.EMPTY': 'No request parameters',
  'DEBUGSERVICE.RESPONSESTATUSTABLE.TITLE': 'Status',
  'DEBUGSERVICE.RESPONSESTATUSTABLE.CALLRESULT': 'Call Result',
  'DEBUGSERVICE.RESPONSESTATUSTABLE.STATUSCODE': 'Status Code',
  'DEBUGSERVICE.RESPONSESTATUSTABLE.RESPTIME': 'Response Time',
  'DEBUGSERVICE.RESPONSESTATUSTABLE.STATUSDESC': 'Status Description',
  'SERVICEUSECASELIST.TITLE.SERVICEUSECASELIST': 'Service Use Case List',
  'SERVICEUSECASELIST.TITLE.APIGSERVICEUSECASELIST': 'APIG Service Use Case List',
  'SERVICEUSECASELIST.FORM.CREATEDATE': 'Create Date',
  'SERVICEUSECASELIST.FORM.PLACEHOLDER': 'Input keywords',
  'DOWNLOAD.MODAL.SELECTENTITY': 'Select Entity',
  'DOWNLOAD.MODAL.SELECTCATALOG': 'Select Catalog',
  'DOWNLOAD.MODAL.TITLEDOMAIN': ' Domain Entity',
  'DOWNLOAD.MODAL.TITLECATALOG': ' Catalog Name',
  'DOWNLOAD.MODAL.SELECTFILE': 'Select File Type',
  'DOWNLOAD.MODAL.DOWNLOADFILE': 'Download File',
  'DOWNLOAD.FORM.FILETYPE': 'Document Type',
  'DOWNLOAD.FORM.VERSION': 'Swagger Version',
  'DOWNLOAD.FORM.EXAMPLE': 'Generate With Message Example',
  'DOWNLOAD.GENERATE.SUCCESSFULTEXT': 'Document generated successfully',
  'DOWNLOAD.GENERATE.SUCCESSFULTIPS': 'Tips: Please click "Download" button to get file.',
  'DOWNLOAD.TITLE.CONFIRM': 'Are you sure to refresh the cache?',
  'DOWNLOAD.TITLE.SUPPORTED': 'Supported Filter Types',
  'DOWNLOAD.TITLE.SEEMORE': 'See more',
  'DOWNLOAD.TITLE.COMPARISON': 'Comparison Operators',
  'DOWNLOAD.TITLE.LOGICAL': 'Logical Operators',
  'DOWNLOAD.TITLE.STRINGFUNCTIONS': 'String Functions',
  'DATAMODELLIST.TABLE.TITLE': 'Data Model List',
  'DATAMODELLIST.TABLE.ADD': 'Add',
  'DATAMODELLIST.TABLE.NAME': 'Name',
  'DATAMODELLIST.TABLE.CODE': 'Code',
  'DATAMODELLIST.TABLE.TYPE': 'Type',
  'DATAMODELLIST.TABLE.CREATIONSOURCE': 'Creation Source',
  'DATAMODELLIST.TABLE.DESC': 'Description',
  'DATAMODELLIST.TABLE.OPT': 'Operation',
  'DATAMODELLIST.TABLE.DELTIP': 'Are you sure to delete the data model?',
  'DATAMODELLIST.TABLE.IGNOREVER': 'Ignore Version',
  'DATAMODELLIST.DRAWER.ADDTITLE': 'Add Data Model',
  'DATAMODELLIST.DRAWER.EDITTITLE': 'Modify Data Model',
  'DATAMODELLIST.DRAWER.CLASSIFICATION': 'Classifiction',
  'DATAMODELLIST.DRAWER.TABLE': 'Table',
  'DATAMODELLIST.DRAWER.VIEWSQL': 'View SQL',
  'DATAMODELLIST.DRAWER.SUBMIT': 'Submit',
  'DATAMODELLIST.DRAWER.CANCEL': 'Cancel',
  'DATAMODELLISTDETAIL.INFO.TITLE': 'Basic Information',
  'DATAMODELLISTDETAIL.INFO.OPT': 'Edit',
  'DATAMODELLISTDETAIL.VIEWSQL.TITLE': 'View SQL',
  'DATAMODELLISTDETAIL.VIEWSQL.PROVIEW': 'Product View',
  'DATAMODELLISTDETAIL.VIEWSQL.QUERYSQL': 'Query SQL',
  'DATAMODELLISTDETAIL.ENTITIES.TITLE': 'Entities Using This Data Model',
  'DATAMODELLISTDETAIL.ENTITIES.NAME': 'Name',
  'DATAMODELLISTDETAIL.ENTITIES.CODE': 'Code',
  'DATAMODELLISTDETAIL.ENTITIES.CREATIONSOURCE': 'Creation Source',
  'DATAMODELLISTDETAIL.ENTITIES.ENTITYTYPE': 'Entity Type',
  'DATAMODELLISTDETAIL.ENTITIES.DESC': 'Description',
  'DATAMODELLISTDETAIL.ENUMS.TITLE': 'Enums Using This Data Model',
  'DATAMODELLISTDETAIL.ENUMS.NAME': 'Name',
  'DATAMODELLISTDETAIL.ENUMS.CODE': 'Code',
  'DATAMODELLISTDETAIL.ENUMS.CREATIONSOURCE': 'Creation Source',
  'DATAMODELLISTDETAIL.ENUMS.ENTITYTYPE': 'Entity Type',
  'DATAMODELLISTDETAIL.ENUMS.DESC': 'Description',
  'DATAMODELLISTDETAIL.ADD.SUCCESS': 'Succeed in adding the data model.',
  'DATAMODELLISTDETAIL.EDIT.SUCCESS': 'Succeed in modifying the data model.',
  'DATAMODELLISTDETAIL.DELETE.SUCCESS': 'Succeed in deleting the data model.',

  'ENUMMANAGEMENTLIST.TABLE.TITLE': 'Enum List',
  'ENUMMANAGEMENTLIST.TABLE.ADD': 'Add',
  'ENUMMANAGEMENTLIST.TABLE.NAME': 'Name',
  'ENUMMANAGEMENTLIST.TABLE.TYPE': 'Type',
  'ENUMMANAGEMENTLIST.TABLE.CREATIONSOURCE': 'Creation Source',
  'ENUMMANAGEMENTLIST.TABLE.DESC': 'Description',
  'ENUMMANAGEMENTLIST.TABLE.OPT': 'Operation',
  'ENUMMANAGEMENTLIST.TABLE.VALUE': 'Value',
  'ENUMMANAGEMENTLIST.TABLE.DELTIP': 'Are you sure to delete the enum?',
  'ENUMMANAGEMENTLIST.DRAWER.ADDTITLE': 'Add Enum',
  'ENUMMANAGEMENTLIST.DRAWER.EDITTITLE': 'Modify Enum',
  'ENUMMANAGEMENTLIST.DRAWER.CONSTANTLIST': 'Constant List',
  'ENUMMANAGEMENTLIST.DRAWER.DATAMODEL': 'Data Model',
  'ENUMMANAGEMENTLIST.DRAWER.VALUECOLUMN': 'Value Column',
  'ENUMMANAGEMENTLIST.DRAWER.NAMECOLUMN': 'Name Column',
  'ENUMMANAGEMENTLIST.ENUMS.TITLE': 'Entity Properties Using This Enum',
  'ENUMMANAGEMENTLIST.ENUMS.ENTITYNAME': 'Entity Name',
  'ENUMMANAGEMENTLIST.ENUMS.PROPERTYNAME': 'Property Name',
  'ENUMMANAGEMENTLIST.ENUMS.PROPERTYTYPE': 'Property Type',
  'ENUMMANAGEMENTLIST.ENUMS.CREATIONSOURCE': 'Creation Source',
  'ENUMMANAGEMENTLIST.ENUMS.DESC': 'Description',
  'ENUMMANAGEMENTLIST.ADD.SUCCESS': 'Succeed in adding the enum.',
  'ENUMMANAGEMENTLIST.EDIT.SUCCESS': 'Succeed in modifying the enum.',
  'ENUMMANAGEMENTLIST.DELETE.SUCCESS': 'Succeed in deleting the enum.',

  'DOMAINDETAIL.VERSION.TEST.TITLE': 'Testing version',
  'DOMAINDETAIL.VERSION.USED.TITLE': 'Running version',
  'DOMAINDETAIL.VERSION.PENDING.TITLE': 'Pending version',
  'DOMAINDETAIL.VERSION.ISHOTFIX': ' is hotfix: ',

  'ERRORCODE.FILTERCONDITIONS.TITLE': 'Filter Conditions',
  'ERRORCODE.COMMON.APPLYDOMAIN': 'Apply Domain',
  'ERRORCODE.COMMON.APPLYDOMAIN.NOTE': `The product environment can only be selected as "All", while the project environment cannot be selected as "All".`,
  'ERRORCODE.COMMON.ERRORCODE': 'Error Code',
  'ERRORCODE.COMMON.ERRORREASON': 'Error Reason',
  'ERRORCODE.COMMON.REASONPARAMETER.NOTE': 'Notes: In Error Reason, variables can be defined using {0}, {1}...',
  'ERRORCODE.COMMON.CREATIONSOURCE': 'Creation Source',
  'ERRORCODE.COMMON.RELATEDSERVICE': 'Related Service',
  'ERRORCODE.ERRORCODELIST.TITLE': 'Error Code List',
  'ERRORCODE.ADD': 'Add',
  'ERRORCODE.ADD.TITLE': 'Add Error Code',
  'ERRORCODE.EDIT.TITLE': 'Edit Error Code',
  'ERRORCODE.RELATED.TITLE': 'Related Service List',
  'ERRORCODE.RELATED.SERVICENAME': 'Service Name',
  'ERRORCODE.RELATED.HTTPSTATUS': 'HTTP Status',
  'ERRORCODE.RELATED.DOMAINENTITY': 'Domain Entity',
  'ERRORCODE.DEFAULT.MATCHCONDITIONS': 'Default Match Conditions',
  'ERRORCODE.DEFAULT.MATCHCONDITIONS.NO_VALUE': 'You cannot use the default match conditions which have no value.',
  'ERRORCODE.DEFAULT.REASONPARAMETER': 'Default Reason Parameter',
  'ERRORCODE.EXPREASON.MATCHRESPONSESTATUS': 'Match Response Status',
  'ERRORCODE.EXPREASON.USEDEFALUTMATCHCONDITIONS': 'Use Default Match Conditions',
  'ERRORCODE.EXPREASON.MATCHCONDITIONS': 'Match Conditions',
  'ERRORCODE.EXPREASON.MATCHCONDITIONS.REQUIRED': 'Please input match conditions.',
  'ERRORCODE.EXPREASON.MATCHPRIORITY': 'Match Priority',
  'ERRORCODE.EXPREASON.REASONPARAMETER': 'Reason Parameter',
  'ERRORCODE.EXPREASON.NO_CONFIG_NEEDED': 'No configuration required, the system has default matching logic.',
  'ERRORCODE.EXPREASON.PRIORITY_LIMIT': 'Just allow input 1~99.',
  'ERRORCODE.EXPREASON.PRIORITY_NOTE': 'Notes: The smaller the input value, the higher the priority.',
  'ERRORCODE.OTHERERRORDETAIL.TITLE': 'View Other Error Detail',
  'ERRORCODE.OTHERERRORDETAIL.LIST.TITLE': 'Other Error List',
  'ERRORCODE.OTHERERRORDETAIL.TRACEID': 'Trace ID',
  'ERRORCODE.OTHERERRORDETAIL.ERRORRESPONSE': 'Error Response',
  'ERRORCODE.OTHERERRORDETAIL.SERVICENAME': 'Service Name',
  'ERRORCODE.OTHERERRORDETAIL.SERVICEPATH': 'Service Path',
  'ERRORCODE.OTHERERRORDETAIL.RELEASEDVERSION': 'Released Version',
  'ERRORCODE.OTHERERRORDETAIL.HAPPENEDDATE': 'Happened Date',
  'ERRORCODE.OTHERERRORDETAIL.OTHERERRORLIST': 'Other Error List',
  'ERRORCODE.OTHERERRORDETAIL.RECORD': 'Record Other Error Infomation',
  'ERRORCODE.OTHERERRORDETAIL.RECORD.EXPIRED_TIME': ' ( Notes: The switch will be turned off at {expiredTime} )',
  'ERRORCODE.OTHERERRORDETAIL.ERRORRESPONSEINFOMATION': 'Error Response Infomation',
  'COPYSCHEMA.TITLE': 'Copy Schema',
  'COPYSCHEMA.NAME.VALID': 'The name cannot contain Spaces.',
  'COPYSCHEMA.NAME.INVALID_FORMAT':
    'SchemaName must start with an uppercase letter and contain only letters, numbers, and underscores.',

  // Filter component internationalization
  'FILTER.COLUMN.OPERATOR': 'Operator',
  'FILTER.COLUMN.DESCRIPTION': 'Description',
  'FILTER.COLUMN.FUNCTION': 'Function',

  // Comparison operators
  'FILTER.COMPARISON.EQUAL': 'Equal',
  'FILTER.COMPARISON.NOT_EQUAL': 'Not equal',
  'FILTER.COMPARISON.GREATER_THAN': 'Greater than',
  'FILTER.COMPARISON.GREATER_EQUAL': 'Greater than or equal',
  'FILTER.COMPARISON.LESS_THAN': 'Less than',
  'FILTER.COMPARISON.LESS_EQUAL': 'Less than or equal',
  'FILTER.COMPARISON.IS_MEMBER': 'Is a member of',

  // Logical operators
  'FILTER.LOGICAL.AND': 'Logical and',
  'FILTER.LOGICAL.OR': 'Logical or',
  'FILTER.LOGICAL.NOT': 'Logical negation',

  // String functions
  'FILTER.STRING.CONCAT': 'The concatenation function of two strings.',
  'FILTER.STRING.CONTAINS': 'The string contains another string.',
  'FILTER.STRING.STARTSWITH': 'The string starts with another string.',
  'FILTER.STRING.ENDSWITH': 'The string ends with another string.',
  'FILTER.STRING.INDEXOF': 'Return the position of the string.',
  'FILTER.STRING.LENGTH': 'Return the length of the string.',
  'FILTER.STRING.SUBSTRING': 'Truncate the string and return a new substring.',
  'FILTER.STRING.TOLOWER': 'Convert the string to lowercase.',
  'FILTER.STRING.TOUPPER': 'Convert the string to uppercase.',
  'FILTER.STRING.TRIM': 'Remove spaces before and after the string.',

  // Service Orchestrate component
  'SERVICE.ORCHESTRATE.INVALID_SOURCE': 'Invalid message source:',
  'SERVICE.ORCHESTRATE.RECEIVED_MESSAGE': 'Received return message:',
  'SERVICE.ORCHESTRATE.UNKNOWN_TYPE': 'Unknown message type:',

  // Script Management component
  'SCRIPT.SAVE.SUCCESS': 'Save successfully!',
  'SCRIPT.SAVE.FAILURE': 'Save failure!',
  'SCRIPT.EXECUTE_AFTER_RESPONSE_STATUS': 'Execute After Response Status',
  'SCRIPT.CODE_SNIPPET': 'Code Snippet',
  'PROJECT.COMMON.SAVE': 'Save',
  'SCRIPT.DESCRIPTION': 'You can input the script for QLExpress, and it runs {timing} call the implement service.',
  'SCRIPT.BEFORE': 'before',
  'SCRIPT.AFTER': 'after',
  'SCRIPT.SNIPPETS_TITLE': 'Snippets',

  // Import File component
  'IMPORT.FILE.SELECT_WARNING': 'Please select a file to upload.',
  'IMPORT.FILE.SUCCESS': 'Import Success!',
  'IMPORT.FILE.FAILURE': 'Import failed.',
  'IMPORT.FILE.ERROR': 'An error occurred during import.',
  'IMPORT.FILE.TITLE': 'Import Domain {type} File(.jar)',
  'IMPORT.FILE.HOTFIX': 'Hotfix',
  'IMPORT.FILE.CONFIGURATION': 'Configuration',
  'IMPORT.FILE.DRAG_TEXT': 'Click or drag domain {type} file(.jar) to this area to upload',
  'IMPORT.FILE.HOTFIX_WARNING': 'When importing domain hotfix file, existing hotfix data will be cleared.',
  'IMPORT.FILE.SWITCH_VERSION':
    'After file import successfully, release domain version and switch domain test version to newly released version.',

  // Homepage and docs page
  'HOMEPAGE.WELCOME': 'Yay! Welcome to umi!',
  'HOMEPAGE.START_GUIDE': 'To get started, edit pages/index.tsx and save to reload.',
  'DOCS.INTRO': 'This is umi docs.',

  // UnusablePage component
  'UNUSABLE.TIP': 'Tip',
  'UNUSABLE.ENVIRONMENT_NOT_CONFIG':
    'The current environment is not a configuration environment, and the hotfix functions cannot be used!',

  // RestartOdhServiceModal component
  'RESTART.TITLE': 'Restart Odh Service',
  'RESTART.FAILED': 'Restart failed.',
  'RESTART.ERROR': 'An error occurred during restart.',
  'RESTART.CONFIRM': 'Are you sure you want to restart the odh service?',

  // Domain object detail
  'DOMAIN.BASIC_INFO': 'Basic Information',
  'DOMAIN.ENTITY_LIST': 'Entity List',
  'DOMAIN.ENTITY.DELETE_SUCCESS': 'Succeed in deleting domain entity!',
  'PROJECT.COMMON.ADD': 'Add',
  'PROJECT.COMMON.CODE': 'Code',

  // BssDomainObjectDetail component additional translations
  'DOMAIN.ENTITY.DELETE_CONFIRM': 'Delete Entity',
  'DOMAIN.ENTITY.DELETE_DESCRIPTION': 'Are you sure to delete the entity?',
  'DOMAIN.ENTITY.TYPE': 'Entity Type',
  'DOMAIN.ENTITY.RELATIONSHIP': 'Entity Relationship',
  'DOMAIN.TYPE.AGGREGATE': 'Aggregate',
  'DOMAIN.TYPE.COLLECTION': 'Collection',
  'DOMAIN.ENTITY.TYPE.SERVICEHOLDER': 'ServiceHolder',
  'DOMAIN.ENTITY.TYPE.AGGREGATEROOT': 'AggregateRoot',
  'DOMAIN.ENTITY.TYPE.ENTITY': 'Entity',
  'DOMAIN.STATE.DRAFT': 'Draft',
  'DOMAIN.STATE.RELEASED': 'Released',
  'DOMAIN.CREATION.PRODUCT': 'Product-Owned',
  'DOMAIN.CREATION.PROJECT': 'Project-Customized',
  'DOMAIN.OPERATION': 'Operation',

  // AddDomainEntity component translations
  'DOMAIN.ENTITY.ADD.TITLE': 'Add Domain Entity',
  'DOMAIN.ENTITY.FORM.NAME': 'Name',
  'DOMAIN.ENTITY.FORM.CODE': 'Code',
  'DOMAIN.ENTITY.FORM.CODE.VALIDATOR':
    'The Code format is camel hump format, which can only contain English letters and numbers, starting with uppercase letters',
  'DOMAIN.ENTITY.FORM.CREATION_SOURCE': 'Creation Source',
  'DOMAIN.ENTITY.FORM.IGNORE_VERSION': 'Ignore Version',
  'DOMAIN.ENTITY.FORM.ENTITY_TYPE': 'Entity Type',
  'DOMAIN.ENTITY.FORM.DEFAULT_PAGE_SIZE': 'Default Page Size',
  'DOMAIN.ENTITY.FORM.DATA_MODEL': 'Data Model',
  'DOMAIN.ENTITY.FORM.ROOT_ENTITY': 'Root Entity',
  'DOMAIN.ENTITY.FORM.DESCRIPTION': 'Description',
  'DOMAIN.ENTITY.ADD.SUCCESS': 'Succeed in adding domain entity!',

  // EditDomainEntity component translations
  'DOMAIN.ENTITY.EDIT.TITLE': 'Edit Domain Entity',
  'DOMAIN.ENTITY.EDIT.SUCCESS': 'Succeed in editing domain entity!',

  // EditDomainObject component translations
  'DOMAIN.OBJECT.EDIT.TITLE': 'Edit Domain Object',
  'DOMAIN.OBJECT.EDIT.SUCCESS': 'Succeed in editing domain object!',
  'DOMAIN.OBJECT.FORM.NAME': 'Name',
  'DOMAIN.OBJECT.FORM.NAME.REQUIRED': 'Please input your name!',
  'DOMAIN.OBJECT.FORM.CODE': 'Code',
  'DOMAIN.OBJECT.FORM.CODE.REQUIRED': 'Please input your code!',
  'DOMAIN.OBJECT.FORM.CODE.VALIDATOR': 'Please input correct code!',
  'DOMAIN.OBJECT.FORM.TYPE': 'Type',

  // AddDomainObject component translations
  'DOMAIN.OBJECT.ADD.TITLE': 'Add Domain Object',
  'DOMAIN.OBJECT.ADD.SUCCESS': 'Succeed in adding domain object!',
  'DOMAIN.OBJECT.FORM.CODE.VALIDATOR_LOWERCASE':
    'The Code format is camel hump format, which can only contain English letters and numbers, starting with lowercase letters.',

  // EntityProp组件国际化
  'ENTITYPROP.TITLE': 'Entity Properties',
  'ENTITYPROP.ADD.TITLE': 'Add Entity Properties',
  'ENTITYPROP.EDIT.TITLE': 'Edit Entity Properties',
  'ENTITYPROP.COLUMN_NAME': 'Column Name',
  'ENTITYPROP.COLUMN_NAME.FORMAT': 'Only uppercase letters, numbers and underscores (_) are allowed.',
  'ENTITYPROP.NAME.NOSPACES': "The entity property name can't contain spaces",
  'ENTITYPROP.ALLOWABLE_VALUES': 'Allowable Values',
  'ENTITYPROP.MIN': 'Min',
  'ENTITYPROP.MAX': 'Max',
  'ENTITYPROP.PRECISION': 'Precision',
  'ENTITYPROP.SCALE': 'Scale',
  'ENTITYPROP.DEFAULT_VALUE': 'Default Value',
  'ENTITYPROP.INDEX_FUNCTION': 'Index Function',
  'ENTITYPROP.SENSITIVE_DATA_LEVEL': 'Sensitive Data Level',
  'ENTITYPROP.IGNORE_VERSION': 'Ignore Version',

  // ErrorModal component
  'ERROR.TITLE': 'Error',
  'ERROR.YOU_CAN': 'You can',
  'ERROR.COPY_ERROR_LOG': 'copy error log',
  'ERROR.CONTACT_ADMIN': 'and contact administrator if needed',
  'ERROR.COPY_SUCCESS': 'Copy Success!',
  'ERROR.TRACE_ID': 'Traced ID',
  'ERROR.STACK': 'Stack',
  'ERROR.MORE': 'more',

  // APIGParamsTable component
  'APIG.PARAM.NAME': 'Name',
  'APIG.PARAM.TYPE': 'Type',
  'APIG.PARAM.REQUIRED': 'Required',
  'APIG.PARAM.YES': 'Yes',
  'APIG.PARAM.NO': 'No',
  'APIG.PARAM.ALLOWABLE_VALUES': 'Allowable Values',
  'APIG.PARAM.FORMAT': 'Format',
  'APIG.PARAM.DESCRIPTIONS': 'Descriptions',

  // EditParamDrawer component
  'APIG.DRAWER.EMPTY': 'Empty',
  'APIG.DRAWER.LENGTH': 'Length',
  'APIG.DRAWER.FORMAT': 'Format',
  'APIG.DRAWER.VALUES': 'Values',
  'APIG.DRAWER.RANGE': 'Range',
  'APIG.DRAWER.IS_ARRAY': 'is Array',
  'APIG.DRAWER.MIN': 'Min',
  'APIG.DRAWER.MAX': 'Max',
  'APIG.DRAWER.EXAMPLE': 'Example',

  // RequestTable component
  'REQUEST.HEADER': 'Header',
  'REQUEST.PATH': 'Path',
  'REQUEST.QUERY': 'Query',
  'REQUEST.BODY': 'Body',
  'REQUEST.CONTENT_TYPE': 'Content Type',

  // ResponseTable component
  'RESPONSE.SUCCESS': 'Success',
  'RESPONSE.FAILED': 'Failed',

  // EnumManagementDrawer component
  'ENUM.FORM.PLEASE_ENTER_VALUE': 'Please enter value',
  'ENUM.FORM.PLEASE_ENTER_NAME': 'Please enter name',
  'ENUM.FORM.DOMAIN_MODEL_NOTE': 'Notes: Only data model of type "Domain Model" can be selected.',

  // DebugService component
  'DEBUGSERVICE.ERROR.CANNOT_BE_NULL': 'cannot be null',
  'DEBUGSERVICE.USECASE.ADD_SUCCESS': 'Succeed in adding Use Case!',
  'DEBUGSERVICE.USECASE.EDIT_SUCCESS': 'Succeed in modifying Use Case!',
  'DEBUGSERVICE.APIGSERVICE.ADD_SUCCESS': 'Succeed in adding APIG Service!',
  'DEBUGSERVICE.APIGSERVICE.EDIT_SUCCESS': 'Succeed in modifying APIG Service!',

  // ErrorCode component
  'ERRORCODE.EXPORT.TOOLTIP': 'Based on Filter Conditions',
  'ERRORCODE.EXPORT.BUTTON': 'Export(.xls)',
  'ERRORCODE.COMMON.ALL': 'All',
  'ERRORCODE.LIST.CREATED_DATE': 'Created Date',
  'ERRORCODE.LIST.UPDATE_DATE': 'Update Date',
  'ERRORCODE.DELETE.TITLE': 'Delete Error Code',
  'ERRORCODE.DELETE.CONFIRM': 'Are you sure to delete this record?',
  'ERRORCODE.DELETE.YES': 'Yes',
  'ERRORCODE.DELETE.NO': 'No',

  // BssDomainDetail component internationalization
  'DOMAIN.OBJECT.LIST': 'Domain Object List',
  'DOMAIN.RELEASED.VERSIONS': 'Released Versions',
  'DOMAIN.VERSION': 'Version',
  'DOMAIN.RELEASED.DATE': 'Released Date',
  'DOMAIN.OPERATOR': 'Operator',
  'DOMAIN.RELEASED.NOTES': 'Released Notes',
  'DOMAIN.DELETE.OBJECT.TITLE': 'Delete Domain Object',
  'DOMAIN.DELETE.OBJECT.DESCRIPTION': 'Are you sure to delete the domain object?',
  'DOMAIN.DELETE.OBJECT.SUCCESS': 'Succeed in deleting domain object!',
  'DOMAIN.CHANGE.VERSION': 'Change Version',
  'DOMAIN.CHANGE.VERSION.CONFIRM': 'Are you sure to change the version?',
  'DOMAIN.CHANGE.VERSION.NO_RESTART': 'Do not restart the odh service.',
  'DOMAIN.REFRESH.CACHE.SUCCESS': 'Refresh Cache successfully!',
  'DOMAIN.REFRESH.CACHE': 'Refresh Cache',
  'DOMAIN.IMPORT': 'Import',
  'DOMAIN.IMPORT.HOTFIX': 'Import Hotfix',
  'DOMAIN.SEARCH': 'Search',
  'DOMAIN.RESET': 'Reset',
  'DOMAIN.DOWNLOAD.SUCCESS': 'The file was downloaded successfully!',
  'DOMAIN.REFRESH.CACHE.FAILED': 'Failed to refresh cache.',
  'DOMAIN.REFRESH.CACHE.OPEN_DATA_API': 'Open Data API',
  'DOMAIN.REFRESH.CACHE.DOMAIN_DUBBO': 'Domain Dubbo',
  'DOMAIN.REFRESH.CACHE.DOMAIN_MODEL_TABLE': 'Domain Model Table',
  'DOMAIN.REFRESH.CACHE.TFM_SERVICE': 'TFM Service',
  'DOMAIN.PUBLIC_SENSITIVE': 'Public Sensitive',

  // ExpandEntity 相关国际化
  'EXPANDENTITY.TITLE': 'Expand Entities',
  'EXPANDENTITY.RELATIONSHIP_TYPE': 'Relationship Type',
  'EXPANDENTITY.EXPAND_ENTITY': 'Expand Entity',
  'EXPANDENTITY.ASSOCIATED_RELATIONSHIP': 'Associated Relationship',
  'EXPANDENTITY.EXPAND_NAME': 'Expand Name',
  'EXPANDENTITY.CREATION_SOURCE': 'Creation Source',
  'EXPANDENTITY.DESCRIPTION': 'Description',
  'EXPANDENTITY.OPERATION': 'Operation',
  'EXPANDENTITY.DELETE.TITLE': 'Delete Entity',
  'EXPANDENTITY.DELETE.DESCRIPTION': 'Are you sure to delete this record?',
  'EXPANDENTITY.DELETE.YES': 'Yes',
  'EXPANDENTITY.DELETE.NO': 'No',
  'EXPANDENTITY.DELETE.SUCCESS': 'Succeed in deleting the expand entity!',

  // AddExpandEntity 相关国际化
  'EXPANDENTITY.ADD.TITLE': 'Add Expand Entity',
  'EXPANDENTITY.ADD.SUBMIT': 'Submit',
  'EXPANDENTITY.ADD.CANCEL': 'Cancel',
  'EXPANDENTITY.ADD.CURRENT_ENTITY': 'Current Entity',
  'EXPANDENTITY.ADD.RELATIONSHIP_TYPE': 'Relationship Type',
  'EXPANDENTITY.ADD.EXPAND_ENTITY': 'Expand Entity',
  'EXPANDENTITY.ADD.ASSOCIATED_RELATIONSHIP': 'Associated Relationship',
  'EXPANDENTITY.ADD.ADD_RELATIONSHIP': '+ Add Associated Relationship',
  'EXPANDENTITY.ADD.EXPAND_NAME': 'Expand Name',
  'EXPANDENTITY.ADD.CREATION_SOURCE': 'Creation Source',
  'EXPANDENTITY.ADD.IGNORE_VERSION': 'Ignore Version',
  'EXPANDENTITY.ADD.DESCRIPTIONS': 'Descriptions',
  'EXPANDENTITY.ADD.SUCCESS': 'Succeed in adding expand entity!',
  'EXPANDENTITY.ADD.RELATIONSHIP_REQUIRED': 'Please input one associated relationship at least!',
  'EXPANDENTITY.ADD.RELATIONSHIP_DUPLICATE': 'Same associated relationship exists, please check!',
  'EXPANDENTITY.ADD.NAME_FORMAT_ERROR':
    'The Expand Name format is camel hump format, which can only contain English letters and numbers, starting with lowercase letters.',

  // EditExpandEntity 相关国际化
  'EXPANDENTITY.EDIT.TITLE': 'Edit Expand Entity',
  'EXPANDENTITY.EDIT.SUCCESS': 'Succeed in editing expand entity!',

  // EntityEvents 相关国际化
  'ENTITYEVENTS.EVENT_NAME': 'Event Name',
  'ENTITYEVENTS.EVENT_CODE': 'Event Code',
  'ENTITYEVENTS.REMARKS': 'Remarks',

  // EntityEvent 相关国际化
  'ENTITYEVENT.TITLE': 'Entity Events',
  'ENTITYEVENT.EVENT_NAME': 'Event Name',
  'ENTITYEVENT.EVENT_CODE': 'Event Code',
  'ENTITYEVENT.CREATION_SOURCE': 'Creation Source',
  'ENTITYEVENT.DESCRIPTION': 'Description',
  'ENTITYEVENT.OPERATION': 'Operation',
  'ENTITYEVENT.DELETE.TITLE': 'Delete Entity Event',
  'ENTITYEVENT.DELETE.DESCRIPTION': 'Are you sure to delete this record?',
  'ENTITYEVENT.DELETE.YES': 'Yes',
  'ENTITYEVENT.DELETE.NO': 'No',
  'ENTITYEVENT.DELETE.SUCCESS': 'Succeed in deleting the entity Event!',

  // AddEntityEvent 相关国际化
  'ENTITYEVENT.ADD.TITLE': 'Add Entity Event',
  'ENTITYEVENT.ADD.SUBMIT': 'Submit',
  'ENTITYEVENT.ADD.CANCEL': 'Cancel',
  'ENTITYEVENT.ADD.EVENT_NAME': 'Event Name',
  'ENTITYEVENT.ADD.RLC_EVENT_CATALOG': 'RLC Event Catalog',
  'ENTITYEVENT.ADD.RLC_EVENT_NAME': 'RLC Event Name',
  'ENTITYEVENT.ADD.CREATION_SOURCE': 'Creation Source',
  'ENTITYEVENT.ADD.IGNORE_VERSION': 'Ignore Version',
  'ENTITYEVENT.ADD.DESCRIPTIONS': 'Descriptions',
  'ENTITYEVENT.ADD.SUCCESS': 'Succeed in adding entity event!',

  // EditEntityEvent 相关国际化
  'ENTITYEVENT.EDIT.TITLE': 'Edit Entity Event',
  'ENTITYEVENT.EDIT.SUCCESS': 'Succeed in editing entity event!',

  // ExtensionService 相关国际化
  'EXTENSIONSERVICE.TITLE': 'Extension Services',
  'EXTENSIONSERVICE.SERVICE_NAME': 'Service Name',
  'EXTENSIONSERVICE.SERVICE_CATALOG': 'Service Catalog',
  'EXTENSIONSERVICE.SERVICE_SUBCATALOG': 'Service Subcatalog',
  'EXTENSIONSERVICE.CREATION_SOURCE': 'Creation Source',
  'EXTENSIONSERVICE.IMPLEMENT_TYPE': 'Implement Type',
  'EXTENSIONSERVICE.SERVICE_PATH': 'Service Path',
  'EXTENSIONSERVICE.OPERATION': 'Operation',
  'EXTENSIONSERVICE.DELETE.TITLE': 'Delete Extension Service',
  'EXTENSIONSERVICE.DELETE.DESCRIPTION': 'Are you sure to delete this record?',
  'EXTENSIONSERVICE.DELETE.YES': 'Yes',
  'EXTENSIONSERVICE.DELETE.NO': 'No',
  'EXTENSIONSERVICE.DELETE.SUCCESS': 'Succeed in deleting the extension service!',
  'EXTENSIONSERVICE.DELETE.FAIL': 'Fail in deleting the extension service!',
  'EXTENSIONSERVICE.TRANSFER': 'Transfer',
  'EXTENSIONSERVICE.TRANSFER.TITLE': 'Transfer Extension Services',
  'EXTENSIONSERVICE.TRANSFER.CURRENT_ENTITY': 'Current Entity',
  'EXTENSIONSERVICE.TRANSFER.TARGET_ENTITY': 'Target Entity',
  'EXTENSIONSERVICE.TRANSFER.TARGET_ENTITY_REQUIRED': 'Please select a target entity',
  'EXTENSIONSERVICE.TRANSFER.SERVICES': 'Services',
  'EXTENSIONSERVICE.TRANSFER.SERVICE_REQUIRED': 'Please select at least one service to transfer',
  'EXTENSIONSERVICE.TRANSFER.SOURCE_SERVICES': 'Source Services',
  'EXTENSIONSERVICE.TRANSFER.TARGET_SERVICES': 'Target Services',
  'EXTENSIONSERVICE.TRANSFER.SUBMIT': 'Submit',
  'EXTENSIONSERVICE.TRANSFER.CANCEL': 'Cancel',
  'EXTENSIONSERVICE.TRANSFER.SUCCESS': 'Successfully transferred extension services',
  'EXTENSIONSERVICE.TRANSFER.FAIL': 'Failed to transfer extension services',
  'EXTENSIONSERVICE.TRANSFER.WARNING':
    'Transferring services to other domain entity will change the Restful API paths of these services, so please proceed with caution. After the transfer is completed, ',
  'EXTENSIONSERVICE.TRANSFER.WARNING_HIGHLIGHT': 'make sure to update the service paths published on APIG accordingly.',
  'EXTENSIONSERVICE.TRANSFER.ITEM_UNIT': 'Item',
  'EXTENSIONSERVICE.TRANSFER.ITEMS_UNIT': 'Items',

  // AddExtensionService 相关国际化
  'EXTENSIONSERVICE.ADD.TITLE': 'Add Extension Service',
  'EXTENSIONSERVICE.ADD.SUBMIT': 'Submit',
  'EXTENSIONSERVICE.ADD.CANCEL': 'Cancel',
  'EXTENSIONSERVICE.ADD.SERVICE_NAME': 'Service Name',
  'EXTENSIONSERVICE.ADD.ENTITY': 'Entity',
  'EXTENSIONSERVICE.ADD.CREATION_SOURCE': 'Creation Source',
  'EXTENSIONSERVICE.ADD.DEPRECATED': 'Deprecated',
  'EXTENSIONSERVICE.ADD.MATCH_VERSION': 'Match Version',
  'EXTENSIONSERVICE.ADD.SERVICE_CATALOG': 'Service Catalog',
  'EXTENSIONSERVICE.ADD.SERVICE_PATH': 'Service Path',
  'EXTENSIONSERVICE.ADD.IMPLEMENT_TYPE': 'Implement Type',
  'EXTENSIONSERVICE.ADD.DUBBO_CLASS_PATH': 'Dubbo Class Path',
  'EXTENSIONSERVICE.ADD.DUBBO_METHOD': 'Dubbo Method',
  'EXTENSIONSERVICE.ADD.RESTFUL_API_PATH': 'Restful API Path',
  'EXTENSIONSERVICE.ADD.TFM_SERVICE_NAME': 'TFM Service Name',
  'EXTENSIONSERVICE.ADD.DESCRIPTIONS': 'Descriptions',
  'EXTENSIONSERVICE.ADD.SUCCESS': 'Succeed in adding entity service!',
  'EXTENSIONSERVICE.ADD.FAIL': 'Failed to add entity service.',
  'EXTENSIONSERVICE.ADD.ERROR': 'An error occurred while adding entity service.',
  'EXTENSIONSERVICE.ADD.DUBBO_METHOD_ERROR': 'This Dubbo method can not support GET method, please select others.',
  'EXTENSIONSERVICE.ADD.RESTFUL_METHOD_ERROR':
    'The Restful method can only support a method with the same name as the Restful method or the POST method, please select others.',

  // EditExtensionService 相关国际化
  'EXTENSIONSERVICE.EDIT.SERVICE_PATH_NOTE':
    "Notes：Changing this service's path or method may cause the loss of extension information in the project, so please modify with caution.",
  'EXTENSIONSERVICE.EDIT.TITLE': 'Edit Extension Service',
  'EXTENSIONSERVICE.EDIT.SUCCESS': 'Succeed in editing entity service!',
  'EXTENSIONSERVICE.EDIT.FAIL': 'Failed to edit entity service.',
  'EXTENSIONSERVICE.EDIT.ERROR': 'An error occurred while editing entity service.',
  'EXTENSIONSERVICE.EDIT.REFRESH': 'Refresh Request API Reference',
  'EXTENSIONSERVICE.EDIT.REFRESH_NOTE':
    "Notes：Choosing 'Yes' will read the latest request and response information from the dubbo client jar and overwrite the configuration of you have modified.",

  // ManageExtensionService 相关国际化
  'MANAGEEXTENSIONSERVICE.BASIC_INFORMATION': 'Basic Information',
  'MANAGEEXTENSIONSERVICE.EDIT': 'Edit',
  'MANAGEEXTENSIONSERVICE.IMPORT_BY_SWAGGER': 'Import By Swagger',
  'MANAGEEXTENSIONSERVICE.SERVICE_ORCHESTRATE': 'Service Orchestrate',
  'MANAGEEXTENSIONSERVICE.NAME': 'Name',
  'MANAGEEXTENSIONSERVICE.SERVICE_CATALOG': 'Service Catalog',
  'MANAGEEXTENSIONSERVICE.SERVICE_SUBCATALOG': 'Service Subcatalog',
  'MANAGEEXTENSIONSERVICE.CREATION_SOURCE': 'Creation Source',
  'MANAGEEXTENSIONSERVICE.SERVICE_METHOD': 'Service Method',
  'MANAGEEXTENSIONSERVICE.SERVICE_PATH': 'Service Path',
  'MANAGEEXTENSIONSERVICE.IMPLEMENT_TYPE': 'Implement Type',
  'MANAGEEXTENSIONSERVICE.DUBBO_CLASS_PATH': 'Dubbo Class Path',
  'MANAGEEXTENSIONSERVICE.DUBBO_METHOD': 'Dubbo Method',
  'MANAGEEXTENSIONSERVICE.RESTFUL_SERVICE_PATH': 'Restful Service Path',
  'MANAGEEXTENSIONSERVICE.HTTP_METHOD': 'Http Method',
  'MANAGEEXTENSIONSERVICE.DESCRIPTIONS': 'Descriptions',
  'MANAGEEXTENSIONSERVICE.IMPLEMENT_TYPE.NEW': 'New',
  'MANAGEEXTENSIONSERVICE.IMPLEMENT_TYPE.DUBBO': 'Projection Dubbo API',
  'MANAGEEXTENSIONSERVICE.IMPLEMENT_TYPE.RESTFUL': 'Projection Restful API',
  'MANAGEEXTENSIONSERVICE.IMPLEMENT_TYPE.CALL_SERVICE': 'Projection Call Service',
  'MANAGEEXTENSIONSERVICE.DEBUG': 'Debug',

  // ManageExtensionService 常量相关国际化
  'MANAGEEXTENSIONSERVICE.CONST.YES': 'Yes',
  'MANAGEEXTENSIONSERVICE.CONST.NO': 'No',
  'MANAGEEXTENSIONSERVICE.CONST.JSON': 'json',
  'MANAGEEXTENSIONSERVICE.CONST.FORM_DATA': 'form-data',
  'MANAGEEXTENSIONSERVICE.CONST.BINARY': 'binary',
  'MANAGEEXTENSIONSERVICE.CONST.PRODUCT_OWNED': 'Product-Owned',
  'MANAGEEXTENSIONSERVICE.CONST.PROJECT_CUSTOMIZED': 'Project-Customized',
  'MANAGEEXTENSIONSERVICE.CONST.SINGLE_DATA': 'Single Data',
  'MANAGEEXTENSIONSERVICE.CONST.ARRAY_DATA': 'Array Data',
  'MANAGEEXTENSIONSERVICE.CONST.NO_DATA': 'No Data',
  'MANAGEEXTENSIONSERVICE.CONST.SUCCESS': 'Success',
  'MANAGEEXTENSIONSERVICE.CONST.EXCEPTION': 'Exception',

  // ManageExtensionService 格式类型相关国际化
  'MANAGEEXTENSIONSERVICE.FORMAT_TYPE.PASSWORD': 'password',
  'MANAGEEXTENSIONSERVICE.FORMAT_TYPE.BYTE': 'byte',
  'MANAGEEXTENSIONSERVICE.FORMAT_TYPE.EMAIL': 'email',
  'MANAGEEXTENSIONSERVICE.FORMAT_TYPE.BINARY': 'binary',
  'MANAGEEXTENSIONSERVICE.FORMAT_TYPE.FLOAT': 'float',
  'MANAGEEXTENSIONSERVICE.FORMAT_TYPE.DOUBLE': 'double',
  'MANAGEEXTENSIONSERVICE.FORMAT_TYPE.DATE_TIME': 'date-time',
  'MANAGEEXTENSIONSERVICE.FORMAT_TYPE.DATE': 'date',
  'MANAGEEXTENSIONSERVICE.FORMAT_TYPE.UTC_TIME': 'utc-time',

  // RequestInformation 和 AddRequestInformation 相关国际化
  'MANAGEEXTENSIONSERVICE.REQUEST.PARAM': 'Param',
  'MANAGEEXTENSIONSERVICE.REQUEST.ADD.SUCCESS': 'Succeed in adding request parameter!',
  'MANAGEEXTENSIONSERVICE.REQUEST.UPDATE.SUCCESS': 'Succeed in modifying request parameter!',
  'MANAGEEXTENSIONSERVICE.REQUEST.OPERATION.ERROR': 'An error occurred while {operation} request parameter.',
  'MANAGEEXTENSIONSERVICE.REQUEST.PARAM.NAME.SPACE.ERROR': "The service param name can't contain spaces",
  'MANAGEEXTENSIONSERVICE.REQUEST.PARAM.IS_ARRAY': 'Is Array',
  'MANAGEEXTENSIONSERVICE.REQUEST.PARAM.ALLOWABLE_VALUES': 'Allowable Values',
  'MANAGEEXTENSIONSERVICE.REQUEST.PARAM.DEFAULT_VALUE': 'Default Value',
  'MANAGEEXTENSIONSERVICE.SCHEMA.ADD.SUCCESS': 'Succeed in adding new schema!',
  'MANAGEEXTENSIONSERVICE.SCHEMA.NAME.INVALID':
    'SchemaName must start with an uppercase letter and contain only letters, numbers, and underscores.',
  'MANAGEEXTENSIONSERVICE.SCHEMA.PLACEHOLDER': 'Please enter item',
  'MANAGEEXTENSIONSERVICE.SCHEMA.NEW': 'New Schema',
  'MANAGEEXTENSIONSERVICE.SCHEMA.EDIT.NOTES':
    'Notes:After submission, you can click on the schema in the Type column of the parameter table to modify its information.',
  'MANAGEEXTENSIONSERVICE.NOTES': 'Notes',
  'MANAGEEXTENSIONSERVICE.MIN': 'Min',
  'MANAGEEXTENSIONSERVICE.MAX': 'Max',
  'MANAGEEXTENSIONSERVICE.MIN.ERROR': 'Min is {min}',
  'MANAGEEXTENSIONSERVICE.VALUES.PLACEHOLDER': 'Multiple values separated by commas for input.',
  'MANAGEEXTENSIONSERVICE.REQUEST.DELETE.SUCCESS': 'Succeed in deleting the request parameter!',
  'MANAGEEXTENSIONSERVICE.REQUEST.DELETE.TITLE': 'Delete Request Param',
  'MANAGEEXTENSIONSERVICE.REQUEST.DELETE.DESCRIPTION': 'Are you sure to delete the request parameter?',
  'MANAGEEXTENSIONSERVICE.REQUEST.PARAMETERS': 'Parameters',
  'MANAGEEXTENSIONSERVICE.REQUEST.CONTENT_TYPE': 'Content Type',
  'MANAGEEXTENSIONSERVICE.REQUEST.BODY.CHANGE.TITLE': 'Confirm Change',
  'MANAGEEXTENSIONSERVICE.REQUEST.BODY.CHANGE.DESCRIPTION':
    'If you change the selection, the previous body parameters will be cleaned. Are you sure you want to change the selection?',
  'MANAGEEXTENSIONSERVICE.REQUEST.BEFORE_CALL_SERVICE_SCRIPT': 'Before Call Service Script',
  'MANAGEEXTENSIONSERVICE.REQUEST.INFORMATION': 'Request Information',
  'PROJECT.COMMON.YES': 'Yes',
  'PROJECT.COMMON.NO': 'No',

  // ResponseInformation 和 AddResponseInformation 相关国际化
  'MANAGEEXTENSIONSERVICE.RESPONSE.ADD.TITLE': 'Add Response {Header} Param',
  'MANAGEEXTENSIONSERVICE.RESPONSE.ADD.SUCCESS': 'Succeed in adding response {Header} parameter!',
  'MANAGEEXTENSIONSERVICE.RESPONSE.ADD.ERROR': 'An error occurred while adding response {Header} parameter.',
  'MANAGEEXTENSIONSERVICE.RESPONSE.HTTP_STATUS': 'Http Status',
  'MANAGEEXTENSIONSERVICE.RESPONSE.CONTENT_TYPE': 'Content Type',
  'MANAGEEXTENSIONSERVICE.RESPONSE.DATA': 'Response Data',
  'MANAGEEXTENSIONSERVICE.RESPONSE.DELETE.SUCCESS': 'Succeed in deleting the response {Header} parameter!',
  'MANAGEEXTENSIONSERVICE.RESPONSE.DELETE.TITLE': 'Delete Response {Header} Param',
  'MANAGEEXTENSIONSERVICE.RESPONSE.DELETE.DESCRIPTION': 'Are you sure to delete the response {Header} parameter?',
  'MANAGEEXTENSIONSERVICE.RESPONSE.MORE': 'more',
  'MANAGEEXTENSIONSERVICE.RESPONSE.INFORMATION': 'Response Information',
  'MANAGEEXTENSIONSERVICE.RESPONSE.AFTER_SERVICE_SCRIPT': 'After Service Response Script',
  'MANAGEEXTENSIONSERVICE.RESPONSE.EDIT.TITLE': 'Edit Response {Header} Param',
  'MANAGEEXTENSIONSERVICE.RESPONSE.UPDATE.SUCCESS': 'Succeed in modifying response {Header} parameter!',
  'MANAGEEXTENSIONSERVICE.RESPONSE.UPDATE.ERROR': 'An error occurred while modifying response {Header} parameter.',
  'MANAGEEXTENSIONSERVICE.RESPONSE.SUCCESS_STATUS_EXISTS': 'A success status code {code} already exists.',

  // ResponseErrorCodeList 相关国际化
  'MANAGEEXTENSIONSERVICE.RESPONSE.ERRORCODE.CODE': 'Code',
  'MANAGEEXTENSIONSERVICE.RESPONSE.ERRORCODE.REASON': 'Reason',
  'MANAGEEXTENSIONSERVICE.RESPONSE.ERRORCODE.PRIORITY': 'Priority',
  'MANAGEEXTENSIONSERVICE.RESPONSE.ERRORCODE.DELETE.SUCCESS': 'Succeed in deleting the error code!',
  'MANAGEEXTENSIONSERVICE.RESPONSE.ERRORCODE.DELETE.ERROR': 'Fail in deleting the error code!',
  'MANAGEEXTENSIONSERVICE.RESPONSE.ERRORCODE.DELETE.TITLE': 'Delete Error Code',
  'MANAGEEXTENSIONSERVICE.RESPONSE.ERRORCODE.DELETE.DESCRIPTION': 'Are you sure to delete this record?',
  'MANAGEEXTENSIONSERVICE.RESPONSE.ERRORCODE.SET.TITLE': 'Set Error Code Allowable Values',
  'PROJECT.COMMON.OK': 'OK',

  // SchemaManagement related internationalization
  'SCHEMAMANAGEMENT.TITLE': 'Schema Management',
  'SCHEMAMANAGEMENT.BASIC_INFO': 'Basic Information',
  'SCHEMAMANAGEMENT.SCHEMA_PROPERTIES': 'Schema Properties',
  'SCHEMAMANAGEMENT.COMPOSITION_SCHEMAS': 'Composition Schemas',
  'SCHEMAMANAGEMENT.ADD': 'Add',
  'SCHEMAMANAGEMENT.ADD_BY_CODE': 'Add By Code',
  'SCHEMAMANAGEMENT.DELETE_SCHEMA_CONFIRM': 'Are you sure to delete the schema?',
  'SCHEMAMANAGEMENT.DELETE_SCHEMA_NOTE':
    'Note: If the schema is generated by reading Dubbo or Swagger, please do not delete it. You can set Hidden to Yes. Because after deleted it, when reading Dubbo or Swagger again, this property will still be added.',
  'SCHEMAMANAGEMENT.DELETE_SCHEMA_PROPERTY_TITLE': 'Delete Schema Property',
  'SCHEMAMANAGEMENT.DELETE_SCHEMA_PROPERTY_CONFIRM': 'Are you sure to delete the schema property?',
  'SCHEMAMANAGEMENT.DELETE_SCHEMA_PROPERTY_NOTE':
    'Note: If the schema is generated by reading Dubbo or Swagger, please do not delete it. You can set Hidden to Yes. Because after deleted it, when reading Dubbo or Swagger again, this property will still be added.',
  'SCHEMAMANAGEMENT.UNSAVED_CHANGES': 'The Schema Properties have not been saved!',
  'SCHEMAMANAGEMENT.UNSAVED_CHANGES_CLEAR': "If you click 'OK', the unsaved data will be cleared.",
  'SCHEMAMANAGEMENT.MODIFY_SUCCESS': 'Succeed in modifying Schema Information!',
  'SCHEMAMANAGEMENT.MODIFY_FAILED': 'Failed to modify Schema Information.',
  'SCHEMAMANAGEMENT.ERROR_OCCURRED': 'An error occurred.',
  'SCHEMAMANAGEMENT.DELETE_ERROR': 'An error occurred while deleting the Schema.',
  'SCHEMAMANAGEMENT.QUERY_ERROR': 'An error occurred while querying Schema Information.',
  'SCHEMAMANAGEMENT.SEARCH': 'Search',
  'SCHEMAMANAGEMENT.SUBMIT': 'Submit',
  'SCHEMAMANAGEMENT.CANCEL': 'Cancel',
  'SCHEMAMANAGEMENT.ADD_PROPERTY': 'Add Property',
  'SCHEMAMANAGEMENT.EDIT_PROPERTY': 'Edit Property',
  'SCHEMAMANAGEMENT.NAME_EXISTS': 'The name already exists, please change it.',
  'SCHEMAMANAGEMENT.NEW_SCHEMA': 'New Schema',
  'SCHEMAMANAGEMENT.PLEASE_ENTER_ITEM': 'Please enter item',
  'SCHEMAMANAGEMENT.NOTES': 'Notes',
  'SCHEMAMANAGEMENT.SCHEMA_MODIFY_INSTRUCTIONS':
    'After submission, you can click on the schema in the Type column of the parameter table to modify its information.',
  'SCHEMAMANAGEMENT.VALUES_PLACEHOLDER': 'Multiple values separated by commas for input.',

  // AddByCode related internationalization
  'SCHEMAMANAGEMENT.ADD_BY_CODE.TITLE': 'Add Schema Properties By Java Code',
  'SCHEMAMANAGEMENT.ADD_BY_CODE.JAVA_CODE': 'Java Code',
  'SCHEMAMANAGEMENT.ADD_BY_CODE.SUCCESS': 'Succeed in adding parameters!',

  // Table columns related internationalization
  'SCHEMAPROPERTY.NAME': 'Name',
  'SCHEMAPROPERTY.TYPE': 'Type',
  'SCHEMAPROPERTY.REQUIRED': 'Required',
  'SCHEMAPROPERTY.ALLOWABLE_VALUES': 'Allowable Values',
  'SCHEMAPROPERTY.EXAMPLE': 'Example',
  'SCHEMAPROPERTY.DESCRIPTION': 'Description',
  'SCHEMAPROPERTY.OPERATION': 'Operation',
  'SCHEMAPROPERTY.ORDER': 'Order',
  'SCHEMAPROPERTY.MIN': 'Min',
  'SCHEMAPROPERTY.MAX': 'Max',
  'SCHEMAPROPERTY.HIDDEN': 'Hidden',
  'SCHEMAPROPERTY.IS_ARRAY': 'Is Array',
  'SCHEMAPROPERTY.DEFAULT_VALUE': 'Default Value',
  'SCHEMAPROPERTY.SENSITIVE_DATA_LEVEL': 'Sensitive Data Level',
  'SCHEMAPROPERTY.DESCRIPTIONS': 'Descriptions',
};
