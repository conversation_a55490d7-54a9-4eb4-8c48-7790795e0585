import { useEffect, useState } from 'react';
import BssODataApiOuter from './BssODataApiOuter';
import BssSearchUseCase from './BssSearchUseCase';
import styles from './index.less';
import APIGService from './APIGService';
import ErrorCode from './ErrorCode';
import { useModel } from 'umi';

const BssODataApi = () => {
  const { initSystemConfigData } = useModel('useSystemConfigDataModel');
  // Search Use Case 界面控制显隐
  const [showSearchUseCase, setShowSearchUseCase] = useState<boolean>(false);
  const [showAPIGService, setShowAPIGService] = useState<boolean>(false);
  const [showErrorCode, setShowErrorCode] = useState<boolean>(false);

  useEffect(() => {
    initSystemConfigData();
  }, []);

  return (
    <div className={styles.bssODataApi}>
      {showSearchUseCase && <BssSearchUseCase onClose={() => setShowSearchUseCase(false)} />}
      {showAPIGService && <APIGService onClose={() => setShowAPIGService(false)} />}
      {showErrorCode && <ErrorCode onClose={() => setShowErrorCode(false)} />}
      <BssODataApiOuter
        hidden={showSearchUseCase || showAPIGService || showErrorCode}
        onOpenSelectUseCase={() => setShowSearchUseCase(true)}
        onOpenSelectAPIGService={() => setShowAPIGService(true)}
        onOpenSelectErrorCode={() => setShowErrorCode(true)}
      />
    </div>
  );
};

export default BssODataApi;
