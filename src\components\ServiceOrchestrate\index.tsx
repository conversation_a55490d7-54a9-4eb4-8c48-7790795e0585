import useI18n from '@/hooks/useI8n';

export const ServiceOrchestrate = (props: any) => {
  const { extensionServiceInfo, setOpenServiceOrchestrate } = props;
  const { formatMessage } = useI18n();

  // 添加消息监听器
  window.addEventListener('message', (event) => {
    // 获取消息的来源
    const origin = event.origin;

    // 验证消息来源是否为同域
    if (origin !== window.location.origin) {
      console.warn(formatMessage('SERVICE.ORCHESTRATE.INVALID_SOURCE'), origin);
      return;
    }

    // 获取消息内容
    const message = event.data;

    // 处理不同的消息类型
    if (message.actionType === 'easycode_flow_return') {
      // console.log('收到返回消息:', message);
      // 在这里处理具体的业务逻辑
      setOpenServiceOrchestrate(false);
    } else {
      console.warn(formatMessage('SERVICE.ORCHESTRATE.UNKNOWN_TYPE'), message);
    }
  });

  return (
    <div
      style={{
        position: 'fixed',
        top: 0,
        left: 0,
        right: 0,
        bottom: 0,
        width: '100%',
        height: '100%',
        // backgroundColor: 'rgba(228,228,228)',
        backgroundColor: 'white',
        zIndex: 9998,
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
      }}
    >
      <iframe
        src={`/odh-web/easycode/index.html?serviceId=${extensionServiceInfo?.serviceId}#/singleFlowDesign`}
        style={{
          width: '100%',
          height: '100%',
          border: 'none',
          zIndex: 9999,
        }}
      />
    </div>
  );
};
