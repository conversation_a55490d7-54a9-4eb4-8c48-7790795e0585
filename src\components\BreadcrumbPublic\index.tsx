import React from "react";
import { Breadcrumb } from "antd";
import { breadCrumbsType } from "@/typing";
import classNames from "classnames";
import styles from "./index.less";

interface IBreadcrumbPublic {
  breadCrumbs: breadCrumbsType[];
  separator?: any;
  className?: string;
}

const BreadcrumbPublic: React.FC<IBreadcrumbPublic> = ({
  breadCrumbs = {},
  separator = "/",
  className = "",
}) => {
  return (
    <div>
      <Breadcrumb
        separator={separator}
        className={classNames(styles.breadCrumbs, className)}
        items={breadCrumbs as any}
      />
    </div>
  );
};

export default BreadcrumbPublic;
