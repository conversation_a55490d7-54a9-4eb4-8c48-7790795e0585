.entityPropertiesContent {
  margin-top: 12px;
  .entityPropertiesLegend {
    height: 46px;
    background: #edf5ff;
    border-radius: 2px 2px 2px 2px;
    opacity: 1;
    border: 1px solid #9cc0ff;
    padding: 0 12px;
    display: flex;
    align-items: center;
    .legendItem {
      font-size: 14px;
      font-family: Nunito Sans;
      font-weight: 400;
      color: #2d3040;
      display: flex;
      align-items: center;
      & > span:nth-child(2) {
        margin-left: 4px;
      }
    }
    .legendItem:not(:first-child) {
      margin-left: 24px;
    }
  }
  .dataTypeObject {
    color: #4477ee;
    cursor: pointer;
  }
}
.iconItem {
  width: 12px;
}
.iconItemActive {
  cursor: pointer;
}
.iconItem:not(:first-child) {
  margin-left: 11px;
}
