import { StrikethroughOutlined } from '@ant-design/icons';
import styles from './index.less';
import CodeMirrorCmp from '../ReactCodemirror';
import { useCallback, useMemo, useRef, useState } from 'react';
import { EditorView, ViewUpdate } from '@codemirror/view';
import { keywordCompletionSource, MySQL } from '@codemirror/lang-sql';
import { globalCompletion } from '@codemirror/lang-python';
import { defineLanguageFacet } from '@codemirror/language';
import { java } from '@codemirror/legacy-modes/mode/clike';
import { debounce } from 'lodash';
import { autocompletion, CompletionContext, CompletionResult } from '@codemirror/autocomplete';

interface ICodePrompt {
  id: string;
  className?: string;
  value?: string;
  basicSetup?: any;
  editable?: boolean;
  placeholder?: string;
  height?: string;
  minHeight?: string;
  maxHeight?: string;
  language?: string;
  allSchema?: any[];
  disabled?: boolean;
  readOnly?: boolean;
  onChange?: (value: string, viewUpdate: ViewUpdate) => void;
}

const CodePrompt: React.FC<ICodePrompt> = (props) => {
  const {
    id,
    editable = true,
    value,
    onChange,
    placeholder,
    basicSetup,
    height,
    className,
    minHeight,
    maxHeight,
    language,
    allSchema = [],
    disabled = false,
    readOnly = false,
  } = props;

  const editorRef = useRef();
  const [cursorPosition, setCursorPostion] = useState<number>(0); // cursor state
  const [varOpen, setVarOpen] = useState<boolean>(false); // select var open modal

  // handleVar close
  const handleVarClose = () => {
    setCursorPostion(0);
    setVarOpen(false);
  };

  // handle var select
  const handleVarSelect = (selectPath: string) => {
    (editorRef?.current as any)?.view.dispatch({
      changes: { from: cursorPosition, insert: selectPath },
    });
    handleVarClose();
  };

  // schemaItem 根据一组path从schemaItem里面获取一组数据
  const findSchema: (schema: any, path: Array<string>) => any = (schema, path) => {
    const { type } = schema;
    if (path.length === 0) {
      return schema;
    }

    const currentLevel = path[0];
    if (type === 'Object' && schema.properties) {
      if (!(currentLevel in schema.properties)) {
        return findSchema(schema, path.slice(1));
      } else {
        // 数组的话 需要再拼接一个items的字段
        // 其他的则为普通对象的字段
        return findSchema(
          schema.properties[currentLevel].type === 'Array'
            ? schema.properties[currentLevel].items
            : schema.properties[currentLevel],
          path.slice(1),
        );
      }
    } else if (schema.type === 'Array' && schema.items && schema.items.type === 'Object') {
      return findSchema(schema.items, path.slice(1));
    } else {
      return {};
    }
  };

  // 动态获取options
  const dynamicGetOptions = (schema: any, autocompleteText: string) => {
    // 一级比较特殊 返回一级
    let dynamicOptions: any[] = [];
    if (autocompleteText === '$.') {
      dynamicOptions = (schema || []).map((scheItemI: any) => ({
        label: `$.${scheItemI.title}`,
        displayLabel: scheItemI.title,
        apply: `$.${scheItemI.title}`,
        type: 'text',
        detail: scheItemI.type,
      }));
    } else {
      // 按照层级往下找
      const splitText = autocompleteText.split('.').filter((i) => i);
      // 删除$.的第一个选项
      splitText.shift();
      // 1、校验是否是符合规则的输入 带中括号或者不带中括号
      const regex = /^[a-zA-Z0-9]+(\[.*\])?$/;
      const isAllOk = splitText.every((j) => regex.test(j));
      if (!isAllOk) {
        return [];
      }
      // 2、移除掉 带数组过滤的场景
      const regexReplace = /([a-zA-Z0-9]+)\[.*\]/;
      const newSplitText = splitText.map((j) => {
        const result = j.replace(regexReplace, '$1');
        return result;
      });
      const singleSchema = allSchema.find((i: any) => i.title === newSplitText[0]);
      const findSchemaRes = findSchema(singleSchema, newSplitText);
      // 3、解析对应规则查找出来的schema 并拼接hints
      const { type } = findSchemaRes;
      if (!type) {
        return [];
      }
      switch (type) {
        case 'Object': {
          const { properties } = findSchemaRes;
          const keys = Object.keys(properties);
          return (keys || []).map((pKey) => ({
            label: `${autocompleteText}${pKey}`,
            displayLabel: pKey,
            apply: `${autocompleteText}${pKey}`,
            type: 'text',
            detail: properties[pKey]?.type,
          }));
        }
        default:
          return [];
      }
    }
    return dynamicOptions;
  };

  // 获取语言本身的默认补全提示
  const getDefaultCompletions = useCallback(
    (context: CompletionContext) => {
      if (!language) {
        return null;
      }
      let word = context.matchBefore(/\w*/);
      let defineLanguage: any = {};
      let languageData: any = {};
      let autocomplete: any = [];
      switch (language) {
        case 'sql':
          return keywordCompletionSource(MySQL)(context);
        case 'python':
          return globalCompletion(context);
        case 'java':
          if (word?.from === word?.to && !context.explicit) return null;
          defineLanguage = defineLanguageFacet(java);
          languageData = defineLanguage?.default?.[0]?.languageData || {};
          autocomplete = languageData?.autocomplete || {};
          return {
            from: word?.from || 0,
            options: autocomplete.map((i: string) => ({ label: i, type: '' })),
          };
        default:
          return null;
      }
    },
    [language],
  );

  // auto complete
  const myCompletions = async (context: CompletionContext): Promise<CompletionResult | null> => {
    // 输入匹配获取
    const regExp = /\$\.\S*$/;
    let word = context.matchBefore(regExp);
    const { text = '' } = word || {};
    if (!text || !(text || '')?.endsWith('.') || (word?.from === word?.to && !context.explicit)) {
      // 如果不匹配自定义补全的条件，尝试默认补全
      return getDefaultCompletions(context);
    }
    //  根据schema 计算下一个层级的hint数据
    const dyOptions = dynamicGetOptions(allSchema, text);
    return {
      from: word?.from || 0,
      options: dyOptions,
    };
  };

  // handle code prompt change
  const handleCodePrompt = (value: string, viewUpdate: ViewUpdate) => {
    onChange?.(value, viewUpdate);
  };

  // debounce code prompt change
  const emitChangeByDebounce = useMemo(() => {
    return debounce((value: string, viewUpdate: ViewUpdate) => {
      handleCodePrompt(value, viewUpdate);
    }, 500);
  }, [id, props]);

  const lodaCodePrompt = useMemo(() => {
    return (
      <CodeMirrorCmp
        disabled={disabled}
        readOnly={readOnly}
        ref={editorRef}
        value={typeof value === 'string' ? value : ''} // 处理value为null的情况
        onChange={emitChangeByDebounce}
        height={height}
        basicSetup={basicSetup}
        className={className}
        minHeight={minHeight}
        maxHeight={maxHeight}
        placeholder={placeholder}
        language={language}
        extensions={[
          autocompletion({
            tooltipClass: () => 'tootip-complete-container cp',
            override: [myCompletions],
            // override: null,
            selectOnOpen: true,
            closeOnBlur: false,
            defaultKeymap: true,
          }),
          EditorView.updateListener.of((view) => {
            // 获取并记录光标位置
            const { from, to } = (view as any)?.state?.selection?.ranges?.[0] || {};
            if (from !== undefined && to !== undefined && from === to) {
              setCursorPostion(from);
            }
          }),
        ]}
      />
    );
  }, [
    EditorView,
    editorRef,
    emitChangeByDebounce,
    value,
    height,
    basicSetup,
    className,
    minHeight,
    maxHeight,
    placeholder,
    language,
    id,
    allSchema,
    editable,
    onChange,
  ]);

  return (
    <div
      className={styles.codePromptWrapper}
      //   className={props?.['aria-invalid'] ? styles.validError : styles.common} 作为表单组件
      onMouseDown={(e) => e.stopPropagation()}
    >
      {/* 变量选择弹窗 */}
      {editable ? (
        <StrikethroughOutlined
          onClick={() => {
            setVarOpen(true);
          }}
          style={{
            position: 'absolute',
            right: '10px',
            top: '8px',
            zIndex: '999',
            cursor: 'pointer',
            color: 'rgba(161,161,161,1)',
          }}
        />
      ) : null}

      {lodaCodePrompt}
    </div>
  );
};

export default CodePrompt;
