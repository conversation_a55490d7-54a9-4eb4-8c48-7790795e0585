import React, { useState, useEffect, useRef, forwardRef, useImperativeHandle } from 'react';
import { ICurrentActiveKeyCatalog } from '@/typing.d';
import { queryCatalogDetailByName } from '@/services';
import { ICatalogDetailResult } from '@/services/typing.d';
import EntityServiceList from '@/components/EntityServices';
import styles from './index.less';
import OperationIconTitle from '@/components/OperationIconTitle';
import { ArrowRightOutlined } from '@ant-design/icons';
import { Checkbox, Flex } from 'antd';
import useI18n from '@/hooks/useI8n';
import { BYCATALOG } from '@/constants';

interface ICatalogDetailRef {
  reload: () => void;
}

interface IBssCatalogDetail {
  currentCatalog?: ICurrentActiveKeyCatalog;
  onSelectService?: (serviceCode: string) => void;
}

const BssCatalogDetail: React.ForwardRefRenderFunction<ICatalogDetailRef, IBssCatalogDetail> = (props, ref) => {
  const { currentCatalog, onSelectService } = props;
  const { formatMessage } = useI18n();

  const [catalogServices, setCatalogServices] = useState<Array<ICatalogDetailResult>>([]); // catalog data
  const [spinning, setSpinning] = useState(false); // service loading
  const [isShowDeprecatedData, setIsShowDeprecatedData] = useState<boolean>(false); // 是否展示过时数据

  // 查询catalog detail
  const querCatalogDetail = async (catalog: string, group: string, subCatalog: string) => {
    if (!catalog || !group || !subCatalog) return;
    setSpinning(true);
    try {
      const { data } = await queryCatalogDetailByName({
        catalog,
        group,
        subCatalog,
      });
      setCatalogServices(data || []);
      setSpinning(false);
    } catch (error) {
      setSpinning(false);
    }
  };

  // 查询catalog detail
  const handleRequest = () => {
    if (
      currentCatalog?.clickCatalog?.catalog &&
      currentCatalog?.clickGroup?.groupName &&
      currentCatalog?.clickSubCatalog?.subCatalog
    ) {
      querCatalogDetail(
        currentCatalog?.clickCatalog?.catalog,
        currentCatalog?.clickGroup?.groupName,
        currentCatalog?.clickSubCatalog?.subCatalog,
      );
    } else {
      setCatalogServices([]);
    }
  };

  // catalog详情提供对外刷新
  useImperativeHandle(ref, () => ({
    reload: handleRequest,
  }));

  useEffect(() => {
    handleRequest();
  }, [currentCatalog?.clickSubCatalog?.subCatalog, currentCatalog?.clickSubCatalog?.currentPathList]); // 添加currentPathList作为依赖，解决点击同名目录不刷新的问题

  useEffect(() => {
    setIsShowDeprecatedData(false);
  }, [catalogServices]);

  return (
    <div className={styles.bssCatalogDetailContainer}>
      {currentCatalog && (
        <div className={styles.activeLineContainer}>
          <div className={styles.topContent}>
            <span className={styles.contentName}>{currentCatalog?.clickCatalog?.catalog}</span>
            <ArrowRightOutlined className={styles.arrowRight} />
            <span className={styles.contentName}>{currentCatalog?.clickGroup?.groupName}</span>
            <ArrowRightOutlined className={styles.arrowRight} />
            <span className={styles.contentName}>{currentCatalog?.clickSubCatalog?.subCatalog}</span>
          </div>
        </div>
      )}
      <Flex align="center" gap="20px">
        <OperationIconTitle title="Service List" />
        <Checkbox
          checked={isShowDeprecatedData}
          onChange={(e) => {
            setIsShowDeprecatedData(e.target.checked);
          }}
        >
          {formatMessage('DOMAINDETAIL.SERVICES.SHOWDEPRECATED')}
        </Checkbox>
      </Flex>
      <div className={styles.serviceContent}>
        <EntityServiceList
          fromType={BYCATALOG}
          isShowDeprecatedData={isShowDeprecatedData}
          spining={spinning}
          serviceListData={catalogServices}
          onSelectService={(serviceCode) => onSelectService?.(serviceCode)}
        />
      </div>
    </div>
  );
};

export default forwardRef(BssCatalogDetail);
