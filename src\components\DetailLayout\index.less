.detailContainer {
  width: 100%;
  height: calc(100% - 44px);
  padding: 16px;
  margin-top: 52px;
  box-sizing: border-box;
  background: #ffffff;
  overflow-y: auto;
  p {
    margin: 0;
  }
  .header {
    display: flex;
    align-items: center;
    height: 44px;
    background: #ffffff;
    box-shadow: 0px 0px 2px 0px rgba(0, 0, 0, 0.12);
    padding: 0 12px;
    position: fixed;
    width: 100%;
    left: 0;
    top: 0;
    z-index: 999;

    .returnIcon {
      font-size: 18px;
      color: #2d3040;
      cursor: pointer;
    }
    .returnText {
      font-size: 18px;
      font-family: Nunito Sans-Bold;
      font-weight: bold;
      color: #2d3040;
      line-height: 28px;
      margin-left: 8px;
      cursor: pointer;
    }
  }
}
