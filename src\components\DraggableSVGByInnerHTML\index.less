.operationIcon {
  color: #797979;
  font-size: 20px;
  position: absolute;
  cursor: pointer;
  top: 10px;
  z-index: 999;
}

.operationIcon:nth-child(1) {
  right: 72px; /* 放大按钮 */
}

.operationIcon:nth-child(2) {
  right: 40px; /* 缩小按钮 */
}

.operationIcon:nth-child(3) {
  right: 8px; /* 重置按钮 */
}

.draggableContainer {
  width: 100%;
  height: 100%;
  min-height: inherit;
  position: relative;
  overflow: hidden; /* 隐藏超出部分 */
}

.draggableSVG {
  position: absolute; /* 绝对定位 */
  cursor: grab; /* 初始光标样式 */
  transform-origin: top left; /* 确保缩放从左上角开始 */
}
