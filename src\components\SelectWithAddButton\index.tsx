import useI18n from '@/hooks/useI8n';
import getUUid from '@/utils/getUUid';
import { DeleteOutlined } from '@ant-design/icons';
import { Button, Divider, Flex, FormInstance, Input, message, Select, Space, Tooltip } from 'antd';
import { forwardRef, useImperativeHandle, useState } from 'react';
import styles from './index.less';

interface ValidationRule {
  validator: (value: string) => boolean;
  message: string;
}

interface ISelectWithAddButton {
  originalOptionsList: OptionItemType[]; // 原选项列表
  addButtonName?: string;
  parentForm: FormInstance;
  formFieldName: string;
  disabled?: boolean;
  customInputChange?: (value: string) => string; // 对输入的自定义处理
  customSelectChange?: (value: string) => void; // 对选项变化的自定义处理
  customInputRules?: ValidationRule[]; // 自定义输入校验规则数组
  customInputAddonAfter?: { node: React.ReactNode; value?: string }; // 自定义输入后缀，若有value，则新增时将value拼接在输入值后面
}

interface OptionItemType {
  label: string;
  value: string | number;
  key: string | number;
  state: string; // new | original
  [key: string]: any; // 允许其他任意字段
}

const SelectWithAddButton = forwardRef((props: ISelectWithAddButton, ref) => {
  const {
    originalOptionsList = [],
    addButtonName = 'New',
    parentForm,
    formFieldName,
    disabled = false,
    customInputChange,
    customSelectChange,
    customInputRules,
    customInputAddonAfter,
  } = props;
  const [newOptionName, setNewOptionName] = useState<string>(''); // 新增数据，未入库
  const [newOptionsList, setNewOptionsList] = useState<OptionItemType[]>([]); // 新增数据列表，未入库

  const { formatMessage } = useI18n();

  const onSelectChange = (value: any) => {
    parentForm.setFieldValue(formFieldName, value);
    customSelectChange?.(value);
  };

  const onInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const value = customInputChange?.(event.target.value || '') || event.target.value;
    setNewOptionName(value);
  };

  // 新增，未入库
  const addItem = (e: React.MouseEvent<HTMLButtonElement | HTMLAnchorElement>) => {
    e.preventDefault();
    if (newOptionName) {
      // 先进行自定义规则校验
      if (customInputRules && customInputRules.length > 0) {
        for (const rule of customInputRules) {
          if (!rule.validator(newOptionName)) {
            message.warning(rule.message);
            return;
          }
        }
      }
      // 自定义输入后缀，若有value，则新增时将value拼接在输入值后面
      const newOptionNameWithCustomRule = customInputAddonAfter?.value
        ? `${newOptionName}${customInputAddonAfter?.value}`
        : newOptionName;
      const existItem = [...originalOptionsList, ...newOptionsList]?.find(
        (item) => item?.label === newOptionNameWithCustomRule,
      );
      if (existItem) {
        message.warning(`${newOptionNameWithCustomRule} has already exists.`);
      } else {
        setNewOptionsList((prev: any) => [
          ...prev,
          {
            label: newOptionNameWithCustomRule,
            value: newOptionNameWithCustomRule,
            key: getUUid(6),
            state: 'new',
          },
        ]);
        parentForm.setFieldValue(formFieldName, newOptionNameWithCustomRule); // 新建后默认选中
        setNewOptionName(''); // 清空输入
      }
    }
  };

  const onInputToSearch = (input: any, option: any) => {
    return option?.label?.toLowerCase()?.indexOf(input?.toLowerCase()) >= 0;
  };

  const deleteItem = (value: any) => {
    setNewOptionsList((prev) => prev.filter((item) => item.value !== value));
    parentForm.setFieldValue(formFieldName, '');
  };

  // 提供给父组件的方法
  useImperativeHandle(ref, () => ({
    resetNewOptionList: () => {
      setNewOptionName('');
      setNewOptionsList([]);
      parentForm.setFieldValue(formFieldName, '');
    },
    setNewOptionsList: (newOptionsList: OptionItemType[]) => {
      setNewOptionsList(newOptionsList);
    },
  }));

  return (
    <Select
      disabled={disabled}
      value={parentForm.getFieldValue(formFieldName)} // 和parentForm双向绑定
      options={[...newOptionsList, ...originalOptionsList]}
      allowClear
      showSearch
      filterOption={onInputToSearch}
      onChange={onSelectChange}
      optionRender={(option) => {
        if (option.data.state === 'new') {
          return (
            <Flex justify="space-between">
              <span className={styles.selectLabel}>{option.label}</span>
              <Flex>
                <div className={styles.dyStyleNew}>New</div>
                {/* <Tooltip title={formatMessage('PROJECT.COMMON.DELETE')}>
                  <DeleteOutlined
                    className={styles.commonIcon}
                    onClick={(e) => {
                      e.stopPropagation();
                      deleteItem(option.value);
                    }}
                  />
                </Tooltip> */}
              </Flex>
            </Flex>
          );
        }
        return option.label;
      }}
      dropdownRender={(menu) => (
        <>
          {menu}
          <Divider style={{ margin: '8px 0' }} />
          <Space style={{ padding: '0 8px 4px' }}>
            <Input
              allowClear
              value={newOptionName}
              placeholder="Please enter item"
              onChange={onInputChange}
              onKeyDown={(e) => e.stopPropagation()}
              addonAfter={customInputAddonAfter?.node}
            />
            <Button type="text" onClick={addItem}>
              + {addButtonName}
            </Button>
          </Space>
        </>
      )}
    />
  );
});

export default SelectWithAddButton;
