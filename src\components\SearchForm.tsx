import { Button, Form, Space } from "antd";

export default function SearchForm(props: any) {
    const [form] = Form.useForm()
    const handleSearch = () => {

    }
    const handleReset = () => {
        form.resetFields()
    }
    return (
        <Form form={props.form} layout="inline" initialValues={props.state}>
            <Form.Item>
                <Space>
                    <Button type='primary' className='mr10' onClick={handleSearch}>搜索</Button>
                    <Button type='default' onClick={handleReset}>重置</Button>
                </Space>
            </Form.Item>
        </Form>
    )
}