import React, { useEffect, useState } from 'react';
import { Modal, Upload, message, Button, Spin, Flex } from 'antd';
import type { RcFile, UploadFile } from 'antd/es/upload/interface';
import { InboxOutlined } from '@ant-design/icons';
import { hotfixImportDomainFile, importDomainFile } from '@/services/domainService';
import ChangeVersionModal from '@/pages/BssODomainApi/BssDomainDetail/ChangeVersion';
import { isHotfix } from '@/global';
import { useModel } from 'umi';
import useI18n from '@/hooks/useI8n';

interface IImportFileModal {
  onOk: () => void;
  open: boolean;
  setVisible: any;
  selectedDomainNode: any;
}

const { Dragger } = Upload;
const ImportFileModal: React.FC<IImportFileModal> = (props) => {
  const { open, setVisible, selectedDomainNode, onOk } = props;
  const { formatMessage } = useI18n();
  const [jsonFileList, setJsonFileList] = useState<UploadFile[]>([]);
  const [spining, setSpining] = useState<boolean>(false);
  const [asyncFlag, setAsyncFlag] = useState<boolean>(false);
  const [openChangeVersionModal, setOpenChangeVersionModal] = useState<boolean>(false);
  const { isConfigEnvironment } = useModel('useSystemConfigDataModel');

  const uploadChange = (newFileList: UploadFile[]) => {
    setJsonFileList(newFileList);
  };

  const onSubmit = async () => {
    setSpining(true);
    try {
      const params = {
        domainId: selectedDomainNode?.domainId,
        autoRelease: asyncFlag ? 'Y' : 'N',
      };
      const formData = new FormData();
      if (jsonFileList?.length > 0) {
        formData.append('domainDefFile', jsonFileList[0] as RcFile);
      } else {
        message.warning(formatMessage('IMPORT.FILE.SELECT_WARNING'));
        return;
      }

      const { success } = isHotfix
        ? await hotfixImportDomainFile(
            {
              ...params,
              autoRelease: 'N',
            },
            formData,
          )
        : await importDomainFile(params, formData);
      if (success) {
        message.success(formatMessage('IMPORT.FILE.SUCCESS'));
        setVisible(false);
        if (!isHotfix && asyncFlag) {
          setOpenChangeVersionModal(true);
        } else {
          onOk?.();
        }
      } else {
        message.error(formatMessage('IMPORT.FILE.FAILURE'));
      }
    } catch (error) {
      console.error('An error occurred:', error);
      message.error(formatMessage('IMPORT.FILE.ERROR'));
    } finally {
      setSpining(false);
    }
  };

  useEffect(() => {
    uploadChange([]);
    if (open) {
      setAsyncFlag(false);
    }
  }, [open]);
  return (
    <>
      <Modal
        title={formatMessage('IMPORT.FILE.TITLE', {
          type: isHotfix ? formatMessage('IMPORT.FILE.HOTFIX') : formatMessage('IMPORT.FILE.CONFIGURATION'),
        })}
        open={open}
        width={850}
        maskClosable={false}
        onCancel={() => setVisible(false)}
        footer={
          <Flex gap="1rem" justify="end">
            <Button onClick={() => setVisible(false)}>{formatMessage('PROJECT.COMMON.CANCEL')}</Button>
            <Spin spinning={spining}>
              <Button onClick={onSubmit} type="primary">
                {formatMessage('PROJECT.COMMON.CONFIRM')}
              </Button>
            </Spin>
          </Flex>
        }
      >
        <Dragger
          accept=".jar,.zip"
          className="dragger"
          maxCount={1}
          onRemove={(file) => {
            const index = jsonFileList.indexOf(file);
            const newFileList = jsonFileList.slice();
            newFileList.splice(index, 1);
            uploadChange(newFileList);
          }}
          beforeUpload={(file) => {
            uploadChange([file]);
            return false;
          }}
          fileList={jsonFileList}
        >
          <p className="ant-upload-drag-icon">
            <InboxOutlined />
          </p>
          <p className="ant-upload-text">
            {formatMessage('IMPORT.FILE.DRAG_TEXT', {
              type: isHotfix ? formatMessage('IMPORT.FILE.HOTFIX') : formatMessage('IMPORT.FILE.CONFIGURATION'),
            })}
          </p>
        </Dragger>
        <div style={{ marginTop: '5px' }}>
          {isHotfix ? (
            <div style={{ color: 'red' }}>{formatMessage('IMPORT.FILE.HOTFIX_WARNING')}</div>
          ) : (
            isConfigEnvironment && (
              <>
                {/* 使用环境不展示该单选框 */}
                <input
                  type="checkbox"
                  checked={asyncFlag}
                  onChange={() => setAsyncFlag((prevAsyncFlag) => !prevAsyncFlag)}
                />
                <span style={{ marginLeft: '5px' }}>{formatMessage('IMPORT.FILE.SWITCH_VERSION')}</span>
              </>
            )
          )}
        </div>
      </Modal>
      {/* 确认切换版本*/}
      <ChangeVersionModal
        selectedDomainNode={selectedDomainNode}
        startStep={0}
        open={openChangeVersionModal}
        onCancel={() => setOpenChangeVersionModal(false)}
        onOk={() => {
          setOpenChangeVersionModal(false);
          //成功切换版本后，重新查询版本信息，渲染表格
          onOk?.();
        }}
      />
    </>
  );
};

export default ImportFileModal;
