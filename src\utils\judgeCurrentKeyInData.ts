import { IGroup<PERSON>ist, ICatalog } from "@/services/typing.d";
import { ICurrentActiveKey } from "@/typing.d";
import { ICurrentActiveKeyCatalog } from "@/typing.d";

const judgeExistInEntity: (
  currentActiveKey: ICurrentActiveKey,
  groupData: Array<IGroupList>
) => boolean = (currentActiveKey, groupData) => {
  const isExist = groupData.some((groupItem) => {
    return (
      groupItem?.group === currentActiveKey?.clickGroup?.group &&
      (groupItem?.domainList || []).some((domainItem) => {
        return (
          domainItem?.domainKey === currentActiveKey?.clickDomain?.domainKey &&
          (domainItem?.entityList || []).some((entityItem) => {
            return (
              entityItem?.entityKey === currentActiveKey?.clickEntity?.entityKey
            );
          })
        );
      })
    );
  });
  return isExist;
};

const judgeExistInCatalog: (
  currentActiveKey: ICurrentActiveKeyCatalog,
  catalogData: Array<ICatalog>
) => boolean = (currentActiveKey, catalogData) => {
  const isExist = catalogData.some((catalogItem) => {
    return (
      catalogItem?.catalog === currentActiveKey?.clickCatalog?.catalog &&
      (catalogItem?.groupList || []).some((groupItem) => {
        return (
          groupItem?.groupName === currentActiveKey?.clickGroup?.groupName &&
          (groupItem?.subCatalogList || []).some((subCatalogItem) => {
            return (
              subCatalogItem?.subCatalog ===
              currentActiveKey?.clickSubCatalog?.subCatalog
            );
          })
        );
      })
    );
  });
  return isExist;
};

export { judgeExistInEntity, judgeExistInCatalog };
