import React, { useState, useEffect } from 'react';
import { Button, Drawer, Form, Input, Select, Space, Spin, message } from 'antd';
import { addDomainEntity } from '@/services/entityService';
import { queryDomainDataModelListService } from '@/services/dataModelService';
import useI18n from '@/hooks/useI8n';

interface IAddDomainEntityDrawer {
  domainObjType: string;
  isProduct: boolean;
  domainObjId: number;
  open: boolean;
  ignoreVersionSource: any;
  onCancel: () => void;
  onOk: () => void;
}

const AddDomainEntityDrawer: React.FC<IAddDomainEntityDrawer> = (props) => {
  const { ignoreVersionSource, domainObjType, open, domainObjId, isProduct, onCancel, onOk } = props;

  const { formatMessage } = useI18n();
  const [submitSpinning, setSubmitSpinning] = useState<boolean>(false);
  const [form] = Form.useForm();
  const [formValues, setFormValues] = useState<any>({}); // formValues
  const [dataModelSource, setDataModelSource] = useState<any>([]);
  const [entityTypeKey, setEntityTypeKey] = useState<string>(''); // A: AggregateRoot, E: Entity, S: ServiceHolder

  const CREATIONSOURCE = [
    { name: 'Product-Owned', key: 'P' },
    { name: 'Project-Customized', key: 'C' },
  ];

  const EntityTypeSource = [
    // A: AggregateRoot, E: Entity, S: ServiceHolder
    { name: 'Entity', key: 'E' },
    ...(domainObjType === 'C' ? [{ name: 'ServiceHolder', key: 'S' }] : [{ name: 'AggregateRoot', key: 'A' }]),
  ];
  const DefaultPageSizeSource = [
    // 5、10、20、50、100
    { name: '5', key: '5' },
    { name: '10', key: '10' },
    { name: '20', key: '20' },
    { name: '50', key: '50' },
    { name: '100', key: '100' },
  ];

  const initValues = {
    creationSrc: isProduct ? 'P' : 'C',
    defaultPage: '10',
  };

  // modal close
  const onClose = async () => {
    form.resetFields();
    onCancel?.();
  };

  const addDomainEntityService = async () => {
    setSubmitSpinning(true);
    try {
      // 调用新增领域实体接口
      const curPostData = {
        ...formValues,
        ignoreVer: formValues?.ignoreVer?.length ? formValues?.ignoreVer?.join(',') : null, // 处理ignoreVer显示空选项的bug
        domainObjId,
      };
      const resultData = await addDomainEntity(curPostData);

      if (resultData?.success) {
        message.success(formatMessage('DOMAIN.ENTITY.ADD.SUCCESS'));
        form.resetFields();
        onOk?.();
      }
    } finally {
      setSubmitSpinning(false);
    }
  };

  const qryDomainDataModelList = async () => {
    if (domainObjId) {
      const { success, data } = await queryDomainDataModelListService(domainObjId, {
        pageNo: 1,
        pageSize: 100,
      });
      if (success) {
        setDataModelSource(data?.list || []);
      }
    }
  };

  const checkDomainEntityData = () => {
    form.validateFields().then(() => {
      addDomainEntityService();
    });
  };

  const onEntityTypeChange = (key: string) => {
    setEntityTypeKey(key);
  };

  useEffect(() => {
    // 用 moduleId 查询表和视图的列名称
    qryDomainDataModelList();
  }, [open]);

  return (
    <Drawer
      title={formatMessage('DOMAIN.ENTITY.ADD.TITLE')}
      onClose={onClose}
      maskClosable={false}
      open={open}
      width={720}
      afterOpenChange={() => {
        form.resetFields();
      }}
      footer={
        <Space style={{ float: 'right' }}>
          <Spin spinning={submitSpinning}>
            <Button type="primary" onClick={checkDomainEntityData}>
              {formatMessage('PROJECT.COMMON.SUBMIT')}
            </Button>
          </Spin>
          <Button onClick={onClose}>{formatMessage('PROJECT.COMMON.CANCEL')}</Button>
        </Space>
      }
    >
      <Form
        form={form}
        labelCol={{ span: 6 }}
        wrapperCol={{ span: 18 }}
        initialValues={initValues}
        onValuesChange={(cValues, alValues) => {
          setFormValues(alValues);
        }}
      >
        <Form.Item
          label={formatMessage('DOMAIN.ENTITY.FORM.NAME')}
          name="entityName"
          rules={[{ required: true, message: '' }]}
        >
          <Input allowClear />
        </Form.Item>
        <Form.Item
          label={formatMessage('DOMAIN.ENTITY.FORM.CODE')}
          name="entityCode"
          rules={[
            { required: true, message: '' },
            {
              validator: (_, value) => {
                const regex = /^[A-Z][a-zA-Z0-9]*$/;
                if (!regex.test(value)) {
                  return Promise.reject(new Error(formatMessage('DOMAIN.ENTITY.FORM.CODE.VALIDATOR')));
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <Input allowClear />
        </Form.Item>
        <Form.Item label={formatMessage('DOMAIN.ENTITY.FORM.CREATION_SOURCE')} name="creationSrc">
          <Select disabled>
            {CREATIONSOURCE.map((i) => (
              <Select.Option key={i.key} value={i.key}>
                {i.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        {isProduct ? (
          <Form.Item label={formatMessage('DOMAIN.ENTITY.FORM.IGNORE_VERSION')} name="ignoreVer">
            <Select mode="multiple">
              {ignoreVersionSource.map((i: any) => (
                <Select.Option key={i.key} value={i.key}>
                  {i.name}
                </Select.Option>
              ))}
            </Select>
          </Form.Item>
        ) : null}
        <Form.Item
          label={formatMessage('DOMAIN.ENTITY.FORM.ENTITY_TYPE')}
          name="entityType"
          rules={[{ required: true, message: '' }]}
        >
          <Select onChange={onEntityTypeChange}>
            {EntityTypeSource.map((i) => (
              <Select.Option key={i.key} value={i.key}>
                {i.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        <Form.Item
          label={formatMessage('DOMAIN.ENTITY.FORM.DEFAULT_PAGE_SIZE')}
          name="defaultPage"
          rules={[{ required: true, message: '' }]}
        >
          <Select>
            {DefaultPageSizeSource.map((i) => (
              <Select.Option key={i.key} value={i.key}>
                {i.name}
              </Select.Option>
            ))}
          </Select>
        </Form.Item>
        {entityTypeKey !== 'S' && (
          <Form.Item
            label={formatMessage('DOMAIN.ENTITY.FORM.DATA_MODEL')}
            name="modelId"
            rules={[{ required: true, message: '' }]}
          >
            <Select
              showSearch
              optionFilterProp="label"
              options={dataModelSource.map((i: any) => {
                return {
                  label: i.modelName,
                  value: i.modelId,
                };
              })}
            >
              {/* {dataModelSource.map((i: any) => (
                      <Select.Option key={i.modelId} value={i.modelId}>
                        {i.modelName}
                      </Select.Option>
                    ))} */}
            </Select>
          </Form.Item>
        )}
        <Form.Item
          label={formatMessage('DOMAIN.ENTITY.FORM.DESCRIPTION')}
          name="comments"
          rules={[{ required: true, message: '' }]}
        >
          <Input.TextArea allowClear />
        </Form.Item>
      </Form>
    </Drawer>
  );
};
export default AddDomainEntityDrawer;
